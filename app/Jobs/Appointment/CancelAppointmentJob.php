<?php

namespace App\Jobs\Appointment;

use App\Enums\Table\BihConfig\Group;
use App\Models\AppointmentPatientSummary;
use App\Models\BihConfigs;
use App\Services\Simrs\Appointment\CancelAppointmentService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CancelAppointmentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(protected int $id)
    {
        $this->onQueue('request-trakcare-queue');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Cancel Appointment Job started ' . $this->id);
            $appointment = AppointmentPatientSummary::withTrashed()->find($this->id);
            if (!$appointment){
                $this->fail('Appointment not found');
                return;
            }
            Log::info('Appointment found' . json_encode($appointment));

            $cancelReason = BihConfigs::whereGroup(Group::CANCEL_APPOINTMENT)
                ->whereValue($appointment->cancel_reason)->first();

            $data = [
                'appointment_id'    => $appointment->simrs_registration_no,
                'reason_id'         => @$cancelReason->key ? $cancelReason->key : 'ID22',
                'reason'            => @$cancelReason->value ? $cancelReason->value : 'Other',
            ];
            Log::info('Cancel Reason found');

            \Illuminate\Support\Facades\Artisan::call('horizon:cancel-delayed', [
                'jobClass' => 'ReminderNotification',
                'tag' => 'UUID ' . $appointment->uuid
            ]);
            Log::info('Reminder Notification canceled');

            $service = (new CancelAppointmentService($data, $appointment))->call();
            if($service->status() != 200){
                Log::error('Cancel Appointment failed', [
                    'Message ' => $service->message(),
                    'On file ' => $service->getFile(),
                    'On line ' => $service->getLine()
                ]);
                $this->fail(json_encode($service));
                return;
            }
            Log::info('Cancel Appointment success');
        }catch (\Exception $e){

            report($e);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $e->getMessage(),
                'On file ' => $e->getFile(),
                'On line ' => $e->getLine()
            ]);

            $this->fail(json_encode($e));

        }

    }

    public function tags(): array
    {
        return ['APPOINTMENT ID ' . $this->id];
    }
}
