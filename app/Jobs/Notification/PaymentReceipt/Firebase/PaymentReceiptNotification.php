<?php

namespace App\Jobs\Notification\PaymentReceipt\Firebase;

use App\Base\ServiceBase;
use App\Enums\General\NotificationID;
use App\Enums\General\NotificationType;
use App\Repositories\LandingPage\PublicUser\PublicUserRepository;
use App\Repositories\Patient\PatientRepository;
use App\Responses\ServiceResponse;
use App\Services\Notification\FirebaseCloudMessage\Base\FirebaseCloudMessageAPI;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class PaymentReceiptNotification extends ServiceBase
{

    use FirebaseCloudMessageAPI;

    protected PatientRepository $patientRepository;
    protected PublicUserRepository $publicUserRepository;

    public function __construct(protected string $id)
    {
      $this->patientRepository = new PatientRepository();
      $this->publicUserRepository = new PublicUserRepository();
    }

    /**
     * main method of this service
     *
     * @return ServiceResponse
     */
    public function call(): ServiceResponse {
        try{
                $patient = $this->patientRepository->getById($this->id);
                if($patient->parent_id != null){
                  $patient = $this->patientRepository->getById($patient->parent_id);
                }

                $publicUser = $this->publicUserRepository->getById($patient->public_user_id);
            // if ($publicUser->mobile_fcm_token) {

                $newData = array_merge([
                    "notification_id"   => NotificationID::PAYMENT_RECEIPT,
                    "code"              => "PAYMENT_RECEIPT",
                    "sound"             => "default",
                    "title"             => "Payment Complete",
                    "body"              => "Tap to download receipt",
                    "type"              => NotificationType::PAYMENT_RECEIPT,
                    "date_time"         => Carbon::now()->setTimezone(config('app.timezone')),
                    "click_action"      => "FLUTTER_NOTIFICATION_CLICK"
                ], $this->data['custom_data'] ?? []);

                $response               = send_fcm_notification($newData, $publicUser->mobile_fcm_token);
            // }

            return self::success($response,'Payment receipt notification was sent successfully');

        }catch (\Throwable $th) {

            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            return self::error(null, $th->getMessage());

        }

    }
}
