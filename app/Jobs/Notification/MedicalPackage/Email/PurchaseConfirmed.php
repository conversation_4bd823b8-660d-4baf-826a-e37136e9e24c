<?php

namespace App\Jobs\Notification\MedicalPackage\Email;

use App\Enums\Table\BihConfig\Key;
use App\Models\BihConfigs;
use App\Repositories\LandingPage\PackageSummary\PackageSummaryRepository;
use App\Services\Notification\Email\MedicalPackagePurchaseConfirmedMailService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Carbon\Carbon;

class PurchaseConfirmed implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected PackageSummaryRepository $packageSummaryRepository;

    /**
     * Create a new job instance.
     */
    public function __construct(protected string $uuid)
    {
        $this->onQueue('booking-notification');
        $this->packageSummaryRepository = new PackageSummaryRepository();
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $packageSummary = $this->packageSummaryRepository->findByCondition([
            'uuid' => $this->uuid
        ]);

        foreach ($packageSummary->packageSummaryDetails as $packageSummaryDetail){


            $data = [
                'email'                     => $packageSummary->publicUser->email,
                'medical_package_name'      => $packageSummaryDetail->package_title,
                'patient_name'              => $packageSummaryDetail->patient->fullname,
                'package_id'                => $packageSummaryDetail->package->simrs_id,
                'package_slug'              => $packageSummaryDetail->package->slug,
                'package_category'          => $packageSummaryDetail->packageCategory->slug,
                'scheduled_date'            => Carbon::parse($packageSummaryDetail->visit_date)->format('d F Y'),
                'scheduled_time'            => $packageSummaryDetail->visit_time_from,
                'my_bookings_link'          => route('profile.mybook.show', ['uuid' => $packageSummaryDetail->uuid, 'type' => 3]),
                'profile_link'              => route('profile.index'),
                'pre_visit_screening_link'  => $packageSummaryDetail->assessment_form_url,
                'qr_code_url'               => @$packageSummaryDetail->qrTransaction->url,
                'healt_screening_prep_link' => @BihConfigs::where('key', Key::HEALTH_SCREENING_PREPARATION_LINK)->first()->value ?? '-',
                'contact_us_number'         => @BihConfigs::where('key', Key::CALL_CENTER)->first()->value ?? '-',
                'contact_us_email'          => @BihConfigs::where('key', Key::EMAIL)->first()->value ?? '-',
            ];

            $service = (new MedicalPackagePurchaseConfirmedMailService($data))->call();

            $body = [
                "content"       => '[EMAIL - MEDICAL PACKAGE][MedicalPackagePurchaseConfirmedMailService WITH REQUEST BODY : '. json_encode($data).' : RESPONSE SERVICE ]' . json_encode($service),
                "avatar_url"    =>
                    "https://media.licdn.com/dms/image/D4E0BAQEtbeSJkNL14Q/company-logo_200_200/0/1664787920525/bithealth_logo?e=**********&v=beta&t=WdlP1wknXx5_nLF7A9CkWDAH9rQn6-aGHrlIZK-XjqI",
                "username"      => "developer-bithealth"
            ];


            Http::withHeaders([
                'Content-Type' => 'application/json'
            ])->post(config('discord.notification_channel.general_notification'), $body);

        }

    }

    public function tags(): array
    {
        return ['UUID ' . $this->uuid];
    }

}
