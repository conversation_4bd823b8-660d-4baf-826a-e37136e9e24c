<?php

namespace App\Livewire\OperatorBookings\Appointment;

use App\Enums\Table\BihConfig\Group;
use App\Enums\Table\BihConfig\Key;
use App\Models\BihConfigs;
use App\Models\Doctor;
use App\Services\Simrs\Doctor\Schedule\WeeklyService;
use Livewire\Component;
use Illuminate\Support\Facades\DB;

class ShowDoctor extends Component
{

    public $uuid;
    public $doctorCSWALink;
    public $schedules;

    public function mount($uuid)
    {
        $this->uuid = $uuid;
    }

    public function render()
    {
        $BooleanLinkAppoitmentExternal = false;
        $LinkAppoitmentExternal = null;
        $doctor = Doctor::whereuuid($this->uuid)->with([
            'specialty',
            'certificates',
            'clinicalInterests',
            'fellowships',
            'regularSchedules',
            'languages',
            'medicalSchools'
        ])->first();

        if(!$doctor){
            $this->redirectRoute('cms.dashboard');
        }

        $this->doctor<PERSON>WALink = @bih_config_value(Key::CUSTOMER_SERVICE, Group::CONTACT_INFORMATION);
        $scheduleService = (new WeeklyService([
            'doctor_id' => $doctor->simrs_doctor_id
        ], true))->call();
        if($scheduleService->status() == 200){
            $this->schedules = $scheduleService->data();
        }

        $doctors_clinic_icon = DB::table('doctors_clinic_icon')
            ->where('simrs_doctor_id', $doctor->simrs_doctor_id)
            ->first();

        if ($doctors_clinic_icon) {
            $BooleanLinkAppoitmentExternal = true;
            $LinkAppoitmentExternal = $doctors_clinic_icon->redirect_url;
        }

        return view('livewire.operator-bookings.appointment.show-doctor',[
            'doctor' => $doctor,
            'BooleanLinkAppoitmentExternal' => $BooleanLinkAppoitmentExternal,
            'LinkAppoitmentExternal' => $LinkAppoitmentExternal
        ]);
    }
}
