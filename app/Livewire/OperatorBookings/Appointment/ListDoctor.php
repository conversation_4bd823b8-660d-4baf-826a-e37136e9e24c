<?php

namespace App\Livewire\OperatorBookings\Appointment;

use App\Enums\Table\AppointmentPatientSummary\Type;
use App\Models\Doctor;
use Carbon\Carbon;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithPagination;

class ListDoctor extends Component
{
    use WithPagination;

    public $keyword;
    public $specialtyIds;
    public $dataFilters = [];

    public function mount($specialtyIds)
    {
        $this->specialtyIds = json_decode($specialtyIds);
    }

    public function render()
    {
        // query for doctors
        $keyword        = $this->keyword;
        $specialtyIds   = $this->specialtyIds;
        $dataFilters    = $this->dataFilters;
        $query = Doctor::query()->orderBy('created_at', 'asc')->when($this->keyword, function ($query) use($keyword){
            $query->where('name', 'LIKE', '%' . $keyword . '%');
        })->when($this->specialtyIds, function ($query) use ($specialtyIds){
            $query->whereHas('specialty', function ($query) use ($specialtyIds){
                $query->whereIn('id', $specialtyIds)->orWhere('parent_id', $specialtyIds);
            });
        })->when($this->dataFilters, function ($q) use($dataFilters){
            foreach ($dataFilters as $filter){

                if($filter['name'] == 'specialty'){
                    $id = $filter['key'];
                    $q->whereHas('specialty', function ($query) use($id){
                        $query->where('id', $id)->orWhere('parent_id', $id);

                    });
                }

                if($filter['name'] == 'language'){
                    $language = $filter['key'];
                    $q->whereHas('languages', function ($query) use ($language){
                        $query->where('name', ucwords($language));
                    });
                }

                if($filter['name'] == 'day'){
                    $day = $filter['key'];
                    $q->whereHas('regularSchedules', function ($query) use($day){
                        $query->where('day', $day);
                    });
                }

                if($filter['name'] == 'time'){
                    $time = collect(config('master_data.times'))->where('id', $filter['key'])->first();
                    $q->whereHas('regularSchedules', function ($query) use($time){
                        $timeFrom   = $time['value']['time_from'];
                        $timeTo     = $time['value']['time_to'];
                        $query->whereTime('time_from', '<=', $timeTo)
                            ->whereTime('time_to', '>=', $timeFrom);
                    });
                }

                if($filter['name'] == 'consultation_type'){
                    if($filter['key'] == Type::VISIT){
                        $q->where('is_available_appointment', true);
                    }else{
                        $q->where('is_available_teleconsultation', true);
                    }
                }

                if($filter['name'] == 'gender'){
                    $q->where('gender', $filter['key']);
                }

            }
        });
        return view('livewire.operator-bookings.appointment.list-doctor',[
            'doctors' => $query->paginate(9)
        ]);
    }

    #[On('doctor-searched')]
    public function onHandleSearching($keyword = null, $specialtyIds = [])
    {
        $this->keyword      = $keyword;
        $this->specialtyIds = $specialtyIds;
        $this->setPage(0);
    }

    #[On('doctor-searched-by-filter')]
    public function onHandleSearchingByFilter(array $data)
    {
        $this->dataFilters = $data;
    }

    public function onHandleResetFilter()
    {
        $this->dataFilters = [];
        $this->render();
    }

    public function onHandleResetSpecificFilter($type)
    {
        $this->unsetObjectByKey($this->dataFilters,$type);
        if(count($this->dataFilters) <=0) $this->isShowFilter = false;
    }

    function unsetObjectByKey(&$array, $key) {
        foreach ($array as $index => $item) {
            if ($item["key"] === $key) {
                unset($array[$index]);
                return true; // Object found and unset
            }
        }
        return false; // Object with specified key not found
    }

//    public function paginationView()
//    {
//        return 'vendor.pagination.tailwind';
//    }

}
