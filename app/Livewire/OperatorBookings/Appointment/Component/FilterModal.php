<?php

namespace App\Livewire\OperatorBookings\Appointment\Component;

use App\Enums\Table\AppointmentPatientSummary\Type;
use App\Enums\Table\BihConfig\Group;
use App\Models\BihConfigs;
use App\Models\Specialty;
use Livewire\Component;

class FilterModal extends Component
{
    public $selectedSpecialtyUuid;
    public $selectedSpecialtyName;
    public $selectedSpecialtyId;
    public $selectedSpecialtyUuids = [];
    public $selectedSpecialtyNames = [];
    public $selectedSpecialtyIds = [];
    public $selectedSpecialtyNamesText = "";
    
    // Main specialty group selection
    public $selectedMainSpecialtyNames = [];
    public $selectedMainSpecialtyNamesText = "";
    public $groupedSpecialties = [];
    public $showGroupedView = true;

    public $selectedLanguage;
    public $selectedDayName;
    public $selectedDayId;
    public $selectedTimeId;
    public $selectedTimeName;
    public $selectedConsultationTypeId;
    public $selectedConsultationTypeName;
    public $selectedGender;
    public $consultationTypes;
    public $specialties;
    public $languages;
    public $days;
    public $times;

    public $isModalSpecialty;
    public $isModalLanguage;
    public $isModalDay;
    public $isModalTime;
    public $hideFormInputs;

    public $searchSpecialty;

    public function mount($hideFromInputs)
    {
        $this->languages            = BihConfigs::where([
            'group' => Group::SPOKEN_LANGUAGE,
        ])->pluck('key')->toArray();
        $this->days                 = config('master_data.days');
        $this->times                = config('master_data.times');
        $this->consultationTypes    = Type::getValueAndId();
        $this->hideFormInputs       = $hideFromInputs;
    }

    public function render()
    {
        $this->specialties = Specialty::where('group_name_en', 'LIKE', '%'.$this->searchSpecialty.'%')->orderBy('group_name_en','asc')->get();

        // Group specialties by main specialty name using the model method
        $this->groupedSpecialties = Specialty::getGroupedByMainName($this->searchSpecialty);

        return view('livewire.operator-bookings.appointment.component.filter-modal');
    }

    public function onHandleSelectedSpecialty($uuid, $name, $id)
    {
        if(in_array($uuid, $this->selectedSpecialtyUuids)) {
            $idx = array_search($uuid, $this->selectedSpecialtyUuids);
            array_splice($this->selectedSpecialtyUuids, $idx, 1);
            array_splice($this->selectedSpecialtyNames, $idx, 1);
            array_splice($this->selectedSpecialtyIds, $idx, 1);
        }else {
            $this->selectedSpecialtyUuids[] = $uuid;
            $this->selectedSpecialtyNames[] = $name;
            $this->selectedSpecialtyIds[] = $id;
        }

        $this->updateSelectedSpecialtyText();
    }

    /**
     * Handle selection of main specialty group
     */
    public function onHandleSelectedMainSpecialty($mainSpecialtyName)
    {
        if(in_array($mainSpecialtyName, $this->selectedMainSpecialtyNames)) {
            // Remove main specialty and all its sub-specialties
            $idx = array_search($mainSpecialtyName, $this->selectedMainSpecialtyNames);
            array_splice($this->selectedMainSpecialtyNames, $idx, 1);

            // Remove all sub-specialties of this main specialty
            $this->removeSubSpecialtiesByMainName($mainSpecialtyName);
        } else {
            // Add main specialty and all its sub-specialties
            $this->selectedMainSpecialtyNames[] = $mainSpecialtyName;
            $this->addSubSpecialtiesByMainName($mainSpecialtyName);
        }

        $this->updateSelectedSpecialtyText();
    }

    /**
     * Remove all sub-specialties of a main specialty
     */
    private function removeSubSpecialtiesByMainName($mainSpecialtyName)
    {
        if (isset($this->groupedSpecialties[$mainSpecialtyName])) {
            foreach ($this->groupedSpecialties[$mainSpecialtyName]['specialties'] as $specialty) {
                $idx = array_search($specialty->uuid, $this->selectedSpecialtyUuids);
                if ($idx !== false) {
                    array_splice($this->selectedSpecialtyUuids, $idx, 1);
                    array_splice($this->selectedSpecialtyNames, $idx, 1);
                    array_splice($this->selectedSpecialtyIds, $idx, 1);
                }
            }
        }
    }

    /**
     * Add all sub-specialties of a main specialty
     */
    private function addSubSpecialtiesByMainName($mainSpecialtyName)
    {
        if (isset($this->groupedSpecialties[$mainSpecialtyName])) {
            foreach ($this->groupedSpecialties[$mainSpecialtyName]['specialties'] as $specialty) {
                if (!in_array($specialty->uuid, $this->selectedSpecialtyUuids)) {
                    $this->selectedSpecialtyUuids[] = $specialty->uuid;
                    $this->selectedSpecialtyNames[] = $specialty->group_name_en;
                    $this->selectedSpecialtyIds[] = $specialty->id;
                }
            }
        }
    }

    /**
     * Update selected specialty text display
     */
    private function updateSelectedSpecialtyText()
    {
        if (!empty($this->selectedMainSpecialtyNames)) {
            $this->selectedMainSpecialtyNamesText = implode(', ', $this->selectedMainSpecialtyNames);
        } else {
            $this->selectedMainSpecialtyNamesText = "";
        }

        if (!empty($this->selectedSpecialtyNames)) {
            $this->selectedSpecialtyNamesText = implode(', ', $this->selectedSpecialtyNames);
        } else {
            $this->selectedSpecialtyNamesText = "";
        }
    }

    public function onHandleSelectedLanguage($language)
    {
        if($language == $this->selectedLanguage){
            $this->selectedLanguage = null;
        }else{
            $this->selectedLanguage = $language;
        }
        $this->isModalLanguage  = false;
    }

    public function onHandleSelectedDay($id, $name)
    {
        if($id == $this->selectedDayId){
            $this->selectedDayId    = null;
            $this->selectedDayName  = null;
        }else{
            $this->selectedDayId    = $id;
            $this->selectedDayName  = $name;
        }
        $this->isModalDay       = false;
    }

    public function onHandleSelectedTime($id, $name)
    {
        if($id == $this->selectedTimeId){
            $this->selectedTimeId   = null;
            $this->selectedTimeName = null;
        }else{
            $this->selectedTimeId   = $id;
            $this->selectedTimeName = $name;
        }
        $this->isModalTime      = false;
    }

    public function onHandleSelectedConsultationType($id, $name)
    {
        if($id == $this->selectedConsultationTypeId){
            $this->selectedConsultationTypeId   = null;
            $this->selectedConsultationTypeName = null;
        }else{
            $this->selectedConsultationTypeId   = $id;
            $this->selectedConsultationTypeName = $name;
        }
    }

    public function onHandleSelectedGender($gender){
        if($gender == $this->selectedGender){
            $this->selectedGender = null;
        }else{
            $this->selectedGender = $gender;
        }
    }
    
    /**
     * Toggle between grouped and individual specialty view
     */
    public function toggleSpecialtyView()
    {
        $this->showGroupedView = !$this->showGroupedView;
    }

    /**
     * Check if main specialty is selected
     */
    public function isMainSpecialtySelected($mainSpecialtyName)
    {
        return in_array($mainSpecialtyName, $this->selectedMainSpecialtyNames);
    }

    /**
     * Check if all sub-specialties of a main specialty are selected
     */
    public function areAllSubSpecialtiesSelected($mainSpecialtyName)
    {
        if (!isset($this->groupedSpecialties[$mainSpecialtyName])) {
            return false;
        }

        foreach ($this->groupedSpecialties[$mainSpecialtyName]['specialties'] as $specialty) {
            if (!in_array($specialty->uuid, $this->selectedSpecialtyUuids)) {
                return false;
            }
        }

        return true;
    }


    public function onHandleReset(): void
    {
        $this->selectedSpecialtyUuid        = null;
        $this->selectedSpecialtyUuids       = [];
        $this->selectedSpecialtyNames       = [];
        $this->selectedSpecialtyIds         = [];
        $this->selectedSpecialtyNamesText   = "";
        $this->selectedMainSpecialtyNames   = [];
        $this->selectedMainSpecialtyNamesText = "";
        $this->selectedSpecialtyName        = null;
        $this->selectedSpecialtyId          = null;
        $this->selectedLanguage             = null;
        $this->selectedDayName              = null;
        $this->selectedDayId                = null;
        $this->selectedTimeId               = null;
        $this->selectedTimeName             = null;
        $this->selectedConsultationTypeId   = null;
        $this->selectedConsultationTypeName = null;
        $this->selectedGender               = null;
    }

    public function onHandleSearch()
    {
        $data = [];
        // Handle specialty filtering - use main specialty names if available, otherwise individual specialties
        if (!empty($this->selectedMainSpecialtyNames)) {
            foreach ($this->selectedMainSpecialtyNames as $mainSpecialtyName) {
                $data[] = [
                    'name'  => 'main_specialty',
                    'key'   => $mainSpecialtyName,
                    'label' => $mainSpecialtyName
                ];
            }
        } elseif (!empty($this->selectedSpecialtyUuids)) {
            foreach ($this->selectedSpecialtyIds as $key => $id) {
                $data[] = [
                    'name'  => 'specialty',
                    'key'   => $id,
                    'label' => $this->selectedSpecialtyNames[$key]
                ];
            }
        }

        if($this->selectedLanguage){
            $data[] = [
                'name'  => 'language',
                'key'   => $this->selectedLanguage,
                'label' => $this->selectedLanguage
            ];
        }

        if($this->selectedDayId){
            $data[] = [
                'name'  => 'day',
                'key'   => $this->selectedDayId,
                'label' => $this->selectedDayName
            ];
        }

        if($this->selectedTimeId){
            $data[] = [
                'name'  => 'time',
                'key'   => $this->selectedTimeId,
                'label' => $this->selectedTimeName
            ];
        }

        if($this->selectedConsultationTypeId){
            $data[] = [
                'name'  => 'consultation_type',
                'key'   => $this->selectedConsultationTypeId,
                'label' => $this->selectedConsultationTypeName
            ];
        }

        if($this->selectedGender){
            $data[] = [
                'name'  => 'gender',
                'key'   => $this->selectedGender,
                'label' => $this->selectedGender == 'M' ? 'Male' : 'Female'
            ];
        }
        $this->dispatch('doctor-searched-by-filter' , data:$data);
        $this->dispatch('filter-search-doctor-status', status:false);
        $this->toggleAllModal();
    }

    public function onHandleModalSpecialty()
    {
        $this->isModalSpecialty = !$this->isModalSpecialty;
    }

    public function onHandleModalLanguage()
    {
        $this->isModalLanguage  = !$this->isModalLanguage;
    }

    public function onHandleModalDay()
    {
        $this->isModalDay = !$this->isModalDay;
    }

    public function onHandleModalTime()
    {
        $this->isModalTime = !$this->isModalTime;
    }

    public function toggleAllModal()
    {
        $this->searchSpecialty = null;
        
        $this->isModalSpecialty = false;
        $this->isModalLanguage  = false;
        $this->isModalDay       = false;
        $this->isModalTime      = false;
    }

}
