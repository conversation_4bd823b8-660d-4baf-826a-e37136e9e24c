<?php

namespace App\Livewire\LandingPage\Appointment;

use App\Enums\Appointment\AppointmentState;
use App\Enums\General\GeneralString;
use App\Enums\General\RegistrationNoType;
use App\Enums\Simrs\ErrorCode;
use App\Enums\Simrs\Service;
use App\Enums\Table\AppointmentPatientSummary\PaymentMethod;
use App\Enums\Table\AppointmentPatientSummary\PaymentStatus;
use App\Enums\Table\AppointmentPatientSummary\SimrsStatus;
use App\Enums\Table\AppointmentPatientSummary\Type;
use App\Events\Appointment\AppointmentSubmitted;
use App\Models\AppointmentPatientSummary;
use App\Models\Doctor;
use App\Models\LandingPage\PublicUser;
use App\Models\Patient;
use App\Repositories\LandingPage\AppointmentPatientSummary\AppointmentPatientSummaryRepository;
use App\Repositories\Simrs\CheckAvailabilityScheduleData;
use App\Services\CreateQRAppointmentService;
use App\Services\LandingPage\Appointment\CreateAppointmentService;
use App\Services\LandingPage\Appointment\UpdateAppointmentService;
use App\Services\PaymentGateway\Xendit\CreateInvoiceService;
use App\Services\Simrs\Doctor\Schedule\DailyService;
use App\Traits\Calendar;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Livewire\Attributes\On;
use Livewire\Component;
use Livewire\WithFileUploads;
class Book extends Component
{

    use WithFileUploads, Calendar;

    public $state = AppointmentState::SCHEDULE;

    public $doctor;
    public $uuid;
    public $isWithoutDoctor;
    public $appointment;

    // form for appointment
    public $selectedDay;
    public $selectedTime;
    public $selected_slot_id;
    public $selected_price;
    public $selectedMedicalConcern;
    public $selectedAttachment;
    public $user;
    public $selectedPatientId;
    public $selectedPayer; // payment method

    public $isConfirmData = false;
    public $type;
    public $attachmentPath;
    public $language;

    public function mount($uuid, $doctor_uuid, $type)
    {

        $currentLanguage = request()->server('HTTP_LINGUISE_ORIGINAL_LANGUAGE') ?? null;

        $this->language = in_array($currentLanguage, ['id', 'en']) ? $currentLanguage : null;

        $this->uuid     = $uuid;
        $this->type     = $type;
        if($doctor_uuid == "NO"){
            $this->isWithoutDoctor  = true;
            $this->doctor           = null;
        }else{
            $this->doctor = Doctor::whereuuid($doctor_uuid)->first();
            $this->isWithoutDoctor = false;
        }
        $this->user     = PublicUser::find(Auth::guard('public')->id());

        // get the state if exist
        $appointment = AppointmentPatientSummary::whereuuid($this->uuid)->first();
        if($appointment){
            $this->state                    = $appointment->sequence;
            $this->selectedMedicalConcern   = $appointment->medical_concern;
            $this->appointment              = $appointment;
        }


    }
    public function render()
    {
//        if($this->state == AppointmentState::FINISH && $this->appointment->type == Type::TELECONSULTATION){
//
//            if(in_array($this->appointment->payment_status,[
//                PaymentStatus::UNPAID,
//                PaymentStatus::EXPIRED
//            ])){
//                sleep(3);
//                $this->redirect(route('profile.mybook.index'));
//           }
//
//        }

        return view('livewire.landingpage.appointment.book');
    }

    public function nextState()
    {
        if($this->state == AppointmentState::SCHEDULE){

            if($this->appointment){
                $updateData = self::constructScheduleData();
                (new UpdateAppointmentService($updateData))->call();
            }else{
                $createData = self::constructScheduleData();
                (new CreateAppointmentService($createData))->call();
            }

            $this->state = $this->state + 1;
            $this->dispatch('appointment-state-updated', state:$this->state);

            $this->appointment = AppointmentPatientSummary::whereuuid($this->uuid)->first();
            $this->render();
        }elseif($this->state == AppointmentState::CONFIRMATION) {

            // check availability schedule to simrs
            $checkAvailabilitySchedule              = new CheckAvailabilityScheduleData();
            $checkAvailabilitySchedule->service     = $this->appointment->type == 1 ? Service::HOSPITAL_VISIT : Service::TELECONSULTATION;
            $checkAvailabilitySchedule->doctor_id   = $this->doctor->simrs_doctor_id;
            $checkAvailabilitySchedule->date        = Carbon::parse($this->appointment->book_date)->format('Y-m-d');
            $checkAvailabilitySchedule->time        = Carbon::parse($this->appointment->book_time_from)->format('H:i') . ' - ' . Carbon::parse($this->appointment->book_time_to)->format('H:i');
            $checkAvailabilitySchedule->price       = (float)$this->appointment->amount;
            [$slotId, $currentPrice]                 = simrs_check_availability_schedule_and_price($checkAvailabilitySchedule);

            if($this->appointment->type == Type::TELECONSULTATION){
                if($currentPrice != (float)$this->appointment->amount){
                    session()->flash('error', 'The price already changed from Rp. ' . number_format($this->appointment->amount) . ' to Rp. ' . number_format($currentPrice));
                    $this->appointment->amount = $currentPrice;
                    $this->appointment->update();
                    $this->dispatch('rerender-detail-component');
                    return;
                }
            }
            if(!$slotId){
                session()->flash('error', 'The schedule is not available anymore, please choose another schedule');
                return;
            }

            $appointmentData    = [
                'slot_id'       => $slotId,
                'patient_id'    => $this->appointment->patient->simrs_patient_id,
                'service_id'    => $this->appointment->type == 1 ? Service::HOSPITAL_VISIT : Service::TELECONSULTATION,
            ];

            if($this->appointment->type == Type::TELECONSULTATION){
                $appointmentData['payor_code']    = (string)PaymentMethod::PERSONAL;
                $appointmentData['plan_code']     = (string)PaymentMethod::PERSONAL;
            }else{
                $appointmentData['payor_code']    = (string)$this->appointment->payment_method;
                $appointmentData['plan_code']     = (string)$this->appointment->payment_method;
            }
            if($this->appointment->medical_concern){
                $appointmentData['notes']       = 'note : ' . $this->appointment->medical_concern;
            }
            if($this->appointment->attachment_url){
                $appointmentData['notes']       = @$appointmentData['notes'] . ' | attachment : ' . asset_gcs($this->appointment->attachment_url);
            }

            $attempt            = 0;
            $except_slot_ids    = (new AppointmentPatientSummaryRepository())->getSlotIdsPatient($this->appointment->patient_id);
            while ($attempt < 6) {
                $createService      = (new \App\Services\Simrs\Appointment\CreateAppointmentService($appointmentData, $this->appointment))->call();
                if($createService->status() == 200){
                    break;
                }elseif ($createService->customCode() == ErrorCode::PATIENT_ALREADY_BOOK){

                    $except_slot_ids[] = $appointmentData['slot_id'];

                    $data = [
                        'date'              => $this->appointment->book_date,
                        'doctor_id'         => $this->appointment->doctor->simrs_doctor_id,
                        'availability'      => 'Y',
                        'except_slot_ids'   => $except_slot_ids
                    ];

                    $dailyService           = (new DailyService($data))->call();
                    if($dailyService->status() == 200){
                        $times = $dailyService->data();
                        foreach($times as $time){
                            $price      = null;
                            if($this->type == Type::TELECONSULTATION){
                                $price = @collect($time['services'])->where('service_code', Service::TELECONSULTATION)->first()['price'];
                                if(!$price || $price <= 10000){
                                    continue;
                                }
                            }

                            if($time['date'] == now()->format('Y-m-d')){
                                $startTime = explode('-', $time['time'])[0];
                                if(Carbon::parse($startTime)->format('H:i') <= Carbon::now()->format('H:i')){
                                    continue;
                                }
                            }
                            $appointmentData['slot_id'] = $time['slot_id'];
                            break;
                        }
                    }

                }
                $attempt++;
            }

            if($this->appointment->type == Type::TELECONSULTATION){

                $prefix = $this->language ? '/' . $this->language : '';

                $data = [
                    'external_id'               => GeneralString::XENDIT_PREFIX_APPOINTMENT_INVOICE . '_' . $this->appointment->id . '_' . now()->timestamp,
                    'customer_email'            => $this->appointment->publicUser->email,
                    'customer_mobile_number'    => $this->appointment->publicUser->formattedPhoneNumber,
                    'customer_name'             => $this->appointment->publicUser->fullname,
                    'success_redirect_url'      => config('app.url') . $prefix . "/appointments/book/" . $this->uuid . "/" . $this->doctor->uuid . "/" . Type::TELECONSULTATION,
                    'failed_redirect_url'       => config('app.url') . $prefix . "/profile/my-bookings",
                    'currency'                  => 'IDR',
                    'invoice_duration'          => config('xendit.invoice_duration_in_second'),
                    'amount'                    => $this->appointment->amount,
                    'description'               => 'Payment for Bali International Hospital : Teleconsultation with ' . $this->doctor->name,
                ];
                $service = (new CreateInvoiceService($data, $this->appointment))->call();

                $updateData = [
                    'uuid'                      => $this->uuid,
                    'sequence'                  => AppointmentState::FINISH,
                    'payment_external_id'       => $service->data->data['external_id'],
                    'payment_invoice_url'       => $service->data->data['invoice_url'],
                    'payment_status'            => PaymentStatus::UNPAID,
                    'amount'                    => $this->appointment->amount,
                    'simrs_status'              => SimrsStatus::UPCOMING,
                    'url_link_teleconsultation' => 'https://meet.google.com/ifg-ezju-yah',
                    'simrs_registration_no'     => $createService->data['appointment_id'],
                    'simrs_episode_id'          => $createService->data['episode_id'],
                    'simrs_episode_no'          => $createService->data['episode_no'],
                    'simrs_slotnumber'          => $createService->data['slotnumber'],
                    'simrs_slot_id'             => $slotId,
                    'registration_no'           => generate_registration_no(RegistrationNoType::APPOINTMENT, $this->appointment->id)
                ];

                (new UpdateAppointmentService($updateData))->call();

                // call submitted appointment event
//                AppointmentSubmitted::dispatch($this->uuid);

                return Redirect::to($service->data->data['invoice_url']);
                $this->dispatch('scrollToTop');
            }else{

                $updateData = [
                    'uuid'                  => $this->uuid,
                    'sequence'              => AppointmentState::FINISH,
                    'payment_status'        => PaymentStatus::UNPAID,
                    'simrs_status'          => SimrsStatus::UPCOMING,
                    'simrs_registration_no' => $createService->data['appointment_id'],
                    'simrs_episode_id'      => $createService->data['episode_id'],
                    'simrs_episode_no'      => $createService->data['episode_no'],
                    'simrs_slotnumber'      => $createService->data['slotnumber'],
                    'simrs_slot_id'         => $slotId,
                    'registration_no'       => generate_registration_no(RegistrationNoType::APPOINTMENT, $this->appointment->id)
                ];

                $data = [
                    'appointment_id'    => $createService->data['appointment_id'],
                    'trx_type'          => 'APP'
                ];

                (new CreateQRAppointmentService($data))->call();

                (new UpdateAppointmentService($updateData))->call();

                $this->state = $this->state + 1;
                $this->appointment = AppointmentPatientSummary::whereuuid($this->uuid)->first();

                // call submitted appointment event
                AppointmentSubmitted::dispatch($this->uuid);

                $this->render();
                $this->dispatch('scrollToTop');
            }

        }else{
            $this->state = $this->state + 1;
            $this->render();
        }
    }

    public function backState()
    {
        $this->state = $this->state - 1;
        $this->dispatch('appointment-state-updated', state:$this->state);
        $this->dispatch('scrollToTop');
        $this->skipRender();
    }

    #[On('appointment-state-updated')]
    public function updateState($state)
    {
        $this->state = $state;
        $this->render();
    }

    public function save()
    {
        $result     = $this->photo->store(path: 'photos');
        $imageName  = $this->photo->getClientOriginalName();
    }

    #[On('appointment-patient-selected')]
    public function listenAppointmentPatientSelected($isSelectedPatient, $id, $allPatientIds): void
    {
        $this->selectedPatientId = $id;
        $this->skipRender();
    }

    #[On('appointment-payer-selected')]
    public function listenAppointmentPayerSelected($selectedPayer): void
    {
        $this->selectedPayer = $selectedPayer;
        $this->skipRender();
    }

//    #[On('schedule-form-date-time-selected')]
//    public function onListenScheduleDateTimeSelected($type, $value): void
//    {
//        if($type == 2){
//            $this->selectedTime = $value;
//        }elseif($type == 1){
//            $this->selectedDay  = $value;
//        }
//        $this->skipRender();
//    }

    #[On('appointment-save-patient-information')]
    public function onListenSavePatientInformation($data)
    {
        $updateData            = [
            'uuid'             => $this->uuid,
            'patient_id'       => $data['patient_id'],
            'payment_method'   => @$data['payment_method'] ?? PaymentMethod::PERSONAL,
            'insurance_id'     => @$data['insurance_id'],
            'insurance_number' => @$data['insurance_number'],
            'insurance_name'   => @$data['insurance_other_name'] ? @$data['insurance_other_name'] : @$data['insurance_name'],
            'company_id'       => @$data['company_id'],
            'company_name'     => @$data['company_other_name'] ? @$data['company_other_name'] : @$data['company_name'],
            'sequence'         => AppointmentState::CONFIRMATION,
            'is_consent'       => @$data['is_consent']
        ];

        (new UpdateAppointmentService($updateData))->call();

        $updatePatient                          = [];
        if(@$data['insurance_other_name']){
            $updatePatient['insurance_name']    = $data['insurance_other_name'];
        }
        if(@$data['company_other_name']){
            $updatePatient['company_name']      = $data['company_other_name'];
        }
        if(count($updatePatient) > 0){
            Patient::whereid($data['patient_id'])->update($updatePatient);
        }
        $this->state            = AppointmentState::CONFIRMATION;
        $this->appointment      = AppointmentPatientSummary::whereuuid($this->uuid)->first();
        $this->render();
    }

    #[On('appointment-save-schedule-information')]
    public function onListenSaveScheduleInformation(): void
    {
        if($this->appointment){
            $updateData = self::constructScheduleData();
            $service    = (new UpdateAppointmentService($updateData))->call();
        }else{
            $createData = self::constructScheduleData();
            $service    = (new CreateAppointmentService($createData))->call();
        }
        $this->state    = $this->state + 1;
        $this->appointment = AppointmentPatientSummary::whereuuid($this->uuid)->first();
        $this->render();
    }

    private function constructScheduleData(): array
    {
        $day = $this->selectedDay ? Carbon::parse($this->selectedDay) : null;
        if($this->selectedTime){
            [$bookTimeFrom, $bookTimeTo] = explode(' - ', $this->selectedTime);
            $bookTimeFrom                = Carbon::parse($bookTimeFrom)->format('H:i:s');
            $bookTimeTo                  = Carbon::parse($bookTimeTo)->format('H:i:s');
        }
        return [
            'uuid'                  => $this->uuid,
            'sequence'              => AppointmentState::PATIENT_INFORMATION,
            'simrs_doctor_id'       => $this->doctor->simrs_doctor_id,
            'book_date'             => $day,
            'book_time_from'        => $bookTimeFrom ?? null,
            'book_time_to'          => $bookTimeTo ?? null,
            'medical_concern'       => $this->selectedMedicalConcern,
            'attachment_url'        => $this->attachmentPath,
            'type'                  => $this->type,
            'public_user_id'        => $this->user->id,
            'amount'                => $this->selected_price,
        ];
    }

    #[On('appointment-updated-form-data')]
    public function listenUpdatedData($data): void
    {
        if(@$data['medical_concern']) $this->selectedMedicalConcern = $data['medical_concern'];
        if(@$data['day']) $this->selectedDay = $data['day'];
        if(@$data['slot_id']){
            $this->selectedTime         = $data['time'];
            $this->selected_slot_id     = $data['slot_id'];
            $this->selected_price       = $data['price'];
        }
        if(@$data['attachment_path']) $this->attachmentPath = $data['attachment_path'];
    }

    public function onHandleConfirmData()
    {
        $this->isConfirmData = !$this->isConfirmData;
    }

}
