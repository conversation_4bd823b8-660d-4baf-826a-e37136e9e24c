<?php

namespace App\Livewire\LandingPage\MedicalPackage\Checkout;

use App\Enums\Simrs\ErrorCode;
use App\Models\LandingPage\Patient;
use App\Models\LandingPage\PublicUser;
use App\Repositories\LandingPage\PackageCart\PackageCartRepository;
use App\Repositories\LandingPage\Promo\PromoRepository;
use App\Services\LandingPage\MedicalPackage\CreateMedicalPackageWithTimeSlotService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use Livewire\Component;
use Psy\Readline\Hoa\Exception;

class SummaryForm extends Component
{

    public $package;
    public $uuid;
    //    public $selectedPatientId;
    public $selectedVisitDate;
    public $attachmentPath;
    public $isConsent;
    public $promo;
    public $packageCarts;
    public $totalPrice;
    public $totalPackageCart;
    public $selectedPackageData = [];
    public $publicUserId;
    public $discount = 0;
    public $selectedPromoCode;
    public $defaultTotalPrice;
    public $language;

    public function mount($uuid)
    {
        $currentLanguage = request()->server('HTTP_LINGUISE_ORIGINAL_LANGUAGE') ?? null;

        $this->language = in_array($currentLanguage, ['id', 'en']) ? $currentLanguage : null;

        $this->uuid             = $uuid;
        $this->packageCarts     = (new PackageCartRepository())->getAllWithSumPackagePrice(Session::get('user_id'));
        $this->totalPackageCart = $this->packageCarts->count();
        $this->totalPrice       = $this->packageCarts->sum('package_price');
        $this->defaultTotalPrice = $this->totalPrice;
        $this->publicUserId     = Session::get('user_id');
    }

    public function render()
    {
        return view('livewire.landing-page.medical-package.checkout.summary-form');
    }

    public function onHandleChangeStatusModal()
    {
        $this->isModalPromo = !$this->isModalPromo;
    }

    #[On('medical-package-updated-form-data')]
    public function onListenUpdatedData($data, $wireKey)
    {

        if (@$data['selected_patient_id']) {
            $this->selectedPackageData[$wireKey]['patient_id'] = $data['selected_patient_id'];
        }

        if (@$data['selected_patient_simrs_id']) {
            $this->selectedPackageData[$wireKey]['patient_simrs_id'] = $data['selected_patient_simrs_id'];
        }

        if (@$data['selected_visit_date']) {
            $this->selectedPackageData[$wireKey]['visit_date'] = $data['selected_visit_date'];
        }

        if (@$data['selected_attachment_path']) {
            $this->selectedPackageData[$wireKey]['attachment_url'] = $data['selected_attachment_path'];
        }

        if (@$data['is_consent']) {
            $this->selectedPackageData[$wireKey]['is_consent'] = $data['is_consent'];
        }

        if (@$data['selected_equipment_id']) {
            $this->selectedPackageData[$wireKey]['selected_equipment_id'] = $data['selected_equipment_id'];
        }

        if (@$data['selected_package_category_simrs_id']) {
            $this->selectedPackageData[$wireKey]['selected_package_category_simrs_id'] = $data['selected_package_category_simrs_id'];
        }

        if (@$data['selected_visit_time']) {
            if($data['selected_visit_time'] == 'clear') {
                $this->selectedPackageData[$wireKey]['visit_time'] = null;
            }else{
                $this->selectedPackageData[$wireKey]['visit_time'] = $data['selected_visit_time'];
            }
        }

        //        if(@$data['patient_is_my_self']){
        //            $this->selectedPackageData[$wireKey]['patient_is_my_self'] = $data['patient_is_my_self'];
        //        }

    }

    #[On('medical-package-proceed-payment')]
    public function pay()
    {
        try {
            $data = [
                'selected_packages' => $this->selectedPackageData,
                'public_user_id'    => $this->publicUserId,
                'total_price'       => (int)$this->totalPrice,
                'promo_code_id'     => $this->selectedPromoCode,
                'total_cart'        => $this->packageCarts->count()
            ];
            $public_user = PublicUser::find($this->publicUserId);
            $data['dob'] = $public_user->dob;
            $data['gender'] = $public_user->gender;
            $data['ktp_number'] = $public_user->origin == 1 ? $public_user->ktp_number : $public_user->passport_number;
            $data['phone'] = $public_user->phone;
            $data['origin'] = $public_user->origin;
            $data['language'] = $this->language;
            $service = (new CreateMedicalPackageWithTimeSlotService($data, $this->packageCarts->count()))->call();
            if ($service->status() == 422) {
                if (@$service->data()['dob']) {
                    session()->flash('error_dob', 'Please fill date of birth required');
                }
                if (optional($service->data())->gender){
                    session()->flash('error_gender', 'Please fill gender required');
                }
                if (@$service->data()['ktp_number']) {
                    session()->flash('error_ktp_number', 'Please fill ktp required');
                }
                if (@$service->data()['phone']) {
                    session()->flash('error_phone', 'Please fill phone required');
                }
                if (@$service->data()['origin']) {
                    session()->flash('error_origin', 'Please fill origin required');
                }
                $this->dispatch('medical_package_error_validation');
                session()->flash('error', 'Please fill all required data');
            } elseif ($service->customCode() == ErrorCode::MEDICAL_PACKAGE_NOT_ENOUGH_SLOT) {
                $this->dispatch('medical_package_error_slot_not_enough', $service->data());
                session()->flash('error', 'The selected slot is unavailable. Kindly choose an alternative date.');
            } else {
                if ($service->status() != 200) {
                    session()->flash('error', "We can't process your request right now. Please reach out to our support team for help.");
                    return;
                } else {
                    if (@$service->data->payment_invoice_url) {
                        return Redirect::to($service->data->payment_invoice_url);
                    } else {
                        return redirect()->to('/medical-packages/success/' . $service->data->uuid);
                    }
                }
            }
        } catch (\Throwable $th) {
            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            session()->flash('error', "We can't process your request right now. Please reach out to our support team for help.");

            return $this->render();
        }
    }

    #[On('package-cart-rerender')]
    public function onListenRerender($uuid)
    {
        $this->mount($uuid);
    }

    #[On('medical-package-promo-updated')]
    public function onListenMedicalPackagePromoUpdated($constructedData)
    {
        $this->discount             = $constructedData['discount'];
        $this->totalPrice           = $this->defaultTotalPrice - $this->discount;
        $this->selectedPromoCode    = $constructedData['id'];
    }

    public function openModalTC()
    {
        // validate first
        $selectedPackageLength = count($this->selectedPackageData);
        $packageDataList   = @array_values($this->selectedPackageData)[0];
        if($packageDataList == null){
            $this->dispatch('medical_package_error_validation');
            session()->flash('error', 'Please fill all required data');
            return;
        }else if(@$packageDataList['patient_id'] == null || @$packageDataList['visit_date'] == null || @$packageDataList['visit_time'] == null ){
            $this->dispatch('medical_package_error_validation');
            session()->flash('error', 'Please fill all required data');
            return;
        }
        $this->dispatch('openModalTC');
    }
}
