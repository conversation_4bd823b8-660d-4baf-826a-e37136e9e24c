<?php

namespace App\Livewire\LandingPage\Profile\Transaction;

use App\Repositories\LandingPage\AppointmentPatientSummary\AppointmentPatientSummaryRepository;
use App\Repositories\LandingPage\PackageSummary\PackageSummaryRepository;
use App\Repositories\LandingPage\PackageSummaryDetail\PackageSummaryDetailRepository;
use App\Services\LandingPage\Profile\GetListOfProfileService;
use App\Repositories\Patient\PatientRepository;
use GPBMetadata\Google\Api\Auth;
use Illuminate\Support\Str;
use Livewire\Attributes\On;
use Livewire\Component;

class ListTransaction extends Component
{
    public $transactions;
    public $status = 1; // 1 = ongoing , 2 history
    public $condition = [];

    public $country_codes;
    public $selfPatient;
    public $otherPatients;
    public $relations;
    public $caregiverRelations;
    public $religions;
    public $maritalStatuses;
    public $educations;
    public $employments;
    public $spokenLanguages;



    public function mount()
    {
        $this->condition['public_user_id'] = \Illuminate\Support\Facades\Auth::guard('public')->id();
        $this->transactions = [];

        if($this->status == 1){

            if(!@$this->condition['package_type_uuid']){
                $appointments       = (new AppointmentPatientSummaryRepository())->getOnGoingTransaction($this->condition);
            }

            if(!in_array(@$this->condition['type'], [
                1,
                2
            ])){
                $packageSummaries       = (new PackageSummaryRepository())->getOnGoingTransactionForWebsite($this->condition);
                $packageSummaryDetails  = (new PackageSummaryDetailRepository())->getOnGoingTransaction($this->condition);
            }

        }elseif($this->status == 2){

            if(!@$this->condition['package_type_uuid']){
                $appointments       = (new AppointmentPatientSummaryRepository())->getHistoryTransaction($this->condition);
            }

            if(!in_array(@$this->condition['type'], [
                1,
                2
            ])){
                $packageSummaries       = (new PackageSummaryRepository())->getHistoryTransaction($this->condition);
                $packageSummaryDetails  = (new PackageSummaryDetailRepository())->getHistoryTransaction($this->condition);
            }

        }
        elseif($this->status == 3){
            $patientRepo = new PatientRepository();
            $data = $patientRepo->getSelfPatient($this->condition['public_user_id']);
            $data->child = $patientRepo->getOtherPatients($data->id);
        
            $transaction = ['type_transaction' => 4];
        
            if (!empty($data->patientInvoiceSendLogs) && count($data->patientInvoiceSendLogs) > 0) {
                $transaction['data'] = $data;
            }
        
            $filteredChild = collect($data->child)->filter(function ($child) {
                return !empty($child->patientInvoiceSendLogs) && count($child->patientInvoiceSendLogs) > 0;
            })->values();
        
            if ($filteredChild->isNotEmpty()) {
                $transaction['child'] = $filteredChild;
            }
        
            $this->transactions = [$transaction];
        }
            foreach ($appointments ?? [] as $appointment){
    
                $this->transactions[] = [
                    'uuid'                      => $appointment->uuid,
                    'bookDateTimeLabel'         => $appointment->bookDateTimeLabel,
                    'type'                      => $appointment->type,
                    'patient_id'                => $appointment->patient_id,
                    'patient'                   => [
                        'uuid'                  => $appointment->patient->uuid,
                        'fullname'              => $appointment->patient->fullname,
                        'relation_patient'      => $appointment->patient->relation_patient,
                        'mr_no'                 => $appointment->patient->mr_no
                    ],
                    'doctor'                    => [
                        'name'                  => $appointment->doctor->name,
                        'specialty'             => [
                            'group_name_en'     => @$appointment->doctor->specialty->group_name_en
                        ],
                    ],
                    'amount'                    => $appointment->amount,
                    'payment_invoice_url'       => $appointment->payment_invoice_url,
                    'simrs_status'              => $appointment->simrs_status,
                    'payment_status'            => $appointment->payment_status,
                    'type_transaction'          => 1, // appointment
                    'created_at'                => $appointment->created_at,
                    'url_link_teleconsultation' => $appointment->url_link_teleconsultation,
                    'show_incomplete_icon'      => $appointment->show_in_complete_icon,
                    'show_verified_icon'        => $appointment->show_verified_icon,
                    'show_component_number_6'   => $appointment->showComponent(6),
                    'show_component_number_7'   => $appointment->showComponent(7),
                    'show_component_number_8'   => $appointment->showComponent(8),
                    'show_component_number_9'   => $appointment->showComponent(9),
                    'simrs_registration_no'     => $appointment->simrs_registration_no,
                ];
            }
    
            foreach ($packageSummaries ?? [] as $packageSummary){
    
                $packageSummaryDetailData = [];
                foreach ($packageSummary->packageSummaryDetails as $packageSummaryDetail){
                    $packageSummaryDetailData[] = [
                        'title'                     => $packageSummaryDetail->package_title ?? $packageSummaryDetail->package->title,
                        'price'                     => $packageSummaryDetail->package_price ?? $packageSummaryDetail->package->price,
                        'visit_date_time_label'     => $packageSummaryDetail->visitDateTimeLabel,
                        'patient_id'                => $packageSummaryDetail->patient_id,
                        'patient_uuid'              => $packageSummaryDetail->patient->uuid,
                        'type'                      => [
                            'name'                  => $packageSummaryDetail->package_type_title,
                            'icon'                  => @$packageSummaryDetail->packageType->icon
                        ],
                        'patient'                   => [
                            'uuid'                  => $packageSummaryDetail->patient->uuid,
                            'fullname'              => $packageSummaryDetail->patient->fullname,
                            'relation_patient'      => $packageSummaryDetail->patient->relation_patient,
                            'is_complete_data'      => $packageSummaryDetail->patient->is_complete_data,
                            'mr_no'                 => $packageSummaryDetail->patient->mr_no,
                        ],
                        'category'                  => [
                            'name'                  => $packageSummaryDetail->package_category_name
                        ],
                        'package'                   => [
                            'title'                 => $packageSummaryDetail->package_title
                        ],
                        'status'                    => $packageSummaryDetail->status,
                        'show_incomplete_icon'      => $packageSummaryDetail->show_in_complete_icon,
                        'show_component_number_8'   => $packageSummaryDetail->showComponentDetail(8),
                    ];
                }
    
                $this->transactions[] = [
                    'uuid'                      => $packageSummary->uuid,
                    'amount_pay'                => $packageSummary->amount_pay,
                    'payment_invoice_url'       => $packageSummary->payment_invoice_url,
                    'status_payment'            => $packageSummary->status_payment,
                    'type_transaction'          => 2, // medical package,
                    'package_summary_details'   => $packageSummaryDetailData,
                    'created_at'                => $packageSummary->created_at,
                ];
    
            }
    
            foreach ($packageSummaryDetails ?? [] as $packageSummaryDetail){
    
                $packageSummaryDetailData   = [];
    
                $packageSummaryDetailData[] = [
                    'title'                 => $packageSummaryDetail->package_title ?? $packageSummaryDetail->package->title,
                    'price'                 => $packageSummaryDetail->package_price ?? $packageSummaryDetail->package->price,
                    'visit_date_time_label' => $packageSummaryDetail->visitDateTimeLabel,
                    'patient_id'            => $packageSummaryDetail->patient_id,
                    'patient_uuid'          => $packageSummaryDetail->patient->uuid,
                    'type'                  => [
                        'name'              => $packageSummaryDetail->package_type_title,
                        'icon'              => @$packageSummaryDetail->packageType->icon
                    ],
                    'patient'                   => [
                        'uuid'                  => $packageSummaryDetail->patient->uuid,
                        'fullname'              => $packageSummaryDetail->patient->fullname,
                        'relation_patient'      => $packageSummaryDetail->patient->relation_patient,
                        'is_complete_data'      => $packageSummaryDetail->patient->is_complete_data,
                        'mr_no'                 => $packageSummaryDetail->patient->mr_no,
                    ],
                    'category'              => [
                        'name'              => $packageSummaryDetail->package_category_name
                    ],
                    'package'               => [
                        'title'             => $packageSummaryDetail->package_title
                    ],
                    'status'                    => $packageSummaryDetail->status,
                    'show_incomplete_icon'      => $packageSummaryDetail->show_in_complete_icon,
                    'show_component_number_8'   => $packageSummaryDetail->showComponentDetail(8)
                ];
    
                $this->transactions[] = [
                    'uuid'                      => $packageSummaryDetail->uuid,
                    'type_transaction'          => 3, // medical package with multiple and paid,
                    'amount_pay'                => $packageSummaryDetail->packageSummary->amount_pay,
                    'package_summary_details'   => $packageSummaryDetailData,
                    'created_at'                => $packageSummaryDetail->created_at,
                ];
            }
    
            $this->transactions = collect($this->transactions)->sortByDesc('created_at')->values()->all();
    
            $service = (new GetListOfProfileService([
                'user_id'   => \Illuminate\Support\Facades\Auth::guard('public')->id()
            ]))->call();
            $this->country_codes        = $service->data()['country_codes'];
            $this->selfPatient          = $service->data()['selfPatient'];
            $this->otherPatients        = $service->data()['otherPatients'];
            $this->relations            = $service->data()['relations'];
    
            $this->caregiverRelations   = $service->data()['caregiverRelations'];
            $this->religions            = $service->data()['religions'];
            $this->maritalStatuses      = $service->data()['maritalStatuses'];
            $this->educations           = $service->data()['educations'];
            $this->employments          = $service->data()['employments'];
            $this->spokenLanguages      = $service->data()['spokenLanguages'];
    }

    public function render()
    {
        return view('livewire.landing-page.profile.transaction.list-transaction',[
            'uuid' => Str::uuid()
        ]);
    }

    #[On('transaction-state-updated')]
    public function updateState($status)
    {
        $this->status       = $status;
        $this->transactions = [];
        $this->mount();
    }

    #[On('transaction-filter-updated')]
    public function updatedFilter($data)
    {
        $this->condition = [];
        if(@$data['patient']['id']) $this->condition['patient_id'] = $data['patient']['id'];
        if(@$data['type']['key']){
            if(in_array(@$data['type']['key'] ?? null, [1,2])){
                $this->condition['type'] = $data['type']['key'];
            }else{
                $this->condition['package_type_uuid'] = $data['type']['key'];
            }
        }


        $this->mount();
    }

    #[On('transaction-filter-removed')]
    public function onListenRemoveCondition($item)
    {
        if($item == 1){
            $this->condition = [];
        }elseif ($item == 2){
            unset($this->condition['patient_id']);
        }elseif ($item == 3){
            unset($this->condition['type']);
            unset($this->condition['package_type_uuid']);
        }
        $this->mount();
    }

}
