<?php

namespace App\Livewire\LandingPage\Profile;

use App\Enums\General\GeneralInt;
use App\Enums\Table\BihConfig\Group;
use App\Jobs\Simrs\Patient\UpdatePatientSimrsJob;
use App\Models\CountryCode;
use App\Models\LandingPage\PublicUser;
use App\Models\MasterData\City;
use App\Models\MasterData\CityArea;
use App\Models\MasterData\PostalCode;
use App\Models\MasterData\Province;
use App\Models\Patient;
use App\Repositories\Config\BihConfigRepository;
use App\Services\GCS\GoogleCloudService;
use App\Services\LandingPage\Patient\SyncToSimrsService;
use App\Services\LandingPage\Profile\NewGetListOfProfileService;
use App\Services\LandingPage\Profile\Patient\StoreProfileBasicInfoService;
use App\Services\LandingPage\Profile\Patient\SyncAddressService;
use App\Services\LandingPage\Profile\Patient\UpdatePatientService;
use App\Services\LandingPage\PublicUser\GetByIdentityNumberPublicUserService;
use App\Services\LandingPage\PublicUser\GetByPhoneOrEmailPublicUserService;
use App\Services\LandingPage\PublicUser\UpdatePublicUserService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\WithFileUploads;
use Illuminate\Http\Request;

class NewProfile extends Component
{
    use WithFileUploads;

    public $is_myself;
    public $photo_profile;
    public $photo_profile_path;

    public $document_family_card;
    public $document_family_card_url;
    public $is_disabled_document_family_card_url;
    public $document_family_card_name;
    public $document_family_card_size;
    public $document_ktp;
    public $document_ktp_url;
    public $is_disabled_document_ktp_url;
    public $document_ktp_name;
    public $document_ktp_size;
    public $document_passport;
    public $document_passport_url;
    public $is_disabled_document_passport_url;
    public $document_passport_name;
    public $document_passport_size;
    public $isDisabled;
    public $patient_id;
    public $public_user_id;
    public $photo;
    public $citizenship; // 1. domestic 2. international
    public $state = 1;
    public $first_name;
    public $last_name;
    public $dob;
    public $nik;
    public $kitas;
    public $is_disabled_kitas_number;
    public $passport;
    public $is_disabled_passport;
    public $phone_number;
    public $email;
    /*
     * country_code
     * 1. country_name
     * 2. country_code
     * 3. extension
     */
    public $country_code;
    public $countryCodes;
    public $search_country_code;
    public $is_modal_country_code;
    public $genders;
    /*
     * 1.
     */
    public $gender;
    public $is_modal_gender;
    public $relation_to_patient;
    public $is_modal_relation;
    public $relations;
    public $isError;
    public $isConfirmation = true;
    public $page;

    // start advance information
    public $isSameAsPhoneNumber;
    public $isModalWhatsapp;
    public $whatsapp_number;
    public $is_disabled_whatsapp_number;
    public $whatsapp_country_code;
    public $whatsapp_country_codes; // master data
    public $isModalHomeNumber;
    public $home_number;
    public $is_disabled_home_number;
    public $home_country_code;
    public $home_country_codes; // master data
    public $search_whatsapp_country_code;
    public $search_home_country_code;
    public $place_of_birth;
    public $is_disabled_place_of_birth;

    public $religions;
    public $religion;
    public $is_disabled_religion;
    public $is_modal_religion;
    public $ethnicities;
    public $ethnicity;
    public $is_disabled_ethnicity;
    public $is_modal_ethnicity;

    public $educations;
    public $education;
    public $is_disabled_education;
    public $is_modal_education;

    public $employments;
    public $employment;
    public $is_disabled_employment;
    public $is_modal_employment;

    public $marital_statuses;
    public $marital_status;
    public $is_disabled_marital_status;
    public $is_modal_marital_status;

    public $primary_languages;
    public $primary_language;
    public $is_disabled_primary_language;
    public $is_modal_primary_language;
    public $covid_vaccination_status; // 1. yes 2. no
    public $is_disabled_covid_vaccination_status;
    public $mother_name;
    public $is_disabled_mother_name;
    public $pic_name;
    public $is_disabled_pic_name;
    public $pic_relation;
    public $is_disabled_pic_relation;
    public $pic_relations;
    public $is_modal_pic_relation;
    public $pic_country_code;
    public $pic_country_codes;
    public $pic_number;
    public $is_disabled_pic_number;
    public $search_pic_country_code;
    public $is_modal_pic_country_code;
    public $image_path;

    // start address information
    public $is_same_as_idcard;

    public $idcard_address;
    public $is_disabled_idcard_address;
    public $idcard_is_modal_country;
    public $idcard_search_country;
    public $idcard_country;
    public $is_disabled_idcard_country;
    public $idcard_countries; // master data
    public $idcard_is_modal_province;
    public $idcard_search_province;
    public $idcard_province;
    public $is_disabled_idcard_province;
    public $idcard_provinces; // master data
    public $idcard_is_modal_city;
    public $idcard_search_city;
    public $idcard_city;
    public $is_disabled_idcard_city;
    public $idcard_cities; // master data
    public $idcard_is_modal_subdistrict;
    public $idcard_search_subdistrict;
    public $idcard_subdistrict;
    public $is_disabled_idcard_subdistrict;
    public $idcard_subdistricts; // master data
    public $idcard_is_modal_postal_code;
    public $idcard_search_postal_code;
    public $idcard_postal_code;
    public $is_disabled_idcard_postal_code;
    public $idcard_postal_codes; // master data
    public $idcard_district;
    public $is_disabled_idcard_district;
    public $idcard_rt;
    public $is_disabled_idcard_rt;
    public $idcard_rw;
    public $is_disabled_idcard_rw;
    public $residence_address;
    public $is_disabled_residence_address;
    public $residence_is_modal_country;
    public $residence_search_country;
    public $residence_country;
    public $is_disabled_residence_country = true;
    public $residence_countries; // master data
    public $residence_is_modal_province;
    public $residence_search_province;
    public $residence_province;
    public $is_disabled_residence_province;
    public $residence_provinces; // master data
    public $residence_is_modal_city;
    public $residence_search_city;
    public $residence_city;
    public $is_disabled_residence_city;
    public $residence_cities; // master data
    public $residence_is_modal_subdistrict;
    public $residence_search_subdistrict;
    public $residence_subdistrict;
    public $is_disabled_residence_subdistrict;
    public $residence_subdistricts; // master data
    public $residence_is_modal_postal_code;
    public $residence_search_postal_code;
    public $residence_postal_code;
    public $is_disabled_residence_postal_code;
    public $residence_postal_codes; // master data
    public $residence_district;
    public $is_disabled_residence_district;
    public $residence_rt;
    public $is_disabled_residence_rt;
    public $residence_rw;
    public $is_disabled_residence_rw;
    public $patient;

    // ornament
    public $create_process_message = "To ensure a smooth booking process, please take a moment to fill out your profile. If you prefer to skip this for now, we'll prompt you to update it later.";
    public $update_process_message = "To ensure the protection of your data, you will not be able to edit submitted data. Please contact our hospital staff to update any personal information you’ve previously saved.";
    public $process_message;


    public function mount($page, $patient_id = null)
    {
        $this->process_message          = $this->create_process_message;
        $this->public_user_id           = Auth::guard('public')->id();
        $service                        = (new NewGetListOfProfileService([
            'user_id' => $this->public_user_id
        ]))->call();

        $this->religions                = $service->data()['religions'];
        $this->countryCodes             = $service->data()['country_codes'];
        $this->whatsapp_country_codes   = $service->data()['country_codes'];
        $this->home_country_codes       = $service->data()['country_codes'];
        $this->pic_country_codes        = $service->data()['country_codes'];
        $this->ethnicities              = $service->data()['ethnics'];
        $this->educations               = $service->data()['educations'];
        $this->employments              = $service->data()['employments'];
        $this->marital_statuses         = $service->data()['maritalStatuses'];
        $this->primary_languages        = $service->data()['spokenLanguages'];

        $this->genders          = $service->data()['genders'];
        $this->relations        = $service->data()['relations'];
        $this->pic_relations    = $service->data()['relations'];
        $indonesiaCountryCode   = CountryCode::whereCountryCode('ID')->first();
        $this->country_code     = [
            'country_name'      => $indonesiaCountryCode->country_name,
            'country_code'      => $indonesiaCountryCode->country_code,
            'extension'         => $indonesiaCountryCode->extension,
            'id'                => $indonesiaCountryCode->id
        ];
        $this->whatsapp_country_code = $this->country_code;
        $this->home_country_code     = $this->country_code;
        $this->pic_country_code      = $this->country_code;
        $this->page = $page;
        $this->idcard_countries      = $service->data()['countries'];
        $this->idcard_provinces      = $service->data()['provinces'];
        $this->residence_countries   = $service->data()['countries'];
        $this->residence_provinces   = $service->data()['provinces'];

        // set default residence country
        $this->residence_country    = CountryCode::whereCountryCode('ID')->first()->toArray();

        // sync data
        if ($patient_id) {

            $this->process_message          = $this->update_process_message;
            $patient = Patient::find($patient_id);
            if ($patient->public_user_id) {
                $this->is_myself = true;
            }
            if ($patient->document_family_card_url) {
                $this->document_family_card_url     = $patient->document_family_card_url;
                $this->document_family_card_name    = 'Family Card';
                $this->is_disabled_document_family_card_url = true;
            }

            if ($patient->document_ktp_url) {
                $this->document_ktp_url             = $patient->document_ktp_url;
                $this->document_ktp_name            = 'Identity Card Number';
                $this->is_disabled_document_ktp_url = true;
            }

            if ($patient->document_passport_url) {
                $this->document_passport_url                = $patient->document_passport_url;
                $this->document_passport_name               = 'Passport';
                $this->is_disabled_document_passport_url    = true;
            }

            if ($patient->image) {
                $this->photo_profile_path = $patient->image;
            }
            if ($patient->ktp_number) {
                $this->citizenship = 1;
            } else {
                $this->citizenship = 2;
            }
            $this->disabledRegionBasedOnCitizenship();

            $this->first_name            = $patient->first_name;
            $this->last_name             = $patient->last_name;
            if (@$patient->dob) {
                $this->dob                   = Carbon::parse($patient->dob)->format('d/m/Y');
            }
            if (@$patient->gender) {
                $this->gender            = (new BihConfigRepository())->getByGroupAndValue(Group::GENDER, $patient->gender)->toArray();
            }
            $this->nik                   = $patient->ktp_number;
            if ($patient->passport_number) {
                $this->passport              = $patient->passport_number;
                $this->is_disabled_passport  = true;
            }

            if ($patient->kitas_number) {
                $this->kitas                        = $patient->kitas_number;
                $this->is_disabled_kitas_number     = true;
            }
            if ($patient->contact_country_code_id) {
                $this->country_code      = CountryCode::find($patient->contact_country_code_id)->toArray();
            }
            $this->phone_number          = $patient->contact_no;
            $this->email                 = $patient->email;
            if ($patient->relation_patient) {
                $this->relation_to_patient = (new BihConfigRepository())->getByGroupAndValue(Group::RELATIONSHIP, $patient->relation_patient)->toArray();
            }

            if ($patient->whatsapp_no) {
                $this->whatsapp_number              = $patient->whatsapp_no;
                $this->is_disabled_whatsapp_number  = true;
            }
            if ($patient->whatsapp_country_code_id) {
                $this->whatsapp_country_code = CountryCode::find($patient->whatsapp_country_code_id)->toArray();
            }

            if ($patient->contact_home) {
                $this->home_number = $patient->contact_home;
                $this->is_disabled_home_number = true;
            }
            if ($patient->contact_home_country_code_id) {
                $this->home_country_code = CountryCode::find($patient->contact_home_country_code_id)->toArray();
            }

            if ($patient->place_of_birth) {
                $this->place_of_birth               = $patient->place_of_birth;
                $this->is_disabled_place_of_birth   = true;
            }

            if ($patient->religion) {
                try {
                    $this->religion          = (new BihConfigRepository())->getByGroupAndValue(Group::RELIGION, $patient->religion)->toArray();
                    $this->is_disabled_religion = true;
                } catch (\Throwable $e) {
                }
            }

            if ($patient->ethnicity) {
                try {
                    $this->ethnicity          = (new BihConfigRepository())->getByGroupAndValue(Group::ETHNIC, $patient->ethnicity)->toArray();
                    $this->is_disabled_ethnicity = true;
                } catch (\Throwable $e) {
                }
            }

            if ($patient->last_education) {
                try {
                    $this->education          = (new BihConfigRepository())->getByGroupAndValue(Group::EDUCATION, $patient->last_education)->toArray();
                    $this->is_disabled_education = true;
                } catch (\Throwable $e) {
                }
            }

            if ($patient->job) {
                try {
                    $this->employment         = (new BihConfigRepository())->getByGroupAndValue(Group::EMPLOYMENT, $patient->job)->toArray();
                    $this->is_disabled_employment = true;
                } catch (\Throwable $e) {
                }
            }

            if ($patient->marital_status) {
                try {
                    $this->marital_status     = (new BihConfigRepository())->getByGroupAndValue(Group::MARITAL_STATUS, $patient->marital_status)->toArray();
                    $this->is_disabled_marital_status = true;
                } catch (\Throwable $e) {
                }
            }

            if ($patient->language) {
                try {
                    $this->primary_language             = (new BihConfigRepository())->getByGroupAndValue(Group::SPOKEN_LANGUAGE, $patient->language)->toArray();
                    $this->is_disabled_primary_language = true;
                } catch (\Throwable $e) {
                }
            }

            if ($patient->covid_vaccine) {
                $this->covid_vaccination_status = $patient->covid_vaccine;
                $this->is_disabled_covid_vaccination_status = true;
            }

            if ($patient->caregiver_name) {
                $this->pic_name             = $patient->caregiver_name;
                $this->is_disabled_pic_name = true;
            }

            if ($patient->caregiver_contact) {
                $this->pic_number     = $patient->caregiver_contact;
                $this->is_disabled_pic_number = true;
            }

            if ($patient->caregiver_contact_country_code_id) {
                $this->pic_country_code     = CountryCode::find($patient->caregiver_contact_country_code_id)->toArray();
            }

            if ($patient->caregiver_relation) {
                $this->pic_relation             = (new BihConfigRepository())->getByGroupAndValue(Group::RELATIONSHIP, $patient->caregiver_relation)->toArray();
                $this->is_disabled_pic_relation = true;
            }

            if ($patient->mother_name) {
                $this->mother_name = $patient->mother_name;
                $this->is_disabled_mother_name = true;
            }

            if (@$patient->idCardAddress) {
                if (@$patient->idCardAddress->address) {
                    $this->idcard_address           = $patient->idCardAddress->address;
                    $this->is_disabled_idcard_address = true;
                }

                if (@$patient->idCardAddress->country) {
                    try {
                        $this->idcard_country           = $patient->idCardAddress->masterDataCountry->toArray();
                        $this->is_disabled_idcard_country = true;
                    } catch (\Throwable $th) {
                    }
                }

                if (@$patient->idCardAddress->province) {
                    try {
                        $this->idcard_province          = $patient->idCardAddress->masterDataProvince->toArray();
                        $this->is_disabled_idcard_province = true;
                    } catch (\Throwable $th) {
                    }
                }

                if (@$patient->idCardAddress->city) {
                    try {
                        $this->idcard_city              = $patient->idCardAddress->masterDataCity->toArray();
                        $this->is_disabled_idcard_city = true;
                    } catch (\Throwable $th) {
                    }
                }
                if (@$patient->idCardAddress->subdistrict) {
                    try {
                        $this->idcard_subdistrict       = $patient->idCardAddress->masterDataSubDistrict->toArray();
                        $this->is_disabled_idcard_subdistrict = true;
                    } catch (\Throwable $th) {
                    }
                }
                if (@$patient->idCardAddress->postal_code) {
                    try {
                        $this->idcard_postal_code       = $patient->idCardAddress->masterPostalCode->toArray();
                        $this->is_disabled_idcard_postal_code = true;
                    } catch (\Throwable $th) {
                    }
                }

                if (@$patient->idCardAddress->ward) {
                    $this->idcard_district              = $patient->idCardAddress->ward;
                    $this->is_disabled_idcard_district = true;
                }

                if (@$patient->idCardAddress->rt) {
                    $this->idcard_rt                    = $patient->idCardAddress->rt;
                    $this->is_disabled_idcard_rt = true;
                }

                if (@$patient->idCardAddress->rw) {
                    $this->idcard_rw                    = $patient->idCardAddress->rw;
                    $this->is_disabled_idcard_rw = true;
                }
            }

            if (@$patient->residenceAddress) {
                if (@$patient->residenceAddress->address) {
                    $this->residence_address           = $patient->residenceAddress->address;
                    $this->is_disabled_residence_address = true;
                }

                if (@$patient->residenceAddress->country) {
                    try {
                        $this->residence_country           = $patient->residenceAddress->masterDataCountry->toArray();
                        $this->is_disabled_residence_country = true;
                    } catch (\Throwable $th) {
                    }
                }

                if (@$patient->residenceAddress->province) {
                    try {
                        $this->residence_province          = $patient->residenceAddress->masterDataProvince->toArray();
                        $this->is_disabled_residence_province = true;
                    } catch (\Throwable $th) {
                    }
                }

                if (@$patient->residenceAddress->city) {
                    try {
                        $this->residence_city              = $patient->residenceAddress->masterDataCity->toArray();
                        $this->is_disabled_residence_city = true;
                    } catch (\Throwable $th) {
                    }
                }
                if (@$patient->residenceAddress->subdistrict) {
                    try {
                        $this->residence_subdistrict       = $patient->residenceAddress->masterDataSubDistrict->toArray();
                        $this->is_disabled_residence_subdistrict = true;
                    } catch (\Throwable $th) {
                    }
                }
                if (@$patient->residenceAddress->postal_code) {
                    try {
                        $this->residence_postal_code       = $patient->residenceAddress->masterPostalCode->toArray();
                        $this->is_disabled_residence_postal_code = true;
                    } catch (\Throwable $th) {
                    }
                }

                if (@$patient->residenceAddress->ward) {
                    $this->residence_district              = $patient->residenceAddress->ward;
                    $this->is_disabled_residence_district = true;
                }

                if (@$patient->residenceAddress->rt) {
                    $this->residence_rt                    = $patient->residenceAddress->rt;
                    $this->is_disabled_residence_rt = true;
                }

                if (@$patient->residenceAddress->rw) {
                    $this->residence_rw                    = $patient->residenceAddress->rw;
                    $this->is_disabled_residence_rw = true;
                }
            }
            $this->patient = Patient::where('id', $patient_id)->first();
            // dd($this->patient);
            // set default residence country
            $this->residence_country    = CountryCode::whereCountryCode('ID')->first()->toArray();
        }
    }
    public function render()
    {
        return view('livewire.landing-page.profile.new-profile');
    }

    public function updating($name, $value)
    {
        switch ($name) {
            case 'first_name':
                $this->first_name = $value;
                break;
            case 'last_name':
                $this->last_name = $value;
                break;
            case 'dob':
                $this->dob = $value;
                break;
            case 'gender':
                $this->gender = $value;
                break;
            case 'nik':
                $this->nik = $value;
                Patient::where('ktp_number', $value)->exists() ? session()->flash('error_nik', 'NIK already exist') : '';
                if (strlen($this->nik) != 16) {
                    session()->flash('error_nik', 'NIK must be 16 digit');
                }
                break;
            case 'passport':
                $this->passport = $value;
                break;
            case 'phone_number':
                info($value);
                $this->phone_number = $value;
                Patient::where('contact_no', $value)->exists() ? session()->flash('error_phone_number', 'Phone number already exist') : '';
                break;
            case 'email':
                // Regex pattern for email validation
                $emailRegex = "/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/";

                if (preg_match($emailRegex, $value)) {
                    $this->email = $value;
                } else {
                    session()->flash('error_email', 'Invalid email format');
                }

                Patient::where('email', $value)->exists() ? session()->flash('error_email', 'Email already exist') : '';

                break;
            case 'relation_to_patient':
                $this->relation_to_patient = $value;
                break;
            case 'search_country_code':
                $this->search_country_code = $value;
                $this->countryCodes = CountryCode::where('country_name', 'like', '%' . $this->search_country_code . '%')
                    ->orWhere('extension', 'like', '%' . $this->search_country_code . '%')->orderBy('country_name')->get();
                break;
            case 'search_whatsapp_country_code':
                $this->search_whatsapp_country_code = $value;
                $this->whatsapp_country_codes = CountryCode::where('country_name', 'like', '%' . $this->search_whatsapp_country_code . '%')
                    ->orWhere('extension', 'like', '%' . $this->search_whatsapp_country_code . '%')->orderBy('country_name')->get();
                break;
            case 'search_home_country_code':
                $this->search_home_country_code = $value;
                $this->home_country_codes = CountryCode::where('country_name', 'like', '%' . $this->search_home_country_code . '%')
                    ->orWhere('extension', 'like', '%' . $this->search_home_country_code . '%')->orderBy('country_name')->get();
                break;
            case 'search_pic_country_code':
                $this->search_pic_country_code = $value;
                $this->pic_country_codes = CountryCode::where('country_name', 'like', '%' . $this->search_pic_country_code . '%')
                    ->orWhere('extension', 'like', '%' . $this->search_pic_country_code . '%')->orderBy('country_name')->get();
                break;
            case 'idcard_search_country':
                $this->idcard_search_country    = $value;
                $this->idcard_countries         = CountryCode::where('country_name', 'like', '%' . $this->idcard_search_country . '%')->orderBy('country_name')->get();
                break;
            case 'idcard_search_province':
                $this->idcard_search_province   = $value;
                $this->idcard_provinces         = Province::where('description', 'like', '%' . $this->idcard_search_province . '%')->orderBy('description')->get();
                break;
            case 'idcard_search_city':
                $this->idcard_search_city   = $value;
                $this->idcard_cities        = City::whereProvinceCode($this->idcard_province['code'])->where('description', 'like', '%' . $this->idcard_search_city . '%')->orderBy('description')->get();
                break;
            case 'idcard_search_subdistrict':
                $this->idcard_search_subdistrict  = $value;
                $this->idcard_subdistricts        = CityArea::whereCityCode($this->idcard_city['code'])->where('description', 'like', '%' . $this->idcard_search_subdistrict . '%')->orderBy('description')->get();
                break;
            case 'idcard_search_postal_code':
                $this->idcard_search_postal_code  = $value;
                $this->idcard_postal_codes        = PostalCode::where([
                    'city_code'         => $this->idcard_city['code'],
                    'city_area_code'    => $this->idcard_subdistrict['code']
                ])->where('description', 'like', '%' . $this->idcard_search_postal_code . '%')->orderBy('description')->get();
                break;
            case 'residence_search_country':
                $this->residence_search_country    = $value;
                $this->residence_countries         = CountryCode::where('country_name', 'like', '%' . $this->residence_search_country . '%')->orderBy('country_name')->get();
                break;
            case 'residence_search_province':
                $this->residence_search_province   = $value;
                $this->residence_provinces         = Province::where('description', 'like', '%' . $this->residence_search_province . '%')->orderBy('description')->get();
                break;
            case 'residence_search_city':
                $this->residence_search_city   = $value;
                $this->residence_cities        = City::whereProvinceCode($this->residence_province['code'])->where('description', 'like', '%' . $this->residence_search_city . '%')->orderBy('description')->get();
                break;
            case 'residence_search_subdistrict':
                $this->residence_search_subdistrict  = $value;
                $this->residence_subdistricts        = CityArea::whereCityCode($this->residence_city['code'])->where('description', 'like', '%' . $this->residence_search_subdistrict . '%')->orderBy('description')->get();
                break;
            case 'residence_search_postal_code':
                $this->residence_search_postal_code  = $value;
                $this->residence_postal_codes        = PostalCode::where([
                    'city_code'         => $this->residence_city['code'],
                    'city_area_code'    => $this->residence_subdistrict['code']
                ])->where('description', 'like', '%' . $this->residence_search_postal_code . '%')->orderBy('description')->get();
                break;
            default:
                break;
        }
    }

    public function onclickData($name, $value)
    {
        switch ($name) {
            case 'countryCode':
        }
    }

    public function onSelectCountryCode($country_code)
    {
        $country_code = json_decode($country_code, true);
        $this->country_code['country_name'] = $country_code['country_name'];
        $this->country_code['country_code'] = $country_code['country_code'];
        $this->country_code['extension']    = $country_code['extension'];
        $this->country_code['id']           = $country_code['id'];
        $this->is_modal_country_code        = !$this->is_modal_country_code;
    }

    public function onSelectWhatsappCountryCode($country_code)
    {
        $country_code = json_decode($country_code, true);
        $this->whatsapp_country_code['country_name'] = $country_code['country_name'];
        $this->whatsapp_country_code['country_code'] = $country_code['country_code'];
        $this->whatsapp_country_code['extension']    = $country_code['extension'];
        $this->whatsapp_country_code['id']           = $country_code['id'];
        $this->isModalWhatsapp                       = !$this->isModalWhatsapp;
    }

    public function onSelectPicCountryCode($country_code)
    {
        $country_code = json_decode($country_code, true);
        $this->pic_country_code['country_name'] = $country_code['country_name'];
        $this->pic_country_code['country_code'] = $country_code['country_code'];
        $this->pic_country_code['extension']    = $country_code['extension'];
        $this->pic_country_code['id']           = $country_code['id'];
        $this->is_modal_pic_country_code        = !$this->is_modal_pic_country_code;
    }

    public function onSelectHomeCountryCode($country_code)
    {
        $country_code = json_decode($country_code, true);
        $this->home_country_code['country_name'] = $country_code['country_name'];
        $this->home_country_code['country_code'] = $country_code['country_code'];
        $this->home_country_code['extension']    = $country_code['extension'];
        $this->home_country_code['id']           = $country_code['id'];
        $this->isModalHomeNumber                 = !$this->isModalHomeNumber;
    }

    public function isModalCountryCode()
    {
        $this->is_modal_country_code = !$this->is_modal_country_code;
    }

    public function onSelectGender($gender)
    {
        $gender = json_decode($gender, true);
        $this->gender = $gender;
    }
    public function isModalGender()
    {
        $this->is_modal_gender = !$this->is_modal_gender;
    }

    public function onSelectReligion($religion)
    {
        $religion       = json_decode($religion, true);
        $this->religion = $religion;
    }

    public function isModalReligion()
    {
        $this->is_modal_religion = !$this->is_modal_religion;
    }

    public function onSelectRelation($relation)
    {
        $relation                   = json_decode($relation, true);
        $this->relation_to_patient  = $relation;
    }
    public function isModalRelation()
    {
        $this->is_modal_relation = !$this->is_modal_relation;
    }

    public function onClickCitizenship($citizenship)
    {
        $this->citizenship = $citizenship;
        $this->disabledRegionBasedOnCitizenship();
    }

    private function disabledRegionBasedOnCitizenship()
    {
        if ($this->citizenship == 2) {
            $this->is_disabled_idcard_province      = true;
            $this->is_disabled_idcard_city          = true;
            $this->is_disabled_idcard_subdistrict   = true;
            $this->is_disabled_idcard_postal_code   = true;
            $this->is_disabled_idcard_district      = true;
            $this->is_disabled_idcard_rt            = true;
            $this->is_disabled_idcard_rw            = true;
        } else {
            $this->is_disabled_idcard_province      = false;
            $this->is_disabled_idcard_city          = false;
            $this->is_disabled_idcard_subdistrict   = false;
            $this->is_disabled_idcard_postal_code   = false;
            $this->is_disabled_idcard_district      = false;
            $this->is_disabled_idcard_rt            = false;
            $this->is_disabled_idcard_rw            = false;
        }
    }

    public function backSection()
    {
        $this->state--;
        //        $this->isConfirmation = false;
        $this->isConfirmation = true;
    }

    public function nextSection($isRefresh, $idlePage = false)
    {
        $this->isError = false;
        if ($this->state == 1) {
            if ($this->patient_id) {
                // update existing
                if (!$this->dob) {
                    session()->flash('error_dob', 'Date of birth is required');
                    $this->isError = true;
                }
                if (!$this->gender) {
                    session()->flash('error_gender', 'Gender is required');
                    $this->isError = true;
                }
                if (!$this->nik && $this->citizenship == 1) {
                    session()->flash('error_nik', 'NIK is required');
                    $this->isError = true;
                } else {

                    if ($this->citizenship == 1) {
                        if (Patient::whereKtpNumber($this->nik)->where('id', '!=', $this->patient_id)->exists()) {
                            session()->flash('error_nik', 'NIK already exist');
                            $this->isError = true;
                        }

                        if (strlen($this->nik) != 16) {
                            session()->flash('error_nik', 'NIK must be 16 digit');
                            $this->isError = true;
                        }
                    }
                }

                if (!$this->passport && $this->citizenship == 2) {
                    session()->flash('error_passport', 'Passport is required');
                    $this->isError = true;
                }
                if (!$this->phone_number) {
                    session()->flash('error_phone_number', 'Phone number is required');
                    $this->isError = true;
                }
                if (!$this->relation_to_patient) {
                    session()->flash('error_relation_to_patient', 'Relation to patient is required');
                    $this->isError = true;
                }
                if (!$this->citizenship) {
                    session()->flash('error_citizenship', 'Citizenship is required');
                    $this->isError = true;
                }

                if ($this->isError) {
                    $this->isError = "Please fill all the fields";
                    return;
                } else {
                    $this->isError = false;
                }

                $data = [
                    'user_id'                   => $this->public_user_id,
                    'origin'                    => $this->citizenship,
                    'first_name'                => $this->first_name,
                    'last_name'                 => $this->last_name,
                    'dob'                       => Carbon::createFromFormat('d/m/Y', $this->dob)->format('Y-m-d'),
                    'gender'                    => $this->gender['value'],
                    'ktp_number'                => $this->nik,
                    'passport_number'           => $this->passport,
                    'email'                     => $this->email,
                    'kitas_number'              => $this->kitas,
                    'contact_no'                => $this->phone_number,
                    'contact_country_code_id'   => $this->country_code['id'],
                    'relation_patient'          => $this->relation_to_patient['value'],
                ];
                $dataPublic = [
                    'origin'                    => $this->citizenship,
                    'first_name'                => $this->first_name,
                    'last_name'                 => $this->last_name,
                    'dob'                       => Carbon::createFromFormat('d/m/Y', $this->dob)->format('Y-m-d'),
                    'gender'                    => $this->gender['value'],
                    'ktp_number'                => $this->nik,
                    'passport_number'           => $this->passport,
                    'email'                     => $this->email,
                    'phone'                     => $this->phone_number,
                ];
                $public_user = PublicUser::where('email', $this->email)->first();
                if (!empty($public_user)) {
                    if ($this->citizenship == 1 && empty($public_user->ktp_number)) {
                        if(!isset($data['identityType'])) {
                            $data['identityType'] = 'ktp';
                            $data['identityNumber'] = @$data['ktp_number'];
                        }
                        $checkDuplicateIdentityUserService = (new GetByIdentityNumberPublicUserService($data))->call();
                        throw_if($checkDuplicateIdentityUserService->status() != 200, $checkDuplicateIdentityUserService->message());
                        if ($checkDuplicateIdentityUserService->data()) {
                                session()->flash('error_nik', 'This ID Number is already exist');
                                $this->isError = true;
                                return;
                        }
                        // $updatePublicUserService = (new UpdatePublicUserService($dataPublic, $this->public_user_id))->call();
                        // throw_if($updatePublicUserService->status() != 200, $updatePublicUserService->message());
                    }

                    if ($this->citizenship == 2 && empty($public_user->passport_number)) {
                        if(!isset($data['identityType'])) {
                            $data['identityType'] = 'passport';
                            $data['identityNumber'] = @$data['passport_number'];
                        }
                        $checkDuplicateIdentityUserService = (new GetByIdentityNumberPublicUserService($data))->call();
                        throw_if($checkDuplicateIdentityUserService->status() != 200, $checkDuplicateIdentityUserService->message());
                        if ($checkDuplicateIdentityUserService->data()) {
                                session()->flash('error_passport', 'This ID Number is already exist');
                                $this->isError = true;
                                return;
                        }
                        // $updatePublicUserService = (new UpdatePublicUserService($dataPublic, $this->public_user_id))->call();
                        // throw_if($updatePublicUserService->status() != 200, $updatePublicUserService->message());
                    }

                    if (empty($public_user->phone)) {
                        $phoneRequest = new Request();
                        $phoneRequest->replace(['phoneNumber' => $data['contact_no']]);
                        $checkPhoneExist = (new GetByPhoneOrEmailPublicUserService($phoneRequest->all()))->call();
                        throw_if($checkPhoneExist->status() != 200, $checkPhoneExist->message());
    
                        if ($checkPhoneExist->data()) {
                            session()->flash('error_phone_number', 'This phone number is already exist');
                            $this->isError = true;
                            return;
                        }
    
                    }

                    $updatePublicUserService = (new UpdatePublicUserService($dataPublic, $this->public_user_id))->call();
                    throw_if($updatePublicUserService->status() != 200, $updatePublicUserService->message());
                }
                // $data = [
                //     'passport_number'  => @$this->passport,
                //     'kitas_number'     => @$this->kitas,
                // ];

                $service = (new UpdatePatientService($data, $this->patient_id))->call();
                if ($service->status() != 200) {
                    $this->isError = "Something went wrong";
                    return;
                }
                // $patient = Patient::find($this->patient_id);
                // if (!$patient->simrs_patient_id) { 
                //     $patient->update([
                //         'simrs_patient_id' => rand(1, 1000)
                //     ]);
                // }
                $syncPatientToSimrsService = (new SyncToSimrsService($this->patient_id))->call();
                throw_if($syncPatientToSimrsService->status() != 200, 'Cannot create patient to simrs');
                UpdatePatientSimrsJob::dispatch($this->patient_id, 0);
            } else {

                // create new one
                if (!$this->first_name) {
                    session()->flash('error_first_name', 'First name is required');
                    $this->isError = true;
                }
                if (!$this->last_name) {
                    session()->flash('error_last_name', 'Last name is required');
                    $this->isError = true;
                }
                if (!$this->dob) {
                    session()->flash('error_dob', 'Date of birth is required');
                    $this->isError = true;
                }
                if (!$this->gender) {
                    session()->flash('error_gender', 'Gender is required');
                    $this->isError = true;
                }
                if (!$this->nik && $this->citizenship == 1) {
                    session()->flash('error_nik', 'NIK is required');
                    $this->isError = true;
                } else {

                    if ($this->citizenship == 1) {
                        if (Patient::whereKtpNumber($this->nik)->where('id', '!=', $this->patient_id)->exists()) {
                            session()->flash('error_nik', 'NIK already exist');
                            $this->isError = true;
                        }

                        if (strlen($this->nik) != 16) {
                            session()->flash('error_nik', 'NIK must be 16 digit');
                            $this->isError = true;
                        }
                    }
                }

                if (!$this->passport && $this->citizenship == 2) {
                    session()->flash('error_passport', 'Passport is required');
                    $this->isError = true;
                }
                if (!$this->phone_number) {
                    session()->flash('error_phone_number', 'Phone number is required');
                    $this->isError = true;
                }
                if (!$this->email) {
                    session()->flash('error_email', 'Email is required');
                    $this->isError = true;
                }
                if (!$this->relation_to_patient) {
                    session()->flash('error_relation_to_patient', 'Relation to patient is required');
                    $this->isError = true;
                }
                if (!$this->citizenship) {
                    session()->flash('error_citizenship', 'Citizenship is required');
                    $this->isError = true;
                }

                if ($this->isError) {
                    $this->isError = "Please fill all the fields";
                    return;
                } else {
                    $this->isError = false;
                }

                $data = [
                    'user_id'                   => $this->public_user_id,
                    'origin'                    => $this->citizenship,
                    'first_name'                => $this->first_name,
                    'last_name'                 => $this->last_name,
                    'dob'                       => Carbon::createFromFormat('d/m/Y', $this->dob)->format('Y-m-d'),
                    'gender'                    => $this->gender['value'],
                    'ktp_number'                => $this->nik,
                    'passport_number'           => $this->passport,
                    'email'                     => $this->email,
                    'kitas_number'              => $this->kitas,
                    'contact_no'                => $this->phone_number,
                    'contact_country_code_id'   => $this->country_code['id'],
                    'relation_patient'          => $this->relation_to_patient['value'],
                ];

                $service = (new StoreProfileBasicInfoService($data))->call();

                if ($service->status() != 200) {
                    $this->isError = "Something went wrong";
                    return;
                }
                $this->patient_id = $service->data()['id'];
                $syncToSimrsService = (new SyncToSimrsService($this->patient_id))->call();
                if ($syncToSimrsService->status() != 200) {
                    Patient::find($this->patient_id)->forceDelete();
                    $this->patient_id = null;
                    $this->isError = "Cannot create patient to simrs";
                    return;
                }
            }

            UpdatePatientSimrsJob::dispatch($this->patient_id, 0);

            if ($isRefresh) {
                if($idlePage) {
                    $this->dispatch('clearance');
                    return;
                }
                return redirect($this->page);
            } else {
                $this->state++;
            }

            //            $this->isConfirmation = false;
            $this->isConfirmation = true;
        } elseif ($this->state == 2) {
            if (!$this->patient_id) {
                $this->isError = "Please complete the basic information section";
                return;
            }

            $data = [
                'religion'                          => @$this->religion['value'],
                'marital_status'                    => @$this->marital_status['value'],
                'place_of_birth'                    => @$this->place_of_birth,
                'ethnicity'                         => @$this->ethnicity['value'],
                'last_education'                    => @$this->education['value'],
                'job'                               => @$this->employment['value'],
                'language'                          => @$this->primary_language['value'],
                'whatsapp_no'                       => @$this->whatsapp_number,
                'whatsapp_country_code_id'          => @$this->whatsapp_country_code['id'],
                'contact_home'                      => @$this->home_number,
                'contact_home_country_code_id'      => @$this->home_country_code['id'],
                'caregiver_name'                    => @$this->pic_name,
                'caregiver_contact'                 => @$this->pic_number,
                'caregiver_contact_country_code_id' => @$this->pic_country_code['id'],
                'caregiver_relation'                => @$this->pic_relation['value'],
                'image'                             => @$this->image_path,
                'covid_vaccine'                     => @$this->covid_vaccination_status,
                'mother_name'                       => @$this->mother_name,
                'document_family_card_url'          => @$this->document_family_card_url,
                'document_ktp_url'                  => @$this->document_ktp_url,
                'document_passport_url'             => @$this->document_passport_url,
            ];

            $service = (new UpdatePatientService($data, $this->patient_id))->call();
            if ($service->status() != 200) {
                $this->isError = "Something went wrong";
                return;
            }

            UpdatePatientSimrsJob::dispatch($this->patient_id, 0);
            if ($isRefresh) {
                if($idlePage) {
                    $this->dispatch('clearance');
                    return;
                }
                return redirect($this->page);
            } else {
                $this->state++;
            }

            //            $this->isConfirmation = false;
            $this->isConfirmation = true;
        } elseif ($this->state == 3) {
            if (!$this->patient_id) {
                $this->isError = "Please complete the basic information section";
                return;
            }

            $data = [
                'idcard_address'            => @$this->idcard_address,
                'idcard_country_code'       => @$this->idcard_country['country_code'],
                'idcard_province_code'      => @$this->idcard_province['code'],
                'idcard_city_code'          => @$this->idcard_city['code'],
                'idcard_subdistrict_code'   => @$this->idcard_subdistrict['code'],
                'idcard_postal_code'        => @$this->idcard_postal_code['code'],
                'idcard_district'           => @$this->idcard_district,
                'idcard_rt'                 => @$this->idcard_rt,
                'idcard_rw'                 => @$this->idcard_rw,
                'residence_address'         => @$this->residence_address,
                'residence_country_code'    => @$this->residence_country['country_code'],
                'residence_province_code'   => @$this->residence_province['code'],
                'residence_city_code'       => @$this->residence_city['code'],
                'residence_subdistrict_code' => @$this->residence_subdistrict['code'],
                'residence_postal_code'     => @$this->residence_postal_code['code'],
                'residence_district'        => @$this->residence_district,
                'residence_rt'              => @$this->residence_rt,
                'residence_rw'              => @$this->residence_rw,
            ];
            $service = (new SyncAddressService($data, $this->patient_id))->call();
            if ($service->status() != 200) {
                $this->isError = "Something went wrong";
                return;
            }

            UpdatePatientSimrsJob::dispatch($this->patient_id, 0);
            if($idlePage) {
                $this->dispatch('clearance');
                return;
            }
            return redirect($this->page);
        }
    }

    public function onClickSubmit() {}

    public function onClickConfirmation()
    {
        //        $this->isConfirmation = !$this->isConfirmation;
        $this->isConfirmation = true;
    }

    public function onHandleCancelUpload()
    {
        $this->photo = null;
    }

    public function updatedDocumentFamilyCard()
    {
        if (!$this->patient_id) {
            session()->flash('error_document_family_card', 'Please complete the basic information section');
            return;
        }

        $fileSizeBytes = $this->document_family_card->getSize();
        $fileSizeMB = round($fileSizeBytes / (1024 * 1024), 2);
        if ($fileSizeMB > GeneralInt::MAX_UPLOAD_FILE_IN_MB) {
            $this->skipRender();
            return;
        };

        // File extension validation
        $allowedExtensions = ['jpg', 'png', 'pdf', 'zip'];
        $extension = $this->document_family_card->getClientOriginalExtension();
        if (!in_array(strtolower($extension), $allowedExtensions)) {
            $this->skipRender();
            return;
        }

        $fileName           = pathinfo($this->document_family_card->getClientOriginalName(), PATHINFO_FILENAME);
        $path               = (new GoogleCloudService())->uploadFile($this->document_family_card, '/profiles' . '/' . $this->patient_id . '/documents', $fileName);
        $this->document_family_card_url  = $path;
        $this->document_family_card_name = $fileName . '.' . $extension;
        $this->document_family_card_size = $fileSizeBytes;
    }

    public function onHandleCancelUploadFamilyCard()
    {
        $this->document_family_card_url = null;
    }

    public function updatedDocumentKtp()
    {

        $fileSizeBytes = $this->document_ktp->getSize();
        $fileSizeMB = round($fileSizeBytes / (1024 * 1024), 2);
        if ($fileSizeMB > GeneralInt::MAX_UPLOAD_FILE_IN_MB) {
            $this->skipRender();
            return;
        };

        // File extension validation
        $allowedExtensions = ['jpg', 'png', 'pdf', 'zip'];
        $extension = $this->document_ktp->getClientOriginalExtension();
        if (!in_array(strtolower($extension), $allowedExtensions)) {
            $this->skipRender();
            return;
        }

        $fileName           = pathinfo($this->document_ktp->getClientOriginalName(), PATHINFO_FILENAME);
        $path               = (new GoogleCloudService())->uploadFile($this->document_ktp, '/profiles' . '/' . $this->patient_id . '/documents', $fileName);
        $this->document_ktp_url  = $path;
        $this->document_ktp_name = $fileName . '.' . $extension;
        $this->document_ktp_size = $fileSizeBytes;
    }

    public function onHandleCancelUploadKtp()
    {
        $this->document_ktp_url = null;
    }

    public function updatedDocumentPassport()
    {

        $fileSizeBytes = $this->document_passport->getSize();
        $fileSizeMB = round($fileSizeBytes / (1024 * 1024), 2);
        if ($fileSizeMB > GeneralInt::MAX_UPLOAD_FILE_IN_MB) {
            $this->skipRender();
            return;
        };

        // File extension validation
        $allowedExtensions = ['jpg', 'png', 'pdf'];
        $extension = $this->document_passport->getClientOriginalExtension();
        if (!in_array(strtolower($extension), $allowedExtensions)) {
            $this->skipRender();
            return;
        }

        $fileName           = pathinfo($this->document_passport->getClientOriginalName(), PATHINFO_FILENAME);
        $path               = (new GoogleCloudService())->uploadFile($this->document_passport, '/profiles' . '/' . $this->patient_id . '/documents', $fileName);
        $this->document_passport_url  = $path;
        $this->document_passport_name = $fileName . '.' . $extension;
        $this->document_passport_size = $fileSizeBytes;
    }

    public function onHandleCancelUploadPassport()
    {
        $this->document_passport_url = null;
    }

    public function updatedPhotoProfile()
    {
        $fileSizeBytes = $this->photo_profile->getSize();
        $fileSizeMB = round($fileSizeBytes / (1024 * 1024), 2);
        if ($fileSizeMB > GeneralInt::MAX_UPLOAD_FILE_IN_MB) {
            session()->flash('error_photo_profile', 'File size must be less than 5MB');
            return;
        };

        // File extension validation
        $allowedExtensions = ['jpg', 'png'];
        $extension = $this->photo_profile->getClientOriginalExtension();
        if (!in_array(strtolower($extension), $allowedExtensions)) {
            session()->flash('error_photo_profile', 'File extension must be jpg or png');
            return;
        }

        $fileName                   = pathinfo($this->photo_profile->getClientOriginalName(), PATHINFO_FILENAME);
        $path                       = (new GoogleCloudService())->uploadFile($this->photo_profile, '/profiles' . '/' . $this->patient_id, $fileName);
        $this->photo_profile_path   = $path;
        Patient::find($this->patient_id)->update(['image' => $this->photo_profile_path]);
    }

    public function onClickDeletePhotoProfile()
    {
        Patient::find($this->patient_id)->update(['image' => null]);
        $this->photo_profile_path = null;
    }

    public function onClickIsSameAsPhoneNumber()
    {
        if (!$this->isSameAsPhoneNumber) {
            $this->whatsapp_number = $this->phone_number;
            $this->whatsapp_country_code = $this->country_code;
        }
        $this->isSameAsPhoneNumber = !$this->isSameAsPhoneNumber;
    }

    public function onClickModalWhatsappNumber()
    {
        $this->isModalWhatsapp = !$this->isModalWhatsapp;
    }

    public function onClickModalPicNumber()
    {
        $this->is_modal_pic_country_code = !$this->is_modal_pic_country_code;
    }

    public function onClickModalHomeNumber()
    {
        $this->isModalHomeNumber    = !$this->isModalHomeNumber;
    }

    public function onSelectEthnicity($ethnicity)
    {
        $ethnicity          = json_decode($ethnicity, true);
        $this->ethnicity    = $ethnicity;
    }

    public function onClickModalEthnicity()
    {
        $this->is_modal_ethnicity = !$this->is_modal_ethnicity;
    }

    public function onSelectEducation($education)
    {
        $education          = json_decode($education, true);
        $this->education    = $education;
    }

    public function onClickModalEducation()
    {
        $this->is_modal_education = !$this->is_modal_education;
    }

    public function onSelectEmployment($employment)
    {
        $employment          = json_decode($employment, true);
        $this->employment    = $employment;
    }

    public function onClickModalEmployment()
    {
        $this->is_modal_employment = !$this->is_modal_employment;
    }

    public function onSelectMaritalStatus($marital_status)
    {
        $marital_status          = json_decode($marital_status, true);
        $this->marital_status    = $marital_status;
    }

    public function onClickModalMaritalStatus()
    {
        $this->is_modal_marital_status = !$this->is_modal_marital_status;
    }

    public function onSelectPrimaryLanguage($primary_language)
    {
        $primary_language          = json_decode($primary_language, true);
        $this->primary_language    = $primary_language;
    }

    public function onClickModalPrimaryLanguage()
    {
        $this->is_modal_primary_language = !$this->is_modal_primary_language;
    }

    public function onClickVaccinationStatus($status)
    {
        $this->covid_vaccination_status = $status;
    }

    public function onSelectPicRelation($picRelation)
    {
        $picRelation        = json_decode($picRelation, true);
        $this->pic_relation = $picRelation;
    }

    public function onClickIsModalPicRelation()
    {
        $this->is_modal_pic_relation = !$this->is_modal_pic_relation;
    }

    public function onClickToState($state)
    {
        $this->state = $state;
        $this->isError = false;
        //        $this->isConfirmation = false;
        $this->isConfirmation = true;
    }

    public function onClickModalIDCardCountry()
    {
        $this->idcard_is_modal_country = !$this->idcard_is_modal_country;
    }

    public function onSelectIDCardCountry($country)
    {
        $country = json_decode($country, true);
        $this->idcard_country['country_name']   = $country['country_name'];
        $this->idcard_country['country_code']   = $country['country_code'];
        $this->idcard_country['extension']      = $country['extension'];
        $this->idcard_country['id']             = $country['id'];
        $this->idcard_is_modal_country          = !$this->idcard_is_modal_country;
    }

    public function onClickModalIDCardProvince()
    {
        $this->idcard_is_modal_province = !$this->idcard_is_modal_province;
    }

    public function onSelectIDCardProvince($province)
    {
        $province                       = json_decode($province, true);
        $this->idcard_province          = $province;
        $this->idcard_is_modal_province = !$this->idcard_is_modal_province;

        // reset child
        $this->idcard_city           = null;
        $this->idcard_subdistrict    = null;
        $this->idcard_postal_code    = null;
        $this->idcard_rw             = null;
        $this->idcard_rt             = null;
        $this->idcard_district       = null;
    }

    public function onClickModalIDCardCity()
    {
        if (!$this->idcard_province) {
            session()->flash('error_idcard_city', 'Please select province first');
            return;
        }
        $this->idcard_cities            = City::whereProvinceCode($this->idcard_province['code'])->orderBy('description')->get();
        $this->idcard_is_modal_city     = !$this->idcard_is_modal_city;
    }

    public function onSelectIDCardCity($city)
    {
        $city                       = json_decode($city, true);
        $this->idcard_city          = $city;
        $this->idcard_is_modal_city = !$this->idcard_is_modal_city;

        // reset child
        $this->idcard_subdistrict   = null;
        $this->idcard_postal_code   = null;
        $this->idcard_rw            = null;
        $this->idcard_rt            = null;
        $this->idcard_district      = null;
    }

    public function onClickModalIDCardSubDistrict()
    {
        if (!$this->idcard_city) {
            session()->flash('error_idcard_subdistrict', 'Please select city first');
            return;
        }
        $this->idcard_subdistricts  = CityArea::whereCityCode($this->idcard_city['code'])->orderBy('description')->get();
        $this->idcard_is_modal_subdistrict = !$this->idcard_is_modal_subdistrict;
    }

    public function onSelectIDCardSubDistrict($subdistrict)
    {
        $subdistrict                       = json_decode($subdistrict, true);
        $this->idcard_subdistrict          = $subdistrict;
        $this->idcard_is_modal_subdistrict = !$this->idcard_is_modal_subdistrict;

        // reset child
        $this->idcard_postal_code               = null;
        $this->idcard_rw                        = null;
        $this->idcard_rt                        = null;
        $this->idcard_district                  = null;
    }

    public function onClickModalIDCardPostalCode()
    {
        if (!$this->idcard_subdistrict) {
            session()->flash('error_idcard_postal_code', 'Please select kecamatan first');
            return;
        }
        $this->idcard_postal_codes         = PostalCode::where([
            'city_code'         => $this->idcard_city['code'],
            'city_area_code'    => $this->idcard_subdistrict['code']
        ])->orderBy('description')->get();
        $this->idcard_is_modal_postal_code = !$this->idcard_is_modal_postal_code;
    }

    public function onSelectIDCardPostalCode($postal_code)
    {
        $postal_code                       = json_decode($postal_code, true);
        $this->idcard_postal_code          = $postal_code;
        $this->idcard_is_modal_postal_code = !$this->idcard_is_modal_postal_code;
        $this->idcard_district             = $postal_code['description'];

        // reset child
        $this->idcard_rw                        = null;
        $this->idcard_rt                        = null;
    }

    public function onClickModalResidenceCountry()
    {
        $this->residence_is_modal_country = !$this->residence_is_modal_country;
    }

    public function onSelectResidenceCountry($country)
    {
        $country = json_decode($country, true);
        $this->residence_country['country_name']   = $country['country_name'];
        $this->residence_country['country_code']   = $country['country_code'];
        $this->residence_country['extension']      = $country['extension'];
        $this->residence_country['id']             = $country['id'];
        $this->residence_is_modal_country          = !$this->residence_is_modal_country;
    }

    public function onClickModalResidenceProvince()
    {
        $this->residence_is_modal_province = !$this->residence_is_modal_province;
    }

    public function onSelectResidenceProvince($province)
    {
        $province                           = json_decode($province, true);
        $this->residence_province           = $province;
        $this->residence_is_modal_province  = !$this->residence_is_modal_province;

        // reset child
        $this->residence_city           = null;
        $this->residence_subdistrict    = null;
        $this->residence_postal_code    = null;
        $this->residence_rw             = null;
        $this->residence_rt             = null;
        $this->residence_district       = null;
    }

    public function onClickModalResidenceCity()
    {
        if (!$this->residence_province) {
            session()->flash('error_residence_city', 'Please select province first');
            return;
        }
        $this->residence_cities        = City::whereProvinceCode($this->residence_province['code'])->orderBy('description')->get();
        $this->residence_is_modal_city = !$this->residence_is_modal_city;
    }

    public function onSelectResidenceCity($city)
    {
        $city                           = json_decode($city, true);
        $this->residence_city           = $city;
        $this->residence_is_modal_city  = !$this->residence_is_modal_city;

        // reset child
        $this->residence_subdistrict    = null;
        $this->residence_postal_code    = null;
        $this->residence_rw             = null;
        $this->residence_rt             = null;
        $this->residence_district       = null;
    }

    public function onClickModalResidenceSubDistrict()
    {
        if (!$this->residence_city) {
            session()->flash('error_residence_subdistrict', 'Please select city first');
            return;
        }
        $this->residence_subdistricts         = CityArea::whereCityCode($this->residence_city['code'])->orderBy('description')->get();
        $this->residence_is_modal_subdistrict = !$this->residence_is_modal_subdistrict;
    }

    public function onSelectResidenceSubDistrict($subdistrict)
    {
        $subdistrict                       = json_decode($subdistrict, true);
        $this->residence_subdistrict          = $subdistrict;
        $this->residence_is_modal_subdistrict = !$this->residence_is_modal_subdistrict;

        // reset child
        $this->residence_postal_code    = null;
        $this->residence_rw             = null;
        $this->residence_rt             = null;
        $this->residence_district       = null;
    }

    public function onClickModalResidencePostalCode()
    {
        if (!$this->residence_subdistrict) {
            session()->flash('error_residence_postal_code', 'Please select kecamatan first');
            return;
        }
        $this->residence_postal_codes         = PostalCode::where([
            'city_code'         => $this->residence_city['code'],
            'city_area_code'    => $this->residence_subdistrict['code']
        ])->orderBy('description')->get();
        $this->residence_is_modal_postal_code = !$this->residence_is_modal_postal_code;
    }

    public function onSelectResidencePostalCode($postal_code)
    {
        $postal_code                       = json_decode($postal_code, true);
        $this->residence_postal_code          = $postal_code;
        $this->residence_is_modal_postal_code = !$this->residence_is_modal_postal_code;
        $this->residence_district             = $postal_code['description'];

        // reset child
        $this->residence_rw             = null;
        $this->residence_rt             = null;
    }

    public function onClickSameAsIDCard()
    {
        $this->is_same_as_idcard            = !$this->is_same_as_idcard;
        if ($this->is_same_as_idcard) {
            if (!$this->is_disabled_residence_country) {
                $this->residence_country        = $this->idcard_country;
            }

            if (!$this->is_disabled_residence_province) {
                $this->residence_province       = $this->idcard_province;
            }

            if (!$this->is_disabled_residence_city) {
                $this->residence_city           = $this->idcard_city;
            }

            if (!$this->is_disabled_residence_subdistrict) {
                $this->residence_subdistrict    = $this->idcard_subdistrict;
            }

            if (!$this->is_disabled_residence_postal_code) {
                $this->residence_postal_code    = $this->idcard_postal_code;
            }

            if (!$this->is_disabled_residence_district) {
                $this->residence_district       = $this->idcard_district;
            }

            if (!$this->is_disabled_residence_rt) {
                $this->residence_rt             = $this->idcard_rt;
            }

            if (!$this->is_disabled_residence_rw) {
                $this->residence_rw             = $this->idcard_rw;
            }

            if (!$this->is_disabled_residence_address) {
                $this->residence_address        = $this->idcard_address;
            }
        }
    }

    public function loadPatientDetails($patientId)
    {
        info('celeng' . $patientId);
    }
}
