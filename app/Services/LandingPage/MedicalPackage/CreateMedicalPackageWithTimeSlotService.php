<?php

namespace App\Services\LandingPage\MedicalPackage;

use App\Base\ServiceBase;
use App\Enums\General\GeneralString;
use App\Enums\General\RegistrationNoType;
use App\Enums\General\TransactionNoType;
use App\Enums\Simrs\ErrorCode;
use App\Enums\Table\AppointmentPatientSummary\PaymentMethod;
use App\Enums\Table\PackageSummary\StatusPayment;
use App\Enums\Table\PromoCode\Type;
use App\Models\AppointmentTemporary;
use App\Models\LandingPage\PackageCart;
use App\Models\LandingPage\Patient;
use App\Models\LandingPage\PublicUser;
use App\Repositories\LandingPage\PackageCart\PackageCartRepository;
use App\Repositories\LandingPage\PackageSummary\PackageSummaryRepository;
use App\Repositories\LandingPage\PackageSummaryDetail\PackageSummaryDetailRepository;
use App\Repositories\LandingPage\PromoCode\PromoCodeRepository;
use App\Responses\ServiceResponse;
use App\Services\CreateQRAppointmentService;
use App\Services\PaymentGateway\Xendit\CreateInvoiceService;
use App\Services\Simrs\Appointment\CreateAppointmentService;
use App\Traits\General;
use App\Services\FormAssessment\Base\FormAssessmentServiceApi;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class CreateMedicalPackageWithTimeSlotService extends ServiceBase
{
    use General;
    protected PackageSummaryRepository $packageSummaryRepository;
    protected PackageCartRepository $packageCartRepository;
    protected PackageSummaryDetailRepository $packageSummaryDetailRepository;
    protected PromoCodeRepository $promoCodeRepository;
    protected ?string $promoCodeName = null;
    protected ?string $promoAmount = null;
    protected array $unavailableSchedules = [];

    public function __construct(protected array $data, protected int $totalPackage)
    {
        $this->packageSummaryRepository         = new PackageSummaryRepository();
        $this->packageCartRepository            = new PackageCartRepository();
        $this->packageSummaryDetailRepository   = new PackageSummaryDetailRepository();
        $this->promoCodeRepository              = new PromoCodeRepository();
    }

    /**
     * Validate the data
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validate(): \Illuminate\Contracts\Validation\Validator {
        return Validator::make($this->data, [
            'selected_packages'                     => 'required|array|min:'.$this->totalPackage,
            'selected_packages.*.patient_id'        => 'required|integer|exists:patients,id',
            'selected_packages.*.patient_simrs_id'  => 'required|exists:patients,simrs_patient_id',
            'selected_packages.*.visit_date'        => 'required|date',
            'dob'                                   => 'required',
            'gender'                                => 'required',
            'ktp_number'                            => 'required',
            'phone'                                 => 'required',
            'origin'                                => 'required',
            'selected_packages.*.visit_time'        => 'required|array',
            'selected_packages.*.attachment_url'    => 'nullable|string|max:255',
            'selected_packages.*.is_consent'        => 'nullable|boolean',
            'selected_packages.*.selected_package_category_simrs_id'      => 'required|exists:package_categories,simrs_id',
            'public_user_id'                        => 'required|integer|exists:public_users,id',
            'total_price'                           => 'required|integer',
            'promo_code_id'                         => 'nullable|exists:promo_codes,id',
            'language'                              => 'nullable|string|max:10',
        ], [
            'dob.required'                              => 'The dob field is required.',
            'gender.required'                           => 'The gender field is required.',
            'ktp_number.required'                       => 'The ktp number field is required.',
            'phone.required'                            => 'The phone field is required.',
            'origin.required'                           => 'The origin field is required.',
            'selected_packages.required'                => 'The selected packages field is required.',
            'selected_packages.array'                   => 'The selected packages must be an array.',
            'selected_packages.min'                     => 'At least one selected package is required.',
            'selected_packages.*.patient_id.required'   => 'The patient ID is required for all selected packages.',
            'selected_packages.*.patient_id.integer'    => 'The patient ID must be an integer for all selected packages.',
            'selected_packages.*.patient_id.exists'     => 'One or more selected patient IDs do not exist in the patients table.',
            'selected_packages.*.patient_simrs_id.required'  => 'The patient SIMRS ID is required for all selected packages.',
            'selected_packages.*.patient_simrs_id.exists'    => 'One or more selected patient SIMRS IDs do not exist in the patients table.',
            'selected_packages.*.selected_package_category_simrs_id.required' => 'The selected package category SIMRS ID is required for all selected packages.',
            'selected_packages.*.selected_package_category_simrs_id.exists'   => 'One or more selected package category SIMRS IDs do not exist in the package_categories table.',
            'selected_packages.*.visit_date.required'   => 'The visit date is required for all selected packages.',
            'selected_packages.*.visit_date.date'       => 'The visit date must be a valid date for all selected packages.',
            'selected_packages.*.visit_time.required'   => 'The visit time is required for all selected packages.',
            'selected_packages.*.attachment_url.string' => 'The attachment url must be a string for all selected packages.',
            'selected_packages.*.attachment_url.max'    => 'The attachment url may not be greater than :max characters for all selected packages.',
            'selected_packages.*.is_consent.boolean'    => 'The is consent field must be a boolean value for all selected packages.',
            'public_user_id.required'                   => 'The public user ID is required.',
            'public_user_id.integer'                    => 'The public user ID must be an integer.',
            'public_user_id.exists'                     => 'The selected public user ID does not exist in the public_user table.',
            'total_price.required'                      => 'The total price field is required.',
            'total_price.integer'                       => 'The total price must be an integer.',
            'promo_code_id.exists'                      => 'One or more selected promo code do not exist in the promo_codes table.'
        ]);
    }

    /**
     * main method of this service
     *
     * @return ServiceResponse
     */
    public function call(): ServiceResponse {

        // validate the request data
        if ($this->validate()->fails()) {
            return self::error($this->validate()->errors()->getMessages(), implode(',',$this->validate()->errors()->all()),422);
        }

        try{

            DB::beginTransaction();

            // price validation
            $currentTotalPrice = $this->packageCartRepository->sumCurrentPackagePrice(array_keys($this->data['selected_packages']));
            if($this->data['promo_code_id']){
                $promoCode = (new PromoCodeRepository())->findById($this->data['promo_code_id']);
                if($promoCode->type == Type::PERCENTAGE){
                    // get max
                    $discount = $currentTotalPrice * $promoCode->max_discount / 100;
                    if($discount > $promoCode->amount_discount){
                        $discount  = $promoCode->amount_discount;
                    }

                }else{
                    if($currentTotalPrice > $promoCode->max_discount){
                        $discount  = $promoCode->max_discount;
                    }else{
                        $discount  = $currentTotalPrice;
                    }
                }

                throw_if((int)($currentTotalPrice-$discount) != (int)$this->data['total_price'], 'BIH_11');
                // update used promo code
                $this->promoCodeRepository->updateById($this->data['promo_code_id'],[
                    'used' => $promoCode->used+1,
                ]);

                $this->promoCodeName    = $promoCode->code;
                $this->promoAmount      = $discount;

            }else{
                throw_if((int)$currentTotalPrice != (int)$this->data['total_price'], 'BIH_11');
            }

            // amount before discount
            $amountBeforeDiscount   = $currentTotalPrice;
            // set current price minus discount
            $currentTotalPrice      = $currentTotalPrice - @$discount ?? 0;

            // create package summary
            $packageSummary = $this->packageSummaryRepository->create([
                'public_user_id'            => $this->data['public_user_id'],
                'status_payment'            => StatusPayment::UNPAID,
                'amount_pay'                => $currentTotalPrice,
                'discount'                  => @$discount ?? 0,
                'amount_before_discount'    => $amountBeforeDiscount,
                'promo_code_id'             => @$promoCode->id
            ]);
            $packageSummary->update([
                'transaction_no'            => generate_transaction_no(TransactionNoType::MEDICAL_PACKAGE, $packageSummary->id),
            ]);

            // create package summary detail
            $dataAppointments   = [];
            $except_slot_ids    = [];
            foreach ($this->data['selected_packages'] as $cartUuid => $selected_package){
                $visitTimes = explode(" - ", $selected_package['visit_time']['time']);

                $package = PackageCart::whereuuid($cartUuid)->first()->package;
                $packageSummaryDetail           = $this->packageSummaryDetailRepository->create([
                    'public_user_id'            => $this->data['public_user_id'],
                    'patient_id'                => $selected_package['patient_id'],
                    'package_type_id'           => $package->package_type_id,
                    'package_category_id'       => $package->package_category_id,
                    'package_summary_id'        => $packageSummary->id,
                    'status'                    => \App\Enums\Table\PackageSummaryDetail\Status::PAYMENT_PENDING,
                    'visit_date'                => $selected_package['visit_date'],
                    'visit_time_from'           => $visitTimes[0],
                    'visit_time_to'             => $visitTimes[1],
                    'mr_no'                     => @Patient::find($selected_package['patient_id'])->mr_no,
                    'attachment_url'            => @$selected_package['attachment_url'],
                    'package_id'                => $package->id,
                    'package_title'             => $package->title,
                    'package_price'             => $package->price,
                    'package_type_title'        => $package->packageType->title,
                    'package_category_name'     => $package->packageCategory->name,
                ]);

                $packageSummaryDetail->update([
                    'registration_no'           => generate_registration_no(RegistrationNoType::MEDICAL_PACKAGE, $packageSummaryDetail->id),
                ]);

                $dataAppointments[]             = [
                    'category_simrs_id'         => $selected_package['selected_package_category_simrs_id'],
                    'equipment_id'              => $selected_package['selected_equipment_id'],
                    'slot_id'                   => $selected_package['visit_time']['slot_id'],
                    'patient_simrs_id'          => $selected_package['patient_simrs_id'],
                    'package_title'             => $package->title,
                    'attachment_url'            => @$selected_package['attachment_url'],
                    'package_summary_detail'    => $packageSummaryDetail,
                    'date'                      => $selected_package['visit_date'],
                    'patient_id'                => $selected_package['patient_id']
                ];
            }

            if($currentTotalPrice > 0){

                $prefix = $this->data['language'] ? '/' . $this->data['language'] : '';

                if (Session::get('bearer_token')) {
                    $tokenQuery = "?token=" . Session::get('bearer_token') . "&platform=mobile";

                    $successRedirectUrl = config('app.url') . $prefix . "/medical-packages/success/" . $packageSummary->uuid . $tokenQuery;
                    $failedRedirectUrl  = config('app.url') . $prefix . "/profile/my-bookings" . $tokenQuery;
                } else {
                    $successRedirectUrl = config('app.url') . $prefix . "/medical-packages/success/" . $packageSummary->uuid;
                    $failedRedirectUrl  = config('app.url') . $prefix . "/profile/my-bookings";
                }
                // create invoice xendit
                $publicUser     = PublicUser::find($this->data['public_user_id']);
                $data = [
                    'external_id'               => GeneralString::XENDIT_PREFIX_MEDICAL_PACKAGE_INVOICE . '_' . $packageSummary->id . '_' . now()->timestamp,
                    'customer_email'            => $publicUser->email,
                    'customer_mobile_number'    => $publicUser->formattedPhoneNumber,
                    'customer_name'             => $publicUser->fullname,
                    'success_redirect_url'      => $successRedirectUrl,
                    'failed_redirect_url'       => $failedRedirectUrl,
                    'currency'                  => 'IDR',
                    'invoice_duration'          => config('xendit.invoice_duration_in_second'),
                    'amount'                    => $currentTotalPrice,
                    'description'               => 'Payment for Bali International Hospital : ' . @$package->title
                ];

                $invoiceService                 = (new CreateInvoiceService($data,$packageSummary))->call();
                throw_if($invoiceService->status() != 200, $invoiceService->message());

                $updateData = [
                    'payment_external_id'       => $invoiceService->data->data['external_id'],
                    'payment_invoice_url'       => $invoiceService->data->data['invoice_url']
                ];

                // update package summary
                $this->packageSummaryRepository->update($packageSummary->id, $updateData);

                $packageSummary['payment_external_id']  = $updateData['payment_external_id'];
                $packageSummary['payment_invoice_url']  = $updateData['payment_invoice_url'];
            }else{
                $updateData = [
                    'status_payment'                    => StatusPayment::PAID,
                ];
                $this->packageSummaryRepository->update($packageSummary->id, $updateData);
                $packageSummary['uuid']                = $packageSummary->uuid;
            }

            // create appointment to simrs
            foreach ($dataAppointments as $dataAppointment){

                $note = $dataAppointment['package_title'];
                if($this->promoCodeName){
                    $note .= '|Promo:' . $this->promoCodeName;
                }

                if($this->promoAmount){
                    $note .= '|Potongan:' . $this->promoAmount;
                }

                if($dataAppointment['attachment_url']){
                    $note .= '|File:' . asset_gcs($dataAppointment['attachment_url']);
                }

                $attempt            = 0;
                $data               = [
                    'slot_id'       => $dataAppointment['slot_id'],
                    'patient_id'    => $dataAppointment['patient_simrs_id'],
                    'payor_code'    => PaymentMethod::PERSONAL,
                    'plan_code'     => PaymentMethod::PERSONAL,
                    'service_id'    => $dataAppointment['category_simrs_id'],
                    'notes'         => $note
                ];
                $except_slot_ids        = $this->packageSummaryDetailRepository->getSlotIdsPatient($dataAppointment['patient_id']);
                // $data['appointment_date'] = Carbon::parse($dataAppointment['package_summary_detail']->visit_date)->addDay();
                // $data['episode_id'] = rand(1000, 9999);
                // $data['appointment_id'] = '2||' . rand(10000, 99999) . '||1';
                // $data['episode_no'] = 'OBI' . str_pad(rand(10000, 99999), 8, '0', STR_PAD_LEFT);
                // $data['appointment_time'] = $dataAppointment['package_summary_detail']->visit_time_from;
                // $data['slotnumber'] = 4;
                // $checkAppointmentTemporary = AppointmentTemporary::where('appointment_date',$data['appointment_date'])->where('patient_id',$data['patient_id'])->where('appointment_time', $data['appointment_time'])->first();
                // // dd($data);
                // if(!empty($checkAppointmentTemporary)){
                //     $createAppointmentService = false;
                // }else{
                //     $createAppointmentService = AppointmentTemporary::create($data);
                // }
                $createAppointmentService = (new CreateAppointmentService($data, $dataAppointment['package_summary_detail']))->call();
                // dd($createAppointmentService,$dataAppointment['package_summary_detail']);
                if($createAppointmentService->status() == 200){
                // if($createAppointmentService){

                    // $this->packageSummaryDetailRepository->updateByCondition([
                    //     'id'                    => $dataAppointment['package_summary_detail']->id,
                    // ],[
                    //     'simrs_episode_id'      => $createAppointmentService->episode_id,
                    //     'simrs_episode_no'      => $createAppointmentService->appointment_id,
                    //     'simrs_appointment_id'  => $createAppointmentService->appointment_id,
                    //     'simrs_appointment_date'=> $createAppointmentService->appointment_date,
                    //     'simrs_appointment_time'=> $createAppointmentService->appointment_time,
                    //     'simrs_slotnumber'      => $createAppointmentService->slotnumber,
                    // ]);
                    $this->packageSummaryDetailRepository->updateByCondition([
                        'id'                    => $dataAppointment['package_summary_detail']->id,
                    ],[
                        'simrs_episode_id'      => @$createAppointmentService->data['episode_id'],
                        'simrs_episode_no'      => @$createAppointmentService->data['appointment_id'],
                        'simrs_appointment_id'  => @$createAppointmentService->data['appointment_id'],
                        'simrs_appointment_date'=> @$createAppointmentService->data['appointment_date'],
                        'simrs_appointment_time'=> @$createAppointmentService->data['appointment_time'],
                        'simrs_slotnumber'      => @$createAppointmentService->data['slotnumber'],
                    ]);

                    $data = [
                        'appointment_id'    => @$createAppointmentService->data['appointment_id'],
                        // 'appointment_id'    => $createAppointmentService->appointment_id,
                        'trx_type'          => 'PCK'
                    ];

                    // $formAssessmentURL = $this->generateFormAssessmentLink(array(
                    //     'pID' => $dataAppointment['patient_simrs_id'],
                    //     // 'epsID' => @$createAppointmentService->data['episode_id'],
                    //     'epsID' => $createAppointmentService->episode_id,
                    //     'prID' => 1,
                    //     'simrs_appointment_date'=> $createAppointmentService->appointment_date,
                    //     'simrs_appointment_time'=> $createAppointmentService->appointment_time,
                    //     ));
                    $formAssessmentURL = $this->generateFormAssessmentLink(array(
                        'pID' => $dataAppointment['patient_simrs_id'],
                        'epsID' => @$createAppointmentService->data['episode_id'],
                        'prID' => 1,
                        'simrs_appointment_date'=> @$createAppointmentService->data['appointment_date'],
                        'simrs_appointment_time'=> @$createAppointmentService->data['appointment_time'],
                        ));
                    if(isset($formAssessmentURL['url'])) {
                        $this->packageSummaryDetailRepository->updateByCondition([
                            'id' => $dataAppointment['package_summary_detail']->id,
                        ],[
                            'assessment_form_url'           => $formAssessmentURL['url'],
                        ]);
                    }

                    (new CreateQRAppointmentService($data))->call();

                }elseif ($createAppointmentService->customCode() == ErrorCode::PATIENT_ALREADY_BOOK){
                    info('NO AVAILABLE SCHEDULE FOR ' . json_encode($selected_package));
                    throw new \Exception('NO AVAILABLE SCHEDULE FOR ' . json_encode($selected_package));
                }

            }

            // delete package cart
            $this->packageCartRepository->deleteByUuids(array_keys($this->data['selected_packages']));

            DB::commit();

            return self::success($packageSummary, 'success');

        }catch (\Throwable $th) {

            DB::rollBack();

            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            $errorCode = $this->findMessageErrorCodeByCode($th->getMessage());
            if($errorCode){
                return self::error($this->unavailableSchedules, $errorCode['message'], 400, $errorCode['code']);
            }

            return self::error(null, $th->getMessage());

        }

    }

    public function generateFormAssessmentLink($data) {
        $result = (new FormAssessmentServiceApi())->post('/api/gen-link-assessment', $data)->call();
        return $result->data;
    }
}
