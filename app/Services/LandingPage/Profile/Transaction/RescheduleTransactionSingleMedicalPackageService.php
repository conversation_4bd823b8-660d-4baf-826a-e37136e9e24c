<?php

namespace App\Services\LandingPage\Profile\Transaction;

use App\Base\ServiceBase;
use App\Enums\Table\AppointmentPatientSummary\SimrsStatus;
use App\Enums\Table\PackageSummaryDetail\CanceledBy;
use App\Enums\Table\PackageSummaryDetail\Status;
use App\Enums\Table\AppointmentPatientSummary\PaymentMethod;
use App\Enums\Simrs\ErrorCode;
use App\Events\MedicalPackage\TransactionRescheduled;
use App\Jobs\Package\CancelAppointmentSpecificJob;
use App\Jobs\Package\CreateDepositJob;
use App\Repositories\LandingPage\PackageSummaryDetail\PackageSummaryDetailRepository;
use App\Responses\ServiceResponse;
use App\Services\Simrs\Appointment\CreateAppointmentService;
use App\Services\CreateQRAppointmentService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;


class RescheduleTransactionSingleMedicalPackageService extends ServiceBase
{

    protected PackageSummaryDetailRepository $packageSummaryDetailRepository;
    protected string $cancelReason = 'Canceled by user';

    public function __construct(protected array $data)
    {
        $this->packageSummaryDetailRepository = new PackageSummaryDetailRepository();
    }

    /**
     * Validate the data
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validate(): \Illuminate\Contracts\Validation\Validator {
        return Validator::make($this->data, [
            'uuid'          => 'required|exists:package_summary_details,uuid',
            'selected_packages.visit_date'        => 'required|date',
            'selected_packages.visit_time'        => 'required|array',
        ], [
            'uuid.required' => 'The UUID field is required.',
            'uuid.exists'   => 'The provided UUID does not exist in our records.',
            'selected_packages.visit_date.required'   => 'The visit date is required for all selected packages.',
            'selected_packages.visit_date.date'       => 'The visit date must be a valid date for all selected packages.',
            'selected_packages.visit_time.required'   => 'The visit time is required for all selected packages.',
        ]);
    }

    /**
     * main method of this service
     *
     * @return ServiceResponse
     */
    public function call(): ServiceResponse {

        // validate the request data
        if ($this->validate()->fails()) {
            return self::error($this->validate()->errors()->getMessages(), implode(',',$this->validate()->errors()->all()),422);
        }

        try{

            DB::beginTransaction();

            $packageSummaryDetail = $this->packageSummaryDetailRepository->findByCondition([
                'uuid'  => $this->data['uuid']
            ]);

            throw_if(!in_array($packageSummaryDetail->status,Status::cancelableTransaction()), 'Transaction cannot be cancel');


            $this->packageSummaryDetailRepository->updateByCondition([
                'uuid'          => $this->data['uuid']
            ],[
                'status'        => Status::CANCEL,
                'cancel_reason' => 'Other',
                'canceled_by'   => CanceledBy::CANCELED_BY_USER,
                'deleted_at'    => Carbon::now(),
            ]);

            // create new package summary detail
            $selected_package = $this->data['selected_packages'];
            $visitTimes = explode(" - ", $selected_package['visit_time']['time']);

            $newPackageSummaryDetail = $packageSummaryDetail->packageSummary->packageSummaryDetails()->create([
                'public_user_id'                    => $packageSummaryDetail->public_user_id,
                'patient_id'                        => $packageSummaryDetail->patient_id,
                'package_type_id'                   => $packageSummaryDetail->package_type_id,
                'package_category_id'               => $packageSummaryDetail->package_category_id,
                'package_summary_id'                => $packageSummaryDetail->package_summary_id,
                'status'                            => \App\Enums\Table\PackageSummaryDetail\Status::UP_COMING,
                'visit_date'                        => $selected_package['visit_date'],
                'visit_time_from'                   => $visitTimes[0],
                'visit_time_to'                     => $visitTimes[1],
                'mr_no'                             => $packageSummaryDetail->mr_no,
                'attachment_url'                    => $packageSummaryDetail->attachment_url,
                'package_id'                        => $packageSummaryDetail->package_id,
                'package_title'                     => $packageSummaryDetail->package_title,
                'package_price'                     => number_format($packageSummaryDetail->package_price, 0, '', ''),
                'package_type_title'                => $packageSummaryDetail->packageType->title,
                'package_category_name'             => $packageSummaryDetail->packageCategory->name,
                'registration_no'                   => $packageSummaryDetail->registration_no,
                'assessment_form_url'               => $packageSummaryDetail->assessment_form_url,
                'parent_reschedule_id'              => $packageSummaryDetail->id,
                'parent_initial_reschedule_id'      => @$packageSummaryDetail->parent_initial_reschedule_id ?? $packageSummaryDetail->id,
            ]);

            $dataAppointment = [
                'category_simrs_id'         => $packageSummaryDetail->packageCategory->simrs_id,
                'equipment_id'              => $selected_package['selected_equipment_id'],
                'slot_id'                   => $selected_package['visit_time']['slot_id'],
                'patient_simrs_id'          => $packageSummaryDetail->patient->simrs_patient_id,
                'package_title'             => $packageSummaryDetail->package_title,
                'attachment_url'            => @$packageSummaryDetail->attachment_url,
                'package_summary_detail'    => $newPackageSummaryDetail,
                'date'                      => $selected_package['visit_date'],
                'patient_id'                => $packageSummaryDetail->patient_id,
            ];

            // create new appointment simrs
            $note = $dataAppointment['package_title'];
            if($packageSummaryDetail->packageSummary->promo_code_id){
                $note .= '|Promo:' . $packageSummaryDetail->packageSummary->promoCode->code;
            }

            if($packageSummaryDetail->packageSummary->discount){
                $note .= '|Potongan:' . $packageSummaryDetail->packageSummary->discount;
            }

            if($dataAppointment['attachment_url']){
                $note .= '|File:' . asset_gcs($dataAppointment['attachment_url']);
            }

            $attempt            = 0;
            $data               = [
                'slot_id'       => $dataAppointment['slot_id'],
                'patient_id'    => $dataAppointment['patient_simrs_id'],
                'payor_code'    => PaymentMethod::PERSONAL,
                'plan_code'     => PaymentMethod::PERSONAL,
                'service_id'    => $dataAppointment['category_simrs_id'],
                'notes'         => $note
            ];

            $except_slot_ids        = $this->packageSummaryDetailRepository->getSlotIdsPatient($dataAppointment['patient_id']);

            $createAppointmentService = (new CreateAppointmentService($data, $dataAppointment['package_summary_detail']))->call();
            if($createAppointmentService->status() == 200){

                $newPackageSummaryDetail->update([
                    'simrs_episode_id'      => @$createAppointmentService->data['episode_id'],
                    'simrs_episode_no'      => @$createAppointmentService->data['appointment_id'],
                    'simrs_appointment_id'  => @$createAppointmentService->data['appointment_id'],
                    'simrs_appointment_date'=> @$createAppointmentService->data['appointment_date'],
                    'simrs_appointment_time'=> @$createAppointmentService->data['appointment_time'],
                    'simrs_slotnumber'      => @$createAppointmentService->data['slotnumber'],
                ]);

                $data = [
                    'appointment_id'    => @$createAppointmentService->data['appointment_id'],
                    'trx_type'          => 'PCK'
                ];

                CancelAppointmentSpecificJob::dispatch($packageSummaryDetail->id);

                (new CreateQRAppointmentService($data))->call();

            }elseif ($createAppointmentService->customCode() == ErrorCode::PATIENT_ALREADY_BOOK){
                info('NO AVAILABLE SCHEDULE FOR ' . json_encode($selected_package));
                DB::rollBack();
                return self::error(null, 'NO AVAILABLE SCHEDULE FOR ' . json_encode($selected_package),1062);
            }


            CreateDepositJob::dispatch($packageSummaryDetail->packageSummary->id);
            TransactionRescheduled::dispatch($newPackageSummaryDetail->uuid, false);

            // send email reschedule info

            DB::commit();

            return self::success(['uuid' => $newPackageSummaryDetail->uuid, 'fulldata' => $newPackageSummaryDetail], 'success');

        }catch (\Throwable $th) {

            DB::rollBack();

            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            return self::error(null, $th->getMessage());

        }

    }
}
