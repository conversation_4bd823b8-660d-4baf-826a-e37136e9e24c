<?php

namespace App\Services\Api\Transaction;

use App\Base\ServiceBase;
use App\Enums\Table\AppointmentPatientSummary\SimrsStatus;
use App\Enums\Table\AppointmentPatientSummary\Type;
use App\Repositories\LandingPage\AppointmentPatientSummary\AppointmentPatientSummaryRepository;
use App\Repositories\LandingPage\PackageSummary\PackageSummaryRepository;
use App\Repositories\LandingPage\PackageSummaryDetail\PackageSummaryDetailRepository;
use App\Responses\ServiceResponse;
use Illuminate\Support\Facades\Log;
use App\Enums\Table\Patient\RelationPatient;
use App\Enums\Table\PackageSummary\StatusPayment;
use App\Enums\Table\PackageSummaryDetail\Status;
use App\Services\GCS\GoogleCloudService;
use Carbon\Carbon;

class ListTransactionService extends ServiceBase
{

    protected array $transaction = [];

    /*
     * CONDITION CONTAIN
     * 1. public_user_id ->int
     * 2. type -> int [1 : hospital visit, 2 : teleconsultation]
     * 3. patient_id -> int
     * 4. package_type_uuid -> strings [exists, package_types,uuid]
     *
     * STATUS -> 1 or 2
     * 1. on going
     * 2. history
     */
    public function __construct(protected array $condition, protected int $status)
    {
    }


    public function generatePackageDetail(object $packageSummaryDetail)
    {
        $gcs = new GoogleCloudService();
        return [
            "appointment_id"        => str_replace('||', '^^', @$packageSummaryDetail->simrs_appointment_id),
            'id'                    => $packageSummaryDetail->id,
            'uuid'                  => $packageSummaryDetail->uuid,
            'title'                 => $packageSummaryDetail->package_title ?? $packageSummaryDetail->package->title,
            'price'                 => floatval($packageSummaryDetail->package_price ?? $packageSummaryDetail->package->price),
            'visit_date_time_label' => $packageSummaryDetail->visitDateTimeLabel,
            'status'                => $packageSummaryDetail->status,
            'status_label'          => Status::getLabel($packageSummaryDetail->status),
            'type'                  => [
                'uuid'              => @$packageSummaryDetail->packageType->uuid,
                'name'              => @$packageSummaryDetail->packageType->name,
                'icon'              => @$gcs->getStaticUrl($packageSummaryDetail->packageType->icon)
            ],
            'patient'                   => [
                'mr_no'                 => $packageSummaryDetail->patient->mr_no,
                'patient_id'            => $packageSummaryDetail->patient_id,
                'fullname'              => $packageSummaryDetail->patient->fullname,
                'relation_patient'      => $packageSummaryDetail->patient->relation_patient,
                'relation_patient_label' => RelationPatient::getLabel($packageSummaryDetail->patient->relation_patient),
                'is_complete_data'      => $packageSummaryDetail->patient->isCompleteData == 3 ? true : false
            ],
            'category'              => [
                'name'              => @$packageSummaryDetail->packageCategory->name
            ],
            'package'               => [
                'title'             => @$packageSummaryDetail->package->title
            ]
        ];
    }

    /**
     * main method of this service
     *
     * @return ServiceResponse
     */
    public function call(): ServiceResponse
    {

        try {
            $this->transactions = [];

            if ($this->status == 1) {

                if (!@$this->condition['package_type_uuid']) {
                    $appointments       = (new AppointmentPatientSummaryRepository())->getOnGoingTransaction($this->condition);
                }

                if (!in_array(@$this->condition['type'], [1,2])) {
                    $packageSummaries       = (new PackageSummaryRepository())->getOnGoingTransactionForWebsite($this->condition);
                    $packageSummaryDetails  = (new PackageSummaryDetailRepository())->getOnGoingTransaction($this->condition);
                }
            } elseif ($this->status == 2) {

                if (!@$this->condition['package_type_uuid']) {
                    $appointments       = (new AppointmentPatientSummaryRepository())->getHistoryTransaction($this->condition);
                }

                if (!in_array(@$this->condition['type'], [
                    1,
                    2
                ])) {
                    $packageSummaries       = (new PackageSummaryRepository())->getHistoryTransaction($this->condition);
                    $packageSummaryDetails  = (new PackageSummaryDetailRepository())->getHistoryTransaction($this->condition);
                }
            }

            $transaction_appointment = [];

            foreach ($appointments ?? [] as $appointment) {
                $initiateTime = Carbon::createFromFormat('Y-m-d H:i:s', "$appointment->book_date $appointment->book_time_from");
                // $initiateTime = Carbon::createFromTimeString($appointment->book_time_from);
                $timeBefore = $initiateTime->copy()->subMinutes(10);
                $currentTime = Carbon::now();
                $appointment->status_tele = false;
                if ($currentTime->greaterThanOrEqualTo($timeBefore)) {
                    $appointment->status_tele = true;
                }
                $this->transactions[] = [
                    "id" => $appointment->id,
                    "uuid" => $appointment->uuid,
                    "type" => 1,
                    "status_id" => $appointment->simrs_status,
                    "status_label" => SimrsStatus::getLabel($appointment->simrs_status),
                    "amount" => floatval($appointment->amount),
                    "discount" => floatval(null),
                    "amount_before_discount" => floatval(null),
                    "payment_status" => $appointment->payment_status > 0 ? StatusPayment::getLabel($appointment->payment_status) : (string)$appointment->payment_status,
                    "payment_invoice_url" => $appointment->payment_invoice_url,
                    "created_at" => $appointment->created_at,
                    "appointment" => [
                        "appointment_id" => str_replace('||', '^^', @$appointment->simrs_registration_no),
                        "id" => $appointment->id,
                        "uuid" => $appointment->uuid,
                        "bookDateTimeLabel" => $appointment->bookDateTimeLabel,
                        "type" => $appointment->type,
                        "type_label" => Type::getLabel($appointment->type),
                        "created_at" => $appointment->created_at,
                        "url_link_teleconsultation" => $appointment->url_link_teleconsultation,
                        "enabled_teleconsultation" => $appointment->showComponent(9), // change to logic for enable button
                        "status_tele" => $appointment->status_tele,
                        "patient" => [
                            "patient_id" => $appointment->patient_id,
                            "mr_no" => $appointment->patient->mr_no ,
                            "fullname" => $appointment->patient->fullname,
                            "relation_patient" => $appointment->patient->relation_patient,
                            "relation_patient_label" => RelationPatient::getLabel($appointment->patient->relation_patient),
                            "is_complete_data" => $appointment->patient->is_complete_data
                        ],
                        "doctor" => [
                            "id" => $appointment->doctor->id,
                            "uuid" => $appointment->doctor->uuid,
                            "name" => $appointment->doctor->name,
                            "specialty" => [
                                "group_name_en" => $appointment->doctor->specialty->group_name_en
                            ]
                        ],
                    ],
                    "package" => []
                ];
            }

            $transaction_package = [];

            foreach ($packageSummaries ?? [] as $packageSummary) {

                $packageSummaryDetailData = [];

                if ($packageSummary->status_payment != StatusPayment::PAID) { // single package and transaction not paid

                    foreach ($packageSummary->packageSummaryDetails as $packageSummaryDetail) {

                        $packageSummaryDetailData[] = $this->generatePackageDetail($packageSummaryDetail);
                    }


                    $this->transactions[] = [
                        "id" => $packageSummary->id,
                        "uuid" => $packageSummary->uuid,
                        "type" => 2,
                        "status_id" => $packageSummary->status_payment,
                        "status_label" => StatusPayment::getLabel($packageSummary->status_payment),
                        "amount" => floatval($packageSummary->amount_pay),
                        "discount" => floatval($packageSummary->discount),
                        "amount_before_discount" => floatval($packageSummary->amount_before_discount),
                        "payment_status" => is_numeric($packageSummary->status_payment) ? StatusPayment::getLabel($packageSummary->status_payment) : (string)$packageSummary->status_payment,
                        "payment_invoice_url" => $packageSummary->payment_invoice_url,
                        "created_at" => $packageSummary->created_at,
                        "appointment" => null,
                        "package" =>  $packageSummaryDetailData
                    ];
                } else { // multiple package and transaction paid

                    foreach ($packageSummary->packageSummaryDetails as $packageSummaryDetail) {

                        $packageSummaryDetailData[] = $this->generatePackageDetail($packageSummaryDetail);

                        $this->transactions[] = [
                            "uuid" => $packageSummary->uuid,
                            "type" => 2,
                            "status_id" => $packageSummary->status_payment,
                            "status_label" => StatusPayment::getLabel($packageSummary->status_payment),
                            "amount" => floatval($packageSummary->amount_pay),
                            "discount" => floatval($packageSummary->discount),
                            "amount_before_discount" => floatval($packageSummary->amount_before_discount),
                            "payment_status" => (string)$packageSummary->status_payment,
                            "payment_invoice_url" => $packageSummary->payment_invoice_url,
                            "created_at" => $packageSummary->created_at,
                            "appointment" => null,
                            "package" =>  $packageSummaryDetailData
                        ];
                    }
                }
            }

            foreach ($packageSummaryDetails ?? [] as $packageSummaryDetail){

                $this->transactions[] = [
                    "id" => $packageSummaryDetail->packageSummary->id,
                    "uuid" => $packageSummaryDetail->packageSummary->uuid,
                    "type" => 2,
                    "status_id" => $packageSummaryDetail->packageSummary->status_payment,
                    "status_label" => StatusPayment::getLabel($packageSummaryDetail->packageSummary->status_payment),
                    "amount" => floatval($packageSummaryDetail->packageSummary->amount_pay),
                    "discount" => floatval($packageSummaryDetail->packageSummary->discount),
                    "amount_before_discount" => floatval($packageSummaryDetail->packageSummary->amount_before_discount),
                    "payment_status" => is_numeric($packageSummaryDetail->packageSummary->status_payment) ? StatusPayment::getLabel($packageSummaryDetail->packageSummary->status_payment) : (string)$packageSummaryDetail->packageSummary->status_payment,
                    "payment_invoice_url" => $packageSummaryDetail->packageSummary->payment_invoice_url,
                    "created_at" => $packageSummaryDetail->packageSummary->created_at,
                    "appointment" => null,
                    "package" =>  [$this->generatePackageDetail($packageSummaryDetail)]
                ];
            }

            /*
            foreach ($appointments ?? [] as $appointment) {
                $this->transactions[] = [
                    'uuid'                      => $appointment->uuid,
                    'bookDateTimeLabel'         => $appointment->bookDateTimeLabel,
                    'type'                      => $appointment->type,
                    'patient'                   => [
                        'patient_id'            => $appointment->patient_id,
                        'fullname'              => $appointment->patient->fullname,
                        'relation_patient'      => $appointment->patient->relation_patient,
                        'relation_patient_label' => RelationPatient::getLabel($appointment->patient->relation_patient),
                        'is_complete_data'      => $appointment->patient->is_complete_data
                    ],
                    'doctor'                    => [
                        'name'                  => $appointment->doctor->name,
                        'specialty'             => [
                            'group_name_en'     => $appointment->doctor->specialty->group_name_en
                        ],
                    ],
                    'amount'                    => $appointment->amount,
                    'payment_invoice_url'       => $appointment->payment_invoice_url,
                    'simrs_status'              => $appointment->simrs_status,
                    'simrs_status_label'        => SimrsStatus::getLabel($appointment->simrs_status),
                    'payment_status'            => $appointment->payment_status,
                    'type_transaction'          => 1, // appointment
                    'created_at'                => $appointment->created_at,
                    'url_link_teleconsultation' => $appointment->url_link_teleconsultation
                ];
            }

            foreach ($packageSummaries ?? [] as $packageSummary) {

                if ($packageSummary->packageSummaryDetails->count() > 1) {

                    $packageSummaryDetailData = [];
                    foreach ($packageSummary->packageSummaryDetails as $packageSummaryDetail) {
                        $packageSummaryDetailData[] = [
                            'detail_id'             => $packageSummaryDetail->id,
                            'title'                 => $packageSummaryDetail->package_title ?? $packageSummaryDetail->package->title,
                            'price'                 => $packageSummaryDetail->package_price ?? $packageSummaryDetail->package->price,
                            'visit_date_time_label' => $packageSummaryDetail->visitDateTimeLabel
                        ];
                    }
                    $this->transactions[] = [
                        'uuid'                      => $packageSummary->uuid,
                        'amount_pay'                => $packageSummary->amount_pay,
                        'payment_invoice_url'       => $packageSummary->payment_invoice_url,
                        'status_payment'            => $packageSummary->status_payment,
                        'type_transaction'          => 2, // medical package,
                        'package_summary_details'   => $packageSummaryDetailData,
                        'created_at'                => $packageSummary->created_at,
                    ];
                } else {
                    $packageSummaryDetailData = [];
                    foreach ($packageSummary->packageSummaryDetails as $packageSummaryDetail) {
                        $packageSummaryDetailData[] = [
                            'title'                 => $packageSummaryDetail->package_title ?? $packageSummaryDetail->package->title,
                            'price'                 => $packageSummaryDetail->package_price ?? $packageSummaryDetail->package->price,
                            'visit_date_time_label' => $packageSummaryDetail->visitDateTimeLabel,
                            'type'                  => [
                                'uuid'              => $packageSummaryDetail->packageType->uuid,
                                'name'              => $packageSummaryDetail->packageType->name,
                                'icon'              => $packageSummaryDetail->packageType->icon
                            ],
                            'patient'                   => [
                                'fullname'              => $packageSummaryDetail->patient->fullname,
                                'relation_patient'      => $packageSummaryDetail->patient->relation_patient,
                                'is_complete_data'      => $packageSummaryDetail->patient->is_complete_data
                            ],
                            'category'              => [
                                'name'              => $packageSummaryDetail->packageCategory->name
                            ],
                            'package'               => [
                                'title'             => $packageSummaryDetail->package->title
                            ],
                            'status'                => $packageSummaryDetail->status,
                        ];
                    }
                    $this->transactions[] = [
                        'uuid'                      => $packageSummary->uuid,
                        'amount_pay'                => $packageSummary->amount_pay,
                        'payment_invoice_url'       => $packageSummary->payment_invoice_url,
                        'status_payment'            => $packageSummary->status_payment,
                        'status_payment_label'      => SimrsStatus::getLabel($packageSummary->status_payment),
                        'type_transaction'          => 2, // medical package,
                        'type_transaction_label'    => Type::getLabel($packageSummary->type),
                        'package_summary_details'   => $packageSummaryDetailData,
                        'created_at'                => $packageSummary->created_at,

                    ];
                }
            }

            foreach ($packageSummaryDetails ?? [] as $packageSummaryDetail) {

                $packageSummaryDetailData   = [];

                $packageSummaryDetailData[] = [
                    'detail_id'             => $packageSummaryDetail->id,
                    'title'                 => $packageSummaryDetail->package_title ?? $packageSummaryDetail->package->title,
                    'price'                 => $packageSummaryDetail->package_price ?? $packageSummaryDetail->package->price,
                    'visit_date_time_label' => $packageSummaryDetail->visitDateTimeLabel,
                    'type'                  => [
                        'uuid'              => $packageSummaryDetail->packageType->uuid, // 'package_types.uuid
                        'name'              => $packageSummaryDetail->packageType->name,
                        'icon'              => $packageSummaryDetail->packageType->icon
                    ],
                    'patient'                   => [
                        'fullname'              => $packageSummaryDetail->patient->fullname,
                        'relation_patient'      => $packageSummaryDetail->patient->relation_patient,
                        'is_complete_data'      => $packageSummaryDetail->patient->is_complete_data
                    ],
                    'category'              => [
                        'name'              => $packageSummaryDetail->packageCategory->name
                    ],
                    'package'               => [
                        'title'             => $packageSummaryDetail->package->title
                    ],
                    'status'                => $packageSummaryDetail->status,
                ];

                $this->transactions[] = [
                    'uuid'                      => $packageSummaryDetail->uuid,
                    'type_transaction'          => 3, // medical package with multiple and paid,
                    'package_summary_details'   => $packageSummaryDetailData,
                    'created_at'                => $packageSummaryDetail->created_at,
                ];
            }
            */

            $this->transactions = collect($this->transactions)->sortByDesc('created_at')->values()->all();

            return self::success($this->transactions, 'success');
        } catch (\Throwable $th) {

            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            return self::error(null, $th->getMessage());
        }
    }
}
