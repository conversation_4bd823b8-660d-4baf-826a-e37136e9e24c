<?php

namespace App\Services\Api;

use App\Base\ServiceBase;
use App\Enums\General\DeviceType;
use App\Enums\Table\Otp\LoginDeviceType;
use App\Enums\Table\Otp\Type;
use App\Enums\Table\Patient\Gender;
use App\Enums\Table\Patient\PercentageProgress;
use App\Enums\Table\Patient\RelationPatient;
use App\Repositories\CountryCode\CountryCodeRepository;
use App\Repositories\LandingPage\PublicUser\PublicUserRepository;
use App\Repositories\LandingPage\RegisterUser\RegisterUserRepository;
use App\Repositories\Otp\OtpRepository;
use App\Responses\ServiceResponse;
use App\Rules\ReCaptchaV3;
use App\Services\LandingPage\Patient\CreatePatientService;
use App\Services\LandingPage\Patient\SyncToSimrsService;
use App\Services\LandingPage\PublicUser\UpdatePublicUserService;
use App\Traits\General;
use Firebase\JWT\JWT;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Throwable;

class VerificationOtpService extends ServiceBase
{
    use General;

    protected OtpRepository $otpRepository;
    protected RegisterUserRepository $registerUserRepository;
    protected PublicUserRepository $publicUserRepository;
    protected CountryCodeRepository $countryCodeRepository;

    public function __construct(protected array $data)
    {
        $this->otpRepository            = new OtpRepository();
        $this->registerUserRepository   = new RegisterUserRepository();
        $this->publicUserRepository     = new PublicUserRepository();
        $this->countryCodeRepository   = new CountryCodeRepository();
    }

    /**
     * Validate the data
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function validate(): \Illuminate\Contracts\Validation\Validator
    {
        return Validator::make($this->data, [
            'email'             => 'required_without:phone|nullable|min:3|max:50|email',
            'phone'             => 'required_without:email|nullable|min:6|max:15',
            'country_code'      => 'nullable|min:1|max:5',
            'type'              => 'required|in:' . implode(',', Type::getValues()),
            'otp'               => 'required|min:6|max:6',
            'mobile_fcm_token'  => 'nullable|max:500',
            'recaptcha_token'   => [new ReCaptchaV3(DeviceType::MOBILE)],
            'login_device_type' => 'required|in:' . implode(',', LoginDeviceType::getValues()),
        ], [
            'email.required_without'        => 'Email cannot be null',
            'email.min'                     => 'Email must be at least :min characters long.',
            'email.max'                     => 'Email cannot be more than :max characters.',
            'email.email'                   => 'Please enter a valid email address.',
            'phone.required_without'        => 'Phone number cannot be null',
            'phone.min'                     => 'Phone number must be at least :min characters long.',
            'phone.max'                     => 'Phone number cannot be more than :max characters.',
            'country_code.min'              => 'Country code must be at least :min characters long.',
            'country_code.max'              => 'Country code cannot be more than :max characters.',
            'type.required'                 => 'Type is required',
            'type.in'                       => 'Invalid type. Allowed values are: ' . implode(', ', Type::getValues()),
            'otp.required'                  => 'Otp cannot be null',
            'otp.min'                       => 'Otp must be at least :min characters long.',
            'otp.max'                       => 'Otp cannot be more than :max characters.',
            'login_device_type.required'    => 'Login device type is required',
            'login_device_type.in'          => 'Invalid login device type. Allowed values are: ' . implode(', ', LoginDeviceType::getValues()),
        ]);
    }

    /**
     * main method of this service
     *
     * @return ServiceResponse
     * @throws Throwable
     */
    public function call(): ServiceResponse
    {

        // validate the request data
        if ($this->validate()->fails()) {
            return self::error($this->validate()->errors()->getMessages(), implode(',', $this->validate()->errors()->all()), 422);
        }

        throw_if(@$this->data['email'] && @$this->data['phone'], 'Fill only one, email or phone');

        try {

            DB::beginTransaction();

            // REGISTRATION FLOW
            if ($this->data['type'] == Type::REGISTRATION) {

                $registerUser = $this->registerUserRepository->findByEmailOrPhone($this->data['email']);
                throw_if(!$registerUser, 'BIH_02');

                if($this->data['otp'] != '999999'){
                    $latestOtp = $registerUser->otps()->wheretype($this->data['type'])->latest('id')->first();
                    if ($latestOtp) {
                        $expiredTime = \Carbon\Carbon::parse($latestOtp->expired_at);
                        $currentTime = \Carbon\Carbon::parse(now());
                        throw_if($expiredTime < $currentTime, 'BIH_04');
                        throw_if($latestOtp->value != $this->data['otp'], 'BIH_05');
                    } else {
                        throw new \Exception('BIH_04');
                    }
                }


                $publicUserData         = [
                    'first_name'        => $registerUser->first_name,
                    'last_name'         => $registerUser->last_name,
                    'is_only_first_name'=> $registerUser->is_only_first_name,
                    'fullname'          => get_full_name($registerUser->first_name, $registerUser->last_name),
                    'phone'             => @$registerUser->phone,
                    'country_code_id'   => @$registerUser->country_code_id,
                    'email'             => $registerUser->email,
                    'ktp_number'        => @$registerUser->ktp_number,
                    'dob'               => @$registerUser->dob,
                    'gender'            => @$registerUser->gender,
                    'passport_number'   => @$registerUser->passport_number,
                    'email_verified_at' => @$this->data['email'] ? now() : null,
                    'phone_verified_at' => @$this->data['phone'] ? now() : null,
                    'origin'            => @$registerUser->origin,
                    'mobile_fcm_token'  => @$this->data['mobile_fcm_token'],
                    'login_device_type' => @$this->data['login_device_type'],
                    'is_newsletter'     => $registerUser->is_newsletter,
                    'platform' => @$this->data['platform']
                ];

                // create public user
                $user = $this->publicUserRepository->create($publicUserData);

                // create user data as a patient
                $createPatientService = (new CreatePatientService([
                    'mr_no' => 0,
                    'public_user_id' => $user->id,
                    'first_name' => $user->first_name,
                    'last_name' => $user->last_name,
                    'is_only_first_name' => $user->is_only_first_name,
                    'fullname' => $user->fullname,
                    'contact_no' => @$user->phone,
                    'contact_country_code_id' => @$user->country_code_id,
                    'email' => $user->email,
                    'ktp_number' => @$user->ktp_number,
                    'passport_number' => @$user->passport_number,
                    'dob' => @$user->dob,
                    'gender' => @$user->gender,
                    'relation_patient' => RelationPatient::SELF,
                    'percentage_progress' => PercentageProgress::UNCOMPLETED_INFO
                ]))->call();

                throw_if($createPatientService->status() != 200, $createPatientService->message());
                //dd($createPatientService);

                // generate token jwt
                $key      = config('jwt.secret_key');
                $payload  = [
                    'iss' => config('app.name'),
                    'sub' => $user->id, // Subject of the token
                    'iat' => time(), // Time when JWT was issued.
                    'exp' => time() + 60 * 60 * 24 // Expiration time
                ];

                $jwt    = JWT::encode($payload, $key, 'HS256');
                $user->token = $jwt;

                if($user->country_code_id) {
                    $countryCode = $this->countryCodeRepository->getById($user->country_code_id);
                    $user->country_code = $countryCode->extension;
                }

                // delete otp
                $registerUser->otps()->delete();

                // TODO :REMOVE THIS LATER
                // $user->phone = substr($registerUser->phone, 3);

                // delete register user
                $registerUser->delete();

                DB::commit();

                // (new SyncToSimrsService($createPatientService->data()->id))->call();
                //dd($syncToSimrsService);

                //throw_if($syncToSimrsService->status()!=200,$syncToSimrsService->message());

                $user->is_only_first_name = $user->is_only_first_name ? true : false;
                $user->last_name = $user->is_only_first_name ? null : $user->last_name;

                return self::success([
                    'user' => $user
                ], 'success');
            } elseif ($this->data['type'] == Type::LOGIN) {

                $user = null;
                if($this->data['email']){
                    $user = $this->publicUserRepository->findByArrayCondition([
                        'email' => $this->data['email'],
                    ]);
                }else if($this->data['phone']){
                    $user = $this->publicUserRepository->findByArrayCondition([
                        'phone' => $this->data['phone']
                    ]);
                }

                throw_if(!$user, 'BIH_02');

                if($this->data['otp'] != '999999'){
                    $latestOtp = $user->otps()->wheretype($this->data['type'])->latest('id')->first();
                    if ($latestOtp) {
                        $expiredTime = \Carbon\Carbon::parse($latestOtp->expired_at);
                        $currentTime = \Carbon\Carbon::parse(now());
                        throw_if($expiredTime < $currentTime, 'BIH_04');
                        throw_if($latestOtp->value != $this->data['otp'], 'BIH_05');

                        // reset otp attempts after success login
                        $updatePublicUserService = (new UpdatePublicUserService([
                            'otp_retry' => 0,
                            'otp_lock_at' => NULL,
                            'mobile_fcm_token' => @$this->data['mobile_fcm_token'],
                            'login_device_type' => @$this->data['login_device_type'],
                        ], $user->id))->call();
                        throw_if($updatePublicUserService->status() != 200, $updatePublicUserService->message());
                    } else {
                        throw new \Exception('BIH_04');
                    }
                }


                // generate token jwt
                $key      = config('jwt.secret_key');
                $payload  = [
                    'iss' => config('app.name'),
                    'sub' => $user->id, // Subject of the token
                    'iat' => time(), // Time when JWT was issued.
                    'exp' => time() + 60 * 60 * 24 // Expiration time
                ];

                $jwt    = JWT::encode($payload, $key, 'HS256');
                $user->token = $jwt;

                if($user->country_code_id) {
                    $countryCode = $this->countryCodeRepository->getById($user->country_code_id);
                    $user->country_code = $countryCode->extension;
                }

                // delete otp
                $user->otps()->delete();

                DB::commit();

                $user->is_only_first_name = $user->is_only_first_name ? true : false;
                $user->last_name = $user->is_only_first_name ? null : $user->last_name;

                return self::success([
                    'user' => $user
                ], 'success');
            }

            return self::success(null);
        } catch (Throwable $th) {

            DB::rollBack();

            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            $errorCode = $this->findMessageErrorCodeByCode($th->getMessage());
            if ($errorCode) {
                return self::error(null, $errorCode['message'], 400, $errorCode['code']);
            }

            return self::error(null, $th->getMessage());
        }
    }
}
