<?php

namespace App\Rules;

use App\Enums\General\DeviceType;
use Closure;
use Google\Cloud\RecaptchaEnterprise\V1\TokenProperties\InvalidReason;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;


class ReCaptchaV3 implements ValidationRule
{
    private string $deviceType;
    public function __construct(string $deviceType)
    {
        $this->deviceType = $deviceType;
    }

    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $siteKey = $this->deviceType == DeviceType::WEB ? $this->getWebSiteKey() : $this->getMobileSiteKey();
        $body = [
            'event' => [
                'token' => $value,
                'siteKey' => $siteKey,
                'expectedAction' => $this->getExpectedAction(),
            ]
        ];

        $response = Http::asJson()->post($this->recaptchaEnterpriseVerificationUrl(), $body);

        if ($response->status() !== ResponseAlias::HTTP_OK) {
            $fail('Validation request was failed with status and message: ['.$response->json('error.status').'] '.$response->json('error.message'));
        }

        if(!$response->json('tokenProperties.valid')) {
            if ($response->json('tokenProperties.invalidReason') !== "DUPE") {
                $fail('The token was invalid for the following reason: '.$response->json('tokenProperties.invalidReason'));
            }
        } else {
            if (strtoupper($response->json('tokenProperties.action')) == strtoupper(config('services.recaptcha.action'))) {
                if ((float)$response->json('riskAnalysis.score') <= 0.0) {
                    $fail('The Google reCAPTCHA verification score was too low, please try again.');
                }
            } else {
                $fail('The action attribute in your reCAPTCHA tag does not match the action you are expecting to score');
            }
        }
    }

    private function recaptchaEnterpriseVerificationUrl(): string
    {
        return "https://recaptchaenterprise.googleapis.com/v1/projects/{$this->getProjectId()}/assessments?key={$this->getApiKey()}";
    }

    private function getProjectId(): ?string
    {
        return config('services.recaptcha.project_id');
    }

    private function getWebSiteKey(): ?string
    {
        return config('services.recaptcha.key_web');
    }

    private function getMobileSiteKey(): ?string
    {
        return config('services.recaptcha.key_mobile');
    }

    private function getApiKey(): ?string
    {
        return config('services.recaptcha.api_key');
    }

    private function getExpectedAction(): ?string
    {
        return config('services.recaptcha.action');
    }
}
