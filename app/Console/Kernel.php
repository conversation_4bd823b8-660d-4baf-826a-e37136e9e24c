<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // $schedule->command('inspire')->hourly();
        // run it every weekdays at 17:00
//        $schedule->command('app:timesheet-reminder')->weekdays()->at('17:00');
        $schedule->command('app:medical-package-reminder')->cron(env('REMINDER_MEDICAL_PACKAGE_CRON_EXPRESSION_FIRST'));
        $schedule->command('app:medical-package-reminder')->cron(env('REMINDER_MEDICAL_PACKAGE_CRON_EXPRESSION_SECOND'));
        $schedule->command('app:synchronize-appointment-transaction null null')->cron('0 1 * * *');
        $schedule->command('app:synchronize-medical-package-transaction null null')->cron('0 1 * * *');
        $schedule->command('app:synchronize-public-user null null')->cron('0 1 * * *');
//        $schedule->command('app:sync-doctor')->cron('0 1 * * *');
        $schedule->command('app:sync-doctor')->everyTenMinutes();
        $schedule->command('app:sync-specialty')->cron('0 1 * * *');
        $schedule->command('app:sync-package-type-category')->cron('0 1 * * *');
        $schedule->command('app:get-data-visit-journey')->cron('0 23 * * *');
        $schedule->command('telescope:prune')->daily();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
