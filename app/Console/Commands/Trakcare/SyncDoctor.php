<?php

namespace App\Console\Commands\Trakcare;

use App\Jobs\Simrs\Doctor\SyncWeeklyScheduleJob;
use App\Models\Doctor;
use App\Models\Specialty;
use App\Repositories\Simrs\Models\ListDoctor;
use App\Repositories\Simrs\SimrsRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SyncDoctor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-doctor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronize doctor data from trakcare to BIH';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {

            $body = [
                "content"       => '['.app()->environment().']  START : SYNCHRONIZING DOCTOR',
                "avatar_url"    =>
                    "https://media.licdn.com/dms/image/D4E0BAQEtbeSJkNL14Q/company-logo_200_200/0/1664787920525/bithealth_logo?e=**********&v=beta&t=WdlP1wknXx5_nLF7A9CkWDAH9rQn6-aGHrlIZK-XjqI",
                "username"      => "developer-bithealth"
            ];

            Http::withHeaders([
                'Content-Type' => 'application/json'
            ])->post(config('discord.notification_channel.scheduler_notification'), $body);

            $data = new ListDoctor();
            $repo = (new SimrsRepository())->listDoctor($data);
            if ($repo->status() != 200) {
                throw new \Exception($repo->data()['error']);
            }

            $affected   = 0;
            $failed     = 0;

            foreach ($repo->data()['data'] as $doctor) {
                try {

                    if(str_contains($doctor['location_id'], '01I')) {
                        continue;
                    }

                    // do not do anything if the doctor does not have specialty
                    if (!$doctor['specialty_id']) {
                        \Log::error('Failed to update or create specialty because the doctor does not have specialty', [
                            'doctor' => $doctor,
                        ]);
                        $failed++;
                        continue;
                    }

                    // update or create specialty
                    try {
                        $specialty = Specialty::updateOrCreate([
                            'simrs_specialty_id'    => $doctor['specialty_id'],
                        ], [
                            'sub_name_en'           => $doctor['specialty_desc'],
                            'group_name_en'         => $doctor['specialty_desc'],
                            'type'                  => 1
                        ]);
                    } catch (\Exception $e) {
                        \Log::error('Failed to update or create specialty', [
                            'doctor' => $doctor,
                            'error' => $e->getMessage(),
                        ]);
                        $failed++;
                        continue;
                    }

                    // update or create doctor
                    try {
                        if(@$doctor['doctor_id']){

                            $doctorData = collect($repo->data()['data'])->where('doctor_id',$doctor['doctor_id']);
                            $isTele     = $doctorData->where('is_tele_doctor','true')->count() > 0 ? 'true' : 'false';
                            $isVisit    = $doctorData->where('is_web_doctor','true')->count() > 0 ? 'true' : 'false';

                            $newDoctor = Doctor::withTrashed()->updateOrCreate([
                                'simrs_doctor_id'               => $doctor['doctor_id'],
                            ], [
                                'name'                          => $doctor['full_name'],
                                'specialty_id'                  => $specialty->id,
                                'is_available_appointment'      => $isVisit == 'false' ? false : true,
                                'is_available_teleconsultation' => $isTele == 'false' ? false : true
                            ]);
                            SyncWeeklyScheduleJob::dispatch($newDoctor->id);
                        }
                    } catch (\Exception $e) {
                        \Log::error('Failed to update or create doctor', [
                            'doctor' => $doctor,
                            'error' => $e->getMessage(),
                        ]);
                        $failed++;
                        continue;
                    }

                    $affected++;
                } catch (\Exception $e) {
                    \Log::error('Failed to process doctor data', [
                        'doctor' => $doctor,
                        'error' => $e->getMessage(),
                    ]);
                    $failed++;
                }
            }

            $message = "SUCCESS TO SYNC DOCTOR DATA FROM TRAKCARE TO BIH. AFFECTED $affected ROWS, FAILED $failed ROWS";

            echo $message;

            $body = [
                "content"       => '['.app()->environment().']  END : ' . $message,
                "avatar_url"    =>
                    "https://media.licdn.com/dms/image/D4E0BAQEtbeSJkNL14Q/company-logo_200_200/0/1664787920525/bithealth_logo?e=**********&v=beta&t=WdlP1wknXx5_nLF7A9CkWDAH9rQn6-aGHrlIZK-XjqI",
                "username"      => "developer-bithealth"
            ];

            Http::withHeaders([
                'Content-Type' => 'application/json'
            ])->post(config('discord.notification_channel.scheduler_notification'), $body);

        } catch (\Throwable $th) {
            report($th);

            Log::error(__FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            echo "FAILED TO SYNC DOCTOR DATA WITH ERROR " . $th->getMessage();
        }
    }
}
