<?php

namespace App\Console\Commands;

use App\Jobs\Simrs\Doctor\SyncWeeklyScheduleJob;
use App\Models\Doctor;
use Illuminate\Console\Command;

class ManualSyncDoctor extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:manual-sync-doctor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $doctorIds = [
            "3300000012",
            "3300000044",
            "3300000014",
            "3300000026",
            "3300000073",
            "3300000020",
            "3300000057",
            "3300000633",
            "3300000027",
            "3300000616",
            "3300000055",
            "3300000055", // duplicate
            "3300000640",
            "3300000630",
            "3300000046",
            "3300000081",
            "3300000019",
            "3300000627",
            "3300000023",
            "3300000613",
            "3300000058",
            "3300000053",
            "3300000631",
            "3300000048",
            "3300000077",
            "3300000057", // duplicate
            "3300000059",
            "3300000018",
            "3300000017",
            "3300000629",
            "3300000035",
            "3300000614",
            "3300000024",
            "3300000039",
            "3300000628",
            "3300000635",
            "3300000013",
            "3300000637",
            "3300000069",
            "3300000072",
            "3300000004",
            "3300000015",
            "3300000056",
            "3300000638",
            "3300000067",
            "3300000071",
            "3300000034",
            "3300000033",
            "3300000042",
            "3300000618",
            "3300000021",
            "3300000617",
            "3300000634",
            "3300000070",
            "3300000057",
            "3300000023",
            "3300000053",
            "3300000048",
            "3300000071",
            "3300000628",
            "3300000072",
            "3300000056",
            "3300000634",
            "3300000070",
            "3300000631"
        ];
        foreach ($doctorIds as $doctorId){
            $id = Doctor::wheresimrs_doctor_id($doctorId)->first();
            if($id){
                SyncWeeklyScheduleJob::dispatchSync($id->id);
            }
        }
    }
}
