<?php

namespace App\Models;

use App\Traits\WithUuid;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Models\Cms\Article;
use Illuminate\Database\Eloquent\SoftDeletes;

class Doctor extends Model
{
    use WithUuid, SoftDeletes;
    protected $guarded = [];

    public function specialty() : BelongsTo
    {
        return $this->belongsTo(Specialty::class);
    }

    public function articles()
    {
        return $this->belongsToMany(Article::class);
    }

    public function certificates()
    {
        return $this->hasMany(DoctorCertificate::class);
    }

    public function clinicalInterests()
    {
        return $this->hasMany(DoctorClinicalInterest::class);
    }

    public function fellowships()
    {
        return $this->hasMany(DoctorFellowship::class);
    }

    public function regularSchedules()
    {
        return $this->hasMany(DoctorRegularSchedule::class);
    }

    public function languages()
    {
        return $this->hasMany(DoctorLanguage::class);
    }

    public function medicalSchools()
    {
        return $this->hasMany(DoctorMedicalSchool::class);
    }

    public function getImageUrlAttribute()
    {
        if(@$this->attributes['image']){
            return asset_gcs($this->attributes['image']);
        }else{
            return null;
        }
     }



}
