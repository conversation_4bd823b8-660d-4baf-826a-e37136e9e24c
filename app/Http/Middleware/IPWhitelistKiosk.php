<?php

namespace App\Http\Middleware;

use App\Enums\Table\BihConfig\Group;
use App\Enums\Table\BihConfig\Key;
use App\Traits\ApiResponser;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Symfony\Component\HttpFoundation\Response;

class IPWhitelistKiosk
{
    use ApiResponser;

    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $status         = true;
        $ip             = $request->ip();
        $message        = "success";
        $allowedIPs     = get_config_by_group(Group::WEBHOOK_IP_WHITELIST_KIOSK);
        $webhookToken   = bih_config_value(Key::WEBHOOK_TOKEN_KIOSK);

        // TODO : ROLLBACK THIS LATER
//        if(!in_array($ip, $allowedIPs->pluck('value')->toArray())){
//            $status     = false;
//            $message    = "unprocessable request, not allowed IP";
//        }

        if($request->header('Authorization') != $webhookToken){
            $status     = false;
            $message    = "unprocessable request, not allowed token";
        }

        if(!$status){

            $data = [
                'status :'          => false,
                'message : '        => $message,
                'IP : '             => $ip,
                'request data : '   => $request->all(),
            ];

             $body = [
                 "content"       => 'INCOMING KIOSK PATH '.$request->path().' DATA : ' . json_encode($data),
                 "avatar_url"    =>
                     "https://media.licdn.com/dms/image/D4E0BAQEtbeSJkNL14Q/company-logo_200_200/0/1664787920525/bithealth_logo?e=**********&v=beta&t=WdlP1wknXx5_nLF7A9CkWDAH9rQn6-aGHrlIZK-XjqI",
                 "username"      => "developer-bithealth"
             ];

             Http::withHeaders([
                 'Content-Type' => 'application/json'
             ])->post(config('discord.notification_channel.trakcare_notification'), $body);

            return $this->error($message, 403, [
                'ip' => $request->ip(),
            ]);
        }

        $data = [
            'status :'          => false,
            'message : '        => 'success',
            'IP : '             => $ip,
            'request data : '   => $request->all(),
        ];

         $body = [
             "content"       => 'INCOMING KIOSK PATH '.$request->path().' DATA : ' . json_encode($data),
             "avatar_url"    =>
                 "https://media.licdn.com/dms/image/D4E0BAQEtbeSJkNL14Q/company-logo_200_200/0/1664787920525/bithealth_logo?e=**********&v=beta&t=WdlP1wknXx5_nLF7A9CkWDAH9rQn6-aGHrlIZK-XjqI",
             "username"      => "developer-bithealth"
         ];

         Http::withHeaders([
             'Content-Type' => 'application/json'
         ])->post(config('discord.notification_channel.trakcare_notification'), $body);


        return $next($request);
    }
}
