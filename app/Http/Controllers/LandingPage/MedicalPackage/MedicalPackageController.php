<?php

namespace App\Http\Controllers\LandingPage\MedicalPackage;

use App\Enums\General\GeneralString;
use App\Enums\Table\BihConfig\Key;
use App\Http\Controllers\Controller;
use App\Models\BihConfigs;
use App\Models\LandingPage\Package;
use App\Models\LandingPage\PackageCart;
use App\Models\LandingPage\PackageCategory;
use App\Models\LandingPage\PackageSummary;
use App\Models\LandingPage\PackageType;
use App\Models\LandingPage\PublicUser;
use App\Repositories\LandingPage\PackageCart\PackageCartRepository;
use App\Services\LandingPage\Profile\GetListOfProfileService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;

class MedicalPackageController extends Controller
{

    public function index(Request $request)
    {
        $data = [
            'type'  => $request->type ?? 'Medical check up',
        ];
        return view('landing-page.medical-package.index', $data);
    }

    public function type($slugType)
    {
        $packageType = PackageType::where('slug', $slugType)->first();
        if(!$packageType) $packageType = PackageType::first();
        $data = [
            'package_categories' => PackageCategory::latest('id')->where('package_type_id', $packageType->id)->paginate(12),
            'package_type'       => $packageType,
        ];

        if($packageType->slug == GeneralString::MEDICAL_CHECK_UP_SLUG){
            $data['medical_check_up_offering_url'] = @BihConfigs::where('key',Key::MEDICAL_CHECK_UP_OFFERING_URL)->first()->value ?? null;
        }

        return view('landing-page.medical-package.type', $data);
    }

    public function category($slugCategory)
    {
        $data = [
            'package_category'  => PackageCategory::whereslug($slugCategory)->first(),
        ];
        return view('landing-page.medical-package.category', $data);
    }

    public function show($slugCategory, $slug)
    {
        $checkPackageInclusion = false;
        $package                = Package::whereslug($slug)->first();
        $category               = PackageCategory::whereslug($slugCategory)->first();
        $isExists               = PackageCart::where([
            'package_id'        => $package->id,
            'public_user_id'    => Session::get('user_id')
        ])->exists();
        $checkPackage = empty($package->simrs_id) ? false : true ;

        if($package->packageCategory->name == 'Women’s Clinic' || $package->packageCategory->name == 'Laboratory'){
            $checkPackageInclusion = true;
        }

        $data = [
            'category'          => $category,
            'package'           => Package::whereslug($slug)->first(),
            'related_packages'  => Package::latest('id')->whereis_published(true)
                ->wherepackage_category_id($package->package_category_id)
                ->whereNotIn('id', [$package->id])
                ->limit(3)->get(),
            'isExists'          => $isExists,
            'checkPackage'          => $checkPackage,
            'checkPackageInclusion' => $checkPackageInclusion
        ];

        return view('landing-page.medical-package.show', $data);
    }

    public function checkoutPackage($packageUuid)
    {
        try {

            $package = Package::whereuuid($packageUuid)->first();
            throw_if(!$package, 'Package not found');
            if($package){
                if(featureFlag('disable_multiple_mcu_booking')) {
                    (new PackageCartRepository())->deleteByArrayCondition([
                        'public_user_id'        => Session::get('user_id'),
                    ]);
                }

                (new PackageCartRepository())->firstOrCreate([
                    'public_user_id'        => Session::get('user_id'),
                    'package_id'            => $package->id,
                ],[
                    'package_type_id'       => $package->package_type_id,
                    'package_category_id'   => $package->package_category_id,
                ]);
            }

            return redirect(route('medical_packages.checkout',Str::uuid()));

        }catch (\Throwable $th){

            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            return back()->withError('something went wrong!');

        }

    }

    public function checkout(Request $request, $uuid)
    {

        // TODO : CHECK THIS LATER FOR PUBLIC USER
        $request->merge(['user_id' => $request->public_user_id ?? Session::get('user_id')]);

        $data = [
            'uuid' => $uuid
        ];
        return view('landing-page.medical-package.checkout',$data);

    }

    public function success($uuid)
    {
        try {
            $packageSummary = PackageSummary::whereuuid($uuid)->first();
            throw_if(!$packageSummary, 'Package summary not found');
            return view('landing-page.medical-package.success', compact('packageSummary'));
        }catch (\Throwable $th){

            return back()->withError('something went wrong!');
        }
    }

    public function addToCart($packageSlug)
    {
        try {

            $package = Package::whereslug($packageSlug)->first();
            if(featureFlag('disable_multiple_mcu_booking')) {
                (new PackageCartRepository())->deleteByArrayCondition([
                    'public_user_id'        => Session::get('user_id'),
                ]);
            }

            (new PackageCartRepository())->firstOrCreate([
                'public_user_id'        => Session::get('user_id'),
                'package_id'            => $package->id,
            ],[
                'package_type_id'       => $package->package_type_id,
                'package_category_id'   => $package->package_category_id,
            ]);

            return back()->withSuccess('Successfully added package to cart.');
        }catch (\Throwable $th){
            return back()->withError('something went wrong!');
        }
    }

    public function deleteFromCart($packageSlug)
    {
        try {
            $package = Package::whereslug($packageSlug)->first();
            (new PackageCartRepository())->deleteByArrayCondition([
                'public_user_id'        => Session::get('user_id'),
                'package_id'            => $package->id,
            ]);
            return back()->withSuccess('Package deleted from cart successfully');
        }catch (\Throwable $th){
            return back()->withError('something went wrong!');
        }
    }
}
