<?php

namespace App\Http\Controllers\LandingPage\Ajax;

use App\Http\Controllers\Controller;
use App\Models\LandingPage\Package;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;

class GetPackageAjaxController extends Controller
{
    use ApiResponser;

    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        return $this->success(Package::with('packageCategory')->latest()->get(), 'success');
    }
}
