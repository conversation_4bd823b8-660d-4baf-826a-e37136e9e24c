<?php

namespace App\Http\Controllers\Api\Doctor;

use App\Http\Controllers\Controller;
use App\Models\SpecialtySymptopm;
use App\Models\Symptom;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\DB;
use App\Repositories\Simrs\SimrsRepository;
use App\Repositories\Simrs\Models\AvailabilityDoctorData;
use App\Services\GCS\GoogleCloudService;
use App\Services\Simrs\Doctor\Schedule\DailyService;
use App\Services\Simrs\Doctor\Schedule\WeeklyService;
use Illuminate\Support\Facades\Log;

class DoctorController extends Controller
{

    public function search_schedule(Request $request)
    {

        try {
            $doctor = DB::table('doctors as d')
                ->select('d.*')
                ->where('d.uuid', $request->doctor_id)
                ->first();

            if (!$doctor) {
                return response()->json([
                    'status' => false,
                    'message' => 'doctor not found'
                ], 404);
            }
            $schedulesDoctorSimrsMonthly = (new WeeklyService([
                'doctor_id' => $doctor->simrs_doctor_id,

            ], false, 3))->call();
            $schedule = $schedulesDoctorSimrsMonthly->data();
            foreach($schedule as $schedules){
                $scheduleTemporary[]  = [
                    "date" => $schedules['full_date']
                ];
            }
            $scheduleMonthly = $scheduleTemporary;
            return response()->json([
                'status' => true,
                'message' => 'doctor schedule search success',
                'data' => $scheduleMonthly
            ]);
        } catch (\Exception $e) {
            report($e);

            Log::error(__FUNCTION__, [
                'Message ' => $e->getMessage(),
                'On file ' => $e->getFile(),
                'On line ' => $e->getLine()
            ]);

            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    public function search_daily_schedule($id, $date)
    {
        try {
            $doctor = DB::table('doctors as d')
                ->select('d.*')
                ->where('d.uuid', $id)
                ->first();
            if (!$doctor) {
                return response()->json([
                    'status' => false,
                    'message' => 'doctor not found'
                ], 404);
            }
            $scheduleDoctorDaily= (new DailyService(['date' => $date, 'doctor_id' => $doctor->simrs_doctor_id, 'avaibility' => 'Y']))->call();
            $schedule = $scheduleDoctorDaily->data();
            $scheduleDaily = '';
            foreach($schedule as $schedules){
                $time = $schedules['time'];
                $times = explode(' - ', $time);
                $price = '';
               foreach($schedules['services'] as $services){
                if($services['service_code'] === "TELE"){
                    $price = $services['price'];
                }
               }
               if($schedules['available'] !== 'N'){
                $current_time = Carbon::now();
                $date_now =Carbon::now()->format('Y-m-d');
                $from_time = Carbon::createFromTimeString($times[0]);
                if($date_now === $date){
                    if($from_time->greaterThan($current_time)) {
                        $scheduleTemporary[]  = [
                            "id" => str_replace('||', '^^', $schedules['slot_id']),
                            "available" => $schedules['available'],
                            "from"      => $times[0],
                            "to"    => $times[1],
                            "tele_price" => (string)$price
                        ];
                        $scheduleDaily = $scheduleTemporary;
                    }
                } else{
                    $scheduleTemporary[]  = [
                        "id" => str_replace('||', '^^', $schedules['slot_id']),
                        "available" => $schedules['available'],
                        "from"      => $times[0],
                        "to"    => $times[1],
                        "tele_price" => (string)$price
                    ];
                    $scheduleDaily = $scheduleTemporary;
                }

               }

            }

            return response()->json([
                'status' => true,
                'message' => 'doctor schedule search success',
                'data' => $scheduleDaily
            ]);
        } catch (\Exception $e) {
            report($e);

            Log::error(__FUNCTION__, [
                'Message ' => $e->getMessage(),
                'On file ' => $e->getFile(),
                'On line ' => $e->getLine()
            ]);

            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function find($id)
    {
        try {
            $doctor = DB::table('doctors as d')
                ->select('d.*')
                ->where('d.uuid', $id)
                ->first();

            if (!$doctor) {
                return response()->json([
                    'status' => false,
                    'message' => 'doctor not found'
                ], 404);
            }

            $gcs = new GoogleCloudService();
            $doctor->image = $gcs->getStaticUrl($doctor->image);

            $doctor->consultation_fee = "1250000"; // hardcode

            // $doctor->image = URL::to('/') . '/storage/' . $doctor->image;
            $schedulesDoctorSimrsWeekly = (new WeeklyService([
                'doctor_id' => $doctor->simrs_doctor_id,

            ], true, false))->call();
            $schedule = $schedulesDoctorSimrsWeekly->data();
            $day = '';
            foreach($schedule as $schedules){
                if(@$schedules['day'] == 'Sunday'){
                    $day = 1;
                }else if(@$schedules['day'] == 'Monday'){
                    $day = 2;
                }else if(@$schedules['day'] == 'Tuesday'){
                    $day = 3;
                }else if(@$schedules['day'] == 'Wednesday'){
                    $day = 4;
                }else if(@$schedules['day'] == 'Thursday'){
                    $day = 5;
                }else if(@$schedules['day'] == 'Friday'){
                    $day = 6;
                }else if(@$schedules['day'] == 'Saturday'){
                    $day = 7;
                }
                $scheduleTemporary[]  = [
                    "time_from"      => @$schedules['timefrom'],
                    "time_to"    => @$schedules['timeto'],
                    "day" => $day,
                    "location" => @$schedules['clinic_desc'],
                    "day_name" => @$schedules['day'],
                ];
            }

            $doctor->schedules = @$scheduleTemporary ?? [];

            $doctor->languages = DB::table('doctor_languages as lang')
                ->select('lang.*')
                ->where('lang.doctor_id', $doctor->id)
                ->get();

            $doctor->specialty = DB::table('specialties as sp')
                ->select('sp.*')
                ->where('sp.id', $doctor->specialty_id)
                ->first();

            $doctor->medical_schools = DB::table('doctor_medical_schools as edu')
                ->select('edu.*')
                ->where('edu.doctor_id', $doctor->id)
                ->get();

            $doctor->certificates = DB::table('doctor_certificates as cer')
                ->select('cer.*')
                ->where('cer.doctor_id', $doctor->id)
                ->get();

            $doctor->clinical_interests = DB::table('doctor_clinical_interests as ci')
                ->select('ci.*')
                ->where('ci.doctor_id', $doctor->id)
                ->get();

            $doctor->fellowships = DB::table('doctor_fellowships as fel')
                ->select('fel.*')
                ->where('fel.doctor_id', $doctor->id)
                ->get();

            $doctorCsWaLink = "";
            if (empty($doctor->is_available_appointment) && empty($doctor->is_available_teleconsultation)) {
                $bihConfig = DB::table('bih_configs as cfg')
                ->select('cfg.*')
                ->where('cfg.group', 'general')
                ->where('cfg.key', 'DOCTOR_CS_WA_LINK')
                ->first();
                $doctorCsWaLink = $bihConfig->value;
            }

            $doctor->doctor_cs_wa_link = $doctorCsWaLink;

            return response()->json([
                'status' => true,
                'message' => 'doctor find success',
                'data' => $doctor
            ]);
        } catch (\Exception $e) {
            report($e);

            Log::error(__FUNCTION__, [
                'Message ' => $e->getMessage(),
                'On file ' => $e->getFile(),
                'On line ' => $e->getLine()
            ]);
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }


    /**
     * Display a listing of the resource.
     */
    public function search(Request $request)
    {

        try {
            $query = DB::table('doctors as d')
                ->select('d.*', 'sp.degree', 'sp.group_name_en', 'sp.sub_name_en', 'sp.group_name_id', 'sp.sub_name_id')
                ->leftJoin('specialties as sp', 'd.specialty_id', '=', 'sp.id')
                ->leftJoin('doctor_regular_schedules as sc', 'd.id', '=', 'sc.doctor_id')
                ->leftJoin('doctor_languages as lang', 'd.id', '=', 'lang.doctor_id')
                ->whereNull('d.deleted_at');

            if ($request->name) {
                $query->where('d.name', 'LIKE', '%' . $request->name . '%');
            }

            if ($request->specialization) {
                $query->whereIn('d.specialty_id', SpecialtySymptopm::whereIn('symptom_id', $request->specialization)->pluck('speciality_id')->toArray());
            }

            if ($request->gender) {
                $query->whereIn('d.gender', $request->gender);
            }

            if ($request->type) {
                foreach ($request->type as $key => $value) {
                    $query->where('d.is_available_' . $value, 1);
                }
            }

            if ($request->language) {
                $query->whereIn('lang.name', $request->language);
            }

            if ($request->day) {
                $query->whereIn('sc.day', $request->day);
            }

            if ($request->time) {
                foreach ($request->time as $key => $value) {
                    $query->where(function ($query) use ($value) {
                        $query->where('sc.time_from', '>=', explode('-', $value)[0])
                            ->orWhere('sc.time_to', '<=', explode('-', $value)[1]);
                    });
                }
            }

            $count = $query->distinct()->count('d.id');

            $offset = ($request->paging["current_page"] - 1) * $request->paging["per_page"];

            $doctors = $query
                ->orderBy('d.id')
                ->limit($request->paging['per_page'])
                ->offset($offset)
                ->get();

            if ($doctors) {
                $gcs = new GoogleCloudService();
                foreach ($doctors as $key => $value) {
                    $value->image = $gcs->getStaticUrl($value->image);
                }
            }


            $data = [
                'doctors' => $doctors,
                'search' => [
                    'name' =>  $request->name ?? '', // doctor name
                    'specialization' => $request->specialization ?? [], // doctor specialtity_id
                    'day' => $request->day ?? [], // doctor_regular_schedules day
                    'time' => $request->time ?? [], // doctor_regular_schedules time_from, time_to
                    'gender' => $request->gender ?? [], //
                    'type' => $request->type ?? [], //
                    'language' => $request->language ?? [], //
                ],
                'paging' => [
                    'current_page' => $request->paging['current_page'],
                    'last_page' => ceil($count / $request->paging['per_page']),
                    'per_page' => $request->paging['per_page'],
                    'total' => $count,
                    'last_id' => $doctors[count($doctors) - 1]->id ?? 0,
                ],
            ];

            return response()->json([
                'status' => true,
                'message' => 'doctor search success',
                'data' => $data
            ]);
        } catch (\Exception $e) {

            report($e);

            Log::error(__FUNCTION__, [
                'Message ' => $e->getMessage(),
                'On file ' => $e->getFile(),
                'On line ' => $e->getLine()
            ]);

            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function search_param(Request $request)
    {

        $days = [
            ['value' => 1, 'name' => 'Sun'],
            ['value' => 2, 'name' => 'Mon'],
            ['value' => 3, 'name' => 'Tue'],
            ['value' => 4, 'name' => 'Wed'],
            ['value' => 5, 'name' => 'Thu'],
            ['value' => 6, 'name' => 'Fri'],
            ['value' => 7, 'name' => 'Sat'],
        ];

        $times = [
            ['value' => '07:00:00-12:00:00', 'name' => 'Morning'], // '07:00:00-12:00:00', '12:00:00-18:00:00', '18:00:00-24:00:00
            ['value' => '13:00:00-18:00:00', 'name' => 'Afternoon'],
            ['value' => '18:00:00-24:00:00', 'name' => 'Night'],
        ];

        $genders = [
            ['value' => 'M', 'name' => 'Male'],
            ['value' => 'F', 'name' => 'Female'],
        ];

        $types = [
            ['value' => 'appointment', 'name' => 'Hospital Visit'],
            ['value' => 'teleconsultation', 'name' => 'Teleconsultation'],
        ];

        $languages = DB::table('doctor_languages')
            ->select('name', 'name as value')
            ->distinct()
            ->orderBy('name', 'asc')
            ->get();

        $specialization = DB::table('specialties')
            ->select('id as value', 'sub_name_en as name')
            ->distinct()
            ->orderBy('sub_name_en', 'asc')
            ->get();

        $body_parts = DB::table('body_parts')
            ->select('id as value', 'name_id as name', 'name_en as name_en', 'image')
            ->where('name_en', '!=', 'All') // Added condition
            ->distinct()
            ->orderBy('name_en', 'asc')
            ->get();


        foreach ($body_parts as $key => $value) {
            $value->image = asset_gcs($value->image);
        }

        $chat = [
            'time' => 'Mon - Fri 08:00 - 20:00',
            'phone' => '************',
        ];


        return response()->json([
            'status' => true,
            'message' => 'master all param success',
            'data' => [
                'specialization' =>  $specialization, // doctor specialtity_id
                'day' =>  $days, //doctor_regular_schedules day
                'time' =>  $times, // doctor_regular_schedules time_from, time_to
                'gender' =>  $genders, //doctor gender
                'type' =>  $types, //doctor is_available_appointment, is_available_teleconsultation
                'language' =>  $languages, //doctor_languages
                'body_parts' =>  $body_parts, //doctor body_parts
                'chat' => $chat
            ]
        ]);
    }

    public function search_clinical_condition(Request $request)
    {

        $body_part_id   = $request->body_part_id;
        $organ_id       = $request->organ_id;
        $symptom_id     = $request->symptom_id;

        $gcs = new GoogleCloudService();

        if ($body_part_id) {
            $clinical_conditions = DB::table('organ_parts as a')
                ->select('a.id as value', 'a.name_id as name', 'a.name_en as name_en', 'a.image')
                ->where('a.body_part_id', $body_part_id)
                ->get();

            foreach ($clinical_conditions as $key => $value) {
                $value->image = $gcs->getStaticUrl($value->image);
            }
        }

        if ($organ_id) {
//            $clinical_conditions = DB::table('symptoms as a')
//                ->select('a.id as value', 'a.name_id as name', 'a.name_en as name_en', 'a.image')
//                ->where('a.organ_part_id', $organ_id)
//                ->get();

            $clinical_conditions         = Symptom::whereHas('organs', function ($q) use ($organ_id) {
                $q->where('organ_part_id', $organ_id);
            })->orWhere('is_body_part_all',true)
                ->select('id as value', 'name_id as name', 'name_en', 'image')
                ->get();
            foreach ($clinical_conditions as $key => $value) {
                $value->image = $gcs->getStaticUrl('public/mobile/organ_parts/a27514c0-fe45-4984-a6b0-8746486bcd85.png');
            }
        }


        if ($symptom_id) {
            $clinical_conditions = DB::table('specialty_symptopms as a')
                ->select('a.speciality_id as value', 'a.speciality_name as name', 'a.speciality_name as name_en')
                // ->join('symptoms as b', 'a.symptom_id', '=', 'b.id')
                // ->join('organ_parts as c', 'b.organ_part_id', '=', 'c.id')
                ->where('a.symptom_id', $symptom_id)
                ->get();
//            $clinical_conditions         = SpecialtySymptopm::wheresymptom_id($symptom_id)->get();
//            dd($clinical_conditions);
        }




        return response()->json([
            'status' => true,
            'message' => 'master clinical condition success',
            'data' => $clinical_conditions
        ]);
    }

    private function mockAvailabilityDoctorResponse()
    {
        $availabilities = [];

        for ($i = 1 ; $i<=90; $i++){
            $availabilities[] = [
                'date' => now()->addDay($i)->format('Y-m-d'),
                'time' => [
                    ["time_from" => "07:00", "time_to" => "07:15"],
                    ["time_from" => "07:15", "time_to" => "07:30"],
                    ["time_from" => "07:30", "time_to" => "07:45"],
                    ["time_from" => "20:00", "time_to" => "20:15"],
                    ["time_from" => "20:15", "time_to" => "20:30"],
                    ["time_from" => "20:30", "time_to" => "20:45"],
                    ["time_from" => "20:45", "time_to" => "21:00"],
                ]
            ];
        }
        return [
            "status" => 200,
            "message" => "loaded",
            "data" => [
                "status" => true,
                "message" => "success",
                "data" => [
                    [
                        "id" => 343,
                        "degree" => "dr.",
                        "name" => "gema antika hariadi",
                        "amount" => 3400000,
                        "availabilities" => $availabilities,
                    ],
                ],
            ],
            "customCode" => null,
        ];
    }
}
