<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\BihConfigs;
use App\Models\LandingPage\PackageType;
use App\Models\LandingPage\Patient;
use App\Models\LandingPage\PublicUser;
use App\Services\LandingPage\Patient\SyncToSimrsByPatientIdService;
use Illuminate\Http\Request;
use App\Repositories\Api\Public\HomeRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\URL;
use App\Services\GCS\GoogleCloudService;
use Illuminate\Support\Str;

class HomeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        //

        try {

            $homeRepository = new HomeRepository();
            $getCOEService = (new \App\Services\LandingPage\Homepage\GetCenterOfExcellenceService())->call();
            throw_if($getCOEService->status() != 200, $getCOEService->message());
            $coeData = $getCOEService->data();

            $center_of_excellences =  array_values($coeData);

            $promos = $homeRepository->getPromo();
            $articles = $homeRepository->getArticle();
            $packages = $homeRepository->getPackage();
            $announcement = $homeRepository->getAnnouncement();
            $package_menus = PackageType::whereHas('packages')->latest('id')->get();

            $gcs = new GoogleCloudService();

            foreach ($center_of_excellences as $key => $value) {
                $center_of_excellences[$key]['image_partnership'] = [
                    $gcs->getStaticUrl("assets/homepage/partnership/45032f0d-423b-4ea0-b9ff-bb466d554f61.png"),
                    $gcs->getStaticUrl("assets/homepage/partnership/f86d7cf2-4adb-4958-b20a-2750380eb21d.png"),
                    asset("assets/homepage/partnership/innoquest.png"),
                ];
            }

            $insurance_corporate_partners = [
                // ONE CARE LOGO
                $gcs->getStaticUrl('243796f7-3c74-42af-abad-fb01c82e4300.png'),
                // APRIL LOGO
                $gcs->getStaticUrl('ede6ce69-8644-4a77-a409-686156c9d831.png'),
                // BUPA LOGO
                $gcs->getStaticUrl('6cb40c67-a389-4a05-bf06-6c68d3bdbfa4.png'),
                // EUROP LOGO
                $gcs->getStaticUrl('2824d8ae-720f-4906-b270-47d30e5a7610.png'),
                // MEDILINX LOGO
                $gcs->getStaticUrl('4f9cd1a5-d11c-4b52-8559-efa16009ec26.png'),
                // MEDITAP LOGO
                $gcs->getStaticUrl('9aa7f647-ff5f-489d-86d7-58e1657d5108.jpg'),
                // INTERNATIONAL SOS LOGO
                $gcs->getStaticUrl('cfc74fde-31b6-40d5-a266-bd54bf995ca2.png'),
                // PENTA MEDICA LOGO
                $gcs->getStaticUrl('0bc76e8e-e638-48c6-8f62-e6feb3ac3e25.jpg'),
                // GLOBAL ASSISTANCE HEALTHCARE LOGO
                $gcs->getStaticUrl('06fad3b0-7727-46a1-b128-12cbeb88a5a2.jpg'),
                // MEDIC ASSIST LOGO
                $gcs->getStaticUrl('7d5fec52-5c68-4649-a3bb-ea4251011724.png'),
                // MQARE LOGO
                $gcs->getStaticUrl('1082e67b-d88e-4d1c-b52e-ecf5bb15dec9.png'),
                // MSIG SINARMAS LOGO
                $gcs->getStaticUrl('d272e00d-fc16-43c5-a0c5-87d1acfc3128.png'),
                // NUSA MEDICA LOGO
                $gcs->getStaticUrl('41111f63-2af8-4309-91d9-6f520f476e33.png'),
                // YAKES PERTAMINA LOGO
                $gcs->getStaticUrl('92f1194c-9b33-4abc-9036-34211eeba72a.jpg'),
                // BNI LIFE LOGO
                $gcs->getStaticUrl('35699aa2-d24c-4596-bd87-3ec897a248e2.png'),
                // BRI LIFE LOGO
                $gcs->getStaticUrl('64fa17f6-4328-4df7-aa82-5da4bd4844a9.png'),
                // CAR LIFE LOGO
                $gcs->getStaticUrl('eebb6eb2-6cae-4f7a-9590-96723c3a2030.png'),
                // JASA RAHARJA LOGO
                $gcs->getStaticUrl('7b6bb280-1bd0-4df1-b609-28496cd818a6.png'),
                // AXA Financial Indonesia LOGO
                $gcs->getStaticUrl('c94d2f1c-ec55-41cf-a73e-9c10c106a820.png'),
                // BCA LIFE LOGO
                $gcs->getStaticUrl('4e6f148d-9726-4514-a093-d98c3f9a1024.png'),
                // AD MEDIKA LOGO
                $gcs->getStaticUrl('public/assets/homepage/partnership/7a486fac-7b82-455b-b2ce-ff6e4378b54a.png'),
                // Halo Doc LOGO
                $gcs->getStaticUrl('public/assets/homepage/partnership/bac9375d-8543-4ca4-a727-513e4dedee72.png'),
                // Tirta LOGO
                $gcs->getStaticUrl('public/assets/homepage/partnership/b1e5b8b2-c68f-4913-a90a-0e53d432f80a.png'),
                // Doc-Doc LOGO
                $gcs->getStaticUrl('public/assets/homepage/partnership/9a024a59-2d30-4995-908b-031e4055c5b4.png'),
                // Medika Plaza LOGO
                $gcs->getStaticUrl('public/assets/homepage/partnership/fa076146-fbe5-41a2-8a41-ab30c62bc290.png'),
                // Global Excel LOGO
                $gcs->getStaticUrl('public/assets/homepage/partnership/ee9e3d01-c31f-42a8-bf84-d3b6ec2bf656.png'),
                // Owlexa Healthcare LOGO
                $gcs->getStaticUrl('3e71ca92-50da-4497-93e6-5387da22d0d5.png'),
                // Across Asia Asist LOGO
                $gcs->getStaticUrl('public/assets/homepage/partnership/747f7429-be19-4d65-8207-cf721aa927d2.png'),
                // HIGIEA LOGO
                $gcs->getStaticUrl('682c22ee-c6c8-4ef4-ae41-b706f75f1218.png'),
                // Fullerton Health Indonesia LOGO
                $gcs->getStaticUrl('0370e937-779f-4846-8b02-5cb57df40435.png'),
            ];

            foreach ($promos as $key => $value) {

                $html = new \Html2Text\Html2Text($value->content);
                $promos[$key]->content = $html->getText();
                $promos[$key]->url =  $request->root() . '/promos/' . $value->slug;
                $promos[$key]->image = $gcs->getStaticUrl($value->image);
            }

            foreach ($articles as $key => $value) {
                $html = new \Html2Text\Html2Text($value->content);
                $articles[$key]->content = $html->getText();
                $articles[$key]->url =  $request->root() . '/articles/' . $value->slug;

                $activity                           = $homeRepository->getArticleActivity($value->id);
                $articles[$key]->count_read         = $activity['count'];
                $articles[$key]->last_read          = $activity['last_activity'];
                $articles[$key]->image              = $gcs->getStaticUrl($value->image);
                $articles[$key]->created_by_name    = $value->user->name;
                $articles[$key]->created_by_image   = $value->user->image;
                unset($articles[$key]->user);
            }

            foreach ($packages as $key => $value) {
                $category = $packages[$key]->packageCategory;

                $html = new \Html2Text\Html2Text($value->content);
                $packages[$key]->content = $html->getText();
                $packages[$key]->url =  $request->root() . '/medical-packages/detail/' . $category->slug . '/' . $value->slug;
                // /medical-packages/detail/magni-nihil-corporis-aut-accusamus/norma-ward
                // /medical-packages/detail/slug-category/slug-package nya
                // /medical-packages/detail/health-screening-center-checkup/executive-female-package-2
                $packages[$key]->image = $gcs->getStaticUrl($value->image);
            }

            if ($announcement) {
                $html = new \Html2Text\Html2Text($announcement->desc);
                $announcement->desc = $html->getText();
                $announcement->image = $gcs->getStaticUrl($announcement->image);
            }

            if ($package_menus) {
                foreach ($package_menus as $key => $value) {
                    if ($value->image) {
                        $package_menus[$key]->image = $gcs->getStaticUrl($value->image);
                    }

                    if ($value->icon) {
                        $package_menus[$key]->icon = $gcs->getStaticUrl($value->icon);
                    }

                    $package_menus[$key]->url =  $request->root() . '/medical-packages/type/' . $value->slug;
                }
            }

            $version = BihConfigs::where('group', 'version')->get();
            $config_informations = BihConfigs::where('group', 'contactInformation')->select("key", "value")->get();

            // $contact_info = [];
            // foreach ($config_informations as $key => $value) {
            //     // $contact_info[$value->key] = $value->value;
            //     $contact_info['name'] = $value->key;
            //     $contact_info['value'] = $value->value;
            // }

            $data = [
                'promos' => $promos,
                'articles' => $articles,
                'packages' => $packages,
                'center_of_excellences' => $center_of_excellences,
                'insurance_corporate_partners' => $insurance_corporate_partners,
                'view_all' => [
                    'promos' => $request->root() . '/promos',
                    'articles' => $request->root() . '/articles',
                    'packages' => $request->root() . '/packages',
                    'coes' => $request->root() . '/patient-centered-care/center-of-excellence',
                    'partners' => $request->root() . '/insurance',
                ],
                'information' => [
                    'announcement' => $announcement,
                    // 'emergency_call' => '02123123123',
                    'contact_info_call_center' => $config_informations->where('key', 'Call Center')->first(),
                    'contact_info_chat_gp' => $config_informations->where('key', 'Chat with GP (Appointment Booking)')->first(),
                    'contact_info_customer_service' => $config_informations->where('key', 'Customer Service')->first(),
                    'contact_info_email' => $config_informations->where('key', 'Email')->first(),
                    'contact_info_emergency' => $config_informations->where('key', 'Emergency Number')->first(),
                    'outpatient_general_consent_url' => route('outpatient-general-consent.index')
                ],
                'package_menus' => $package_menus,
                'version' => [
                    'source' => 'config',
                    'version_ios' => $version->where('key', 'version_ios')->first()?->value,
                    'version_android' => $version->where('key', 'version_android')->first()?->value,
                    'url_update_ios' => $version->where('key', 'url_update_ios')->first()?->value,
                    'url_update_android' => $version->where('key', 'url_update_android')->first()?->value,
                ]

            ];

            return response()->json([
                'status' => true,
                'message' => 'Success',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function cart(Request $request, $user_id)
    {
        $homeRepository = new HomeRepository();

        $token = $request->header('Authorization');

        $platform = $request->header('Platform');

        $uuid = Str::uuid();

        $url = ($request->root() . '/medical-packages/checkout/' . $uuid . '?platform=' . $platform . '&token=' . $token);

        if ($user_id > 0) {
            $user = PublicUser::find($user_id);
            // $user_name = $user ? $user->fullname : '';
            $first_name = $user ? $user->first_name : '';
            $last_name = $user ? $user->last_name : '';
            $total = $homeRepository->countCart($user_id);

            $isOnlyFirstName = $user->is_only_first_name ? true : false;
            $last_name = $isOnlyFirstName ? null : $last_name;
        } else {
            //$user_name = '';
            $first_name = '';
            $last_name = '';
            $total = 0;
        }

        $data = [
            'user_id' => $user_id,
            //'user_name' => $user_name,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'cart' => [
                'total' => $total,
                'cart_url' => $url,
            ]
        ];

        return response()->json([
            'status' => true,
            'message' => 'Success',
            'data' => $data
        ]);
    }

    public function account(Request $request, $user_id)
    {

        $config = BihConfigs::where('group', 'account')->get();

        if ($user_id > 0) {
            $patient = Patient::where('public_user_id', $user_id)->first();

            if(@!(int)$patient->mr_no){
                (new SyncToSimrsByPatientIdService(@$patient->id, true))->call();
                $patient = Patient::where('public_user_id', $user_id)->first();
            }

            if ($patient?->image) {
                $gcs = new GoogleCloudService();
                $patient->image = $gcs->getStaticUrl($patient->image);
            }
        }

        $response = [
            'user_id'       => $user_id,
            'image'         => $patient->image ?? '',
            //'fullname'    => $patient->fullname ?? '',
            'first_name'    => $patient->first_name ?? '',
            'last_name'     => $patient->is_only_first_name ? null : ($patient->last_name ?? ''),
            'config'        => $config,
            'mr_no'         => @$patient->mr_no,
        ];


        return response()->json([
            'status' => true,
            'message' => 'Success',
            'data' => $response
        ]);
    }
}
