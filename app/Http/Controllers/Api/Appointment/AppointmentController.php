<?php

namespace App\Http\Controllers\Api\Appointment;

use App\Events\Appointment\AppointmentSubmitted;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\DB;
use App\Services\GCS\GoogleCloudService;
use Illuminate\Support\Str;
use App\Enums\Table\Patient\PercentageProgress;
use App\Services\PaymentGateway\Xendit\CreateInvoiceService;
use App\Enums\General\GeneralString;
use App\Enums\Table\AppointmentState;
use App\Enums\Table\PaymentStatus;
use App\Enums\Table\SimrsStatus;
// use App\Services\Appointment\UpdateAppointmentService;
use App\Enums\Appointment;
use App\Enums\General\RegistrationNoType;
use App\Enums\Simrs\Service;
use App\Services\LandingPage\Appointment\UpdateAppointmentService;
use App\Enums\Table\Patient\RelationPatient;
use App\Enums\Table\Q_R_Transactions\TrxType as Q_R_TransactionsTrxType;
use App\Enums\Table\AppointmentPatientSummary\Type;
use App\Services\CreateQRAppointmentService;
use App\Services\Simrs\Appointment\CreateAppointmentService;
use App\Services\Simrs\Doctor\Schedule\DailyService;
use Illuminate\Support\Facades\Log;
use App\Models\Doctor;
use App\Models\LandingPage\PackageCategory;
use App\Models\LandingPage\PackageSummaryDetail;
use App\Models\AppointmentPatientSummary;
use App\Models\BihConfigs;
use App\Enums\Table\BihConfig\Key;
use App\Enums\Table\Package\Reschedule;
use App\Services\Simrs\Equipment\ScheduleWeeklyService;
use App\Services\Simrs\Equipment\ScheduleDailyService;
use App\Services\Simrs\Doctor\Schedule\WeeklyService;
use App\Services\LandingPage\Profile\Transaction\RescheduleTransactionAppointmentService;
use App\Services\LandingPage\Profile\Transaction\RescheduleTransactionSingleMedicalPackageService;

class AppointmentController extends Controller
{

    public function list(Request $request)
    {
        $appointments = DB::table('appointment_patient_summaries')
            ->where('public_user_id', $request->user_id)
            ->get();

        return response()->json([
            'status' => true,
            'data' => $appointments
        ], 200);
    }

    public function find(Request $request, $id)
    {
        $appointment = DB::table('appointment_patient_summaries')
            ->where('uuid', $id)
            ->where('public_user_id', $request->user_id)
            // ->where('deleted_at', null)
            ->first();

        if ($appointment == null) {
            return response()->json([
                'status' => false,
                'message' => 'Appointment not found'
            ], 404);
        }

        if ($appointment->attachment_url != null) {
            $gcs = new GoogleCloudService();
            $appointment->attachment_url = $gcs->getStaticUrl($appointment->attachment_url);
        }

        if ($appointment->patient_id != null) {
            $patient = DB::table('patients')
                ->where('id', $appointment->patient_id)
                ->first();

            $appointment->patient = $patient;
        }

        if ($appointment->simrs_doctor_id != null) {
            $doctor = DB::table('doctors')
                ->where('simrs_doctor_id', $appointment->simrs_doctor_id)
                ->first();
            $appointment->doctor = $doctor;
        }



        return response()->json([
            'status' => true,
            'data' => $appointment
        ], 200);
    }

    public function store(Request $request)
    {
        try {

            // get the bearer token
            $bearerToken = explode(' ', $request->header('Authorization'))[1];

            $uuid = Str::uuid();
            $user_id = $request->user_id;

            $request->slot_id = str_replace('^^', '||', $request->slot_id);

            $attachment = "";

            if ($request->hasFile('attachment')) {
                $gcs = new GoogleCloudService();
                $upload = $gcs->uploadFile($request->file('attachment'), 'appointments/' . $user_id, $uuid);
                $attachment = $upload;
            }

            $doctor = DB::table('doctors')
                ->where('id', $request->doctor_id)
                ->first();

            if ($doctor == null) {
                return response()->json([
                    'status' => false,
                    'message' => 'Doctor not found'
                ], 404);
            }
            $price = '';
            if($request->type == 2){
            $scheduleDoctorDaily= (new DailyService(['date' => $request->book_date, 'doctor_id' => $doctor->simrs_doctor_id, 'avaibility' => 'Y']))->call();
            $schedule = $scheduleDoctorDaily->data();
            foreach($schedule as $schedules){
                if(empty($schedules['services'])){
                    return response()->json([
                        'status' => false,
                        'message' => 'Schedule is not found please contact Admin'
                    ], 404);
                }
               foreach($schedules['services'] as $services){
                if($services['service_code'] === "TELE" && $schedules['slot_id'] === $request->slot_id){
                    $price = $services['price'];
                }
               }

            }
        }
            $payor = '';
            if($request->payment_method == '2'){
                $payor = \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::PERSONAL;
            }else if($request->payment_method == '3'){
                $payor = \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::INSURANCE;
            }else if($request->payment_method == '4'){
                $payor = \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::COMPANY;
            }
            $price = '';
            if($request->type == 2){
            $scheduleDoctorDaily= (new DailyService(['date' => $request->book_date, 'doctor_id' => $doctor->simrs_doctor_id, 'avaibility' => 'Y']))->call();
            $schedule = $scheduleDoctorDaily->data();
            foreach($schedule as $schedules){

                if(empty($schedules['services'])){
                    return response()->json([
                        'status' => false,
                        'message' => 'Schedule is not found please contact Admin'
                    ], 404);
                }
               foreach($schedules['services'] as $services){
                if($services['service_code'] === "TELE" && $schedules['slot_id'] === $request->slot_id){
                    $price = $services['price'];
                }
               }

            }
        }
            $payor = '';
            if($request->payment_method == '2'){
                $payor = \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::PERSONAL;
            }else if($request->payment_method == '3'){
                $payor = \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::INSURANCE;
            }else if($request->payment_method == '4'){
                $payor = \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::COMPANY;
            }
            $patient = DB::table('patients')->where('id', $request->patient_id)->first();

            if ($patient == null) {
                return response()->json([
                    'status' => false,
                    'message' => 'Patient not found'
                ], 404);
            }

            $specialty = DB::table('specialties')->where('id', $doctor->specialty_id)->first();

            $contryCode = DB::table('country_codes')->where('id', $patient->contact_country_code_id)->first();
            $appointmentTrakcare = (new CreateAppointmentService(
                ['slot_id' => $request->slot_id,
                'patient_id' => $patient->simrs_patient_id,
                'notes'=> 'note : ' . $request->medical_concern . ' | attachment : ' . asset_gcs($attachment),
                'payor_code' => "{$payor}",//$request->payment_method == '2'? \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::PERSONAL? $request->payment_method == '3':  \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::INSURANCE : \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::COMPANY,
                'plan_code' => "{$payor}" ,//$request->payment_method == '2'? \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::PERSONAL? $request->payment_method == '3':  \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::INSURANCE : \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::COMPANY,
                'service_id'=>$request->type  == 1 ? Service::HOSPITAL_VISIT : Service::TELECONSULTATION]))->call();
            $newAppointment = $appointmentTrakcare->data();
            if($request->type == 1){
                $generatedQr = (new CreateQRAppointmentService([
                    'appointment_id' => $newAppointment['appointment_id'],
                    'trx_type' => Q_R_TransactionsTrxType::APP
                    ]))->call();
                $qrNew = $generatedQr->data();
                info($qrNew);
            }
            DB::table('appointment_patient_summaries')
                ->insert([
                    'uuid' => $uuid,
                    'sequence' => \App\Enums\Appointment\AppointmentState::FINISH,
                    'public_user_id' => $user_id,
                    'patient_id' => $request->patient_id,
                    'book_date' => $request->book_date,
                    'book_time_from' => $request->book_time_from,
                    'book_time_to' => $request->book_time_to,
                    'simrs_doctor_id' => $request->simrs_doctor_id,
                    'type' => $request->type,
                    'payment_status' => \App\Enums\Table\AppointmentPatientSummary\PaymentStatus::UNPAID,
                    'simrs_status'   => \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::UPCOMING,
                    'medical_concern' => $request->medical_concern,
                    'payment_method' => $request->payment_method == '0' ?  null : $request->payment_method,
                    'attachment_url' => $attachment,
                    'company_id' => $request->company_id,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                    'simrs_registration_no' => @$newAppointment['appointment_id'],
                    'simrs_episode_id'      => @$newAppointment['episode_id'],
                    'simrs_episode_no'      => @$newAppointment['episode_no'],
                    'simrs_slotnumber'      => @$newAppointment['slotnumber'],
                    'simrs_slot_id'         => $request->slot_id,

                ]);


            $appointment = DB::table('appointment_patient_summaries')->where('uuid', $uuid)->first();

            // teleconsultation

            if ($request->type == 2) {
                $public_user = DB::table('public_users')->where('id', $user_id)->first();


                $data = [
                    'external_id'               => GeneralString::XENDIT_PREFIX_APPOINTMENT_INVOICE . '_' . $appointment->id . '_' . now()->timestamp,
                    'customer_email'            => $public_user->email, //$this->appointment->publicUser->email, // user email
                    'customer_mobile_number'    => '+62' . $public_user->phone, //$this->appointment->publicUser->formattedPhoneNumber, //getFormattedPhoneNumberAttribute
                    'customer_name'             => $public_user->fullname, //$this->appointment->publicUser->fullname, // user
                    'success_redirect_url'      => config('app.url') . "/appointments/book/" . $uuid . "/" . $doctor->uuid . "/" . $request->type . "?token=" . $bearerToken . "&platform=mobile",
                    'failed_redirect_url'       => config('app.url') . "/profile/my-bookings" . "?token=" . $bearerToken . "&platform=mobile",
                    'currency'                  => 'IDR',
                    'invoice_duration'          => config('xendit.invoice_duration_in_second'),
                    'amount'                    => $price,
                    'description'               => 'Payment for Bithealth'
                ];

                $service = (new CreateInvoiceService($data))->call();

                $updateDataTeleconsul = [
                    'uuid'                  => $appointment->uuid,
                    'sequence'              => \App\Enums\Appointment\AppointmentState::FINISH,
                    'payment_external_id'   => $service->data->data['external_id'],
                    'payment_invoice_url'   => $service->data->data['invoice_url'],
                    'payment_status'        => \App\Enums\Table\AppointmentPatientSummary\PaymentStatus::UNPAID,
                    'amount'                => $price,
                    'simrs_status'          => \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::PAYMENT_PENDING
                ];

                // (new UpdateAppointmentService($updateDataTeleconsul))->call();
                // dd($updateData);
                // dd($appointment);
                DB::table('appointment_patient_summaries')
                    ->where('id', $appointment->id)
                    ->update([
                        'sequence'              => \App\Enums\Appointment\AppointmentState::FINISH,
                        'payment_external_id'   => $service->data->data['external_id'],
                        'payment_invoice_url'   => $service->data->data['invoice_url'],
                        'payment_status'        => \App\Enums\Table\AppointmentPatientSummary\PaymentStatus::UNPAID,
                        'amount'                => $price,
                        'simrs_status'          => \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::PAYMENT_PENDING
                    ]);
            }else{
                AppointmentSubmitted::dispatch($appointment->uuid);
            }

            DB::table('appointment_patient_summaries')
                    ->where('id', $appointment->id)
                    ->update([
                        'registration_no'       => generate_registration_no(RegistrationNoType::APPOINTMENT,  $appointment->id)

                    ]);

            // } else {
            //     // dd('else');
            //     $updateData = [
            //         'uuid'                  => $uuid,
            //         'sequence'              => \App\Enums\Appointment\AppointmentState::FINISH,
            //         'payment_status'        => \App\Enums\Table\AppointmentPatientSummary\PaymentStatus::UNPAID,
            //         'simrs_status'          => \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::UPCOMING
            //     ];

            //     // dd($updateData);
            //     (new UpdateAppointmentService($updateData))->call();
            // }
//            AppointmentSubmitted::dispatch($appointment->uuid);

            return response()->json([
                'status' => true,
                'message' => 'Appointment created successfully',
                'data' => [
                    'uuid' => $uuid,
                    'date' => $request->book_date,
                    'time' => $request->book_time_from . ' - ' . $request->book_time_to,
                    'doctor' => [
                        'name' => $doctor ? $doctor->name : null,
                        'specialty_id' => $doctor ? $doctor->specialty_id : null,
                    ],
                    'specialist' => [
                        'degree' => $specialty ? $specialty->degree : null,
                        'group_name_en' => $specialty ? $specialty->group_name_en : null,
                        'sub_name_en' => $specialty ? $specialty->sub_name_en : null,
                        'group_name_id' => $specialty ? $specialty->group_name_id : null,
                        'sub_name_id' => $specialty ? $specialty->sub_name_id : null,
                    ],
                    'patient' => [
                        'id' => $patient ? $patient->id : null,
                        'mr_no' => $patient ? $patient->mr_no : null,
                        'ktp_number' => $patient ? $patient->ktp_number : null,
                        'passport_number' => $patient ? $patient->passport_number : null,
                        'fullname' => $patient ? ($patient->is_only_first_name ? $patient->first_name : $patient->fullname) : null,
                        'contact_country_code_id' => $patient ? $patient->contact_country_code_id : null,
                        'contact_no' => $patient ? $patient->contact_no : null,
                        "is_complete_profile" => $patient ? $patient->percentage_progress == PercentageProgress::COMPLETE_ADDRESS_INFO : false,
                        'relation_name' => RelationPatient::getLabel($patient ? $patient->relation_patient : null),
                    ],
                    'country_code' => [
                        'id' => $contryCode ? $contryCode->id : null,
                        'extension' => $contryCode ? $contryCode->extension : null,
                        'name' => $contryCode ? $contryCode->country_name : null,
                        'code' => $contryCode ? $contryCode->country_code : null
                    ],
                    'teleconsultation' => $updateDataTeleconsul ?? null
                ]
            ], 200);
        } catch (\Exception $e) {
            report($e);

            Log::error(__FUNCTION__, [
                'Message ' => $e->getMessage(),
                'On file ' => $e->getFile(),
                'On line ' => $e->getLine()
            ]);
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function get_insurances(Request $request)
    {
        $insurances = DB::table('insurances')
            ->select('id', 'name')
            ->get();

        return response()->json([
            'status' => true,
            'data' => $insurances
        ], 200);
    }

    public function get_companies(Request $request)
    {
        $companies = DB::table('companies')
            ->select('id', 'name')
            ->get();

        return response()->json([
            'status' => true,
            'data' => $companies
        ], 200);
    }

    public function date_available(Request $request)
    {
        try {
            $type = @$request->type;
            $packageCategoryId = @$request->package_category_id;
            $doctorId = @$request->doctor_id;

            throw_if(!$type, 'Type is required');
            throw_if(($type == Type::VISIT || $type == Type::TELECONSULTATION) && !$doctorId, 'doctor id is required for type appointment');
            throw_if($type != Type::VISIT && $type != Type::TELECONSULTATION && !$packageCategoryId, 'package category id is required for type medical package');

            if($type == Type::VISIT || $type == Type::TELECONSULTATION) {
                $doctor = Doctor::where('id', $doctorId)
                    ->whereNull('deleted_at')->first();

                if(!$doctor) {
                    return response()->json([
                        'status' => false,
                        'message' => 'Doctor not found'
                    ], 404);
                }

                $scheduleService    = (new WeeklyService([
                    'doctor_id'     => $doctor->simrs_doctor_id,
                    'service'       => $type == 1 ? Service::HOSPITAL_VISIT : Service::TELECONSULTATION,
                ],false, 3))->call();

                if($scheduleService->status() == 200){
                    $schedulesData      = $scheduleService->data();

                    $schedules = [];
                    foreach($schedulesData as $schedule){
                        $temp = [
                            'date'  => $schedule['year'].'-'.$schedule['month'].'-'.$schedule['date'],
                        ];
                        $schedules[] = $temp;
                    }

                    return response()->json([
                        'status' => true,
                        'message' => 'schedule success',
                        'data' => $schedules
                    ], 200);
                } else {
                    return response()->json([
                        'status' => false,
                        'message' => 'something went wrong',
                    ], 500);
                }
            } else {

                $packageCategory = PackageCategory::where('id', $packageCategoryId)
                    ->whereNull('deleted_at')->first();

                if(!$packageCategory) {
                    return response()->json([
                        'status' => false,
                        'message' => 'Package category not found'
                    ], 404);
                }

                $data = [
                    'service'       => $packageCategory->simrs_id,
                ];

                $scheduleService = (new ScheduleWeeklyService($data, 3))->call();
                if ($scheduleService->status() == 200) {
                    $schedulesData = $scheduleService->data();

                    $schedules = [];
                    foreach ($schedulesData as $schedule) {
                        $schedules[] = [
                            'equipment_id' => $schedule['equipment_id'],
                            'date' => $schedule['full_date']
                        ];
                    }
                    return response()->json([
                        'status' => true,
                        'message' => 'schedule success',
                        'data' => $schedules
                    ], 200);
                } else {
                    return response()->json([
                        'status' => false,
                        'message' => 'something went wrong',
                    ], 500);
                }
            }

        } catch (\Exception $e) {
            report($e);

            Log::error(__FUNCTION__, [
                'Message ' => $e->getMessage(),
                'On file ' => $e->getFile(),
                'On line ' => $e->getLine()
            ]);
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function time_available(Request $request)
    {

        try {
            $type = @$request->type;
            $date = @$request->date;
            $equipmentId = @$request->equipment_id;
            $doctorId = @$request->doctor_id;

            throw_if(!$type, 'Type is required');
            throw_if(!$date, 'Date is required');
            throw_if(($type == Type::VISIT || $type == Type::TELECONSULTATION) && !$doctorId, 'doctor id is required for type appointment');
            throw_if($type != Type::VISIT && $type != Type::TELECONSULTATION && !$equipmentId, 'equipment id is required for type medical package');

            if($type == Type::VISIT || $type == Type::TELECONSULTATION) {
                $doctor = Doctor::where('id', $doctorId)
                    ->whereNull('deleted_at')->first();

                if(!$doctor) {
                    return response()->json([
                        'status' => false,
                        'message' => 'Doctor not found'
                    ], 404);
                }

                $availableTimes = [];
                $scheduleService = (new DailyService([
                    'date'          => $date,
                    'doctor_id'     => $doctor->simrs_doctor_id,
                    'availability'  => 'Y'
                ]))->call();

                if($scheduleService->status() == 200){
                    $schedulesData      = $scheduleService->data();

                    $schedules = [];
                    foreach($schedulesData as $schedule){
                        $price      = null;
                        if($type == Type::TELECONSULTATION){
                            $price = @collect($schedule['services'])->where('service_code', Service::TELECONSULTATION)->first()['price'];
                            if(!$price || $price <= 10000){
                                continue;
                            }
                        }

                        [$fromTime, $toTime] = explode('-', $schedule['time']);
                        if($schedule['date'] == now()->format('Y-m-d')){
                            if(Carbon::parse($fromTime)->format('H:i') <= Carbon::now()->format('H:i')){
                                continue;
                            }
                        }

                        $schedules[] = [
                            'from'      => $fromTime,
                            'to'        => $toTime,
                            'slot_id'   => $schedule['slot_id'],
                            'price'     => (float)$price,
                            'available' => 'Y',
                        ];
                    }

                    return response()->json([
                        'status' => true,
                        'message' => 'schedule success',
                        'data' => $schedules
                    ], 200);
                } else {
                    return response()->json([
                        'status' => false,
                        'message' => 'something went wrong',
                    ], 500);
                }
            } else {

                $data = [
                    'equipment_id'      => $equipmentId,
                    'date'              => $date,
                    'availability'      => 'Y',
                ];

                $service = (new ScheduleDailyService($data))->call();

                if ($service->status() == 200) {
                    $schedulesData = $service->data();

                    $schedules = [];
                    foreach ($schedulesData as $schedule) {
                        [$fromTime, $toTime] = explode('-', $schedule['time']);
                        if($schedule['date'] == now()->format('Y-m-d')){
                            if(Carbon::parse($fromTime)->format('H:i') <= Carbon::now()->format('H:i')){
                                continue;
                            }
                        }

                        $filteredAppoinments = array_filter($schedule['appointments'], function ($e) {
                            return $e['appointment_status'] != "X";
                        });

                        $schedules[] = [
                            'from'          => $fromTime,
                            'to'            => $toTime,
                            'slot_id'       => $schedule['slot_id'],
                        ];
                    }

                    return response()->json([
                        'status' => true,
                        'message' => 'schedule success',
                        'data' => $schedules
                    ], 200);
                } else {
                    return response()->json([
                        'status' => false,
                        'message' => 'something went wrong',
                    ], 500);
                }
            }

        } catch (\Exception $e) {
            report($e);

            Log::error(__FUNCTION__, [
                'Message ' => $e->getMessage(),
                'On file ' => $e->getFile(),
                'On line ' => $e->getLine()
            ]);
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }

    }

    public function time_available_mcu($equipmentId, $date)
    {

        try {


            $data = [
                'equipment_id'      => $equipmentId,
                'date'              => $date,
                'availability'      => 'Y',
            ];

            $service = (new ScheduleDailyService($data))->call();

            if ($service->status() == 200) {
                $schedulesData = $service->data();

                $schedules = [];
                foreach ($schedulesData as $schedule) {
                    [$fromTime, $toTime] = explode('-', $schedule['time']);
                    if($schedule['date'] == now()->format('Y-m-d')){
                        if(Carbon::parse($fromTime)->format('H:i') <= Carbon::now()->format('H:i')){
                            continue;
                        }
                    }

                    $filteredAppoinments = array_filter($schedule['appointments'], function ($e) {
                        return $e['appointment_status'] != "X";
                    });

                    $schedules[] = [
                        'from'          => $fromTime,
                        'to'            => $toTime,
                        'slot_id'       => $schedule['slot_id'],
                    ];
                }

                return response()->json([
                    'status' => true,
                    'message' => 'schedule success',
                    'data' => $schedules
                ], 200);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => 'something went wrong',
                ], 500);
            }

        } catch (\Exception $e) {
            report($e);

            Log::error(__FUNCTION__, [
                'Message ' => $e->getMessage(),
                'On file ' => $e->getFile(),
                'On line ' => $e->getLine()
            ]);
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }

    }

    public function reschedule(Request $request)
    {
        try {
            $uuid = @$request->uuid;
            $type = @$request->type;
            $equipmentId = @$request->equipment_id;
            $bookDate = @$request->book_date;
            $bookTimeFrom = @$request->book_time_from;
            $bookTimeTo = @$request->book_time_to;
            $slotId = str_replace('^^', '||', $request->slot_id);

            throw_if(!$uuid, 'Uuid is required');
            throw_if(!$type, 'Type is required');
            throw_if($type == 3 && !$equipmentId, 'Equipment id is required for type medical package');
            throw_if(!$bookDate, 'Book date is required');
            throw_if(!$bookTimeFrom || !$bookTimeTo, 'Book time from an to is required');
            throw_if(!$slotId, 'Slot id is required');

            if($type == Type::VISIT || $type == Type::TELECONSULTATION) {
                $appointmentPatientSummary = AppointmentPatientSummary::where('uuid', $uuid)->first();
                if(!$appointmentPatientSummary) {
                    return response()->json([
                        'status' => false,
                        'message' => 'Appointment not found'
                    ], 404);
                }
                $rescheduleRule = $this->rescheduleRules($type, null, $appointmentPatientSummary);
                $isRescheduleable = $rescheduleRule['isRescheduleable'];
                $rescheduleMessage = $rescheduleRule['rescheduleMessage'];
                throw_if(!$isRescheduleable, $rescheduleMessage);

                $service = (new RescheduleTransactionAppointmentService([
                    'uuid' => $uuid,
                    'appointment' => [
                        'selectedDay'           => $bookDate,
                        'selectedTime'          => $bookTimeFrom." - ".$bookTimeTo,
                        'selected_slot_id'      => $slotId
                    ]
                ]))->call();

            } else if($type == 3) {
                $packageSummaryDetail = PackageSummaryDetail::where('uuid', $uuid)->first();
                if(!$packageSummaryDetail) {
                    return response()->json([
                        'status' => false,
                        'message' => 'Appointment not found'
                    ], 404);
                }
                $rescheduleRule = $this->rescheduleRules($type, $packageSummaryDetail, null);
                $isRescheduleable = $rescheduleRule['isRescheduleable'];
                $rescheduleMessage = $rescheduleRule['rescheduleMessage'];
                throw_if(!$isRescheduleable, $rescheduleMessage);

                $service = (new RescheduleTransactionSingleMedicalPackageService([
                    'uuid' => $uuid,
                    'selected_packages' => [
                        'visit_date' => $bookDate,
                        'visit_time' => [
                            'time'      => $bookTimeFrom." - ".$bookTimeTo,
                            'slot_id'   => $slotId,
                        ],
                        'selected_equipment_id' => $equipmentId,
                    ]
                ]))->call();

            }

            if($service->status() == 1062) {
                return response()->json([
                    'status' => false,
                    'message' => 'You have another appointment for specified date & time, please select different date & time.'
                ], 400);
            }
            throw_if($service->status() != 200, "We can't process your request right now. Please reach out to our support team for help.");


            $fullData = $service->data['fulldata'];
            if($type != 3) {
                $contryCode = DB::table('country_codes')->where('id', $fullData->patient->contact_country_code_id)->first();
                $finalData = [
                    'uuid'                  => $fullData->uuid,
                    'date'                  => $fullData->book_date,
                    'time'                  => $fullData->book_time_from." - ".$fullData->book_time_to,
                    'appointment'           => [
                        'type'              => (string)$fullData->type,
                        'doctor'            => [
                            'name'          => $fullData->doctor->name,
                            'specialty_id'  => $fullData->doctor->specialty_id,
                            'gender'        => $fullData->doctor->gender,
                            'image_url'     => @$fullData->doctor->image_url,
                        ],
                        'specialist'        => [
                            'degree' => $fullData->doctor->specialty ? $fullData->doctor->specialty->degree : null,
                            'group_name_en' => $fullData->doctor->specialty ? $fullData->doctor->specialty->group_name_en : null,
                            'sub_name_en' => $fullData->doctor->specialty ? $fullData->doctor->specialty->sub_name_en : null,
                            'group_name_id' => $fullData->doctor->specialty ? $fullData->doctor->specialty->group_name_id : null,
                            'sub_name_id' => $fullData->doctor->specialty ? $fullData->doctor->specialty->sub_name_id : null,
                        ],
                        'teleconsultation'  => $type == Type::TELECONSULTATION ? [
                            'uuid'                  => $fullData->uuid,
                            'sequence'              => $fullData->sequence,
                            'payment_external_id'   => $fullData->payment_external_id,
                            'payment_invoice_url'   => $fullData->payment_invoice_url,
                            'payment_status'        => $fullData->payment_status,
                            'amount'                => (string)$fullData->amount,
                            'simrs_status'          => $fullData->simrs_status
                        ] : null,
                    ],
                    'patient'               => [
                        'id' => @$fullData->patient->id ?? null,
                        'mr_no' => @$fullData->patient->mr_no ?? null,
                        'ktp_number' => @$fullData->patient->ktp_number ?? null,
                        'passport_number' => @$fullData->patient->passport_number ?? null,
                        'fullname' => @$fullData->patient->fullname ?? null,
                        'contact_country_code_id' => @$fullData->patient->contact_country_code_id ?? null,
                        'contact_no' => @$fullData->patient->contact_no ?? null,
                        'is_complete_profile' => @$fullData->patient->percentage_progress == PercentageProgress::COMPLETE_ADDRESS_INFO ? true : false,
                        'relation_name' => RelationPatient::getLabel(@$fullData->patient->relation_patient ?? null),
                        'dob'   => @$fullData->patient->dob,
                    ],
                    'country_code'          => [
                        'id' => $contryCode ? $contryCode->id : null,
                        'extension' => $contryCode ? $contryCode->extension : null,
                        'name' => $contryCode ? $contryCode->country_name : null,
                        'code' => $contryCode ? $contryCode->country_code : null
                    ],
                    'package_summary'       => null,
                    'package_summary_detail'=> null
                ];
            }else {
                $finalData = [
                    'uuid'      => $fullData->uuid,
                    'date'      => $fullData->visit_date,
                    'time'      => $fullData->visit_time_from." - ".$fullData->visit_time_to,
                    'patient'   => [
                        'id' => $fullData->patient->id ?? null,
                        'mr_no' => $fullData->patient->mr_no ?? null,
                        'ktp_number' => $fullData->patient->ktp_number ?? null,
                        'passport_number' => $fullData->patient->passport_number ?? null,
                        'fullname' => $fullData->patient->fullname ?? null,
                        'contact_country_code_id' => $fullData->patient->contact_country_code_id ?? null,
                        'contact_no' => $fullData->patient->contact_no ?? null,
                        'is_complete_profile' => $fullData->patient->percentage_progress == PercentageProgress::COMPLETE_ADDRESS_INFO ? true : false,
                        'relation_name' => RelationPatient::getLabel($fullData->patient->relation_patient ?? null),
                        'dob'   => @$fullData->patient->dob,
                    ],
                    'package_summary'           => $fullData->packageSummary,
                    'package_summary_detail'    => $fullData,
                    'appointment'               => null,
                    'country_code'              => null
                ];
            }

            return response()->json([
                'status' => true,
                'message' => 'Appointment created successfuly',
                'data' => $finalData,
            ], 201);

        } catch (\Exception $e) {
            report($e);

            Log::error(__FUNCTION__, [
                'Message ' => $e->getMessage(),
                'On file ' => $e->getFile(),
                'On line ' => $e->getLine()
            ]);
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    private function rescheduleRules($type, $packageSummaryDetail = null, $appointmentPatientSummary = null)
    {
        $isRescheduleable = true;
        $rescheduleMessage = '';

        if($type == Type::VISIT || $type == Type::TELECONSULTATION) {
            $defaultReschedulePolicy = @BihConfigs::where('key', Key::DEFAULT_APPOINTMENT_RESCHEDULE_POLICY)->first()->value ?? '{}';
        }else{
            $defaultReschedulePolicy = @BihConfigs::where('key', Key::DEFAULT_MCU_RESCHEDULE_POLICY)->first()->value ?? '{}';
        }

        $defaultReschedulePolicy = json_decode($defaultReschedulePolicy, true);
        $rescheduleConfig = [
            'reschedule_policy' => @$defaultReschedulePolicy['reschedule_policy'],
            'reschedule_period' => @$defaultReschedulePolicy['reschedule_period'],
            'reschedule_period_with_promocode' => @$defaultReschedulePolicy['reschedule_period_with_promocode'],
            'reschedule_to_visit_period' => @$defaultReschedulePolicy['reschedule_to_visit_period'],
        ];

        if($type == 3 && $packageSummaryDetail->package->reschedule_policy) {
            $rescheduleConfig = [
                'reschedule_policy' => $packageSummaryDetail->package->reschedule_policy,
                'reschedule_period' => $packageSummaryDetail->package->reschedule_period,
                'reschedule_period_with_promocode' => $packageSummaryDetail->package->reschedule_period_with_promocode,
                'reschedule_to_visit_period' => $packageSummaryDetail->package->reschedule_to_visit_period,
            ];
        }


        if(@$rescheduleConfig['reschedule_policy'] == Reschedule::RESCHEDULE_POLICY_NOT_ALLOWED) {
            $isRescheduleable = false;
            $rescheduleMessage = ($type == Type::VISIT || $type == Type::TELECONSULTATION) ? '' : 'This package cannot be rescheduled';
        }

        if($type == Type::VISIT || $type == Type::TELECONSULTATION) {
            $visitDateTime = Carbon::parse($appointmentPatientSummary->book_date.' '.$appointmentPatientSummary->book_time_from);
        }else{
            $visitDateTime = Carbon::parse($packageSummaryDetail->visit_date.' '.$packageSummaryDetail->visit_time_from);
        }
        $hoursToVisitDate = $visitDateTime->subHours(@$rescheduleConfig['reschedule_to_visit_period'] ?? 48);

        $now = Carbon::now();
        if ($now->greaterThan($hoursToVisitDate)) {
            $isRescheduleable = false;
            $rescheduleMessage = 'Reschedule is not allowed within '.(@$rescheduleConfig['reschedule_to_visit_period'] ?? 48).' hours before the appointment';
        }

        if($type == Type::VISIT || $type == Type::TELECONSULTATION) {
            $initialRescheduleId = @$appointmentPatientSummary->parent_initial_reschedule_id ?? $appointmentPatientSummary->id;
            $initialAppointment = AppointmentPatientSummary::withTrashed()->find($initialRescheduleId);
        } else {
            $initialRescheduleId = @$packageSummaryDetail->parent_initial_reschedule_id ?? $packageSummaryDetail->id;
            $initialAppointment = PackageSummaryDetail::withTrashed()->find($initialRescheduleId);
        }
        $initialCreateDateTime = Carbon::parse($initialAppointment->created_at);

        if(@$rescheduleConfig['reschedule_policy'] == Reschedule::RESCHEDULE_POLICY_SPECIFIC_PERIOD) {
            $reschedulePeriod = $rescheduleConfig['reschedule_period'];

            if($type == 3) {
                $isWithPromocode = $packageSummaryDetail->packageSummary->promocode_id != null;
                if($isWithPromocode) {
                    $reschedulePeriod = $rescheduleConfig['reschedule_period_with_promocode'];
                }
            }

            $limitRescheduleDays = $initialCreateDateTime->copy()->addDays(@$reschedulePeriod ?? 0);
            if($now->greaterThan($limitRescheduleDays)) {
                $isRescheduleable = false;
                $rescheduleMessage = 'Based on our policy, this package cannot be rescheduled since it has exceeded '.$reschedulePeriod.' day(s) since purchase date ('.$initialCreateDateTime->format('d M Y').')';
            }
        }else if(@$rescheduleConfig['reschedule_policy'] == Reschedule::RESCHEDULE_POLICY_INDEFINITELY) {
            $limitRescheduleDays = Carbon::now()->addDays(365);
        }

        return [
            'isRescheduleable' => $isRescheduleable,
            'rescheduleMessage' => $rescheduleMessage,
        ];
    }
}
