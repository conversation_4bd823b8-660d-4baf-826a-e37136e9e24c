<?php

namespace App\Http\Controllers\Api\MyBooking;

use App\Http\Controllers\Controller;
use App\Services\Api\Transaction\ListCancelReasonService;
use App\Services\LandingPage\Patient\SyncToSimrsByPatientIdService;
use App\Traits\ApiResponser;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Services\GCS\GoogleCloudService;
use Illuminate\Support\Str;
use App\Enums\Table\AppointmentPatientSummary\SimrsStatus;
use App\Models\Doctor as ModelsDoctor;
use App\Repositories\Patient\PatientRepository;
use App\Enums\Table\AppointmentPatientSummary\Type;
use App\Models\BihConfigs;
use App\Models\Cms\PackageType;
use App\Models\LandingPage\PackageSummaryDetail;
use App\Models\PackageDetailTest;
use App\Repositories\LandingPage\AppointmentPatientSummary\AppointmentPatientSummaryRepository;
use App\Repositories\LandingPage\PackageSummary\PackageSummaryRepository;
use App\Services\Api\Transaction\ListTransactionService;
use App\Services\Api\GetListPatientService;
use App\Services\LandingPage\Profile\Transaction\CancelTransactionSingleMedicalPackageService;
use App\Services\LandingPage\Profile\Transaction\CancelTransactionTeleconsultationService;
use Illuminate\Support\Facades\Log;
use App\Enums\Table\PackageSummaryDetail\Status;
use App\Enums\Table\Package\Reschedule;
use App\Enums\Table\BihConfig\Key;
use App\Models\AppointmentPatientSummary;
use App\Repositories\Simrs\Models\GetAppointment;
use App\Repositories\Simrs\SimrsRepository;
use Carbon\Carbon;

class BookingController extends Controller
{

    use ApiResponser;

    public function cancel(Request $request)
    {
        try {
            if ($request->type == 1) {
                $service    = (new CancelTransactionTeleconsultationService([
                    'uuid'  => $request->uuid,
                    'reason' => $request->reason
                ]))->call();
                throw_if($service->status() != 200, $service->message());
            } elseif ($request->type == 2) {

                $service    = (new CancelTransactionSingleMedicalPackageService([
                    'uuid'  => $request->uuid,
                    'reason' => $request->reason
                ]))->call();
                throw_if($service->status() != 200, $service->message());
            } else {
                throw new \Exception('Type not found');
            }

            return $this->success(null, 'success');
        } catch (\Throwable $th) {

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            return $this->error($th->getMessage());
        }
    }


    public function data(Request $request)
    {
        try {
            if (!isset($request->category)) {
                return response()->json(
                    [
                        'status' => false,
                        'message' => 'Category is required',
                        'data' =>   null,
                    ],
                    400
                );
            }

            $data = [
                'public_user_id' => $request->user_id,
            ];

            if (isset($request->filters['patient_id'])) {
                $data['patient_id'] = $request->filters['patient_id'];
            }

            if (isset($request->filters['type'])) {
                if (is_numeric($request->filters['type'])) {
                    $data['type'] = (int)$request->filters['type'];
                } else {
                    $data['package_type_uuid'] = $request->filters['type'];
                }
            }

            if (isset($request->filters['date'])) {
                $data['created_at'] = $request->filters['date'];
            }

            $service = (new ListTransactionService($data, $request->category))->call();

            if ($service->status() != 200) {
                return response()->json(
                    [
                        'status' => false,
                        'message' => 'something went wrong',
                        'data' =>  null
                    ],
                    400
                );
            }
            return response()->json(
                [
                    'status' => true,
                    'message' => 'Success',
                    'data' =>  $service->data
                ],
                200
            );
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function detail(Request $request)
    {
        try {
            $user_id = $request->user_id;
            $patient_id = $request->patient_id;
            $transaction_type = $request->transaction_type;
            $transaction_header_uuid = $request->transaction_header_uuid;
            $transaction_detail_uuid = $request->transaction_detail_uuid;

            $patient_repo = new PatientRepository();

            $gcs = new GoogleCloudService();

            $appointment = [];
            $packages = [];
            $url ='';

            // $package_details = [];
            if ($transaction_type == 1) {
                $appointment_repo = new AppointmentPatientSummaryRepository();

                $appointment = $appointment_repo->findByCondition([
                    'uuid' => $transaction_header_uuid
                ]);

                if($appointment->type == 1){
                    $qr_transactions = DB::table('q_r_transactions')->where('appointment_id', $appointment->simrs_registration_no)->first();
                    $url = @$qr_transactions->url;
                }
                $appointment->appointment_id = @$appointment->simrs_registration_no;
                $appointment->consultation_room = "Bali International Hospital";
                $appointment->status_label = SimrsStatus::getLabel($appointment->simrs_status);
                $appointment->amount = floatval($appointment->amount);
                $appointment->discount = 0;
                $appointment->amount_before_discount = 0;
                $appointment->type_label = Type::getLabel($appointment->type);
                $appointment->enabled_teleconsultation =  $appointment->showComponent(9); // change to logic for enable button
                $appointment->qr_url = @$url;
                $initiateTime = Carbon::createFromFormat('Y-m-d H:i:s', "$appointment->book_date $appointment->book_time_from");
                // $initiateTime = Carbon::createFromTimeString($appointment->book_time_from);
                $timeBefore = $initiateTime->subMinutes(10);
                $currentTime = Carbon::now();
                $appointment->status_tele = false;
                if ($currentTime->greaterThanOrEqualTo($timeBefore)) {
                    $appointment->status_tele = true;
                }
                // $appointment->payment_channel = empty($appointment->payment_channel) ? null : Str::title($appointment->payment_channel);
                // $appointment->xendit_payment_method =  empty($appointment->xendit_payment_method) ? null : Str::title($appointment->xendit_payment_method);

                $guarantor_type = 'Personal';
                $guarantor_name = null;

                if ($appointment->payment_method == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::INSURANCE) {
                    $guarantor_type = 'Insurance';

                } else if ($appointment->payment_method == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::COMPANY) {
                    $guarantor_type = 'Company';

                }

                $appointment->guarantor = [
                    'type' => $guarantor_type,
                    'name' => @$guarantor_name,
                    'number' => @$appointment->insurance_number,
                ];

                $xendit = null;

                if (!empty($appointment->payment_channel) && !empty($appointment->xendit_payment_method)) {
                    $xendit = [
                        'channel' => Str::title($appointment->payment_channel),
                        'method' => Str::title(str_replace('_', ' ', $appointment->xendit_payment_method)),
                    ];
                }

                $appointment->xendit = $xendit;

                $appointment->cancel_status = [
                    'status' => 'dummy - user/system',
                    'reason' => 'dummy - reason',
                    'contact' => 'dummy - contact',
                ];

                $appointment->attachment_url = $gcs->getStaticUrl($appointment->attachment_url);
                $appointment->attachment_name = basename($appointment->attachment_url);
                $appointment->doctor = ModelsDoctor::where('simrs_doctor_id', $appointment->simrs_doctor_id)
                    ->select('uuid','id', 'name', 'specialty_id','gender')
                    ->first();
                $appointment->doctor?->append('image_url');

                if ($appointment->doctor != null) {
                    $appointment->doctor->specialty = DB::table('specialties')
                        ->where('id', $appointment->doctor->specialty_id)
                        ->select('id','uuid', 'degree', 'group_name_en')
                        ->first();
                }
            } else {

                $package_repo = new PackageSummaryRepository();
                $package_detail_repo = new PackageSummaryDetail();

                $packages = $package_repo->findByCondition([
                    'uuid' => $transaction_header_uuid
                ]);

                $packages->status_label = SimrsStatus::getLabel($packages->status_payment);
                $packages->amount = floatval($packages->amount_pay);
                $packages->discount = floatval($packages->discount);
                $packages->amount_before_discount = floatval($packages->amount_before_discount);
                $packages->payment_status = is_numeric($packages->status_payment) ? \App\Enums\Table\PackageSummary\StatusPayment::getLabel($packages->status_payment) : $packages->status_payment;
                // $packages->payment_channel = Str::title($packages->payment_channel);
                // $packages->payment_method = Str::title(str_replace('_', ' ', $packages->payment_method));

                $xendit = null;

                if (!empty($packages->payment_channel) && !empty($packages->payment_method)) {
                    $xendit = [
                        'channel' => Str::title($packages->payment_channel),
                        'method' => Str::title(str_replace('_', ' ', $packages->payment_method)),
                    ];
                }

                $packages->xendit = $xendit;

                $packages->cancel_status = [
                    'status' => 'dummy - user/system',
                    'reason' => 'dummy - reason',
                    'contact' => 'dummy - contact',
                ];


                $package_details = PackageSummaryDetail::whereIn('uuid', $transaction_detail_uuid)->get();

                foreach ($package_details as $package_detail) {
                    $qr_transactions = DB::table('q_r_transactions')->where('appointment_id', $package_detail->simrs_appointment_id)->first();
                    $url = @$qr_transactions->url;
                    // $package_detail->package_type = PackageType::where('id', $package_detail->package_type_id)->first();
                    $package_detail->status = Status::getLabel($package_detail->status);
                    $package_detail->file = $gcs->getStaticUrl($package_detail->file);
                    $package_detail->file_name = $package_detail->file_name ?? basename($package_detail->file);
                    $package_detail->attachment_url = $gcs->getStaticUrl($package_detail->attachment_url);
                    $package_detail->attachment_name = $package_detail->attachment_name ?? basename($package_detail->attachment_url);
                    $package_detail->price = floatval($package_detail->package_price ?? $package_detail->package->price);
                    $package_detail->status_label = Status::getLabel($package_detail->status);
                    $package_detail->packageType;
                    $package_detail->qr_url = @$url;
                    $package_detail->appointment_id = @$package_detail->simrs_appointment_id;

                    if($package_detail->packageType != null){
                        $package_detail->packageType->icon = $gcs->getStaticUrl($package_detail->packageType->icon);
                    }
                    $package_detail->packageCategory;
                    $package_detail->package;
                    if ($package_detail->package != null) {
                        $package_detail->package->image = $gcs->getStaticUrl($package_detail->package->image);
                    }

//                    $package_detail->pre_screening_link = @BihConfigs::where('key', Key::PRE_VISIT_SCREENING_LINK)->first()->value ?? '-';
                    $package_detail->pre_screening_link = @$package_detail->assessment_form_url;

                    // UPCOMING & MCU
                    if ($package_detail->status == 1 && $package_detail->package_type_id == 1) {
                        $config = BihConfigs::where('key', 'PreVisitScreeningLink')->first();
//                        $package_detail->pre_screening_link = $config->value;
                        $package_detail->pre_screening_link = @$package_detail->assessment_form_url;;
                    }


                    // dd($package_detail);

                    $detail_test = PackageDetailTest::where('package_id', $package_detail->package_id)
                        ->select('id', 'package_id', 'section', 'abbrivation')->get();

                    // get section name by distinct
                    $section = $detail_test->unique('section')->pluck('section')->toArray();

                    // dd($section);

                    $section_name = [];
                    foreach ($section as $key => $value) {
                        $model_section['name'] = $value;
                        //get abbreviation by section
                        $section_abbrivation = $detail_test->where('section', $value)->pluck('abbrivation')->toArray();
                        $model_section['abbrivations'] = $section_abbrivation;

                        //push to array
                        $section_name[] = $model_section;
                    }
                    $package_detail->package_detail_tests = $section_name;
                    $patientData        = [
                        'id'            => @$package_detail->patient->id,
                        'phone_number'  => @$package_detail->patient->contactWithCode,
                        'is_complete'   => @$package_detail->patient->isCompleteData,
                        'image'         => asset_gcs(@$package_detail->patient->image),
                        'dob'           => @$package_detail->patient->dob,
                    ];
                    $package_detail->patient = $patientData;
                    // dd($package_detail);

                    // $package_detail->section_name = $section_name;

                    // dd($package_detail);

                    // // get abbreviation by section
                    // $detail_test = $detail_test->groupBy('section');
                    // foreach ($detail_test as $key => $value) {
                    //     $detail_test[$key] = $value->pluck('abbrivation')->toArray();
                    // }

                    // $package_detail->package_detail_tests = $detail_test;
                }
            }

            $patient = $patient_repo->getById($patient_id);
            $patient->relation_patient_label = $patient->relation_patient == "0" ? "" : \App\Enums\Table\Patient\RelationPatient::getLabel($patient->relation_patient);
            $patient->family_card_image_url = $gcs->getStaticUrl($patient->family_card_image);
            $patient->family_card_image_name = basename($patient->family_card_image);
            $patient->image = $gcs->getStaticUrl($patient->image);


            $whatsapp = $patient->whatsappCountryCodes->first();
            $patient->whatsapp_code = [
                "extension" => $whatsapp?->extension,
                "name" => $whatsapp?->country_name,
                "code" => $whatsapp?->country_code,
            ];

            $caregiver = $patient->caregiverContactCountryCodes->first();
            $patient->caregiver_code = [
                "extension" => $caregiver?->extension,
                "name" => $caregiver?->country_name,
                "code" => $caregiver?->country_code,
            ];

            $patient->is_complete_data = $patient->isCompleteData;
            $reschedule = $this->rescheduleRules($transaction_type, @$package_details[0] ?? null, @$appointment ?? null);

            $isOnlyFirstName = $patient->is_only_first_name ? true : false;
            $patient->last_name = $isOnlyFirstName ? null : $patient->last_name;

            $result = [
                'patient' =>  $patient,
                'appointment' => !empty($appointment)  ? $appointment : null,
                'package' =>  !empty($packages) ? $packages : null,
                'package_detail' => !empty($package_details) ? $package_details : [],
                'reschedule' => [
                    'is_rescheduleable' => $reschedule['isRescheduleable'],
                    'message'           => $reschedule['rescheduleMessage']
                ]
            ];

            if(@!(int)$patient->mr_no){
                (new SyncToSimrsByPatientIdService(@$patient->id,$patient->public_user_id == $request->user_id))->call();
            }

            return response()->json(
                [
                    'status' => true,
                    'message' => 'on development',
                    'data' =>  $result
                ],
                200
            );
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    public function getUrlLink(Request $request)
    {
        $data = new GetAppointment();
        $data->appointment_id =         $request->appointment_id;
        $repository = (new SimrsRepository())->getAppointment($data);
        if ($repository->status() != 200) {
            return response()->json([
                'status' => false,
                'message' => 'Apologies, the URL is currently unavailable. Please try again later.'
            ], 500);
        }

        if (@!$repository->data()['data']) {
            return response()->json([
                'status' => false,
                'message' => 'Apologies, the URL is currently unavailable. Please try again later.'
            ], 404);
        }
        $url_link_teleconsultation = @$repository->data['data'][0]['meeting_url'];
        if ($url_link_teleconsultation) {
            AppointmentPatientSummary::whereUuid($request->uuid)->update([
                'url_link_teleconsultation' => $url_link_teleconsultation
            ]);
            return response()->json([
                'status' => true,
                'message' => 'tele url added success',
                'data' => $url_link_teleconsultation
            ]);
        } else {
            return response()->json([
                'status' => true,
                'message' => 'Apologies, the URL is currently unavailable. Please try again later.',
                'data' => null,
            ], 200);
        }
    }

    public function parameters(Request $request)
    {
        $package_type = PackageType::all();
        $response = [];

        $type = Type::getValueAndIdString();
        $response = $type;

        $service = (new GetListPatientService($request->all()))->call();
        $patients = $service->data;

        foreach ($package_type as $type) {
            $data = [
                'id' => $type->uuid,
                'name' => $type->name
            ];
            $response[] = $data;
        }

        $response = [
            'type' => $response,
            'patient' => $patients
        ];

        return response()->json(
            [
                'status' => true,
                'message' => 'Success',
                'data' =>  $response
            ],
            200
        );
    }

    public function cancelReason(Request $request)
    {
        try {

            $service = (new ListCancelReasonService([]))->call();
            throw_if($service->status() != 200, $service->message());

            return $this->success([
                'reasons' => $service->data()
            ], 'success');
        } catch (\Throwable $th) {

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            return $this->error($th->getMessage());
        }
    }

    private function rescheduleRules($type, $packageSummaryDetail = null, $appointmentPatientSummary = null)
    {
        $isRescheduleable = true;
        $rescheduleMessage = '';

        if($type == 1) {
            $defaultReschedulePolicy = @BihConfigs::where('key', Key::DEFAULT_APPOINTMENT_RESCHEDULE_POLICY)->first()->value ?? '{}';
        }else{
            $defaultReschedulePolicy = @BihConfigs::where('key', Key::DEFAULT_MCU_RESCHEDULE_POLICY)->first()->value ?? '{}';
        }

        $defaultReschedulePolicy = json_decode($defaultReschedulePolicy, true);
        $rescheduleConfig = [
            'reschedule_policy' => @$defaultReschedulePolicy['reschedule_policy'],
            'reschedule_period' => @$defaultReschedulePolicy['reschedule_period'],
            'reschedule_period_with_promocode' => @$defaultReschedulePolicy['reschedule_period_with_promocode'],
            'reschedule_to_visit_period' => @$defaultReschedulePolicy['reschedule_to_visit_period'],
        ];

        if($type == 2 && $packageSummaryDetail->package->reschedule_policy) {
            $rescheduleConfig = [
                'reschedule_policy' => $packageSummaryDetail->package->reschedule_policy,
                'reschedule_period' => $packageSummaryDetail->package->reschedule_period,
                'reschedule_period_with_promocode' => $packageSummaryDetail->package->reschedule_period_with_promocode,
                'reschedule_to_visit_period' => $packageSummaryDetail->package->reschedule_to_visit_period,
            ];
        }


        if(@$rescheduleConfig['reschedule_policy'] == Reschedule::RESCHEDULE_POLICY_NOT_ALLOWED) {
            $isRescheduleable = false;
            $rescheduleMessage = $type == 1 ? '' : 'This package cannot be rescheduled';
        }

        if($type == 1) {
            $visitDateTime = Carbon::parse($appointmentPatientSummary->book_date.' '.$appointmentPatientSummary->book_time_from);
        }else{
            $visitDateTime = Carbon::parse($packageSummaryDetail->visit_date.' '.$packageSummaryDetail->visit_time_from);
        }
        $hoursToVisitDate = $visitDateTime->subHours(@$rescheduleConfig['reschedule_to_visit_period'] ?? 48);

        $now = Carbon::now();
        if ($now->greaterThan($hoursToVisitDate) && @$rescheduleConfig['reschedule_policy'] != Reschedule::RESCHEDULE_POLICY_NOT_ALLOWED) {
            $isRescheduleable = false;
            $rescheduleMessage = 'Reschedule is not allowed within '.(@$rescheduleConfig['reschedule_to_visit_period'] ?? 48).' hours before the appointment';
        }

        if($type == 1) {
            $initialRescheduleId = @$appointmentPatientSummary->parent_initial_reschedule_id ?? $appointmentPatientSummary->id;
            $initialAppointment = AppointmentPatientSummary::withTrashed()->find($initialRescheduleId);
        } else {
            $initialRescheduleId = @$packageSummaryDetail->parent_initial_reschedule_id ?? $packageSummaryDetail->id;
            $initialAppointment = PackageSummaryDetail::withTrashed()->find($initialRescheduleId);
        }
        $initialCreateDateTime = Carbon::parse($initialAppointment->created_at);

        if(@$rescheduleConfig['reschedule_policy'] == Reschedule::RESCHEDULE_POLICY_SPECIFIC_PERIOD) {
            $reschedulePeriod = $rescheduleConfig['reschedule_period'];

            if($type == 2) {
                $isWithPromocode = $packageSummaryDetail->packageSummary->promocode_id != null;
                if($isWithPromocode) {
                    $reschedulePeriod = $rescheduleConfig['reschedule_period_with_promocode'];
                }
            }

            $limitRescheduleDays = $initialCreateDateTime->copy()->addDays(@$reschedulePeriod ?? 0);
            if($now->greaterThan($limitRescheduleDays)) {
                $isRescheduleable = false;
                $rescheduleMessage = 'Based on our policy, this package cannot be rescheduled since it has exceeded '.$reschedulePeriod.' day(s) since purchase date ('.$initialCreateDateTime->format('d M Y').')';
            }
        }else if(@$rescheduleConfig['reschedule_policy'] == Reschedule::RESCHEDULE_POLICY_INDEFINITELY) {
            $limitRescheduleDays = Carbon::now()->addDays(365);
        }

        return [
            'isRescheduleable' => $isRescheduleable,
            'rescheduleMessage' => $rescheduleMessage,
        ];
    }
}
