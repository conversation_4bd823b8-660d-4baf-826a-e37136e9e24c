<?php

namespace App\Http\Controllers\Api\Queue;

use App\Http\Controllers\Controller;
use App\Models\LandingPage\PublicUser;
use App\Models\Patient;
use App\Services\Notification\FirebaseCloudMessage\QueueCalledService;
use App\Services\Notification\FirebaseCloudMessage\QueueService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Mockery\Exception;

class QueueController extends Controller
{
    public function send_notification_called(Request $request){
        try{
            $patient = Patient::whereSimrsPatientId($request->patient_id)->first();

            if(!$patient){
                return response()->json([
                    'status' => false,
                    'message' => 'patient not found'
                ], 404);
            }

            if($patient->public_user_id !== null){
                $public_user = DB::table('public_users as p')
                    ->select('p.*')
                    ->where('p.id', $patient->public_user_id)
                    ->first();

            }else{
                $parent = Patient::whereId($patient->parent_id)->first();
                $public_user = PublicUser::whereId($parent->public_user_id)->first();
            }

            $queueCalled = (new QueueService([
                'user_fcm_token' => $public_user->mobile_fcm_token,
                'patient_name' => $patient->fullname,
                'appointment_id' => $request->appointment_id,
                'display_name' => $request->display_name,
                'menu_name' => $request->menu_name,
                'login_device_type' =>  $public_user->login_device_type,
                'notification_type' => 1
            ]))->call();
            if($queueCalled->status() != 200){

                if($queueCalled->status() == 422){
                    info(json_encode($queueCalled->data()));
                }
                throw new Exception($queueCalled->message());
            }
            throw_if($queueCalled->status() != 200, $queueCalled->message());

            $queue = $queueCalled->data();

            return response()->json([
                'status' => true,
                'message' => 'Notification successfully',
                // 'data' => $queue
            ]);

        }catch(\Throwable $th){
            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            return response()->json([
                'status' => false,
                'message' => $th->getMessage()
            ], 500);
        }
    }
    public function send_notification_transfer(Request $request){
        try{

            $patient = Patient::whereSimrsPatientId($request->patient_id)->first();

            if(!$patient){
                return response()->json([
                    'status' => false,
                    'message' => 'patient not found'
                ], 404);
            }
            if($patient->public_user_id !== null){
                $public_user = DB::table('public_users as p')
                    ->select('p.*')
                    ->where('p.id', $patient->public_user_id)
                    ->first();

            }else{
                $parent = Patient::whereId($patient->parent_id)->first();
                $public_user = PublicUser::whereId($parent->public_user_id)->first();
            }
            $queueCalled = (new QueueService([
                'user_fcm_token' => $public_user->mobile_fcm_token,
                'patient_name' => $patient->fullname,
                'appointment_id' => $request->appointment_id,
                'display_name_prev' => $request->display_name_prev,
                'display_name_next' => $request->display_name_next,
                'menu_name_prev' => $request->menu_name_prev,
                'menu_name_next' => $request->menu_name_next,
                'login_device_type' =>  $public_user->login_device_type,
                'notification_type' => 2
            ]))->call();

            throw_if($queueCalled->status() != 200, $queueCalled->message());

            $queue = $queueCalled->data();

            return response()->json([
                'status' => true,
                'message' => 'Notification successfully',
                // 'data' => $queue
            ]);
        }catch(\Throwable $th){
            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            return response()->json([
                'status' => false,
                'message' => $th->getMessage()
            ], 500);
        }
    }
}
