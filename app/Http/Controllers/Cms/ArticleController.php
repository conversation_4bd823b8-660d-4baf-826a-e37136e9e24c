<?php

namespace App\Http\Controllers\Cms;

use App\Http\Controllers\Controller;
use App\Models\Cms\Career;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Models\Cms\Article;
use App\Models\Cms\ArticleCategory;
use App\Http\Requests\ArticleStoreRequest;
use App\Http\Requests\ArticleUpdateRequest;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\File;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Session;
use App\Models\Doctor;
use App\Services\GCS\GoogleCloudService;
use App\Services\AuditTrail\AuditTrailService;

class ArticleController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {

            $model = Article::join('article_categories', 'articles.article_category_id', '=', 'article_categories.id')
                ->select('articles.*', 'article_categories.name as category')->orderBy('articles.id', 'desc');

            $req = $request->search['value'];

            return DataTables::eloquent($model)
                ->addIndexColumn()
                ->filter(function ($query) use ($req) {
                    if ($req) {
                        $query->where('articles.title', 'like', '%' . $req . '%')
                            ->orWhere('articles.content', 'like', '%' . $req . '%')
                            ->orWhere('article_categories.name', 'like', '%' . $req . '%')
                            ->orWhere('articles.is_published', 'like', '%' . $req . '%')
                            ->orWhere('articles.is_highlighted', 'like', '%' . $req . '%');
                    }
                })
                ->addColumn('type', function ($data) {
                    return $data->type == 'blog' ? 'Blog' : 'News';
                })
                ->addColumn('published_at', function ($data) {
                    return $data->published_at ? date('Y-m-d', strtotime($data->published_at)) : '';
                })
                ->addColumn('is_published', function ($data) {
                    if ($data->is_published) {
                        $res = '<span class="badge bg-label-success">Yes</span>';
                    } else {
                        $res = '<span class="badge bg-label-secondary">No</span>';
                    }
                    return $res;
                })
                ->addColumn('is_highlighted', function ($data) {
                    if ($data->is_highlighted) {
                        $res = '<span class="badge bg-label-success">Yes</span>';
                    } else {
                        $res = '<span class="badge bg-label-secondary">No</span>';
                    }
                    return $res;
                })
                ->addColumn('action', function ($data) {
                    $button = '<a href="' . route('cms.articles.edit', $data->uuid) . '" class="edit btn btn-primary btn-sm"><span class="fas fa-pencil-alt"></span></a>';
                    $button .= '&nbsp;&nbsp;';
                    $button .= '<button type="button" name="delete" data-id="' . $data->uuid . '" class="delete btn btn-danger btn-sm"><span class="fas fa-trash"></span></button>';
                    return $button;
                })
                ->rawColumns(['action', 'is_published', 'is_highlighted'])
                ->toJson();
        }

        return view('cms.articles.index');
    }

    public function create()
    {
        $articleCategories = ArticleCategory::all();
        $doctors = Doctor::all();
        return view('cms.articles.create', compact('doctors', 'articleCategories'));
    }

    public function store(Request $request)
    {

        try {

            if ($request->is_published != 'on' && $request->is_highlighted) {
                alert()->error('error', 'Is Published should be ‘Yes’ if ‘Is Highlighted’ is ‘Yes’');
                return redirect()->back();
            }

            $article_exsiting = Article::where('title', $request->title)->first();
            if ($article_exsiting) {
                alert()->error('error', 'Article already exists');
                return redirect()->route('cms.articles');
            }

            $slug               = Str::slug($request->title);
            Article::withTrashed()->where('slug', $slug)->update([
                'slug' => $slug . '-' . Str::uuid()
            ]);

            $article                = new Article();
            $article->uuid          = Str::uuid();
            $article->title         = $request->title;
            $article->slug          = $slug;
            $article->content       = $request->content;
            $article->is_published  = ($request->is_published == 'on') ? true : false;

            if ($request->is_published) {
                $article->published_at = Carbon::now();
            }

            $article->is_highlighted = ($request->is_highlighted == 'on') ? true : false;
            $article->user_id = auth()->user()->id;
            $article->type = $request->type;
            $article->article_category_id = $request->article_category_id;
            $article->time_to_read = $request->time_to_read;

            if ($request->hasFile('image')) {
                $gcs = new GoogleCloudService();
                $upload = $gcs->uploadFile($request->file('image'), 'articles', $article->uuid);
                $article->image = $upload;
            }

            // SEO
            $article->seo_description = $request->seo_description;

            $article->seo_title = $request->is_seo_use_title ? $request->title : $request->seo_title;

            // $article->seo_image = $request->is_seo_use_image ? $article->image : $request->seo_image;

            if ($request->is_seo_use_image) {
                $article->seo_image = $article->image;
            } else if ($request->hasFile('seo_image')) {
                $gcs = new GoogleCloudService();
                $upload = $gcs->uploadFile($request->file('seo_image'), 'articles', 'seo_image_' . $article->uuid);
                $article->seo_image = $upload;
            }


            $article->save();

            if ($request->is_highlighted) {
                $articles = Article::where('uuid', '!=', $article->uuid)
                    ->where('type', $article->type)
                    ->where('is_highlighted', true)
                    ->get();

                foreach ($articles as $_article) {
                    $_article->is_highlighted = false;
                    $_article->save();
                }
            }

            if ($request->doctors_id) {
                $article->doctors()->attach($request->doctors_id);
            }

            alert()->success('success', 'Article created successfully');

            (new AuditTrailService)->create(
                auth()->user()->name,
                'create',
                request()->path(),
                request()->ip(),
                'cms',
                json_encode(request()->all()),
                json_encode($article),
                null
            );

            return redirect()->route('cms.articles');

        }catch (\Throwable $th){

            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            alert()->error('error', $th->getMessage());

            return back()->withInput();

        }

    }

    public function edit($uuid)
    {
        $article = Article::where('uuid', $uuid)->first();

        if (!$article) {
            alert()->error('error', 'Article not found');
            return redirect()->route('cms.articles');
        }

        $articleCategories = ArticleCategory::all();
        $doctors = Doctor::all();


        if ($article->image) {
            $gcs = new GoogleCloudService();
            $article->image = $gcs->getStaticUrl($article->image);
        }

        if($article->seo_image) {
            $gcs = new GoogleCloudService();
            $article->seo_image = $gcs->getStaticUrl($article->seo_image);
        }

        return view('cms.articles.edit', compact('article', 'doctors', 'articleCategories'));
    }

    public function update($uuid, Request $request)
    {

        $content = request('content') ? urldecode(base64_decode(request('content'))) : null;

        try {

            $article = Article::where('uuid', $uuid)->first();

            if (!$article) {
                alert()->error('error', 'Article not found');
                return redirect()->route('cms.articles');
            }

            if ($request->is_published != 'on' && $request->is_highlighted) {
                alert()->error('error', 'Is Published should be ‘Yes’ if ‘Is Highlighted’ is ‘Yes’');
                return redirect()->back();
            }

            $article->title = $request->title;
            $article->slug = Str::slug($request->title);
            $article->content = $content;

            $article->is_published = ($request->is_published == 'on') ? true : false;

            if ($request->is_published) {
                $article->published_at = Carbon::now();
            }

            $article->is_highlighted = ($request->is_highlighted == 'on') ? true : false;
            $article->user_id = auth()->user()->id;

            $article->type = $request->type;
            $article->article_category_id = $request->article_category_id;
            $article->time_to_read = $request->time_to_read;

            if ($request->hasFile('image')) {

                $gcs = new GoogleCloudService();

                if ($article->image) {
                    $gcs->deleteFile($article->image);
                }

                $upload = $gcs->uploadFile($request->file('image'), 'articles', $article->uuid);
                $article->image = $upload;
            }

            $article->save();

            if ($request->is_highlighted) {
                $articles = Article::where('uuid', '!=', $uuid)
                    ->where('type', $article->type)
                    ->where('is_highlighted', true)
                    ->get();

                foreach ($articles as $_article) {
                    $_article->is_highlighted = false;
                    $_article->save();
                }
            }

            $article->doctors()->sync($request->doctors_id);

            alert()->success('success', 'Article updated successfully');

            (new AuditTrailService)->create(
                auth()->user()->name,
                'edit',
                request()->path(),
                request()->ip(),
                'cms',
                json_encode(request()->all()),
                json_encode($article),
                null
            );

            return redirect()->route('cms.articles');

        }catch (\Throwable $th){

            report($th);

            Log::error(self::class . __FUNCTION__, [
                'Message ' => $th->getMessage(),
                'On file ' => $th->getFile(),
                'On line ' => $th->getLine()
            ]);

            alert()->error('error', $th->getMessage());

            return back()->withInput();

        }

    }

    public function destroy($uuid)
    {

        $article = Article::where('uuid', $uuid)->first();

        if (!$article) {
            alert()->error('error', 'Article not found');
            return response()->json();
        }

        if ($article->is_highlighted) {
            alert()->error('error', 'Article is highlighted cannot be deleted');
            return response()->json();
        }

        $gcs = new GoogleCloudService();
        if ($article->image) {
            $gcs->deleteFile($article->image);
        }

        $article->doctors()->detach();
        $article->delete();

        alert()->success('success', 'Article deleted successfully');

        (new AuditTrailService)->create(
            auth()->user()->name,
            'delete',
            request()->path(),
            request()->ip(),
            'cms',
            json_encode(request()->all()),
            json_encode($article),
            null
        );
        return response()->json();
    }
}
