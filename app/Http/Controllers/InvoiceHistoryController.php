<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Traits\ApiResponser;
use App\Traits\AuthToken;
use App\Repositories\Patient\PatientRepository;
use Illuminate\Support\Facades\Auth;

class InvoiceHistoryController extends Controller
{
    use ApiResponser, AuthToken;

    public function InvoiceHistory(Request $request)
    {
        try {
            $publicUserId = $request->input('user_id');
            $patientRepo = new PatientRepository();
            $mainPatient = $patientRepo->getSelfPatient($publicUserId);
            $childPatients = $patientRepo->getOtherPatients($mainPatient->id);

            $allPatients = collect([$mainPatient])->merge($childPatients);
            $invoiceHistories = [];

            foreach ($allPatients as $patient) {
                foreach ($patient->patientInvoiceSendLogs as $invoice) {
                    $invoiceHistories[] = [
                        'name' => $patient->fullname,
                        'date' => $invoice->created_at->format('Y-m-d'),
                        'title' => $invoice->filename,
                        'payment_receipt_url' => $invoice->url,
                    ];
                }
            }
            return $this->success($invoiceHistories, 'successfully');
        } catch (\Throwable $th) {
            return $this->error($th->getMessage());
        }
    }
}
