<aside id="layout-menu" class="layout-menu menu-vertical menu bg-menu-theme">
    <div class="app-brand demo">
        <a href="" class="app-brand-link">
            <img src="{{ asset('/assets/svg/bih-logo.png') }}" alt="" style="width: 180px">
            {{-- <span class="app-brand-logo demo">

                <svg width="32" height="22" viewBox="0 0 32 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M0.00172773 0V6.85398C0.00172773 6.85398 -0.133178 9.01207 1.98092 10.8388L13.6912 21.9964L19.7809 21.9181L18.8042 9.88248L16.4951 7.17289L9.23799 0H0.00172773Z"
                        fill="#0D4D8B" />
                    <path opacity="0.06" fill-rule="evenodd" clip-rule="evenodd"
                        d="M7.69824 16.4364L12.5199 3.23696L16.5541 7.25596L7.69824 16.4364Z" fill="#161616" />
                    <path opacity="0.06" fill-rule="evenodd" clip-rule="evenodd"
                        d="M8.07751 15.9175L13.9419 4.63989L16.5849 7.28475L8.07751 15.9175Z" fill="#161616" />
                    <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M7.77295 16.3566L23.6563 0H32V6.88383C32 6.88383 31.8262 9.17836 30.6591 10.4057L19.7824 22H13.6938L7.77295 16.3566Z"
                        fill="#0D4D8B" />
                </svg>
            </span> --}}
            {{-- <span class="app-brand-text demo menu-text fw-bold">CMS</span> --}}
        </a>

        {{-- <a href="javascript:void(0);" class="layout-menu-toggle menu-link text-large ms-auto">
            <i class="ti menu-toggle-icon d-none d-xl-block ti-sm align-middle"></i>
            <i class="ti ti-x d-block d-xl-none ti-sm align-middle"></i>
        </a> --}}
    </div>

    <div class="menu-inner-shadow"></div>

    <ul class="menu-inner py-1">

        <!-- Page -->


        @if (in_array('/cms/dashboard', session('role_navigations')))
            <li class="menu-item">
                <a href="{{ route('cms.dashboard') }}" class="menu-link">
                    <i class="menu-icon tf-icons ti ti-dashboard"></i>
                    <div data-i18n="Page 1">Dashboard</div>
                </a>
            </li>
        @endif


        @if (in_array('/cms/roles', session('role_navigations')) ||
                in_array('/cms/users', session('role_navigations')) ||
                in_array('/cms/public-users', session('role_navigations')))

            <li class="menu-item">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <i class="menu-icon tf-icons ti ti-users"></i>
                    <div data-i18n="ManagementUsers">Management Users</div>
                </a>
                <ul class="menu-sub">

                    @if (in_array('/cms/public-users', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.public-users') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-app-window"></i>
                                <div data-i18n="Page 2">Public Users</div>
                            </a>
                        </li>
                    @endif

                    @if (in_array('/cms/users', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.users') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-app-window"></i>
                                <div data-i18n="Page 2">CMS Users</div>
                            </a>
                        </li>
                    @endif

                    @if (in_array('/cms/roles', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.roles') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-smart-home"></i>
                                <div data-i18n="Page 1">CMS Role Settings</div>
                            </a>
                        </li>
                    @endif




                </ul>
            </li>
        @endif

        <li class="menu-header small text-uppercase">
            <span class="menu-header-text" data-i18n="CONTENT MANAGEMENT">CONTENT MANAGEMENT</span>
        </li>

        @if (in_array('/cms/package-types', session('role_navigations')) ||
                in_array('/cms/package-categories', session('role_navigations')) ||
                in_array('/cms/packages', session('role_navigations')))

            <li class="menu-item">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <i class="menu-icon tf-icons ti ti-first-aid-kit"></i>
                    <div data-i18n="Package">Medical Packages</div>
                </a>
                <ul class="menu-sub">

                    @if (in_array('/cms/packages', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.packages') }}" class="menu-link">
                                <div data-i18n="Packages">All Packages</div>
                            </a>
                        </li>
                    @endif


                    @if (in_array('/cms/package-types', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.package-types') }}" class="menu-link">
                                <div data-i18n="PackageTypes">Types Settings</div>
                            </a>
                        </li>
                    @endif

                    @if (in_array('/cms/package-categories', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.package-categories') }}" class="menu-link">
                                <div data-i18n="PackageCategories ">Category Settings</div>
                            </a>
                        </li>
                    @endif


                </ul>
            </li>
        @endif

        @if (in_array('/cms/promos', session('role_navigations')) || in_array('/cms/promo-codes', session('role_navigations')))
            <li class="menu-item">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <i class="menu-icon tf-icons ti ti-speakerphone"></i>
                    <div data-i18n="Promos">Promotions</div>
                </a>
                <ul class="menu-sub">

                    @if (in_array('/cms/promo-codes', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.promo-codes') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-smart-home"></i>
                                <div data-i18n="Page 1">Promo Code</div>
                            </a>
                        </li>
                    @endif

                    @if (in_array('/cms/promos', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.promos') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-app-window"></i>
                                <div data-i18n="Promos&Banners">Promo Banner</div>
                            </a>
                        </li>
                    @endif

                </ul>
            </li>
        @endif


        @if (in_array('/cms/articles', session('role_navigations')) ||
                in_array('/cms/article-categories', session('role_navigations')))
            <li class="menu-item">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <i class="menu-icon tf-icons ti ti-news"></i>
                    <div data-i18n="BlogsNews">Blogs & News </div>
                </a>
                <ul class="menu-sub">

                    @if (in_array('/cms/articles', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.articles') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-article"></i>
                                <div data-i18n="Article">All Blogs & News</div>
                            </a>
                        </li>
                    @endif

                    @if (in_array('/cms/article-categories', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.article-categories') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-article"></i>
                                <div data-i18n="ArticleCategories">Category Settings</div>
                            </a>
                        </li>
                    @endif



                </ul>
            </li>
        @endif

        @if (in_array('/cms/doctor-profiles', session('role_navigations')))
            <li class="menu-item">
                <a href="{{ route('cms.doctor-profiles') }}" class="menu-link">
                    <i class="menu-icon tf-icons ti ti-user"></i>
                    <div data-i18n="DOctor-Profile">Doctor Profile</div>
                </a>


            </li>
        @endif



        @if (in_array('/cms/careers', session('role_navigations')))
            <li class="menu-item">
                <a href="{{ route('cms.careers') }}" class="menu-link">
                    <i class="menu-icon tf-icons ti ti-tie"></i>
                    <div data-i18n="Career">Careers</div>
                </a>

                {{-- <a href="{{ route('cms.careers') }}" class="menu-link menu-toggle">
                    <i class="menu-icon tf-icons ti ti-forms"></i>
                    <div data-i18n="Career">Careers</div>
                </a> --}}
                {{-- <ul class="menu-sub">

                    @if (in_array('/cms/careers', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.careers') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-app-window"></i>
                                <div data-i18n="Career">Career Masters</div>
                            </a>
                        </li>
                    @endif

                </ul> --}}
            </li>
        @endif

        @if (in_array('/cms/trainings', session('role_navigations')))
        <li class="menu-item">
            <a href="{{ route('cms.trainings.index') }}" class="menu-link">
                <i class="menu-icon tf-icons ti ti-news"></i>
                <div data-i18n="Career">Training</div>
            </a>

            {{-- <a href="{{ route('cms.careers') }}" class="menu-link menu-toggle">
                <i class="menu-icon tf-icons ti ti-forms"></i>
                <div data-i18n="Career">Careers</div>
            </a> --}}
            {{-- <ul class="menu-sub">

                @if (in_array('/cms/careers', session('role_navigations')))
                    <li class="menu-item">
                        <a href="{{ route('cms.careers') }}" class="menu-link">
                            <i class="menu-icon tf-icons ti ti-app-window"></i>
                            <div data-i18n="Career">Career Masters</div>
                        </a>
                    </li>
                @endif

            </ul> --}}
        </li>
        @endif


    @if (in_array('/cms/announcements', session('role_navigations')))
            <li class="menu-item">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <i class="menu-icon tf-icons ti ti-info-circle"></i>
                    <div data-i18n="Information">Informations</div>
                </a>
                <ul class="menu-sub">

                    @if (in_array('/cms/announcements', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.announcements') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-app-window"></i>
                                <div data-i18n="Announcement">Announcement</div>
                            </a>
                        </li>
                    @endif

                </ul>
            </li>
        @endif

        @if (in_array('/cms/reports/users', session('role_navigations')))
            <li class="menu-item">
                <a href="{{ route('cms.report-users') }}" class="menu-link">
                    <i class="menu-icon tf-icons ti ti-receipt"></i>
                    <div data-i18n="Report">Reports</div>
                </a>


            </li>
            @endif
        @if (in_array('/cms/bookings/patients', session('role_navigations')) ||
                in_array('/cms/bookings/appointments', session('role_navigations')) ||
                in_array('/cms/bookings/medical-package', session('role_navigations')) ||
                in_array('/cms/bookings/send-mcu-report', session('role_navigations'))||
                in_array('/cms/bookings/send-patient-invoice', session('role_navigations')))
            <li class="menu-item">
                <a href="javascript:void(0);" class="menu-link menu-toggle">
                    <i class="menu-icon tf-icons ti ti-speakerphone"></i>
                    <div data-i18n="Bookings">Bookings</div>
                </a>
                <ul class="menu-sub">
                    @if (in_array('/cms/bookings/patients', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.bookings.patients') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-smart-home"></i>
                                <div data-i18n="Patients">Patients</div>
                            </a>
                        </li>
                    @endif

                    @if (in_array('/cms/bookings/appointments', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.bookings.appointments') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-app-window"></i>
                                <div data-i18n="Appointment">Appointment</div>
                            </a>
                        </li>
                    @endif

                    @if (in_array('/cms/bookings/medical-package', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.bookings.medical-packages') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-app-window"></i>
                                <div data-i18n="MedPac">Medical Package</div>
                            </a>
                        </li>
                    @endif

                    @if (in_array('/cms/bookings/send-mcu-report', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.bookings.send-mcu-report') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-app-window"></i>
                                <div data-i18n="MedPac">Send MCU Report</div>
                            </a>
                        </li>
                    @endif
                    @if (in_array('/cms/bookings/send-patient-invoice', session('role_navigations')))
                        <li class="menu-item">
                            <a href="{{ route('cms.bookings.send-patient-invoice') }}" class="menu-link">
                                <i class="menu-icon tf-icons ti ti-app-window"></i>
                                <div data-i18n="MedPac">Send Patient Invoice</div>
                            </a>
                        </li>
                    @endif

                </ul>
            </li>
        @endif

        @if (in_array('/cms/general-payments', session('role_navigations')))
            <li class="menu-item">
                <a href="{{ route('cms.general-payments') }}" class="menu-link">
                    <i class="menu-icon tf-icons ti ti-coin"></i>
                    <div data-i18n="GeneralPayment">General Payment</div>
                </a>
            </li>
        @endif

        @if (in_array('/cms/inquiry', session('role_navigations')))
            <li class="menu-item">
                <a href="{{ route('cms.inquiry') }}" class="menu-link">
                    <i class="menu-icon tf-icons ti ti-files"></i>
                    <div data-i18n="GeneralPayment">Inquiry Requests</div>
                </a>
            </li>
        @endif

        @if (in_array('/cms/public-holiday', session('role_navigations')))
            <li class="menu-item">
                <a href="{{ route('cms.public-holiday') }}" class="menu-link">
                    <i class="menu-icon tf-icons ti ti-files"></i>
                    <div data-i18n="GeneralPayment">Public Holiday</div>
                </a>
            </li>
        @endif




    </ul>
</aside>
