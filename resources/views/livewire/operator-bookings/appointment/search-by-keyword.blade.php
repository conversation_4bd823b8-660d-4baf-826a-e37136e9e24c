<div>
    <div class="flex flex-col gap-2">
        <span class="text-2xl md:text-3xl font-semibold">Search</span>
        <span class="text-sm md:text-base font-light text-[#667085]">Search for a specific doctor.</span>
    </div>
    <div class="flex flex-col lg:w-2/3 mt-4 md:mt-8 relative">
        <div class="flex lg:flex-row flex-col-reverse justify-center gap-4 w-full">
            <div class="w-full items-center rounded-2xl border border-gray-200 py-2 pl-6 pr-2 flex flex-row justify-between bg-white focus-within:border-blue-500 focus-within:ring focus-within:ring-blue-200">
                <input
                    type="text"
                    class="w-full outline-none bg-transparent text-xs md:text-base font-light text-black"
                    placeholder="Type doctor name or use filter"
                    name="keyword"
                    wire:model.live.debounce.500ms="keyword"
                    id="searchInput"
                />
                <div wire:loading.attr="disabled" wire:click="onHandleSearching" class="flex rounded-xl bg-blue-900 py-2 px-3 text-white text-sm gap-2 items-center hover:cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M9 3.5C5.96243 3.5 3.5 5.96243 3.5 9C3.5 12.0376 5.96243 14.5 9 14.5C10.519 14.5 11.893 13.8852 12.8891 12.8891C13.8852 11.893 14.5 10.519 14.5 9C14.5 5.96243 12.0376 3.5 9 3.5ZM2 9C2 5.13401 5.13401 2 9 2C12.866 2 16 5.13401 16 9C16 10.6625 15.4197 12.1906 14.4517 13.3911L17.7803 16.7197C18.0732 17.0126 18.0732 17.4874 17.7803 17.7803C17.4874 18.0732 17.0126 18.0732 16.7197 17.7803L13.3911 14.4517C12.1906 15.4197 10.6625 16 9 16C5.13401 16 2 12.866 2 9Z" fill="white"/>
                    </svg>
                    <span class="text-sm font-light hidden md:block">Search</span>
                </div>
            </div>
            @if($this->isFilter)
                <div wire:click="onHandleFilter" class="items-center rounded-2xl border
                        p-4 flex flex-row justify-between
                        bg-[#0D4D8B] gap-2 hover:cursor-pointer">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M15.625 10.625L16.875 10.625C17.2202 10.625 17.5 10.3452 17.5 9.99999C17.5 9.65482 17.2202 9.375 16.875 9.375L15.625 9.37501C15.2798 9.37502 15 9.65484 15 10C15 10.3452 15.2798 10.625 15.625 10.625Z" fill="white"/>
                        <path d="M10 5C10 4.65482 10.2798 4.375 10.625 4.375L16.875 4.37501C17.2202 4.37501 17.5 4.65484 17.5 5.00001C17.5 5.34519 17.2202 5.62501 16.875 5.62501L10.625 5.625C10.2798 5.625 10 5.34518 10 5Z" fill="white"/>
                        <path d="M10 15C10 14.6548 10.2798 14.375 10.625 14.375L16.875 14.375C17.2202 14.375 17.5 14.6548 17.5 15C17.5 15.3452 17.2202 15.625 16.875 15.625L10.625 15.625C10.2798 15.625 10 15.3452 10 15Z" fill="white"/>
                        <path d="M3.12501 5.62501L4.37501 5.625C4.72018 5.625 5 5.34517 5 4.99999C5 4.65482 4.72017 4.375 4.37499 4.375L3.12499 4.37501C2.77982 4.37502 2.5 4.65484 2.5 5.00002C2.5 5.3452 2.77983 5.62502 3.12501 5.62501Z" fill="white"/>
                        <path d="M4.37501 15.625L3.12501 15.625C2.77983 15.625 2.5 15.3452 2.5 15C2.5 14.6548 2.77982 14.375 3.12499 14.375L4.37499 14.375C4.72017 14.375 5 14.6548 5 15C5 15.3452 4.72018 15.625 4.37501 15.625Z" fill="white"/>
                        <path d="M2.5 10C2.5 9.65482 2.77982 9.375 3.125 9.375H9.375C9.72018 9.375 10 9.65482 10 10C10 10.3452 9.72018 10.625 9.375 10.625H3.125C2.77982 10.625 2.5 10.3452 2.5 10Z" fill="white"/>
                        <path d="M7.5 3.125C6.46447 3.125 5.625 3.96447 5.625 5C5.625 6.03553 6.46447 6.875 7.5 6.875C8.53553 6.875 9.375 6.03553 9.375 5C9.375 3.96447 8.53553 3.125 7.5 3.125Z" fill="white"/>
                        <path d="M10.625 10C10.625 8.96447 11.4645 8.125 12.5 8.125C13.5355 8.125 14.375 8.96447 14.375 10C14.375 11.0355 13.5355 11.875 12.5 11.875C11.4645 11.875 10.625 11.0355 10.625 10Z" fill="white"/>
                        <path d="M7.5 13.125C6.46447 13.125 5.625 13.9645 5.625 15C5.625 16.0355 6.46447 16.875 7.5 16.875C8.53553 16.875 9.375 16.0355 9.375 15C9.375 13.9645 8.53553 13.125 7.5 13.125Z" fill="white"/>
                    </svg>
                    <span class="text-sm text-white">Filter</span>
                </div>
            @else
                <div wire:click="onHandleFilter" class="items-center rounded-2xl border
                        border-[#0D4D8B] p-4 flex flex-row justify-between
                        bg-white gap-2 hover:cursor-pointer" onclick="toggleVisibility()">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M15.625 10.625L16.875 10.625C17.2202 10.625 17.5 10.3452 17.5 9.99999C17.5 9.65482 17.2202 9.375 16.875 9.375L15.625 9.37501C15.2798 9.37502 15 9.65484 15 10C15 10.3452 15.2798 10.625 15.625 10.625Z" fill="#0D4D8B"/>
                        <path d="M10 5C10 4.65482 10.2798 4.375 10.625 4.375L16.875 4.37501C17.2202 4.37501 17.5 4.65484 17.5 5.00001C17.5 5.34519 17.2202 5.62501 16.875 5.62501L10.625 5.625C10.2798 5.625 10 5.34518 10 5Z" fill="#0D4D8B"/>
                        <path d="M10 15C10 14.6548 10.2798 14.375 10.625 14.375L16.875 14.375C17.2202 14.375 17.5 14.6548 17.5 15C17.5 15.3452 17.2202 15.625 16.875 15.625L10.625 15.625C10.2798 15.625 10 15.3452 10 15Z" fill="#0D4D8B"/>
                        <path d="M3.12501 5.62501L4.37501 5.625C4.72018 5.625 5 5.34517 5 4.99999C5 4.65482 4.72017 4.375 4.37499 4.375L3.12499 4.37501C2.77982 4.37502 2.5 4.65484 2.5 5.00002C2.5 5.3452 2.77983 5.62502 3.12501 5.62501Z" fill="#0D4D8B"/>
                        <path d="M4.37501 15.625L3.12501 15.625C2.77983 15.625 2.5 15.3452 2.5 15C2.5 14.6548 2.77982 14.375 3.12499 14.375L4.37499 14.375C4.72017 14.375 5 14.6548 5 15C5 15.3452 4.72018 15.625 4.37501 15.625Z" fill="#0D4D8B"/>
                        <path d="M2.5 10C2.5 9.65482 2.77982 9.375 3.125 9.375H9.375C9.72018 9.375 10 9.65482 10 10C10 10.3452 9.72018 10.625 9.375 10.625H3.125C2.77982 10.625 2.5 10.3452 2.5 10Z" fill="#0D4D8B"/>
                        <path d="M7.5 3.125C6.46447 3.125 5.625 3.96447 5.625 5C5.625 6.03553 6.46447 6.875 7.5 6.875C8.53553 6.875 9.375 6.03553 9.375 5C9.375 3.96447 8.53553 3.125 7.5 3.125Z" fill="#0D4D8B"/>
                        <path d="M10.625 10C10.625 8.96447 11.4645 8.125 12.5 8.125C13.5355 8.125 14.375 8.96447 14.375 10C14.375 11.0355 13.5355 11.875 12.5 11.875C11.4645 11.875 10.625 11.0355 10.625 10Z" fill="#0D4D8B"/>
                        <path d="M7.5 13.125C6.46447 13.125 5.625 13.9645 5.625 15C5.625 16.0355 6.46447 16.875 7.5 16.875C8.53553 16.875 9.375 16.0355 9.375 15C9.375 13.9645 8.53553 13.125 7.5 13.125Z" fill="#0D4D8B"/>
                    </svg>
                    <span class="text-sm text-[#0D4D8B]">Filter</span>
                </div>
            @endif
        </div>
        <div class="absolute mt-16 bg-white w-full rounded-xl shadow-xl p-6 {{ $isFilter ? '' : 'hidden' }}">
            <livewire:operator-bookings.appointment.component.filter-modal :hide-from-inputs="!featureFlag('show_doctor_filter_visit_tele') ? [5] : []"/>
        </div>
    </div>

    <div class="mt-8" wire:loading.remove wire:target="onHandleSearching">
        <livewire:operator-bookings.appointment.list-doctor specialtyIds="{!! json_encode([])  !!}"/>
    </div>
    <div class="rounded-3xl p-6" wire:loading>
        <span class="text-3xl">Loading..</span>
    </div>
</div>

@push('script')
    <script>
        function toggleVisibility() {
            var filterComponent = document.getElementById('filterComponent');

            // Toggle the visibility
            if (filterComponent.style.display === 'none' || filterComponent.style.display === '') {
                filterComponent.style.display = 'block';
            } else {
                filterComponent.style.display = 'none';
            }
        }
    </script>
@endpush
