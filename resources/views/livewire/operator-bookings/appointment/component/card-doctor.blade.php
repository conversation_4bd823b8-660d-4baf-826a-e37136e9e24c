<div class="flex flex-col p-4 rounded-3xl shadow-md gap-4 bg-white justify-between">
    <div class="flex flex-row gap-4">
        <div class="h-20 w-1/4">
            @if(!$doctor->image)
                <img class="rounded-full object-cover" src="{{ asset_gcs('public/assets/icon/318e26c9-a4f1-4301-be62-8edf42acaf4e.svg') }}" alt="Bali International Hospital">
            @else
                <img class="rounded-full object-cover" src="{{ asset_gcs($doctor->image) }}" alt="Bali International Hospital">
            @endif
        </div>
        <div class="flex flex-col gap-[2px] w-3/4">
            @if(featureFlag('show_doctor_filter_visit_tele'))
                <div class="flex flex-wrap gap-1">
                    <span class="text-xs font-medium p-[2px] px-2 bg-[#F2F4F7] rounded-2xl text-[#475467] {{ $doctor->is_available_appointment ? '' : 'hidden' }}">Hospital Visit</span>
                    <span class="text-xs font-medium p-[2px] px-2 bg-[#F2F4F7] rounded-2xl text-[#475467] {{ $doctor->is_available_teleconsultation ? '' : 'hidden' }}">Teleconsultation</span>
                </div>
            @endif
            <span class="text-base font-semibold">{{ @$doctor->name ?? 'Dr. Dendi Ikhlas' }}</span>
            <span class="text-gray-400 font-medium text-sm">{{ @$doctor->specialty->group_name_en ?? '-' }}</span>
            {{-- @if($doctor->experience_year)
                <span class="flex gap-1 text-sm items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M11 4V3C11 1.89543 10.1046 1 9 1H7C5.89543 1 5 1.89543 5 3V4H4C2.89543 4 2 4.89543 2 6V9C2 10.1046 2.89543 11 4 11H12C13.1046 11 14 10.1046 14 9V6C14 4.89543 13.1046 4 12 4H11ZM9 2.5H7C6.72386 2.5 6.5 2.72386 6.5 3V4H9.5V3C9.5 2.72386 9.27614 2.5 9 2.5ZM9 9C9 9.55228 8.55228 10 8 10C7.44772 10 7 9.55228 7 9C7 8.44772 7.44772 8 8 8C8.55228 8 9 8.44772 9 9Z" fill="#667085"/>
                      <path d="M3 11.8291V11.9998C3 13.1044 3.89543 13.9998 5 13.9998H11C12.1046 13.9998 13 13.1044 13 11.9998V11.8291C12.6872 11.9397 12.3506 11.9998 12 11.9998H4C3.64936 11.9998 3.31278 11.9397 3 11.8291Z" fill="#667085"/>
                    </svg>
                    <span class="text-[#667085] text-sm font-light">{{ $doctor->experience_year }} years</span>
                </span>
            @endif --}}
        </div>
    </div>
    <div class="flex flex-col gap-4">
        <hr>
        <a href="{{route('cms.bookings.appointments.doctors.show',['uuid'=>$doctor->uuid,'name'=>Str::slug($doctor->name)])}}" class="bg-[#0D4D8B] rounded-lg py-3 px-6 text-center hover:cursor-pointer">
            <span class="text-white font-semibold text-base">View Profile</span>
        </a>
        <div>
{{--            @if($doctor->regularSchedules->count() > 0)--}}
{{--                <div wire:click="showSchedule({{ $doctor->id }})"--}}
{{--                     class="{{ $doctor->id == $scheduleId ? 'border-blue-300 bg-[#E5F2FF] border-t border-x rounded-x-lg rounded-t-lg border-b-white border-b-2' : 'border-[#0D4D8B] border rounded-lg' }} py-3 px-6 text-center flex flex-row justify-between items-center hover:cursor-pointer">--}}
{{--                    <div class="flex gap-2">--}}
{{--                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">--}}
{{--                            <path d="M5.25 12C5.25 11.5858 5.58579 11.25 6 11.25H6.01C6.42421 11.25 6.76 11.5858 6.76 12V12.01C6.76 12.4242 6.42421 12.76 6.01 12.76H6C5.58579 12.76 5.25 12.4242 5.25 12.01V12Z" fill="#0D4D8B"/>--}}
{{--                            <path d="M6 13.25C5.58579 13.25 5.25 13.5858 5.25 14V14.01C5.25 14.4242 5.58579 14.76 6 14.76H6.01C6.42421 14.76 6.76 14.4242 6.76 14.01V14C6.76 13.5858 6.42421 13.25 6.01 13.25H6Z" fill="#0D4D8B"/>--}}
{{--                            <path d="M7.25 12C7.25 11.5858 7.58579 11.25 8 11.25H8.01C8.42421 11.25 8.76 11.5858 8.76 12V12.01C8.76 12.4242 8.42421 12.76 8.01 12.76H8C7.58579 12.76 7.25 12.4242 7.25 12.01V12Z" fill="#0D4D8B"/>--}}
{{--                            <path d="M8 13.25C7.58579 13.25 7.25 13.5858 7.25 14V14.01C7.25 14.4242 7.58579 14.76 8 14.76H8.01C8.42421 14.76 8.76 14.4242 8.76 14.01V14C8.76 13.5858 8.42421 13.25 8.01 13.25H8Z" fill="#0D4D8B"/>--}}
{{--                            <path d="M9.25 10C9.25 9.58579 9.58579 9.25 10 9.25H10.01C10.4242 9.25 10.76 9.58579 10.76 10V10.01C10.76 10.4242 10.4242 10.76 10.01 10.76H10C9.58579 10.76 9.25 10.4242 9.25 10.01V10Z" fill="#0D4D8B"/>--}}
{{--                            <path d="M10 11.25C9.58579 11.25 9.25 11.5858 9.25 12V12.01C9.25 12.4242 9.58579 12.76 10 12.76H10.01C10.4242 12.76 10.76 12.4242 10.76 12.01V12C10.76 11.5858 10.4242 11.25 10.01 11.25H10Z" fill="#0D4D8B"/>--}}
{{--                            <path d="M9.25 14C9.25 13.5858 9.58579 13.25 10 13.25H10.01C10.4242 13.25 10.76 13.5858 10.76 14V14.01C10.76 14.4242 10.4242 14.76 10.01 14.76H10C9.58579 14.76 9.25 14.4242 9.25 14.01V14Z" fill="#0D4D8B"/>--}}
{{--                            <path d="M12 9.25C11.5858 9.25 11.25 9.58579 11.25 10V10.01C11.25 10.4242 11.5858 10.76 12 10.76H12.01C12.4242 10.76 12.76 10.4242 12.76 10.01V10C12.76 9.58579 12.4242 9.25 12.01 9.25H12Z" fill="#0D4D8B"/>--}}
{{--                            <path d="M11.25 12C11.25 11.5858 11.5858 11.25 12 11.25H12.01C12.4242 11.25 12.76 11.5858 12.76 12V12.01C12.76 12.4242 12.4242 12.76 12.01 12.76H12C11.5858 12.76 11.25 12.4242 11.25 12.01V12Z" fill="#0D4D8B"/>--}}
{{--                            <path d="M12 13.25C11.5858 13.25 11.25 13.5858 11.25 14V14.01C11.25 14.4242 11.5858 14.76 12 14.76H12.01C12.4242 14.76 12.76 14.4242 12.76 14.01V14C12.76 13.5858 12.4242 13.25 12.01 13.25H12Z" fill="#0D4D8B"/>--}}
{{--                            <path d="M13.25 10C13.25 9.58579 13.5858 9.25 14 9.25H14.01C14.4242 9.25 14.76 9.58579 14.76 10V10.01C14.76 10.4242 14.4242 10.76 14.01 10.76H14C13.5858 10.76 13.25 10.4242 13.25 10.01V10Z" fill="#0D4D8B"/>--}}
{{--                            <path d="M14 11.25C13.5858 11.25 13.25 11.5858 13.25 12V12.01C13.25 12.4242 13.5858 12.76 14 12.76H14.01C14.4242 12.76 14.76 12.4242 14.76 12.01V12C14.76 11.5858 14.4242 11.25 14.01 11.25H14Z" fill="#0D4D8B"/>--}}
{{--                            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.75 2C6.16421 2 6.5 2.33579 6.5 2.75V4H13.5V2.75C13.5 2.33579 13.8358 2 14.25 2C14.6642 2 15 2.33579 15 2.75V4H15.25C16.7688 4 18 5.23122 18 6.75V15.25C18 16.7688 16.7688 18 15.25 18H4.75C3.23122 18 2 16.7688 2 15.25V6.75C2 5.23122 3.23122 4 4.75 4H5V2.75C5 2.33579 5.33579 2 5.75 2ZM4.75 7.5C4.05964 7.5 3.5 8.05964 3.5 8.75V15.25C3.5 15.9404 4.05964 16.5 4.75 16.5H15.25C15.9404 16.5 16.5 15.9404 16.5 15.25V8.75C16.5 8.05964 15.9404 7.5 15.25 7.5H4.75Z" fill="#0D4D8B"/>--}}
{{--                        </svg>--}}
{{--                        <span class="font-medium text-base text-[#0D4D8B]">Regular Schedule</span>--}}
{{--                    </div>--}}
{{--                    <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>--}}
{{--                </div>--}}
{{--            @endif--}}
            @if($doctor->id == $scheduleId)
                <div class="relative">
                    <div class="flex flex-col absolute z-10 w-full">
                        <div class="bg-white w-full rounded-b-xl border-x border-b border-blue-300">
                            <div class="flex flex-col w-full justify-center mb-4">
                                <div class="grid grid-cols-3 bg-[#E5F2FF] bg-opacity-50 py-3 text-center mb-2">
                                    <span class="text-sm font-semibold text-[#0D4D8B]">Day</span>
                                    <span class="text-sm font-semibold text-[#0D4D8B]">Time</span>
                                    <span class="text-sm font-semibold text-[#0D4D8B]">Location</span>
                                </div>
                                @foreach($doctor->regularSchedules as $schedule)
                                    <div class="grid grid-cols-3 text-center items-center">
                                        <span class="text-sm font-light">{{ \App\Enums\Table\DoctorRegularSchedule\Day::getNameEn($schedule->day)}}</span>
                                        <span class="text-sm font-light">{{ \Illuminate\Support\Carbon::parse($schedule->time_from)->format('H:s') . ' - ' . \Illuminate\Support\Carbon::parse($schedule->time_to)->format('H:s') }}</span>
                                        <span class="text-sm font-light mr-2">{{ $schedule->location }}</span>
                                    </div>
                                    @if(!$loop->last)
                                        <hr class="my-2 mx-4">
                                    @endif
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</div>
