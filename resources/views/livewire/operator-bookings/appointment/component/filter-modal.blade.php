<div class="flex flex-col gap-4">
    <span class="text-base font-semibold text-[#0D4D8B]">Filter Doctor by 1 or More Options</span>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">

        @if(!in_array(1, $hideFormInputs))
            <div class="relative flex-col">
                <span class="text-base font-light text-[#475467]">Specialties</span>
                <div wire:click="onHandleModalSpecialty"
                     class="hover:cursor-pointer mt-2 text-base font-light w-full hs-dropdown-toggle py-3
                px-4 inline-flex justify-between items-center gap-x-2 rounded-lg border border-gray-200 bg-white
                text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none">
                    @if(!empty($selectedMainSpecialtyNames))
                        {{ $selectedMainSpecialtyNamesText }}
                    @elseif(!empty($selectedSpecialtyNames))
                        {{ $selectedSpecialtyNamesText }}
                    @else
                        Any Specialties
                    @endif
                    <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                </div>
                @if($isModalSpecialty)
                    <div wire:click.stop class="hover:cursor-pointer rounded-xl flex flex-col p-4 border shadow absolute w-full bg-white h-80 overflow-auto mt-2 z-10">
                        <!-- Search Bar and View Toggle -->
                        <div class="sticky top-0 bg-white pb-2 z-20">
                            <div class="relative mb-2">
                                <input
                                    type="text"
                                    wire:model.live.debounce.500ms="searchSpecialty"
                                    placeholder="Search specialties..."
                                    class="w-full p-2 border rounded-lg focus:ring-2 focus:ring-[#16A34A] focus:border-[#16A34A] outline-none"
                                >
                                <div class="absolute right-3 top-2.5">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                            </div>
                            <!-- View Toggle -->
                            {{-- <div class="flex gap-2 mb-2">
                                <button
                                    wire:click="toggleSpecialtyView"
                                    class="px-3 py-1 text-xs rounded-full border {{ $showGroupedView ? 'bg-[#16A34A] text-white' : 'bg-white text-gray-600 hover:bg-gray-50' }}"
                                >
                                    Grouped View
                                </button>
                                <button
                                    wire:click="toggleSpecialtyView"
                                    class="px-3 py-1 text-xs rounded-full border {{ !$showGroupedView ? 'bg-[#16A34A] text-white' : 'bg-white text-gray-600 hover:bg-gray-50' }}"
                                >
                                    Individual View
                                </button>
                            </div> --}}
                        </div>

                        <div class="overflow-y-auto mt-2">
                            @if($showGroupedView)
                                <!-- Grouped View - Only Main Specialties -->
                                @foreach($groupedSpecialties as $mainSpecialtyName => $group)
                                    <div wire:click.stop="onHandleSelectedMainSpecialty('{{ $mainSpecialtyName }}')"
                                         class="flex flex-row items-center gap-3 py-3 px-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                                        @if($this->isMainSpecialtySelected($mainSpecialtyName))
                                            <div class="bg-[#16A34A] p-[2px] rounded">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                                    <path d="M10 3L4.5 8.5L2 6" stroke="white" stroke-width="1.6666" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            </div>
                                        @else
                                            <div class="p-[2px] rounded h-4 w-4 border hover:border-[#16A34A]">
                                            </div>
                                        @endif
                                        <div class="flex-1">
                                            <span class="text-base font-medium text-gray-800">{{ $mainSpecialtyName }}</span>
                                            {{-- <div class="text-xs text-gray-500 mt-1">{{ count($group['specialties']) }} specialties available</div> --}}
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <!-- Individual View -->
                                @foreach($specialties as $specialty)
                                    <div wire:click.stop="onHandleSelectedSpecialty('{{ $specialty->uuid }}','{{ $specialty->group_name_en }}','{{ $specialty->id }}')" class="flex flex-row items-center gap-3 py-[10px]" id="parent">
                                        @if(in_array($specialty->uuid, $selectedSpecialtyUuids))
                                            <div class="bg-[#16A34A] p-[2px] rounded">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                                    <path d="M10 3L4.5 8.5L2 6" stroke="white" stroke-width="1.6666" stroke-linecap="round" stroke-linejoin="round"/>
                                                </svg>
                                            </div>
                                        @else
                                            <div class="p-[2px] rounded h-4 w-4 border hover:border-[#16A34A]" id="child">
                                            </div>
                                        @endif
                                        <span class="text-base font-light">{{ $specialty->group_name_en }}</span>
                                    </div>
                                @endforeach
                            @endif

                            @if($specialties->isEmpty())
                                <div class="text-center py-4 text-gray-500">
                                    No specialties found
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
        @endif

        @if(!in_array(2, $hideFormInputs))
            <div class="relative flex-col">
                    <span class="text-base font-light text-[#475467]">Spoken Language</span>
                    <div wire:click="onHandleModalLanguage"
                         class="hover:cursor-pointer mt-2 text-base font-light w-full hs-dropdown-toggle py-3
                px-4 inline-flex justify-between items-center gap-x-2 rounded-lg border border-gray-200 bg-white
                text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none">
                        {{ $selectedLanguage ? $selectedLanguage : 'All Language' }}
                        <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                    </div>
                    @if($isModalLanguage)
                        <div class="hover:cursor-pointer rounded-xl flex flex-col p-4 border shadow absolute w-full bg-white mt-2 z-10">
                            @foreach($languages as $language)
                                <div wire:click="onHandleSelectedLanguage('{{$language}}')"
                                     class="flex flex-row items-center gap-3 py-[10px]" id="parent">
                                    @if($selectedLanguage == $language)
                                        <div class="bg-[#16A34A] p-[2px] rounded">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                                <path d="M10 3L4.5 8.5L2 6" stroke="white" stroke-width="1.6666" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </div>
                                    @else
                                        <div class="p-[2px] rounded h-4 w-4 border hover:border-[#16A34A]" id="child">
                                        </div>
                                    @endif
                                    <span class="text-base font-light">{{ $language }}</span>

                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
        @endif
        @if(!in_array(3, $hideFormInputs))
            <div class="relative flex-col">
                <span class="text-base font-light text-[#475467]">Available Day</span>
                <div wire:click="onHandleModalDay"
                     class="hover:cursor-pointer mt-2 text-base font-light w-full hs-dropdown-toggle py-3
            px-4 inline-flex justify-between items-center gap-x-2 rounded-lg border border-gray-200 bg-white
            text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none">
                    @if($selectedDayName)
                        {{ $selectedDayName }}
                    @else
                        <div class="flex flex-row gap-2 items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M5.25 12C5.25 11.5858 5.58579 11.25 6 11.25H6.01C6.42421 11.25 6.76 11.5858 6.76 12V12.01C6.76 12.4242 6.42421 12.76 6.01 12.76H6C5.58579 12.76 5.25 12.4242 5.25 12.01V12Z" fill="#667085"/>
                                <path d="M6 13.25C5.58579 13.25 5.25 13.5858 5.25 14V14.01C5.25 14.4242 5.58579 14.76 6 14.76H6.01C6.42421 14.76 6.76 14.4242 6.76 14.01V14C6.76 13.5858 6.42421 13.25 6.01 13.25H6Z" fill="#667085"/>
                                <path d="M7.25 12C7.25 11.5858 7.58579 11.25 8 11.25H8.01C8.42421 11.25 8.76 11.5858 8.76 12V12.01C8.76 12.4242 8.42421 12.76 8.01 12.76H8C7.58579 12.76 7.25 12.4242 7.25 12.01V12Z" fill="#667085"/>
                                <path d="M8 13.25C7.58579 13.25 7.25 13.5858 7.25 14V14.01C7.25 14.4242 7.58579 14.76 8 14.76H8.01C8.42421 14.76 8.76 14.4242 8.76 14.01V14C8.76 13.5858 8.42421 13.25 8.01 13.25H8Z" fill="#667085"/>
                                <path d="M9.25 10C9.25 9.58579 9.58579 9.25 10 9.25H10.01C10.4242 9.25 10.76 9.58579 10.76 10V10.01C10.76 10.4242 10.4242 10.76 10.01 10.76H10C9.58579 10.76 9.25 10.4242 9.25 10.01V10Z" fill="#667085"/>
                                <path d="M10 11.25C9.58579 11.25 9.25 11.5858 9.25 12V12.01C9.25 12.4242 9.58579 12.76 10 12.76H10.01C10.4242 12.76 10.76 12.4242 10.76 12.01V12C10.76 11.5858 10.4242 11.25 10.01 11.25H10Z" fill="#667085"/>
                                <path d="M9.25 14C9.25 13.5858 9.58579 13.25 10 13.25H10.01C10.4242 13.25 10.76 13.5858 10.76 14V14.01C10.76 14.4242 10.4242 14.76 10.01 14.76H10C9.58579 14.76 9.25 14.4242 9.25 14.01V14Z" fill="#667085"/>
                                <path d="M12 9.25C11.5858 9.25 11.25 9.58579 11.25 10V10.01C11.25 10.4242 11.5858 10.76 12 10.76H12.01C12.4242 10.76 12.76 10.4242 12.76 10.01V10C12.76 9.58579 12.4242 9.25 12.01 9.25H12Z" fill="#667085"/>
                                <path d="M11.25 12C11.25 11.5858 11.5858 11.25 12 11.25H12.01C12.4242 11.25 12.76 11.5858 12.76 12V12.01C12.76 12.4242 12.4242 12.76 12.01 12.76H12C11.5858 12.76 11.25 12.4242 11.25 12.01V12Z" fill="#667085"/>
                                <path d="M12 13.25C11.5858 13.25 11.25 13.5858 11.25 14V14.01C11.25 14.4242 11.5858 14.76 12 14.76H12.01C12.4242 14.76 12.76 14.4242 12.76 14.01V14C12.76 13.5858 12.4242 13.25 12.01 13.25H12Z" fill="#667085"/>
                                <path d="M13.25 10C13.25 9.58579 13.5858 9.25 14 9.25H14.01C14.4242 9.25 14.76 9.58579 14.76 10V10.01C14.76 10.4242 14.4242 10.76 14.01 10.76H14C13.5858 10.76 13.25 10.4242 13.25 10.01V10Z" fill="#667085"/>
                                <path d="M14 11.25C13.5858 11.25 13.25 11.5858 13.25 12V12.01C13.25 12.4242 13.5858 12.76 14 12.76H14.01C14.4242 12.76 14.76 12.4242 14.76 12.01V12C14.76 11.5858 14.4242 11.25 14.01 11.25H14Z" fill="#667085"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5.75 2C6.16421 2 6.5 2.33579 6.5 2.75V4H13.5V2.75C13.5 2.33579 13.8358 2 14.25 2C14.6642 2 15 2.33579 15 2.75V4H15.25C16.7688 4 18 5.23122 18 6.75V15.25C18 16.7688 16.7688 18 15.25 18H4.75C3.23122 18 2 16.7688 2 15.25V6.75C2 5.23122 3.23122 4 4.75 4H5V2.75C5 2.33579 5.33579 2 5.75 2ZM4.75 7.5C4.05964 7.5 3.5 8.05964 3.5 8.75V15.25C3.5 15.9404 4.05964 16.5 4.75 16.5H15.25C15.9404 16.5 16.5 15.9404 16.5 15.25V8.75C16.5 8.05964 15.9404 7.5 15.25 7.5H4.75Z" fill="#667085"/>
                            </svg>
                            <span>All Day</span>
                        </div>
                    @endif
                    <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                </div>
                @if($isModalDay)
                    <div class="hover:cursor-pointer rounded-xl flex flex-col p-4 border shadow absolute w-full bg-white overflow-auto mt-2 z-20">
                        @foreach($days as $day)
                            <div wire:click="onHandleSelectedDay('{{ $day['id'] }}','{{ $day['name'] }}')"
                                 class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm
                    text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 hover:cursor-pointer" id="day-{{$day['id']}}">
                                @if($selectedDayId == $day['id'])
                                    <div class="bg-[#16A34A] p-[2px] rounded">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                            <path d="M10 3L4.5 8.5L2 6" stroke="white" stroke-width="1.6666" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                @else
                                    <div class="p-[2px] rounded h-4 w-4 border hover:border-[#16A34A]">
                                    </div>
                                @endif
                                <span>{{ $day['name'] }}</span>
                            </div>
                        @endforeach
                    </div>
                @endif
    </div>
        @endif

        @if(!in_array(4, $hideFormInputs))
            <div class="relative flex-col">
                <span class="text-base font-light text-[#475467]">Time</span>
                <div wire:click="onHandleModalTime"
                     class="hover:cursor-pointer mt-2 text-base font-light w-full hs-dropdown-toggle py-3
            px-4 inline-flex justify-between items-center gap-x-2 rounded-lg border border-gray-200 bg-white
            text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none">
                    @if($selectedTimeName)
                        {{ $selectedTimeName }}
                    @else
                        <div class="flex flex-row gap-2 items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path d="M5.25 12C5.25 11.5858 5.58579 11.25 6 11.25H6.01C6.42421 11.25 6.76 11.5858 6.76 12V12.01C6.76 12.4242 6.42421 12.76 6.01 12.76H6C5.58579 12.76 5.25 12.4242 5.25 12.01V12Z" fill="#667085"/>
                                <path d="M6 13.25C5.58579 13.25 5.25 13.5858 5.25 14V14.01C5.25 14.4242 5.58579 14.76 6 14.76H6.01C6.42421 14.76 6.76 14.4242 6.76 14.01V14C6.76 13.5858 6.42421 13.25 6.01 13.25H6Z" fill="#667085"/>
                                <path d="M7.25 12C7.25 11.5858 7.58579 11.25 8 11.25H8.01C8.42421 11.25 8.76 11.5858 8.76 12V12.01C8.76 12.4242 8.42421 12.76 8.01 12.76H8C7.58579 12.76 7.25 12.4242 7.25 12.01V12Z" fill="#667085"/>
                                <path d="M8 13.25C7.58579 13.25 7.25 13.5858 7.25 14V14.01C7.25 14.4242 7.58579 14.76 8 14.76H8.01C8.42421 14.76 8.76 14.4242 8.76 14.01V14C8.76 13.5858 8.42421 13.25 8.01 13.25H8Z" fill="#667085"/>
                                <path d="M9.25 10C9.25 9.58579 9.58579 9.25 10 9.25H10.01C10.4242 9.25 10.76 9.58579 10.76 10V10.01C10.76 10.4242 10.4242 10.76 10.01 10.76H10C9.58579 10.76 9.25 10.4242 9.25 10.01V10Z" fill="#667085"/>
                                <path d="M10 11.25C9.58579 11.25 9.25 11.5858 9.25 12V12.01C9.25 12.4242 9.58579 12.76 10 12.76H10.01C10.4242 12.76 10.76 12.4242 10.76 12.01V12C10.76 11.5858 10.4242 11.25 10.01 11.25H10Z" fill="#667085"/>
                                <path d="M9.25 14C9.25 13.5858 9.58579 13.25 10 13.25H10.01C10.4242 13.25 10.76 13.5858 10.76 14V14.01C10.76 14.4242 10.4242 14.76 10.01 14.76H10C9.58579 14.76 9.25 14.4242 9.25 14.01V14Z" fill="#667085"/>
                                <path d="M12 9.25C11.5858 9.25 11.25 9.58579 11.25 10V10.01C11.25 10.4242 11.5858 10.76 12 10.76H12.01C12.4242 10.76 12.76 10.4242 12.76 10.01V10C12.76 9.58579 12.4242 9.25 12.01 9.25H12Z" fill="#667085"/>
                                <path d="M11.25 12C11.25 11.5858 11.5858 11.25 12 11.25H12.01C12.4242 11.25 12.76 11.5858 12.76 12V12.01C12.76 12.4242 12.4242 12.76 12.01 12.76H12C11.5858 12.76 11.25 12.4242 11.25 12.01V12Z" fill="#667085"/>
                                <path d="M12 13.25C11.5858 13.25 11.25 13.5858 11.25 14V14.01C11.25 14.4242 11.5858 14.76 12 14.76H12.01C12.4242 14.76 12.76 14.4242 12.76 14.01V14C12.76 13.5858 12.4242 13.25 12.01 13.25H12Z" fill="#667085"/>
                                <path d="M13.25 10C13.25 9.58579 13.5858 9.25 14 9.25H14.01C14.4242 9.25 14.76 9.58579 14.76 10V10.01C14.76 10.4242 14.4242 10.76 14.01 10.76H14C13.5858 10.76 13.25 10.4242 13.25 10.01V10Z" fill="#667085"/>
                                <path d="M14 11.25C13.5858 11.25 13.25 11.5858 13.25 12V12.01C13.25 12.4242 13.5858 12.76 14 12.76H14.01C14.4242 12.76 14.76 12.4242 14.76 12.01V12C14.76 11.5858 14.4242 11.25 14.01 11.25H14Z" fill="#667085"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5.75 2C6.16421 2 6.5 2.33579 6.5 2.75V4H13.5V2.75C13.5 2.33579 13.8358 2 14.25 2C14.6642 2 15 2.33579 15 2.75V4H15.25C16.7688 4 18 5.23122 18 6.75V15.25C18 16.7688 16.7688 18 15.25 18H4.75C3.23122 18 2 16.7688 2 15.25V6.75C2 5.23122 3.23122 4 4.75 4H5V2.75C5 2.33579 5.33579 2 5.75 2ZM4.75 7.5C4.05964 7.5 3.5 8.05964 3.5 8.75V15.25C3.5 15.9404 4.05964 16.5 4.75 16.5H15.25C15.9404 16.5 16.5 15.9404 16.5 15.25V8.75C16.5 8.05964 15.9404 7.5 15.25 7.5H4.75Z" fill="#667085"/>
                            </svg>
                            <span>All Time</span>
                        </div>
                    @endif
                    <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                </div>
                @if($isModalTime)
                    <div class="hover:cursor-pointer rounded-xl flex flex-col p-4 border shadow absolute w-full bg-white overflow-auto mt-2">
                        @foreach($times as $time)
                            <div wire:click="onHandleSelectedTime('{{ $time['id'] }}','{{ $time['name'] }}')"
                                 class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm
                    text-gray-800 hover:bg-gray-100 focus:outline-none focus:bg-gray-100 hover:cursor-pointer" id="time-{{$time['id']}}">
                                @if($selectedTimeId == $time['id'])
                                    <div class="bg-[#16A34A] p-[2px] rounded">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                            <path d="M10 3L4.5 8.5L2 6" stroke="white" stroke-width="1.6666" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                @else
                                    <div class="p-[2px] rounded h-4 w-4 border hover:border-[#16A34A]">
                                    </div>
                                @endif
                                <span>{{ $time['name'] }}</span>
                            </div>
                        @endforeach
                    </div>
                @endif
            </div>
        @endif

        @if(!in_array(5, $hideFormInputs))
            <div class="flex flex-col gap-2">
                <span class="text-base font-light text-[#475467]">Consultation Type</span>
                <div class="grid grid-cols-2">
                    @foreach($consultationTypes as $type)
                        <div wire:click="onHandleSelectedConsultationType('{{$type['id']}}','{{$type['name']}}')"
                             class="flex flex-row gap-2 items-center hover:cursor-pointer">
                            @if($selectedConsultationTypeId == $type['id'])
                                <div class="bg-[#16A34A] p-[2px] rounded">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                        <path d="M10 3L4.5 8.5L2 6" stroke="white" stroke-width="1.6666" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                            @else
                                <div class="p-[2px] rounded h-4 w-4 border hover:border-[#16A34A]">
                                </div>
                            @endif
                            <span class="text-sm font-light">{{ $type['name'] }}</span>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        @if(!in_array(6, $hideFormInputs))
            <div class="flex flex-col gap-2">
                <span class="text-base font-light text-[#475467]">Sex</span>
                <div class="flex flex-row gap-4">
                    <div wire:click="onHandleSelectedGender('M')"
                         class="border border-[#16A34A] py-2 px-4 rounded-full flex items-center gap-2 hover:cursor-pointer
                 {{ $selectedGender == "M" ? 'bg-[#16A34A]' : 'bg-white' }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.65677 1.75C8.65677 1.33579 8.99256 1 9.40677 1H13.2349C13.3366 1 13.4335 1.02024 13.522 1.05692C13.6104 1.09352 13.6933 1.14778 13.7652 1.21969C13.8857 1.34021 13.9567 1.49153 13.978 1.6483C13.983 1.68496 13.9853 1.72191 13.9849 1.75884V5.57808C13.9849 5.9923 13.6491 6.32808 13.2349 6.32808C12.8206 6.32808 12.4849 5.9923 12.4849 5.57808V3.56073L10.0649 5.98072C10.5023 6.68744 10.7549 7.5207 10.7549 8.41294C10.7549 10.9686 8.68309 13.0404 6.12743 13.0404C3.57177 13.0404 1.5 10.9686 1.5 8.41294C1.5 5.85728 3.57177 3.78551 6.12743 3.78551C7.24838 3.78551 8.27624 4.18409 9.07702 4.84725L11.4243 2.5L9.40677 2.5C8.99256 2.5 8.65677 2.16421 8.65677 1.75ZM2.8 8.41294C2.8 6.57525 4.28974 5.08551 6.12743 5.08551C7.96512 5.08551 9.45486 6.57525 9.45486 8.41294C9.45486 10.2506 7.96512 11.7404 6.12743 11.7404C4.28974 11.7404 2.8 10.2506 2.8 8.41294Z"
                                  fill="{{ $selectedGender == "M" ? 'white' : '#16A34A' }}"/>
                        </svg>
                        <span class="text-sm font-medium {{ $selectedGender == "M" ? 'text-white' : 'text-[#16A34A]' }}">Male</span>
                    </div>
                    <div wire:click="onHandleSelectedGender('F')"
                         class="border border-[#16A34A] py-2 px-4 rounded-full flex items-center gap-2 hover:cursor-pointer
                {{ $selectedGender == "F" ? 'bg-[#16A34A]' : 'bg-white' }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.8 4.62743C3.8 2.78974 5.28974 1.3 7.12743 1.3C8.96512 1.3 10.4549 2.78974 10.4549 4.62743C10.4549 6.46512 8.96512 7.95486 7.12743 7.95486C5.28974 7.95486 3.8 6.46512 3.8 4.62743ZM7.12743 0C4.57177 0 2.5 2.07177 2.5 4.62743C2.5 6.94477 4.2034 8.86427 6.42639 9.2021C6.40919 9.26515 6.4 9.3315 6.4 9.4L6.4 11.066H5.15C4.73579 11.066 4.4 11.4018 4.4 11.816C4.4 12.2302 4.73579 12.566 5.15 12.566H6.4V13.3152C6.4 13.7294 6.73579 14.0652 7.15 14.0652C7.56421 14.0652 7.9 13.7294 7.9 13.3152V12.566H9.0652C9.47942 12.566 9.8152 12.2302 9.8152 11.816C9.8152 11.4018 9.47942 11.066 9.0652 11.066H7.9L7.9 9.4C7.9 9.32904 7.89014 9.26038 7.87173 9.19532C10.0735 8.83931 11.7549 6.92972 11.7549 4.62743C11.7549 2.07177 9.68309 0 7.12743 0Z"
                                  fill="{{ $selectedGender == "F" ? 'white' : '#16A34A' }}"/>
                        </svg>
                        <span class="text-sm font-medium {{ $selectedGender == "F" ? 'text-white' : 'text-[#16A34A]' }}">Female</span>
                    </div>
                </div>
        </div>
        @endif

    </div>
    <div class="flex flex-row py-5 px-6 gap-4">
        <div wire:click="onHandleReset"
            class="border border-[#0D4D8B] py-3 w-full flex justify-center items-center rounded-xl hover:cursor-pointer">
            <span class="text-[#0D4D8B]">Reset</span>
        </div>
        <div wire:click="onHandleSearch"
            class="bg-[#0D4D8B] py-3 w-full flex justify-center items-center rounded-xl hover:cursor-pointer">
            <div wire:loading.remove wire:target="onHandleSearch">
                <span class="text-white">Search</span>
            </div>
            <div wire:loading wire:target="onHandleSearch">
                @include('landing-page.component.spinner_with_text',['utility' => 'bg-brand flex flex-row gap-1 items-center text-white'])
            </div>
        </div>
    </div>
</div>
