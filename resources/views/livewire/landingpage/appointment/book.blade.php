
<div>
    <div class="movingColors hidden lg:block">
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
    </div>
    <div class="movingColors hidden lg:block">
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
    </div>
    @if(!$isWithoutDoctor)
        <div class="movingColors2 hidden lg:block">
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
        </div>
        <div class="movingColors2 hidden lg:block">
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
        </div>
    @endif
    <div class="h-32" id="content">
    </div>
    <div class="rounded-b-3xl relative z-10">
        <div class="absolute mx-auto blur-3xl opacity-50 w-1/2 h-full inset-0 z-1
            bg-gradient-to-b from-green-200 via-cyan-100 to-green-100 rounded-b-3xl"
             style="">
        </div>
        <div class="absolute mx-auto blur-3xl opacity-30 w-1/2 h-full inset-0 z-0
            bg-gradient-to-b from-blue-400 via-cyan-100 to-green-100 rounded-b-3xl"
             style="top: 30%; left: -20%">
        </div>
        <div class="relative z-1">
            <div class="flex flex-row px-4 md:px-40 py-4 gap-4 items-center text-sm md:text-base font-light">
                <a href="/" class="text-blue-800">Home</a>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                @if(!$isWithoutDoctor)
                    <a href="{{route('doctors.index')}}" class="text-blue-800 hidden md:block">Find a Doctor</a>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none"
                         class="hidden md:block">
                        <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <a href="{{route('doctors.show',['uuid' => $doctor->uuid, 'name' => Str::slug($doctor->name)])}}" class="text-blue-800 hidden md:block linguise_doctor_name">{{ $doctor->name }}</a>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none" class="hidden md:block">
                        <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                @endif
                <span class="text-[#667085]"> {{ $type == \App\Enums\Table\AppointmentPatientSummary\Type::VISIT ? 'Book a Clinic Appointment' : 'Schedule a Teleconsultation' }} </span>
            </div>
        </div>
    </div>
    <div class="mx-4 md:mx-40">
        @if(!in_array($state,[\App\Enums\Appointment\AppointmentState::FINISH]))
            <div class="flex-col flex items-center">
                <div class="flex flex-row justify-between mt-10 w-full md:w-2/3">
                    <div class="flex flex-col items-center gap-2 w-1/2 z-20">
                        <div class="h-8 w-8 {{in_array($state, [2,3]) ? 'bg-[#16A34A]' : 'bg-white' }} rounded-full border-2 border-[#16A34A] justify-center flex items-center">
                            @if(in_array($state, [2,3]))
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M8.81621 12.9837C8.64954 12.9837 8.49121 12.917 8.37454 12.8003L6.01621 10.442C5.77454 10.2003 5.77454 9.80033 6.01621 9.55866C6.25788 9.31699 6.65788 9.31699 6.89954 9.55866L8.81621 11.4753L13.0995 7.19199C13.3412 6.95033 13.7412 6.95033 13.9829 7.19199C14.2245 7.43366 14.2245 7.83366 13.9829 8.07533L9.25788 12.8003C9.14121 12.917 8.98288 12.9837 8.81621 12.9837Z" fill="white"/>
                                </svg>
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <circle cx="10" cy="10" r="2.5" fill="#16A34A"/>
                                </svg>
                            @endif
                        </div>
                        <span class="text-xs md:text-base text-center font-medium {{ in_array($state, [1]) ? 'text-[#16A34A]' : '' }}">Appointment Details</span>
                    </div>

                    <div class="border-t-2 {{ in_array($state, [1,2,3])  ? 'border-[#16A34A]' : 'border-[#C6C6C6]' }} w-full -mx-12 md:-mx-24 mt-4 z-10">
                    </div>

                    <div class="flex flex-col items-center gap-2 w-1/2 z-20">
                        @if(in_array($state, [2]))
                            <div class="h-8 w-8 bg-white rounded-full border-2 border-[#16A34A] justify-center flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <circle cx="10" cy="10" r="2.5" fill="#16A34A"/>
                                </svg>
                            </div>
                        @elseif(in_array($state, [3]))
                            <div class="h-8 w-8 bg-[#16A34A] rounded-full border-2 border-[#16A34A] justify-center flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M8.81621 12.9837C8.64954 12.9837 8.49121 12.917 8.37454 12.8003L6.01621 10.442C5.77454 10.2003 5.77454 9.80033 6.01621 9.55866C6.25788 9.31699 6.65788 9.31699 6.89954 9.55866L8.81621 11.4753L13.0995 7.19199C13.3412 6.95033 13.7412 6.95033 13.9829 7.19199C14.2245 7.43366 14.2245 7.83366 13.9829 8.07533L9.25788 12.8003C9.14121 12.917 8.98288 12.9837 8.81621 12.9837Z" fill="white"/>
                                </svg>
                            </div>
                        @else
                            <div class="h-8 w-8 bg-white border-2 border-[#C6C6C6] rounded-full"></div>
                        @endif
                        <span class="text-center text-xs md:text-base font-medium {{ in_array($state, [2]) ? 'text-[#16A34A]' : '' }}">Patient’s Information</span>
                    </div>
                    <div class="border-t-2 {{ in_array($state, [3]) ? 'border-[#16A34A]' : 'border-[#C6C6C6]' }} w-full -mx-12 md:-mx-24 mt-4">
                    </div>
                    <div class="flex flex-col items-center gap-2 w-1/2 z-20">
                        @if(in_array($state, [3]))
                            <div class="h-8 w-8 bg-white rounded-full border-2 border-[#16A34A] justify-center flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <circle cx="10" cy="10" r="2.5" fill="#16A34A"/>
                                </svg>
                            </div>
                        @else
                            <div class="h-8 w-8 bg-white border-2 border-[#C6C6C6] rounded-full"></div>
                        @endif
                        <span class="text-xs md:text-base text-center">Confirmation</span>
                    </div>
                </div>
            </div>
        @endif
        @if(in_array($state, [\App\Enums\Appointment\AppointmentState::SCHEDULE]))
            <div class="w-full md:w-2/3 mx-auto relative z-10">
                @if($isWithoutDoctor)
                    <div class="flex flex-col md:mt-10 pt-8 md:px-6 pb-10 gap-2 bg-white bg-opacity-50 rounded-3xl z-10 relative">
                        <div class="flex flex-row gap-1 items-center md:text-base text-sm">
                            <span>Doctor Name</span>
                            <span class="text-red-500">*</span>
                        </div>
                        <div class="flex flex-col gap-4">
                            <a class="bg-white border rounded-2xl p-4 flex flex-row items-center justify-between hover:cursor-pointer" href="{{route('doctors.index')}}">
                                <div class="flex flex-row gap-4 items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56" fill="none">
                                        <path d="M39.328 15.925C39.328 9.9015 34.428 5 28.404 5C22.38 5 17.4795 9.9015 17.4795 15.925C17.4805 21.948 22.3805 28.342 28.404 28.342C34.428 28.342 39.328 21.948 39.328 15.925Z" fill="url(#paint0_linear_5196_51757)"/>
                                        <path d="M8 42.8035C8 46.7775 17.135 50 28.404 50C39.6725 50 48.808 46.7775 48.808 42.8035C48.808 35.9565 44.0735 30.2205 37.7005 28.6775C39.3975 29.7795 40.5665 31.622 40.748 33.746C42.1985 34.071 43.287 35.3675 43.287 36.915C43.287 38.707 41.829 40.165 40.037 40.165C38.245 40.165 36.787 38.707 36.787 36.915C36.787 35.396 37.8365 34.1205 39.247 33.766C39.0385 31.801 37.7365 30.1615 35.9635 29.4605L28.62 36.342L21.179 29.369C19.162 30.05 17.696 31.9275 17.625 34.1545C17.9145 34.3225 18.1485 34.5825 18.292 34.8975C19.3605 35.4015 20.3035 36.4625 21.026 37.973C21.147 38.2275 21.1625 38.5145 21.074 38.7765C21.507 39.891 21.7545 41.109 21.7545 42.132C21.7545 43.5655 21.7545 44.921 20.192 45.269C20.0225 45.41 19.811 45.487 19.5885 45.487H18.544C18.0205 45.487 17.5945 45.0605 17.5945 44.5375L17.5955 44.5005C17.6155 43.996 18.038 43.588 18.544 43.588H19.588C19.695 43.588 19.7995 43.606 19.9 43.6415C19.9635 43.6235 19.9835 43.6085 19.9835 43.6085C20.0995 43.4025 20.0995 42.5445 20.0995 42.1325C20.0995 41.3015 19.8915 40.298 19.527 39.3655C19.3375 39.258 19.185 39.0955 19.091 38.899C18.4575 37.5735 17.597 36.7175 16.8975 36.7175C16.182 36.7175 15.2785 37.643 14.6485 39.0195C14.5455 39.2435 14.365 39.4265 14.1425 39.5355C13.813 40.428 13.632 41.3465 13.632 42.132C13.632 42.478 13.632 43.3965 13.7645 43.612C13.766 43.612 13.7955 43.63 13.8785 43.6505C13.9865 43.609 14.1025 43.5875 14.2185 43.5875H15.2645C15.7525 43.5875 16.159 43.959 16.2085 44.441L16.214 44.5C16.214 45.0605 15.788 45.487 15.265 45.487H14.219C14.0115 45.487 13.811 45.418 13.646 45.2915C13.0485 45.179 12.6275 44.9155 12.361 44.4865C12.038 43.968 11.979 43.287 11.979 42.132C11.979 41.1185 12.213 39.952 12.638 38.8375C12.5755 38.599 12.5965 38.35 12.7 38.1255C13.143 37.1575 13.706 36.3285 14.328 35.729C14.67 35.3995 15.0375 35.1345 15.422 34.941C15.567 34.603 15.818 34.326 16.1255 34.1505C16.184 31.8355 17.4135 29.811 19.2465 28.6445C12.803 30.138 8 35.907 8 42.8035ZM30.904 42.5625C30.904 42.3895 31.061 42.25 31.2535 42.25H33.154V40.35C33.154 40.1565 33.2935 40 33.4665 40H35.3415C35.5135 40 35.654 40.1565 35.654 40.35V42.25H37.5545C37.748 42.25 37.904 42.3895 37.904 42.5625V44.4375C37.904 44.6105 37.7475 44.75 37.5545 44.75H35.654V46.65C35.654 46.8435 35.5135 47 35.3415 47H33.4665C33.2935 47 33.154 46.8435 33.154 46.65V44.75H31.2535C31.061 44.75 30.904 44.6105 30.904 44.4375V42.5625Z" fill="url(#paint1_linear_5196_51757)"/>
                                        <path d="M40.0371 38.4141C40.8655 38.4141 41.5371 37.7425 41.5371 36.9141C41.5371 36.0856 40.8655 35.4141 40.0371 35.4141C39.2087 35.4141 38.5371 36.0856 38.5371 36.9141C38.5371 37.7425 39.2087 38.4141 40.0371 38.4141Z" fill="url(#paint2_linear_5196_51757)"/>
                                        <defs>
                                            <linearGradient id="paint0_linear_5196_51757" x1="32.0454" y1="4.51372" x2="23.0917" y2="28.4588" gradientUnits="userSpaceOnUse">
                                                <stop stop-color="#3674B3"/>
                                                <stop offset="1" stop-color="#0B4074"/>
                                            </linearGradient>
                                            <linearGradient id="paint1_linear_5196_51757" x1="35.2059" y1="28.1996" x2="30.7807" y2="52.3595" gradientUnits="userSpaceOnUse">
                                                <stop stop-color="#3674B3"/>
                                                <stop offset="1" stop-color="#0B4074"/>
                                            </linearGradient>
                                            <linearGradient id="paint2_linear_5196_51757" x1="40.5371" y1="35.3516" x2="39.4434" y2="38.4766" gradientUnits="userSpaceOnUse">
                                                <stop stop-color="#3674B3"/>
                                                <stop offset="1" stop-color="#0B4074"/>
                                            </linearGradient>
                                        </defs>
                                    </svg>
                                    <div class="flex flex-col gap-1">
                                        <span class="md:text-base text-sm font-medium text-[#072746]">Select Doctor</span>
                                    </div>
                                </div>
                                <div class="flex flex-row gap-2 items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.21967 5.21967C8.51256 4.92678 8.98744 4.92678 9.28033 5.21967L13.5303 9.46967C13.8232 9.76256 13.8232 10.2374 13.5303 10.5303L9.28033 14.7803C8.98744 15.0732 8.51256 15.0732 8.21967 14.7803C7.92678 14.4874 7.92678 14.0126 8.21967 13.7197L11.9393 10L8.21967 6.28033C7.92678 5.98744 7.92678 5.51256 8.21967 5.21967Z" fill="#8FC640"/>
                                    </svg>
                                </div>
                            </a>
                        </div>
                    </div>
                @else
                    <livewire:landing-page.appointment.component.schedule :doctor="$doctor" :appointment_uuid="$uuid" :type="$type"/>
                @endif
            </div>
        @endif
        <div class="w-full md:w-2/3 mx-auto {{ $state == \App\Enums\Appointment\AppointmentState::PATIENT_INFORMATION ? 'block' : 'hidden' }}">
            @php
                $page = request()->fullUrl();
            @endphp
            <livewire:landing-page.appointment.component.patient-information :page="$page" :appointment="$appointment" id="patient-information-{{ \Illuminate\Support\Str::uuid() }}"
                                                                             wire:key="patient-information-{{ \Illuminate\Support\Str::uuid() }}"/>
        </div>
        @if(in_array($state, [\App\Enums\Appointment\AppointmentState::CONFIRMATION]))
            <div class="md:w-2/3 mx-auto">
                <livewire:landing-page.appointment.component.detail :appointment="$appointment"/>
                <div wire:click="onHandleConfirmData" class="flex flex-row gap-3 items-center px-6 hover:cursor-pointer z-10 relative">
                    <div class="flex items-center justify-center border border-[#344054] h-[20px] w-[20px] rounded {{ $isConfirmData ? 'bg-[#0D4D8B]' : '' }}">
                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                            <path d="M11.6673 3.5L5.25065 9.91667L2.33398 7" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <span class="text-base font-light">By clicking, you confirm that all the patient data provided is correct.</span>
                </div>
                @if(Session::has('error'))
                    <div class="p-4 border rounded-xl border-red-500 text-red-500 font-medium -mb-4 relative z-10 mt-4">
                        {{Session::get('error')}}
                    </div>
                @endif
                <div class="flex flex-row justify-between gap-6 mx-auto mt-8 z-10 relative">
                    <div class="py-4 bg-white border-[#0D4D8B] border w-full rounded-xl flex justify-center mx-auto hover:cursor-pointer items-center"
                         wire:click="backState" id="backButton">
                        <span class="text-xs md:text-base font-medium text-[#0D4D8B]">Back</span>
                    </div>
                    <div class="w-full" wire:loading wire:target="nextState">
                    </div>
                    @if($isConfirmData)
                        @if($appointment->type == \App\Enums\Table\AppointmentPatientSummary\Type::VISIT)
                            <div class="py-4 w-full rounded-xl flex justify-center mx-auto items-center bg-[#0D4D8B] hover:cursor-pointer"
                                 wire:click="nextState" wire:loading.remove wire:target="nextState">
                                <span class="text-base font-medium text-white">Submit</span>
                            </div>
                            <div class="w-full" wire:loading wire:target="nextState">
                                <livewire:landing-page.component.loading-component text="Loading..." wire:key="{{ \Illuminate\Support\Str::uuid() }}"/>
                            </div>
                        @else
                            <div class="py-4 w-full rounded-xl flex justify-center mx-auto items-center bg-[#0D4D8B] hover:cursor-pointer"
                                 wire:click="nextState" onclick="onClickPayment()" id="continueToPaymentButton">
                                <span class="text-xs md:text-base font-medium text-white">Continue To Payment</span>
                            </div>
                            <div class="w-1/2 hidden" id="loadingContinueToPayment">
                                <livewire:landing-page.component.loading-component text="Loading..." wire:key="{{ \Illuminate\Support\Str::uuid() }}"/>
                            </div>
                        @endif
                    @else
                        <div class="py-4 w-full rounded-xl flex justify-center mx-auto items-center bg-[#0D4D8B] hover:cursor-not-allowed opacity-50">
                            <span class="font-medium text-white text-xs md:text-base">{{ $appointment->type == \App\Enums\Table\AppointmentPatientSummary\Type::VISIT ? 'Submit' : 'Continue To Payment' }}</span>
                        </div>
                    @endif
                </div>
            </div>
        @endif

        @if(in_array($state, [\App\Enums\Appointment\AppointmentState::FINISH]))
            <div class="mx-auto">
                <livewire:landing-page.appointment.component.created :appointment="$appointment"/>
            </div>
            <div class="md:w-1/2 flex flex-row justify-between gap-6 mx-auto relative z-10">
                @if($appointment->patient->is_complete_data)
                    <a href="/" class="py-4 bg-white border-[#0D4D8B] border-2 w-full rounded-xl flex justify-center mx-auto mt-16 hover:cursor-pointer items-center"
                    >
                        <span class="text-sm md:text-base font-medium text-[#0D4D8B]">Back to Home</span>
                    </a>
                @else
                    <a href="{{route('profile.show', $appointment->patient->uuid)}}" class="py-4 bg-white border-[#0D4D8B] border-2 w-full rounded-xl flex justify-center mx-auto mt-16 hover:cursor-pointer items-center"
                    >
                        <span class="text-sm md:text-base font-medium text-[#0D4D8B]">Complete Profile</span>
                    </a>
                @endif

                <a href="{{ route('profile.mybook.index') }}" class="py-4 bg-[#0D4D8B] w-full rounded-xl flex justify-center mx-auto mt-16 hover:cursor-pointer items-center">
                    <span class="text-base font-medium text-white">View My Bookings</span>
                </a>
            </div>
        @endif
    </div>
    <div class="h-28">

{{--        @include('landing-page.partials.homepage.contact')--}}
    </div>
</div>

@push('script')
    <script>
        function onClickPayment() {
            $('#continueToPaymentButton').addClass('hidden');
            $('#backButton').addClass('hidden');
            $('#loadingContinueToPayment').removeClass('hidden');
        }


        window.onload = function(){
            const platform = @json(Session::get('platform'));
            try {
                if(platform === 'mobile'){
                    console.log('PAYMENT SUCCESS')
                    PaymentResult.postMessage('payment success');
                }
            } catch (error) {
                console.error('An error occurred while posting the message:', error);
            }
        }

    </script>
@endpush

@script
    <script>
        document.getElementById('content').scrollIntoView({ behavior: 'smooth' });
        $wire.on('scrollToTop', (event) => {
            document.getElementById('content').scrollIntoView({ behavior: 'smooth' });
        });
        $wire.on('refreshThePage', (event) => {
            document.getElementById('content').scrollIntoView({ behavior: 'smooth' });
            // wait for 1.5 second
            setTimeout(() => {
                window.location.reload();
            }, 500);
        });

    </script>
@endscript
