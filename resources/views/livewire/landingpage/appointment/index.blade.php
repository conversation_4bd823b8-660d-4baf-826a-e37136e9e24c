@section('seo_meta')
    <link rel="canonical" href="https://bih.id/doctors">
    <link rel="alternate" hreflang="en-id" href="https://bih.id/doctors">
@endsection

<div>
    <div class="relative">
        <div class="absolute h-1/2 blur-3xl opacity-30 w-1/2 inset-0 z-10
            bg-gradient-to-b from-green-200 via-cyan-100 to-green-100 rounded-b-3xl"
             style="">
        </div>
        <div class="relative z-20">
            <div id="banner" class="relative">
                <img class="w-full h-[360px] object-cover"
                     src="{{ asset_gcs('assets/homepage/finddoctor/1de26d50-5ca2-4a36-92d8-82ed117e4162.jpg') }}"
                     alt="Image">
                <div class="mx-4 md:mx-40 absolute inset-x-0 bottom-0 flex flex-col
        justify-between text-white text-4xl font-semibold mb-10 gap-2">
                    <h1 class="text-3xl md:text-[60px] font-semibold">Find a Doctor</h1>
                    <span class="text-lg font-light">Explore Our Doctors by Using the Options Below </span>
                </div>
            </div>

            <div id="breadcrumb" class="">
                <div class="relative z-1">
                    <div class="flex flex-row px-4 md:px-40 py-4 gap-4 items-center text-base font-light">
                        <a href="/" class="text-blue-800">Home</a>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <span> Find a Doctor </span>
                    </div>
                </div>
            </div>

            <div id="browse" class="mx-4 md:mx-40 md:mt-6">
{{--                <div class="grid grid-cols-2 md:grid-cols-3 gap-0 md:gap-4 p-2 rounded-2xl bg-gradient-to-r from-[#fafeff] via-transparent">--}}
{{--                    <div wire:click="onHandleFindByKeyword('{{\App\Enums\General\FindDoctorBy::KEYWORD}}')"--}}
{{--                        class="p-3 rounded-2xl flex flex-row gap-2 {{ $findBy == \App\Enums\General\FindDoctorBy::KEYWORD ? 'bg-white shadow-sm text-[#16A34A]' : '' }} items-center hover:cursor-pointer">--}}
{{--                        @if($findBy == \App\Enums\General\FindDoctorBy::KEYWORD)--}}
{{--                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">--}}
{{--                                <path fill-rule="evenodd" clip-rule="evenodd" d="M9 3.5C5.96243 3.5 3.5 5.96243 3.5 9C3.5 12.0376 5.96243 14.5 9 14.5C10.519 14.5 11.893 13.8852 12.8891 12.8891C13.8852 11.893 14.5 10.519 14.5 9C14.5 5.96243 12.0376 3.5 9 3.5ZM2 9C2 5.13401 5.13401 2 9 2C12.866 2 16 5.13401 16 9C16 10.6625 15.4197 12.1906 14.4517 13.3911L17.7803 16.7197C18.0732 17.0126 18.0732 17.4874 17.7803 17.7803C17.4874 18.0732 17.0126 18.0732 16.7197 17.7803L13.3911 14.4517C12.1906 15.4197 10.6625 16 9 16C5.13401 16 2 12.866 2 9Z" fill="#16A34A"/>--}}
{{--                            </svg>--}}
{{--                        @else--}}
{{--                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">--}}
{{--                                <path fill-rule="evenodd" clip-rule="evenodd" d="M7 1.5C3.96243 1.5 1.5 3.96243 1.5 7C1.5 10.0376 3.96243 12.5 7 12.5C8.51899 12.5 9.89296 11.8852 10.8891 10.8891C11.8852 9.89296 12.5 8.51899 12.5 7C12.5 3.96243 10.0376 1.5 7 1.5ZM0 7C0 3.13401 3.13401 0 7 0C10.866 0 14 3.13401 14 7C14 8.66252 13.4197 10.1906 12.4517 11.3911L15.7803 14.7197C16.0732 15.0126 16.0732 15.4874 15.7803 15.7803C15.4874 16.0732 15.0126 16.0732 14.7197 15.7803L11.3911 12.4517C10.1906 13.4197 8.66252 14 7 14C3.13401 14 0 10.866 0 7Z" fill="#667085"/>--}}
{{--                            </svg>--}}
{{--                        @endif--}}
{{--                        <div wire:loading.remove wire:target="onHandleFindByKeyword">--}}
{{--                            <span class="text-xs md:text-base font-medium">Search by Keywords</span>--}}
{{--                        </div>--}}
{{--                        <div wire:loading wire:target="onHandleFindByKeyword">--}}
{{--                            @include('landing-page.component.spinner_with_text')--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                    <div wire:click="onHandleFindBySpecialization('{{\App\Enums\General\FindDoctorBy::SPECIALIZATION}}')"--}}
{{--                         class="p-3 rounded-2xl flex flex-row gap-2 {{ $findBy == \App\Enums\General\FindDoctorBy::SPECIALIZATION ? 'bg-white shadow-sm text-[#16A34A]' : '' }} items-center hover:cursor-pointer">--}}
{{--                        @if($findBy == \App\Enums\General\FindDoctorBy::SPECIALIZATION)--}}
{{--                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="18" viewBox="0 0 16 18" fill="none">--}}
{{--                                <path d="M5.63916 13.3049C5.63916 18.166 13.903 18.6521 13.903 13.3049V9.41602" stroke="#16A34A" stroke-width="1.1"/>--}}
{{--                                <path d="M13.9028 7.94728H13.9126V7.957H13.9028V7.94728Z" stroke="#16A34A" stroke-width="1.1" stroke-linejoin="round"/>--}}
{{--                                <path d="M15.361 7.95833C15.361 8.76375 14.7081 9.41667 13.9027 9.41667C13.0973 9.41667 12.4443 8.76375 12.4443 7.95833C12.4443 7.15292 13.0973 6.5 13.9027 6.5C14.7081 6.5 15.361 7.15292 15.361 7.95833Z" stroke="#16A34A" stroke-width="1.1"/>--}}
{{--                                <path d="M3.20839 1.63824V1.63824C1.86603 1.63824 0.777832 2.72644 0.777832 4.0688V8.44379C0.777832 11.1285 2.95422 13.3049 5.63894 13.3049V13.3049C8.32366 13.3049 10.5001 11.1285 10.5001 8.44379V3.58268C10.5001 2.50879 9.6295 1.63824 8.55561 1.63824V1.63824M3.20839 1.63824C3.20839 2.17518 3.64367 2.61046 4.18061 2.61046C4.71755 2.61046 5.15283 2.17518 5.15283 1.63824C5.15283 1.10129 4.71755 0.666016 4.18061 0.666016C3.64367 0.666016 3.20839 1.10129 3.20839 1.63824ZM8.55561 1.63824C8.55561 2.17518 8.12033 2.61046 7.58339 2.61046C7.04644 2.61046 6.61117 2.17518 6.61117 1.63824C6.61117 1.10129 7.04644 0.666016 7.58339 0.666016C8.12033 0.666016 8.55561 1.10129 8.55561 1.63824Z" stroke="#16A34A" stroke-width="1.1"/>--}}
{{--                            </svg>--}}
{{--                        @else--}}
{{--                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="18" viewBox="0 0 16 18" fill="none">--}}
{{--                                <path d="M5.63916 13.3049C5.63916 18.166 13.903 18.6521 13.903 13.3049V9.41602" stroke="#667085" stroke-width="1.1"/>--}}
{{--                                <path d="M13.9028 7.94728H13.9126V7.957H13.9028V7.94728Z" stroke="#667085" stroke-width="1.1" stroke-linejoin="round"/>--}}
{{--                                <path d="M15.361 7.95833C15.361 8.76375 14.7081 9.41667 13.9027 9.41667C13.0973 9.41667 12.4443 8.76375 12.4443 7.95833C12.4443 7.15292 13.0973 6.5 13.9027 6.5C14.7081 6.5 15.361 7.15292 15.361 7.95833Z" stroke="#667085" stroke-width="1.1"/>--}}
{{--                                <path d="M3.20839 1.63824V1.63824C1.86603 1.63824 0.777832 2.72644 0.777832 4.0688V8.44379C0.777832 11.1285 2.95422 13.3049 5.63894 13.3049V13.3049C8.32366 13.3049 10.5001 11.1285 10.5001 8.44379V3.58268C10.5001 2.50879 9.6295 1.63824 8.55561 1.63824V1.63824M3.20839 1.63824C3.20839 2.17518 3.64367 2.61046 4.18061 2.61046C4.71755 2.61046 5.15283 2.17518 5.15283 1.63824C5.15283 1.10129 4.71755 0.666016 4.18061 0.666016C3.64367 0.666016 3.20839 1.10129 3.20839 1.63824ZM8.55561 1.63824C8.55561 2.17518 8.12033 2.61046 7.58339 2.61046C7.04644 2.61046 6.61117 2.17518 6.61117 1.63824C6.61117 1.10129 7.04644 0.666016 7.58339 0.666016C8.12033 0.666016 8.55561 1.10129 8.55561 1.63824Z" stroke="#667085" stroke-width="1.1"/>--}}
{{--                            </svg>--}}
{{--                        @endif--}}
{{--                        <div wire:loading.remove wire:target="onHandleFindBySpecialization">--}}
{{--                            <span class="text-xs md:text-base font-medium">by Specialization</span>--}}
{{--                        </div>--}}
{{--                        <div wire:loading wire:target="onHandleFindBySpecialization">--}}
{{--                            @include('landing-page.component.spinner_with_text')--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                    <div wire:click="onHandleFindByCondition('{{\App\Enums\General\FindDoctorBy::CONDITION}}')"--}}
{{--                         class="p-3 rounded-2xl flex flex-row gap-2 {{ $findBy == \App\Enums\General\FindDoctorBy::CONDITION ? 'bg-white shadow-sm text-[#16A34A]' : '' }} items-center hover:cursor-pointer">--}}
{{--                        @if($findBy == \App\Enums\General\FindDoctorBy::CONDITION)--}}

{{--                        @else--}}
{{--                            <svg xmlns="http://www.w3.org/2000/svg" width="13" height="18" viewBox="0 0 13 18" fill="none">--}}
{{--                                <path d="M4.89358 9.51271C4.83664 9.51271 4.77907 9.49462 4.73032 9.4575C4.61204 9.36722 4.58939 9.19814 4.67952 9.07986C4.94784 8.72818 5.02475 8.46709 4.99204 8.40119C4.98606 8.38908 4.92252 8.3441 4.72025 8.3441C4.19917 8.3441 3.92362 8.2708 3.76916 8.09119C3.61581 7.91298 3.64837 7.69892 3.66976 7.55721C3.6814 7.4803 3.69241 7.40779 3.67762 7.35998C3.65356 7.2881 3.58184 7.2546 3.46655 7.20867C3.37344 7.17155 3.26806 7.12956 3.2064 7.01852C3.13374 6.89112 3.14082 6.76388 3.16535 6.66652C3.08262 6.60471 2.97268 6.4987 2.99156 6.32836C2.9903 6.22738 3.05667 6.14308 3.12446 6.07529C2.92817 6.02465 2.75862 5.9339 2.66802 5.6824C2.56296 5.39048 2.73235 5.21134 2.86856 5.06742C2.9516 4.9795 3.0551 4.87019 3.17149 4.69388C3.33176 4.44458 3.41905 4.2118 3.41905 4.1209C3.41905 2.66304 4.14381 1.58911 5.40742 1.17436C6.76335 0.729877 8.45304 1.16461 9.17403 2.14432C9.90854 3.13929 10.376 4.7472 8.42914 6.75554C8.32564 6.86218 8.15483 6.86501 8.0482 6.76152C7.94125 6.65787 7.93873 6.48737 8.04222 6.38058C9.41341 4.96613 9.64179 3.68491 8.74024 2.46392C8.15704 1.67074 6.70831 1.31481 5.5754 1.68631C4.97128 1.88449 3.9579 2.46455 3.9579 4.12089C3.9579 4.44175 3.70137 4.86563 3.62289 4.988C3.47866 5.2063 3.35221 5.33999 3.25988 5.43751C3.23377 5.46519 3.20326 5.49759 3.18391 5.52055C3.18706 5.52684 3.18895 5.52889 3.18895 5.52889C3.21065 5.54886 3.33758 5.57167 3.43022 5.58819C3.48055 5.59731 3.53544 5.60722 3.59521 5.61917C3.70263 5.64056 3.78615 5.72486 3.80691 5.83244C3.85142 6.06396 3.73283 6.23053 3.62667 6.34031C3.66677 6.37822 3.70405 6.4254 3.72843 6.48643C3.76523 6.59527 3.73849 6.67564 3.71584 6.72833C3.86715 6.79155 4.0949 6.90841 4.19052 7.19498C4.24353 7.36627 4.21978 7.52323 4.20248 7.63789C4.19744 7.67076 4.19052 7.71685 4.18942 7.745C4.22985 7.76435 4.35835 7.80524 4.72025 7.80524C5.10387 7.80524 5.35772 7.92541 5.4749 8.16227C5.66426 8.54494 5.3799 9.05014 5.10795 9.4067C5.05495 9.47622 4.97474 9.51271 4.89358 9.51271Z" fill="#667085" stroke="#667085" stroke-width="0.2"/>--}}
{{--                                <path d="M9.22197 9.51354C9.16377 9.51354 9.10495 9.49451 9.05556 9.45566C8.31444 8.87104 8.11249 7.69488 8.45568 5.95958C8.48462 5.81346 8.62618 5.71799 8.77214 5.74756C8.9181 5.77634 9.0131 5.91806 8.98416 6.06401C8.68783 7.56339 8.82782 8.58982 9.389 9.03257C9.50602 9.12474 9.52584 9.29413 9.43367 9.41099C9.38051 9.47831 9.30187 9.51354 9.22197 9.51354Z" fill="#667085" stroke="#667085" stroke-width="0.2"/>--}}
{{--                                <path d="M3.80053 16.7203C3.58332 16.7203 3.40559 16.6618 3.26341 16.5445C2.9652 16.2988 2.86721 15.8408 2.94601 15.0597C2.94601 15.0597 2.94601 15.0591 2.94617 15.0588C3.15913 13.0091 3.53661 11.7999 4.16998 11.1386C4.81359 10.4672 5.26499 10.2377 5.63208 10.3953C6.09135 10.5918 6.12045 11.2893 6.0945 11.8754C6.09402 11.8886 6.1129 11.9093 6.18635 11.9732C6.26656 12.0433 6.37666 12.139 6.43926 12.2896C6.7911 13.1431 6.7521 14.1698 6.32963 15.1814C5.96993 16.0358 5.11368 16.3941 4.32349 16.6322C4.12831 16.691 3.95451 16.7203 3.80053 16.7203ZM3.48203 15.1144C3.40811 15.8474 3.52811 16.0644 3.60613 16.1289C3.72141 16.2236 3.97511 16.1745 4.16826 16.1163C5.11809 15.8304 5.61589 15.4885 5.83294 14.9732C6.09214 14.3522 6.32162 13.4177 5.94162 12.4957C5.92762 12.4623 5.88138 12.4221 5.83231 12.3793C5.71844 12.2802 5.54637 12.1302 5.55612 11.8537C5.5945 10.9895 5.42196 10.8914 5.42023 10.8905C5.41881 10.8905 5.21387 10.8285 4.55894 11.5114C4.02135 12.0726 3.67911 13.2176 3.48203 15.1144Z" fill="#667085" stroke="#667085" stroke-width="0.2"/>--}}
{{--                                <path d="M9.85906 16.7203C9.70492 16.7203 9.53128 16.6911 9.33593 16.6322C8.54574 16.3941 7.68981 16.0358 7.33026 15.1821C6.90749 14.1695 6.86832 13.1431 7.21954 12.2906C7.28276 12.139 7.39286 12.0434 7.47308 11.9735C7.54637 11.9094 7.56524 11.8886 7.56462 11.8732C7.53882 11.2893 7.56808 10.5918 8.02734 10.3953C8.39475 10.2376 8.84647 10.4677 9.48944 11.1383C10.123 11.7999 10.5005 13.0091 10.7134 15.0591V15.0597C10.7921 15.8408 10.6942 16.2988 10.396 16.5445C10.2538 16.6618 10.0761 16.7203 9.85906 16.7203ZM8.25603 10.8889C8.2447 10.8889 8.23936 10.8905 8.23936 10.8905C8.23747 10.8914 8.06477 10.9899 8.10284 11.8515C8.1129 12.1302 7.94083 12.2802 7.82696 12.3796C7.77789 12.4221 7.73165 12.4627 7.71718 12.497C7.33781 13.4177 7.56745 14.3523 7.82696 14.9738C8.0437 15.4885 8.54166 15.8304 9.49133 16.1163C9.68416 16.1745 9.9377 16.2239 10.0535 16.1289C10.1315 16.0648 10.2513 15.8474 10.1774 15.1145C9.98048 13.2176 9.63823 12.0726 9.10064 11.5111C8.54858 10.9355 8.31611 10.8889 8.25603 10.8889Z" fill="#667085" stroke="#667085" stroke-width="0.2"/>--}}
{{--                                <path d="M5.82496 12.1327C5.77904 12.1327 5.73264 12.1211 5.69001 12.0962C5.56136 12.0216 5.51763 11.8568 5.59234 11.7282C6.00348 11.0204 6.1529 9.42066 6.01307 7.22405C5.99687 6.98624 5.9327 6.831 5.8226 6.76416C5.71549 6.69951 5.43113 6.63613 4.7276 6.94251C4.59108 7.00228 4.43238 6.93953 4.37309 6.80316C4.31363 6.6668 4.37607 6.5081 4.51244 6.44865C5.2287 6.13628 5.74852 6.08878 6.10225 6.30379C6.37073 6.46689 6.52157 6.76447 6.55066 7.1885C6.65431 8.81827 6.65306 10.9745 6.05821 11.9987C6.0082 12.0849 5.91792 12.1327 5.82496 12.1327Z" fill="#667085" stroke="#667085" stroke-width="0.2"/>--}}
{{--                                <path d="M7.83423 12.1331C7.74144 12.1331 7.65084 12.0853 7.60083 11.9991C7.01101 10.983 6.8141 9.3959 7.0464 7.53005C7.22555 6.07298 7.13165 5.24552 6.75055 4.92623C6.40579 4.63746 5.70446 4.66923 4.54403 5.02674C4.40185 5.07046 4.25133 4.99072 4.20729 4.84853C4.16356 4.70635 4.24331 4.55567 4.38549 4.51179C5.77398 4.08398 6.58478 4.08477 7.09657 4.51321C7.64549 4.9731 7.79019 5.8951 7.581 7.59627C7.36616 9.32103 7.54326 10.827 8.06701 11.7286C8.14156 11.8572 8.09784 12.022 7.96918 12.0969C7.92671 12.1214 7.88016 12.1331 7.83423 12.1331Z" fill="#667085" stroke="#667085" stroke-width="0.2"/>--}}
{{--                                <path d="M7.39548 6.94735C7.2728 6.94735 7.16176 6.86273 7.13313 6.73785C7.04207 6.33882 6.86685 6.07034 6.59727 5.91699C6.18031 5.67965 5.51532 5.70576 4.62054 5.9939C4.47914 6.03936 4.32737 5.96166 4.2816 5.8201C4.23598 5.67855 4.31384 5.52677 4.4554 5.48116C5.51422 5.13986 6.30221 5.129 6.86371 5.44876C7.2662 5.67776 7.53358 6.07113 7.65846 6.618C7.69149 6.76302 7.6009 6.9074 7.45588 6.94059C7.43543 6.94515 7.4153 6.94735 7.39548 6.94735Z" fill="#667085" stroke="#667085" stroke-width="0.2"/>--}}
{{--                                <path d="M1.29626 17.0005C1.15093 17.0005 1.03124 16.8848 1.02715 16.7388C0.883552 11.5431 1.19198 9.38632 4.88247 8.97613C5.02921 8.96056 5.16353 9.06625 5.17989 9.2141C5.19641 9.36195 5.08977 9.49516 4.94192 9.51152C1.84502 9.85581 1.41973 11.4431 1.56569 16.7237C1.56978 16.8725 1.4526 16.9965 1.30381 17.0005C1.30129 17.0005 1.29878 17.0005 1.29626 17.0005Z" fill="#667085" stroke="#667085" stroke-width="0.2"/>--}}
{{--                                <path d="M12.363 17.001C12.3605 17.001 12.3579 17.001 12.3554 17.001C12.2069 16.9969 12.0896 16.8729 12.0937 16.7242C12.2287 11.8207 11.8078 9.99355 9.17327 9.50912C9.027 9.48223 8.93011 9.34193 8.95717 9.1955C8.98422 9.04923 9.1242 8.9514 9.27079 8.97939C12.3337 9.54247 12.7703 11.7282 12.6322 16.7393C12.6281 16.8852 12.5083 17.001 12.363 17.001Z" fill="#667085" stroke="#667085" stroke-width="0.2"/>--}}
{{--                            </svg>--}}
{{--                        @endif--}}

{{--                        <div wire:loading.remove wire:target="onHandleFindByCondition">--}}
{{--                            <span class="text-xs md:text-base font-medium">by Clinical Conditions</span>--}}
{{--                        </div>--}}
{{--                        <div wire:loading wire:target="onHandleFindByCondition">--}}
{{--                            @include('landing-page.component.spinner_with_text')--}}
{{--                        </div>--}}
{{--                    </div>--}}
{{--                </div>--}}

                <div class="flex flex-col md:mt-8">
                    @if($findBy == \App\Enums\General\FindDoctorBy::KEYWORD)
                        <livewire:landing-page.appointment.search-by-keyword/>
                    @elseif($findBy == \App\Enums\General\FindDoctorBy::CONDITION)
                        <livewire:landing-page.appointment.search-by-organ/>
                    @elseif($findBy == \App\Enums\General\FindDoctorBy::SPECIALIZATION)
                        <livewire:landing-page.appointment.search-by-specialization/>
                    @endif
                </div>
            </div>
            {{-- <livewire:landing-page.component.general-practitioner/> --}}
        </div>
    </div>
{{--    <div class="mt-32 xl:mx-40">--}}
{{--        @include('landing-page.partials.homepage.contact')--}}
{{--    </div>--}}
</div>

@push('script')
    <script>
        function onLoadingSymt(){
            document.getElementById('is-loading-symt-element').style.display = 'block';
            document.getElementById('symt-element').style.display = 'none';
        }
    </script>
@endpush
