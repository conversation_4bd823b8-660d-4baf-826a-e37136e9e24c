<div>
    <div class="flex flex-col pt-8 pb-10 md:px-6 rounded-xl gap-4 z-10 relative">
        <div class="flex flex-col gap-7">
            <span class="text-xl font-medium text-[#0B4074]">Appointment Details</span>
            <div class="rounded-xl flex flex-col
                justify-between md:p-6 gap-4
                bg-white bg-opacity-50">
                <div class="py-[18px] px-4 flex flex-row gap-2 bg-white rounded-xl">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M3.83473 15.4434L2.84475 16.0238C2.5692 16.1853 2.3999 16.4808 2.3999 16.8002C2.3999 17.1196 2.5692 17.4151 2.84475 17.5766L11.5448 22.6766C11.8258 22.8414 12.174 22.8414 12.4551 22.6766L21.1551 17.5766C21.4306 17.4151 21.5999 17.1196 21.5999 16.8002C21.5999 16.4808 21.4306 16.1853 21.1551 16.0238L20.1651 15.4434L13.3653 19.4295C12.5222 19.9238 11.4777 19.9238 10.6345 19.4295L3.83473 15.4434Z" fill="#3674B3"/>
                        <path d="M3.83473 10.6434L2.84475 11.2238C2.5692 11.3853 2.3999 11.6808 2.3999 12.0002C2.3999 12.3196 2.5692 12.6151 2.84475 12.7766L11.5448 17.8766C11.8258 18.0414 12.174 18.0414 12.4551 17.8766L21.1551 12.7766C21.4306 12.6151 21.5999 12.3196 21.5999 12.0002C21.5999 11.6808 21.4306 11.3853 21.1551 11.2238L20.1651 10.6434L13.3653 14.6295C12.5222 15.1238 11.4777 15.1238 10.6345 14.6295L3.83473 10.6434Z" fill="#3674B3"/>
                        <path d="M12.4551 1.32377C12.174 1.159 11.8258 1.159 11.5448 1.32377L2.84475 6.42377C2.5692 6.5853 2.3999 6.88078 2.3999 7.20019C2.3999 7.51961 2.5692 7.81509 2.84475 7.97662L11.5448 13.0766C11.8258 13.2414 12.174 13.2414 12.4551 13.0766L21.1551 7.97662C21.4306 7.81509 21.5999 7.51961 21.5999 7.20019C21.5999 6.88078 21.4306 6.5853 21.1551 6.42377L12.4551 1.32377Z" fill="#3674B3"/>
                    </svg>
                    <div class="flex gap-2 flex-col">
                        <span class="text-base font-medium">Appointment Type</span>
                        <span class="text-base font-medium text-[#667085]">{{ \App\Enums\Table\AppointmentPatientSummary\Type::getLabel($appointment->type) }}</span>
                    </div>
                </div>
                <div class="py-[18px] px-4 flex flex-row gap-2 bg-white rounded-xl">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none">
                        <path d="M15.4375 5.4442C15.4375 2.55292 13.0855 0.200195 10.194 0.200195C7.30244 0.200195 4.9502 2.55292 4.9502 5.4442C4.95068 8.33524 7.30268 11.4044 10.194 11.4044C13.0855 11.4044 15.4375 8.33524 15.4375 5.4442Z" fill="#3674B3"/>
                        <path d="M0.399902 18.3461C0.399902 20.2536 4.7847 21.8004 10.1938 21.8004C15.6027 21.8004 19.9877 20.2536 19.9877 18.3461C19.9877 15.0596 17.7152 12.3063 14.6561 11.5656C15.4707 12.0946 16.0318 12.979 16.1189 13.9985C16.8152 14.1545 17.3377 14.7768 17.3377 15.5196C17.3377 16.3798 16.6378 17.0796 15.7777 17.0796C14.9175 17.0796 14.2177 16.3798 14.2177 15.5196C14.2177 14.7905 14.7214 14.1783 15.3985 14.0081C15.2984 13.0649 14.6734 12.278 13.8224 11.9415L10.2975 15.2446L6.72582 11.8976C5.75766 12.2244 5.05398 13.1256 5.0199 14.1946C5.15886 14.2752 5.27118 14.4 5.34006 14.5512C5.85294 14.7932 6.30558 15.3024 6.65238 16.0275C6.71046 16.1496 6.7179 16.2874 6.67542 16.4132C6.88326 16.9481 7.00206 17.5328 7.00206 18.0238C7.00206 18.7119 7.00206 19.3625 6.25206 19.5296C6.1707 19.5972 6.06918 19.6342 5.96238 19.6342H5.46102C5.20974 19.6342 5.00526 19.4295 5.00526 19.1784L5.00574 19.1607C5.01534 18.9185 5.21814 18.7227 5.46102 18.7227H5.96214C6.0135 18.7227 6.06366 18.7313 6.1119 18.7484C6.14238 18.7397 6.15198 18.7325 6.15198 18.7325C6.20766 18.6336 6.20766 18.2218 6.20766 18.024C6.20766 17.6252 6.10782 17.1435 5.93286 16.6959C5.8419 16.6443 5.7687 16.5663 5.72358 16.472C5.4195 15.8357 5.00646 15.4248 4.6707 15.4248C4.32726 15.4248 3.89358 15.8691 3.59118 16.5298C3.54174 16.6373 3.4551 16.7252 3.3483 16.7775C3.19014 17.2059 3.10326 17.6468 3.10326 18.0238C3.10326 18.1899 3.10326 18.6308 3.16686 18.7342C3.16758 18.7342 3.18174 18.7428 3.22158 18.7527C3.27342 18.7328 3.3291 18.7224 3.38478 18.7224H3.88686C4.1211 18.7224 4.31622 18.9008 4.33998 19.1321L4.34262 19.1604C4.34262 19.4295 4.13814 19.6342 3.8871 19.6342H3.38502C3.28542 19.6342 3.18918 19.6011 3.10998 19.5404C2.82318 19.4864 2.6211 19.3599 2.49318 19.154C2.33814 18.9051 2.30982 18.5782 2.30982 18.0238C2.30982 17.5373 2.42214 16.9774 2.62614 16.4424C2.59614 16.328 2.60622 16.2084 2.6559 16.1007C2.86854 15.636 3.13878 15.2381 3.43734 14.9504C3.6015 14.7922 3.7779 14.665 3.96246 14.5721C4.03206 14.4099 4.15254 14.2769 4.30014 14.1927C4.32822 13.0815 4.91838 12.1097 5.79822 11.5498C2.70534 12.2667 0.399902 15.0358 0.399902 18.3461ZM11.3938 18.2304C11.3938 18.1474 11.4692 18.0804 11.5616 18.0804H12.4738V17.1684C12.4738 17.0756 12.5408 17.0004 12.6238 17.0004H13.5238C13.6064 17.0004 13.6738 17.0756 13.6738 17.1684V18.0804H14.5861C14.6789 18.0804 14.7538 18.1474 14.7538 18.2304V19.1304C14.7538 19.2135 14.6787 19.2804 14.5861 19.2804H13.6738V20.1924C13.6738 20.2853 13.6064 20.3604 13.5238 20.3604H12.6238C12.5408 20.3604 12.4738 20.2853 12.4738 20.1924V19.2804H11.5616C11.4692 19.2804 11.3938 19.2135 11.3938 19.1304V18.2304Z" fill="#3674B3"/>
                        <path d="M15.7776 16.2398C16.1753 16.2398 16.4976 15.9174 16.4976 15.5198C16.4976 15.1222 16.1753 14.7998 15.7776 14.7998C15.38 14.7998 15.0576 15.1222 15.0576 15.5198C15.0576 15.9174 15.38 16.2398 15.7776 16.2398Z" fill="#3674B3"/>
                    </svg>
                    <div class="flex gap-2 flex-col">
                        <span class="text-base font-medium">Doctor</span>
                        <span class="text-base font-medium text-[#667085]">{{ $appointment->doctor->name }}</span>
                        <span class="text-base font-light text-[#667085]">{{ $appointment->doctor->specialty->group_name_en ?? '-' }}</span>
                    </div>
                </div>
                <div class="py-[18px] px-4 flex flex-row gap-2 bg-white rounded-xl">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M6.30039 14.4004C6.30039 13.9033 6.70333 13.5004 7.20039 13.5004H7.21239C7.70945 13.5004 8.11239 13.9033 8.11239 14.4004V14.4124C8.11239 14.9094 7.70945 15.3124 7.21239 15.3124H7.20039C6.70333 15.3124 6.30039 14.9094 6.30039 14.4124V14.4004Z" fill="#3674B3"/>
                        <path d="M7.20039 15.9004C6.70333 15.9004 6.30039 16.3033 6.30039 16.8004V16.8124C6.30039 17.3094 6.70333 17.7124 7.20039 17.7124H7.21239C7.70945 17.7124 8.11239 17.3094 8.11239 16.8124V16.8004C8.11239 16.3033 7.70945 15.9004 7.21239 15.9004H7.20039Z" fill="#3674B3"/>
                        <path d="M8.70039 14.4004C8.70039 13.9033 9.10333 13.5004 9.60039 13.5004H9.61239C10.1094 13.5004 10.5124 13.9033 10.5124 14.4004V14.4124C10.5124 14.9094 10.1094 15.3124 9.61239 15.3124H9.60039C9.10333 15.3124 8.70039 14.9094 8.70039 14.4124V14.4004Z" fill="#3674B3"/>
                        <path d="M9.60039 15.9004C9.10333 15.9004 8.70039 16.3033 8.70039 16.8004V16.8124C8.70039 17.3094 9.10333 17.7124 9.60039 17.7124H9.61239C10.1094 17.7124 10.5124 17.3094 10.5124 16.8124V16.8004C10.5124 16.3033 10.1094 15.9004 9.61239 15.9004H9.60039Z" fill="#3674B3"/>
                        <path d="M11.1004 12.0004C11.1004 11.5033 11.5033 11.1004 12.0004 11.1004H12.0124C12.5094 11.1004 12.9124 11.5033 12.9124 12.0004V12.0124C12.9124 12.5094 12.5094 12.9124 12.0124 12.9124H12.0004C11.5033 12.9124 11.1004 12.5094 11.1004 12.0124V12.0004Z" fill="#3674B3"/>
                        <path d="M12.0004 13.5004C11.5033 13.5004 11.1004 13.9033 11.1004 14.4004V14.4124C11.1004 14.9094 11.5033 15.3124 12.0004 15.3124H12.0124C12.5094 15.3124 12.9124 14.9094 12.9124 14.4124V14.4004C12.9124 13.9033 12.5094 13.5004 12.0124 13.5004H12.0004Z" fill="#3674B3"/>
                        <path d="M11.1004 16.8004C11.1004 16.3033 11.5033 15.9004 12.0004 15.9004H12.0124C12.5094 15.9004 12.9124 16.3033 12.9124 16.8004V16.8124C12.9124 17.3094 12.5094 17.7124 12.0124 17.7124H12.0004C11.5033 17.7124 11.1004 17.3094 11.1004 16.8124V16.8004Z" fill="#3674B3"/>
                        <path d="M14.4004 11.1004C13.9033 11.1004 13.5004 11.5033 13.5004 12.0004V12.0124C13.5004 12.5094 13.9033 12.9124 14.4004 12.9124H14.4124C14.9094 12.9124 15.3124 12.5094 15.3124 12.0124V12.0004C15.3124 11.5033 14.9094 11.1004 14.4124 11.1004H14.4004Z" fill="#3674B3"/>
                        <path d="M13.5004 14.4004C13.5004 13.9033 13.9033 13.5004 14.4004 13.5004H14.4124C14.9094 13.5004 15.3124 13.9033 15.3124 14.4004V14.4124C15.3124 14.9094 14.9094 15.3124 14.4124 15.3124H14.4004C13.9033 15.3124 13.5004 14.9094 13.5004 14.4124V14.4004Z" fill="#3674B3"/>
                        <path d="M14.4004 15.9004C13.9033 15.9004 13.5004 16.3033 13.5004 16.8004V16.8124C13.5004 17.3094 13.9033 17.7124 14.4004 17.7124H14.4124C14.9094 17.7124 15.3124 17.3094 15.3124 16.8124V16.8004C15.3124 16.3033 14.9094 15.9004 14.4124 15.9004H14.4004Z" fill="#3674B3"/>
                        <path d="M15.9004 12.0004C15.9004 11.5033 16.3033 11.1004 16.8004 11.1004H16.8124C17.3094 11.1004 17.7124 11.5033 17.7124 12.0004V12.0124C17.7124 12.5094 17.3094 12.9124 16.8124 12.9124H16.8004C16.3033 12.9124 15.9004 12.5094 15.9004 12.0124V12.0004Z" fill="#3674B3"/>
                        <path d="M16.8004 13.5004C16.3033 13.5004 15.9004 13.9033 15.9004 14.4004V14.4124C15.9004 14.9094 16.3033 15.3124 16.8004 15.3124H16.8124C17.3094 15.3124 17.7124 14.9094 17.7124 14.4124V14.4004C17.7124 13.9033 17.3094 13.5004 16.8124 13.5004H16.8004Z" fill="#3674B3"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M6.90039 2.40039C7.39745 2.40039 7.80039 2.80333 7.80039 3.30039V4.80039H16.2004V3.30039C16.2004 2.80333 16.6033 2.40039 17.1004 2.40039C17.5974 2.40039 18.0004 2.80333 18.0004 3.30039V4.80039H18.3004C20.1229 4.80039 21.6004 6.27785 21.6004 8.10039V18.3004C21.6004 20.1229 20.1229 21.6004 18.3004 21.6004H5.70039C3.87785 21.6004 2.40039 20.1229 2.40039 18.3004V8.10039C2.40039 6.27785 3.87785 4.80039 5.70039 4.80039H6.00039V3.30039C6.00039 2.80333 6.40333 2.40039 6.90039 2.40039ZM5.70039 9.00039C4.87196 9.00039 4.20039 9.67196 4.20039 10.5004V18.3004C4.20039 19.1288 4.87196 19.8004 5.70039 19.8004H18.3004C19.1288 19.8004 19.8004 19.1288 19.8004 18.3004V10.5004C19.8004 9.67196 19.1288 9.00039 18.3004 9.00039H5.70039Z" fill="#3674B3"/>
                    </svg>
                    <div class="flex flex-col gap-2">
                        <span class="text-base font-medium">Appointment Date & Time</span>
                        <span class="text-base font-medium text-[#667085]">{{ $appointment->book_date_time_label }}</span>
                    </div>
                </div>
                <div class="py-[18px] px-4 flex flex-row gap-2 bg-white rounded-xl">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M13.5664 10.0763V6.47168H10.4324V10.0763C10.4324 10.2736 10.2726 10.4334 10.0753 10.4334H6.4707V13.5673H10.0753C10.2726 13.5673 10.4324 13.7272 10.4324 13.9245V17.5291H13.5664V13.9245C13.5664 13.7272 13.7262 13.5673 13.9235 13.5673H17.5288V10.4334H13.9235C13.7262 10.4334 13.5664 10.2736 13.5664 10.0763Z" fill="white"/>
                        <path d="M12 2C6.48571 2 2 6.48571 2 12C2 17.5143 6.48571 22 12 22C17.5143 22 22 17.5143 22 12C22 6.48571 17.5143 2 12 2ZM18.2427 13.9241C18.2427 14.1214 18.0829 14.2813 17.8856 14.2813H14.2803V17.8858C14.2803 18.0832 14.1205 18.243 13.9232 18.243H10.075C9.87765 18.243 9.71783 18.0832 9.71783 17.8858V14.2813H6.11417C5.91685 14.2813 5.75703 14.1214 5.75703 13.9241V10.0759C5.75703 9.87856 5.91685 9.71874 6.11417 9.71874H9.71874V6.11417C9.71874 5.91685 9.87856 5.75703 10.0759 5.75703H13.9241C14.1214 5.75703 14.2813 5.91685 14.2813 6.11417V9.71874H17.8865C18.0838 9.71874 18.2437 9.87856 18.2437 10.0759V13.9241H18.2427Z" fill="#3674B3"/>
                    </svg>
                    <div class="flex gap-2 flex-col">
                        <span class="text-base font-medium">Medical Concern or Request</span>
                        <span class="text-base font-light text-[#667085]">{{ $appointment->medical_concern ?? '-' }}</span>
                    </div>
                </div>
                @if($appointment->attachment_url)
                    <div class="py-[18px] px-4 flex flex-row gap-2 bg-white rounded-xl">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.625 1.5H9C11.0711 1.5 12.75 3.17893 12.75 5.25V7.125C12.75 8.16053 13.5895 9 14.625 9H16.5C18.5711 9 20.25 10.6789 20.25 12.75V20.625C20.25 21.6605 19.4105 22.5 18.375 22.5H5.625C4.58947 22.5 3.75 21.6605 3.75 20.625V3.375C3.75 2.33947 4.58947 1.5 5.625 1.5ZM12.5303 11.4697C12.3897 11.329 12.1989 11.25 12 11.25C11.8011 11.25 11.6103 11.329 11.4697 11.4697L8.46967 14.4697C8.17678 14.7626 8.17678 15.2374 8.46967 15.5303C8.76256 15.8232 9.23744 15.8232 9.53033 15.5303L11.25 13.8107L11.25 18C11.25 18.4142 11.5858 18.75 12 18.75C12.4142 18.75 12.75 18.4142 12.75 18L12.75 13.8107L14.4697 15.5303C14.7626 15.8232 15.2374 15.8232 15.5303 15.5303C15.8232 15.2374 15.8232 14.7626 15.5303 14.4697L12.5303 11.4697Z" fill="#3674B3"/>
                            <path d="M14.25 5.25C14.25 3.93695 13.768 2.73648 12.9712 1.8159C16.3701 2.70377 19.0462 5.37988 19.9341 8.77881C19.0135 7.98204 17.8131 7.5 16.5 7.5H14.625C14.4179 7.5 14.25 7.33211 14.25 7.125V5.25Z" fill="#3674B3"/>
                        </svg>
                        <div class="flex flex-col gap-2">
                            <div class="flex gap-1">
                                <span class="text-base font-medium">Additional File</span>
                                <span class="text-base font-light text-[#667085]">(.jpg, .png, .pdf, .zip)</span>
                            </div>
                            <a href="{{ route('static-page-gcs', ['path' => $appointment->attachment_url]) }}"
                               target="_blank" class="hover:cursor-pointer text-base font-medium text-[#0D4D8B]">{{ get_file_name_from_path($appointment->attachment_url) }}</a>
                        </div>
                    </div>
                @endif

            </div>
        </div>
        <div class="flex flex-col gap-4">
            <span class="text-xl font-medium text-[#0B4074]">Patient’s Information</span>
            <div class="flex flex-col p-4 bg-blue-100 bg-opacity-10 gap-4 rounded-3xl">
                <div class="flex flex-row bg-white rounded-xl p-4 items-center gap-4 shadow-sm">
                    @if($appointment->patient->image)
                        <img class="w-[56px] h-[56px] rounded-full object-cover"
                             src="{{asset_gcs($appointment->patient->image)}}" alt="Image">
                    @else
                        <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
                            <rect x="1.25" y="1.25" width="57.5" height="57.5" stroke="white" stroke-width="1.5"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M45.5992 46.5599C50.0017 42.4113 52.75 36.5266 52.75 30C52.75 17.4355 42.5645 7.25 30 7.25C17.4355 7.25 7.25 17.4355 7.25 30C7.25 36.5266 9.99832 42.4113 14.4008 46.5599C18.4736 50.3979 23.9621 52.75 30 52.75C36.0379 52.75 41.5264 50.3979 45.5992 46.5599ZM16.3386 43.562C19.5458 39.5615 24.4737 37 30 37C35.5263 37 40.4542 39.5615 43.6614 43.562C40.174 47.0748 35.341 49.25 30 49.25C24.659 49.25 19.826 47.0748 16.3386 43.562ZM38.75 23C38.75 27.8325 34.8325 31.75 30 31.75C25.1675 31.75 21.25 27.8325 21.25 23C21.25 18.1675 25.1675 14.25 30 14.25C34.8325 14.25 38.75 18.1675 38.75 23Z" fill="#98A2B3"/>
                        </svg>
                    @endif
                    <div class="flex flex-col gap-1">
                        <div class="flex gap-1 items-center">
                            @if($appointment->patient->verified_at)
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                                </svg>
                            @endif
                            <span class="text-base font-light text-[#344054] linguise_patient_name">{{ $appointment->patient->fullname }}</span>
                            <span class="text-base font-light text-[#344054] {{ $appointment->patient->relation_patient ? '' : 'hidden' }}">({{\App\Enums\Table\Patient\RelationPatient::getLabel($appointment->patient->relation_patient)}})</span>
                        </div>
                        <span class="text-sm text-[#475467]">{{ \Carbon\Carbon::parse($appointment->patient->dob)->format('d F Y') }}</span>
                        <span class="text-sm text-[#475467]">{{ $appointment->patient->contact_with_code }}</span>
                    </div>
                </div>
                @if($appointment->type == \App\Enums\Table\AppointmentPatientSummary\Type::VISIT)
                    <div class="flex flex-row p-4 gap-4 rounded-xl shadow-sm bg-white">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M4.5 3.75C2.84315 3.75 1.5 5.09315 1.5 6.75V7.5H22.5V6.75C22.5 5.09315 21.1569 3.75 19.5 3.75H4.5Z" fill="#3674B3"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M22.5 9.75H1.5V17.25C1.5 18.9069 2.84315 20.25 4.5 20.25H19.5C21.1569 20.25 22.5 18.9069 22.5 17.25V9.75ZM4.5 13.5C4.5 13.0858 4.83579 12.75 5.25 12.75H11.25C11.6642 12.75 12 13.0858 12 13.5C12 13.9142 11.6642 14.25 11.25 14.25H5.25C4.83579 14.25 4.5 13.9142 4.5 13.5ZM5.25 15.75C4.83579 15.75 4.5 16.0858 4.5 16.5C4.5 16.9142 4.83579 17.25 5.25 17.25H8.25C8.66421 17.25 9 16.9142 9 16.5C9 16.0858 8.66421 15.75 8.25 15.75H5.25Z" fill="#3674B3"/>
                        </svg>
                        <div class="flex flex-col gap-2">
                            <span class="text-base font-medium">Guarantor</span>
                            <span class="text-base font-light text-[#667085]">{{ $appointment->payment_method_label }}</span>
                            @if($appointment->payment_method == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::INSURANCE)
                                <span class="text-base font-light text-[#667085]">{{ $appointment->insurance_name }}</span>
                                <span class="text-base font-light text-[#667085]">{{ $appointment->insurance_number }}</span>
                            @elseif($appointment->payment_method == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::COMPANY)
                                <span class="text-base font-light text-[#667085]">{{ $appointment->company_name }}</span>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
        </div>
        @if($appointment->type == \App\Enums\Table\AppointmentPatientSummary\Type::TELECONSULTATION)
            <div class="flex flex-col gap-4">
                <span class="text-xl font-medium text-[#0B4074]">Payment Summary</span>
                <div class="flex flex-col md:p-4 bg-blue-100 bg-opacity-10 gap-4 rounded-3xl">
                    <div class="flex flex-col bg-white rounded-xl p-4 gap-4 shadow-sm">
                        <div class="flex flex-row justify-between items-center">
                            <span class="text-base font-light">Teleconsultation Fee</span>
                            <span class="text-base font-light">Rp{{str_replace(',', '.', number_format((int)$appointment->amount))}}</span>
                        </div>
                        <hr>
                        <div class="flex flex-row justify-between items-center">
                            <span class="text-[18px] font-semibold text-[#0D4D8B]">Total</span>
                            <span class="text-[18px] font-semibold text-[#0D4D8B]">Rp{{str_replace(',', '.', number_format((int)$appointment->amount))}}</span>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</div>
