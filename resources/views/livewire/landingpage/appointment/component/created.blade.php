<div>
    <div class="flex flex-col gap-6 items-center relative z-10">
        <img src="{{ asset_gcs('asset/public/icon/9580d6dc-0c6a-4441-9933-279b574fa206.png') }}"
             alt="Payment success" class="h-[110px] w-[110px]"/>
        @if($appointment->type == \App\Enums\Table\AppointmentPatientSummary\Type::VISIT)
            <span class="text-2xl text-[#0D4D8B] mx-auto font-semibold text-center">Congratulations! Your reservation has been confirmed.</span>
        @else
            <div class="flex flex-col gap-2">
                <span class="text-xl md:text-2xl text-[#0D4D8B] mx-auto font-semibold text-center">Teleconsultation is being Processed</span>
                <span class="text-sm text-[18px] text-[#344054] font-light mx-auto text-center">Your teleconsultation request is being processed. We will notify you once your teleconsultation schedule is confirmed.</span>
            </div>
        @endif
        @if(!$appointment->patient->is_complete_data)
            <div class="md:w-2/3 flex flex-row mx-auto md:mt-4">
                <div class="border border-[#B54708] rounded-xl py-3 px-4 bg-[#FFFAEB]">
                    <div class="flex flex-row gap-4">
                        <div>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M14.4866 12.0005L9.15329 2.66714C9.037 2.46194 8.86836 2.29127 8.66457 2.17252C8.46078 2.05378 8.22915 1.99121 7.99329 1.99121C7.75743 1.99121 7.52579 2.05378 7.322 2.17252C7.11822 2.29127 6.94958 2.46194 6.83329 2.66714L1.49995 12.0005C1.38241 12.204 1.32077 12.4351 1.32129 12.6701C1.32181 12.9052 1.38447 13.136 1.50292 13.339C1.62136 13.5421 1.79138 13.7102 1.99575 13.8264C2.20011 13.9425 2.43156 14.0026 2.66662 14.0005H13.3333C13.5672 14.0002 13.797 13.9385 13.9995 13.8213C14.202 13.7042 14.3701 13.5359 14.487 13.3332C14.6038 13.1306 14.6653 12.9007 14.6653 12.6668C14.6652 12.4329 14.6036 12.2031 14.4866 12.0005Z" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 6V8.66667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M8 11.333H8.00667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="flex flex-col text-[#93370D] gap-1 text-sm md:text-base">
                            <span class="font-semibold text-[#B54708]">Patient data is still incomplete</span>
                            <span>To ensure a smoother experience during your visit to the hospital,
                                @if($appointment->type == \App\Enums\Table\AppointmentPatientSummary\Type::VISIT)
                                    Please complete your profile before visiting the hospital.
                                @else
                                    Please complete your profile to join the Teleconsultation.
                                @endif
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        @endif
        <div class="flex flex-col rounded-2xl md:w-2/3 p-3 md:p-6 gap-4 shadow-xl bg-white">
            <div class="flex flex-row gap-4 items-center">
                @if(!$appointment->doctor->image)
                    <img class="w-[40px] h-[40px] rounded-full object-cover" src="{{ asset_gcs('public/assets/icon/318e26c9-a4f1-4301-be62-8edf42acaf4e.svg') }}" alt="Bali International Hospital">
                @else
                    <img class="w-[40px] h-[40px] rounded-full object-cover"
                         src="{{asset_gcs($appointment->doctor->image)}}" alt="Image">
                @endif
                <div class="flex flex-col gap-1">
                    <span class="text-sm md:text-base font-semibold">{{ $appointment->doctor->name }}</span>
                    <span class="text-xs md:text-sm font-medium text-[#667085]">{{ $appointment->doctor->specialty->group_name_en }}</span>
                </div>
            </div>
            <hr>
            <div class="flex flex-row gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.75 2.25C7.16421 2.25 7.5 2.58579 7.5 3V4.5H16.5V3C16.5 2.58579 16.8358 2.25 17.25 2.25C17.6642 2.25 18 2.58579 18 3V4.5H18.75C20.4069 4.5 21.75 5.84315 21.75 7.5V18.75C21.75 20.4069 20.4069 21.75 18.75 21.75H5.25C3.59315 21.75 2.25 20.4069 2.25 18.75V7.5C2.25 5.84315 3.59315 4.5 5.25 4.5H6V3C6 2.58579 6.33579 2.25 6.75 2.25ZM5.25 6C4.42157 6 3.75 6.67157 3.75 7.5V8.65135C4.19126 8.39609 4.70357 8.25 5.25 8.25H18.75C19.2964 8.25 19.8087 8.39609 20.25 8.65135V7.5C20.25 6.67157 19.5784 6 18.75 6H5.25ZM20.25 11.25C20.25 10.4216 19.5784 9.75 18.75 9.75H5.25C4.42157 9.75 3.75 10.4216 3.75 11.25V18.75C3.75 19.5784 4.42157 20.25 5.25 20.25H18.75C19.5784 20.25 20.25 19.5784 20.25 18.75V11.25ZM11.25 12.75C11.25 12.3358 11.5858 12 12 12H12.0075C12.4217 12 12.7575 12.3358 12.7575 12.75V12.7575C12.7575 13.1717 12.4217 13.5075 12.0075 13.5075H12C11.5858 13.5075 11.25 13.1717 11.25 12.7575V12.75ZM13.5 12.75C13.5 12.3358 13.8358 12 14.25 12H14.2575C14.6717 12 15.0075 12.3358 15.0075 12.75V12.7575C15.0075 13.1717 14.6717 13.5075 14.2575 13.5075H14.25C13.8358 13.5075 13.5 13.1717 13.5 12.7575V12.75ZM15.75 12.75C15.75 12.3358 16.0858 12 16.5 12H16.5075C16.9217 12 17.2575 12.3358 17.2575 12.75V12.7575C17.2575 13.1717 16.9217 13.5075 16.5075 13.5075H16.5C16.0858 13.5075 15.75 13.1717 15.75 12.7575V12.75ZM6.75 15C6.75 14.5858 7.08579 14.25 7.5 14.25H7.5075C7.92171 14.25 8.2575 14.5858 8.2575 15V15.0075C8.2575 15.4217 7.92171 15.7575 7.5075 15.7575H7.5C7.08579 15.7575 6.75 15.4217 6.75 15.0075V15ZM9 15C9 14.5858 9.33579 14.25 9.75 14.25H9.7575C10.1717 14.25 10.5075 14.5858 10.5075 15V15.0075C10.5075 15.4217 10.1717 15.7575 9.7575 15.7575H9.75C9.33579 15.7575 9 15.4217 9 15.0075V15ZM11.25 15C11.25 14.5858 11.5858 14.25 12 14.25H12.0075C12.4217 14.25 12.7575 14.5858 12.7575 15V15.0075C12.7575 15.4217 12.4217 15.7575 12.0075 15.7575H12C11.5858 15.7575 11.25 15.4217 11.25 15.0075V15ZM13.5 15C13.5 14.5858 13.8358 14.25 14.25 14.25H14.2575C14.6717 14.25 15.0075 14.5858 15.0075 15V15.0075C15.0075 15.4217 14.6717 15.7575 14.2575 15.7575H14.25C13.8358 15.7575 13.5 15.4217 13.5 15.0075V15ZM15.75 15C15.75 14.5858 16.0858 14.25 16.5 14.25H16.5075C16.9217 14.25 17.2575 14.5858 17.2575 15V15.0075C17.2575 15.4217 16.9217 15.7575 16.5075 15.7575H16.5C16.0858 15.7575 15.75 15.4217 15.75 15.0075V15ZM6.75 17.25C6.75 16.8358 7.08579 16.5 7.5 16.5H7.5075C7.92171 16.5 8.2575 16.8358 8.2575 17.25V17.2575C8.2575 17.6717 7.92171 18.0075 7.5075 18.0075H7.5C7.08579 18.0075 6.75 17.6717 6.75 17.2575V17.25ZM9 17.25C9 16.8358 9.33579 16.5 9.75 16.5H9.7575C10.1717 16.5 10.5075 16.8358 10.5075 17.25V17.2575C10.5075 17.6717 10.1717 18.0075 9.7575 18.0075H9.75C9.33579 18.0075 9 17.6717 9 17.2575V17.25ZM11.25 17.25C11.25 16.8358 11.5858 16.5 12 16.5H12.0075C12.4217 16.5 12.7575 16.8358 12.7575 17.25V17.2575C12.7575 17.6717 12.4217 18.0075 12.0075 18.0075H12C11.5858 18.0075 11.25 17.6717 11.25 17.2575V17.25ZM13.5 17.25C13.5 16.8358 13.8358 16.5 14.25 16.5H14.2575C14.6717 16.5 15.0075 16.8358 15.0075 17.25V17.2575C15.0075 17.6717 14.6717 18.0075 14.2575 18.0075H14.25C13.8358 18.0075 13.5 17.6717 13.5 17.2575V17.25Z" fill="#3674B3"/>
                </svg>
                <div class="flex flex-col gap-2 text-sm md:text-base">
                    <span class="font-medium">Appointment Date & Time</span>
                    <span class="text-[#667085] font-medium">{{ $appointment->book_date_time_label }}</span>
                </div>
            </div>
            <div class="flex flex-row gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.0008 3C10.344 3 9.00082 4.34315 9.00082 6C9.00082 7.65685 10.344 9 12.0008 9C13.6577 9 15.0008 7.65685 15.0008 6C15.0008 4.34315 13.6577 3 12.0008 3ZM7.50082 6C7.50082 3.51472 9.51554 1.5 12.0008 1.5C14.4861 1.5 16.5008 3.51472 16.5008 6C16.5008 8.48528 14.4861 10.5 12.0008 10.5C9.51554 10.5 7.50082 8.48528 7.50082 6ZM5.27791 19.6409C7.34314 20.5158 9.61471 21 12.0011 21C14.3873 21 16.6587 20.5159 18.7238 19.6412C18.4161 16.1987 15.5235 13.5 12.0008 13.5C8.47819 13.5 5.58569 16.1985 5.27791 19.6409ZM3.75207 20.1053C3.82941 15.6156 7.4928 12 12.0008 12C16.5089 12 20.1724 15.6157 20.2496 20.1056C20.2547 20.4034 20.0831 20.676 19.8125 20.8002C17.4335 21.8918 14.7873 22.5 12.0011 22.5C9.21468 22.5 6.56825 21.8917 4.18914 20.7999C3.91847 20.6757 3.74694 20.4031 3.75207 20.1053Z" fill="#3674B3"/>
                </svg>
                <div class="flex flex-col gap-2 text-sm md:text-base">
                    <span class="font-medium">Patient Information</span>
                    <div class="flex flex-col gap-1">
                        <div class="flex gap-1 items-center">
                            @if($appointment->patient->verified_at)
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                                </svg>
                            @endif
                            <span class="font-medium text-[#344054] linguise_patient_name">{{ $appointment->patient->fullname }}</span>
                            <span class="font-light text-[#344054] {{ $appointment->patient->relation_patient ? '' : 'hidden' }}">({{\App\Enums\Table\Patient\RelationPatient::getLabel($appointment->patient->relation_patient)}})</span>
                        </div>
                        @if($appointment->patient->dob)
                            <span class="text-base font-light text-[#475467]">{{ \Carbon\Carbon::parse($appointment->patient->dob)->format('d F Y') }}</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
