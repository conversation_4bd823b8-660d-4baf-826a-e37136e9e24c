<div class="mt-10">
    <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 {{ $isOpenModalPatient ? '' : 'hidden' }}">
        <div class="sm:max-w-lg w-full m-3 sm:mx-auto">
            <div class="max-h-full overflow-hidden flex flex-col bg-white border shadow-sm rounded-xl pointer-events-auto p-6 mt-16 md:mt-28 xl:mt-0">
                <div class="flex justify-between items-center">
                    <span class="text-xl font-semibold">
                        Select Patient Profile
                    </span>
                    <div wire:click="onHandleCloseModal" class="flex justify-center items-center size-7 text-sm font-semibold rounded-full border border-transparent text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none hover:cursor-pointer">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.46967 5.46967C5.76256 5.17678 6.23744 5.17678 6.53033 5.46967L12 10.9393L17.4697 5.46967C17.7626 5.17678 18.2374 5.17678 18.5303 5.46967C18.8232 5.76256 18.8232 6.23744 18.5303 6.53033L13.0607 12L18.5303 17.4697C18.8232 17.7626 18.8232 18.2374 18.5303 18.5303C18.2374 18.8232 17.7626 18.8232 17.4697 18.5303L12 13.0607L6.53033 18.5303C6.23744 18.8232 5.76256 18.8232 5.46967 18.5303C5.17678 18.2374 5.17678 17.7626 5.46967 17.4697L10.9393 12L5.46967 6.53033C5.17678 6.23744 5.17678 5.76256 5.46967 5.46967Z" fill="#1D2939" stroke="#475467" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                </div>
                <div class="overflow-y-auto mt-[20px]">
                    <div class="flex flex-col gap-3">
                        <span class="text-base font-medium text-[#09335D]">Myself</span>
                        <div class="hover:cursor-pointer">
                            <div wire:click="onHandleSelectedPatient('{{ $user->patient->id }}')"
                                 id="card-patient-{{$user->patient->id}}" class="flex flex-row justify-between border rounded-xl p-4 items-center hover:border-[#0D4D8B]">
                                <div class="flex flex-row gap-4 items-center">
                                    <div class="flex flex-col gap-1 items-center justify-center">
                                        @if($user->patient->image)
                                            <img class="w-[56px] h-[56px] rounded-full object-cover"
                                                 src="{{asset_gcs($user->patient->image)}}" alt="Image">
                                        @else
                                            <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
                                                <rect x="1.25" y="1.25" width="57.5" height="57.5" stroke="white" stroke-width="1.5"/>
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M45.5992 46.5599C50.0017 42.4113 52.75 36.5266 52.75 30C52.75 17.4355 42.5645 7.25 30 7.25C17.4355 7.25 7.25 17.4355 7.25 30C7.25 36.5266 9.99832 42.4113 14.4008 46.5599C18.4736 50.3979 23.9621 52.75 30 52.75C36.0379 52.75 41.5264 50.3979 45.5992 46.5599ZM16.3386 43.562C19.5458 39.5615 24.4737 37 30 37C35.5263 37 40.4542 39.5615 43.6614 43.562C40.174 47.0748 35.341 49.25 30 49.25C24.659 49.25 19.826 47.0748 16.3386 43.562ZM38.75 23C38.75 27.8325 34.8325 31.75 30 31.75C25.1675 31.75 21.25 27.8325 21.25 23C21.25 18.1675 25.1675 14.25 30 14.25C34.8325 14.25 38.75 18.1675 38.75 23Z" fill="#98A2B3"/>
                                            </svg>
                                        @endif
                                        <div class="flex items-center gap-3">
                                            @if(!$user->patient->is_complete_data)
                                                <div data-hs-overlay="#hs-large-modal-{{$user->patient->uuid}}">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" class="hover:cursor-pointer">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M20.6705 3.3295C20.2312 2.89017 19.5188 2.89017 19.0795 3.3295L17.9223 4.48667L19.5133 6.07766L20.6705 4.9205C21.1098 4.48116 21.1098 3.76884 20.6705 3.3295ZM18.4527 7.13832L16.8617 5.54733L8.46085 13.9482C8.02029 14.3887 7.69644 14.9321 7.51857 15.5292L7.11458 16.8854L8.47078 16.4814C9.0679 16.3036 9.61129 15.9797 10.0519 15.5391L18.4527 7.13832ZM18.0188 2.26884C19.044 1.24372 20.706 1.24372 21.7312 2.26884C22.7563 3.29397 22.7563 4.95603 21.7312 5.98116L11.1125 16.5998C10.4957 17.2166 9.73498 17.67 8.899 17.919L6.21411 18.7188C5.95019 18.7974 5.6644 18.7251 5.46967 18.5303C5.27494 18.3356 5.20259 18.0498 5.28121 17.7859L6.08099 15.101C6.33001 14.265 6.7834 13.5043 7.40019 12.8875L18.0188 2.26884ZM5.25 6.74999C4.42157 6.74999 3.75 7.42156 3.75 8.24999V18.75C3.75 19.5784 4.42157 20.25 5.25 20.25H15.75C16.5784 20.25 17.25 19.5784 17.25 18.75V14C17.25 13.5858 17.5858 13.25 18 13.25C18.4142 13.25 18.75 13.5858 18.75 14V18.75C18.75 20.4068 17.4069 21.75 15.75 21.75H5.25C3.59315 21.75 2.25 20.4068 2.25 18.75V8.24999C2.25 6.59314 3.59315 5.24999 5.25 5.24999H10C10.4142 5.24999 10.75 5.58578 10.75 5.99999C10.75 6.4142 10.4142 6.74999 10 6.74999H5.25Z" fill="#0D4D8B"/>
                                                    </svg>
                                                </div>
                                            @endif
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-1">
                                        <div class="flex gap-1">
                                            @if($user->patient->verified_at || $user->patient->mr_no)
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                                                </svg>
                                            @endif
                                                <span class="text-sm text-[#344054] linguise_patient_name">{{ $user->patient->fullname }}</span>
                                        </div>
                                        @if($user->patient->dob)
                                            <span class="text-sm text-[#475467]">{{ \Carbon\Carbon::parse($user->patient->dob)->format('d F Y') }}</span>
                                        @endif
                                        <span class="text-sm text-[#475467]">{{ $user->patient->contact_with_code }}</span>
                                        @if(!$user->patient->is_mandatory_complete)
                                        <div class="flex flex-row gap-2 py-1 px-3 bg-[#F79009] text-white items-center rounded-full hover:cursor-pointer edit-profile">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M7 2.1875C4.34213 2.1875 2.1875 4.34213 2.1875 7C2.1875 9.65787 4.34213 11.8125 7 11.8125C9.65787 11.8125 11.8125 9.65787 11.8125 7C11.8125 4.34213 9.65787 2.1875 7 2.1875ZM1.3125 7C1.3125 3.85888 3.85888 1.3125 7 1.3125C10.1411 1.3125 12.6875 3.85888 12.6875 7C12.6875 10.1411 10.1411 12.6875 7 12.6875C3.85888 12.6875 1.3125 10.1411 1.3125 7ZM7 4.8125C7.24162 4.8125 7.4375 5.00838 7.4375 5.25V7.4375C7.4375 7.67912 7.24162 7.875 7 7.875C6.75838 7.875 6.5625 7.67912 6.5625 7.4375V5.25C6.5625 5.00838 6.75838 4.8125 7 4.8125ZM6.5625 9.1875C6.5625 8.94588 6.75838 8.75 7 8.75H7.00437C7.246 8.75 7.44187 8.94588 7.44187 9.1875V9.19187C7.44187 9.4335 7.246 9.62937 7.00437 9.62937H7C6.75838 9.62937 6.5625 9.4335 6.5625 9.19187V9.1875Z" fill="white" />
                                            </svg>
                                            <span class="text-xs lg:text-sm font-light">Mandatory Data is incomplete, <b data-hs-overlay="#hs-large-modal-{{$user->patient->uuid}}" wire:click.stop="">click here to update</b></span>
                                        </div>
                                        @elseif(!$user->patient->is_complete_data)
                                            <div class="flex flex-row gap-2 py-1 px-3 bg-[#F79009] text-white items-center rounded-full hover:cursor-pointer">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7 2.1875C4.34213 2.1875 2.1875 4.34213 2.1875 7C2.1875 9.65787 4.34213 11.8125 7 11.8125C9.65787 11.8125 11.8125 9.65787 11.8125 7C11.8125 4.34213 9.65787 2.1875 7 2.1875ZM1.3125 7C1.3125 3.85888 3.85888 1.3125 7 1.3125C10.1411 1.3125 12.6875 3.85888 12.6875 7C12.6875 10.1411 10.1411 12.6875 7 12.6875C3.85888 12.6875 1.3125 10.1411 1.3125 7ZM7 4.8125C7.24162 4.8125 7.4375 5.00838 7.4375 5.25V7.4375C7.4375 7.67912 7.24162 7.875 7 7.875C6.75838 7.875 6.5625 7.67912 6.5625 7.4375V5.25C6.5625 5.00838 6.75838 4.8125 7 4.8125ZM6.5625 9.1875C6.5625 8.94588 6.75838 8.75 7 8.75H7.00437C7.246 8.75 7.44187 8.94588 7.44187 9.1875V9.19187C7.44187 9.4335 7.246 9.62937 7.00437 9.62937H7C6.75838 9.62937 6.5625 9.4335 6.5625 9.19187V9.1875Z" fill="white"/>
                                                </svg>
                                                <span class="text-xs lg:text-sm font-light">Data is incomplete</span>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                                <div class="flex items-center gap-3">
                                    <div class="h-4 w-4 bg-[#F2F4F7] border border-primary-blue rounded-full hover:cursor-pointer" id="self-id-inactive-{{$user->patient->id}}">
                                    </div>
                                    <div class="hidden" id="self-id-active-{{$user->patient->id}}">
                                        <div class="h-4 w-4 bg-[#0D4D8B] rounded-full hover:cursor-pointer flex items-center justify-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none" class="">
                                                <path d="M8.33268 2.5L3.74935 7.08333L1.66602 5" stroke="white" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col gap-3 mt-8">
                        @if($patients->count() > 0)
                            <div class="grid grid-cols-2 items-center">
                                <span class="text-base font-medium text-[#09335D]">Others</span>
                                <div class="py-2 lg:py-4 px-2 lg:px-0 border border-[#0D4D8B] flex flex-row gap-1 lg:gap-3
                        rounded-xl justify-center hover:cursor-pointer w-full items-center new-form" data-hs-overlay="#hs-large-modal">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M11.9996 6.00039C11.9996 7.98862 10.3878 9.60039 8.39961 9.60039C6.41138 9.60039 4.79961 7.98862 4.79961 6.00039C4.79961 4.01217 6.41138 2.40039 8.39961 2.40039C10.3878 2.40039 11.9996 4.01217 11.9996 6.00039Z" fill="#0D4D8B"/>
                                        <path d="M1.93794 19.714C1.46118 19.4087 1.18503 18.8656 1.25487 18.3039C1.6966 14.7508 4.72693 12.0004 8.39949 12.0004C12.072 12.0004 15.1024 14.75 15.5441 18.303C15.614 18.8648 15.3378 19.4078 14.861 19.7131C12.9959 20.9074 10.7785 21.6004 8.39949 21.6004C6.02045 21.6004 3.80312 20.9081 1.93794 19.714Z" fill="#0D4D8B"/>
                                        <path d="M19.4996 6.90039C19.4996 6.40333 19.0967 6.00039 18.5996 6.00039C18.1026 6.00039 17.6996 6.40333 17.6996 6.90039V9.30039H15.2996C14.8026 9.30039 14.3996 9.70333 14.3996 10.2004C14.3996 10.6974 14.8026 11.1004 15.2996 11.1004H17.6996V13.5004C17.6996 13.9974 18.1026 14.4004 18.5996 14.4004C19.0967 14.4004 19.4996 13.9974 19.4996 13.5004V11.1004H21.8996C22.3967 11.1004 22.7996 10.6974 22.7996 10.2004C22.7996 9.70333 22.3967 9.30039 21.8996 9.30039H19.4996V6.90039Z" fill="#0D4D8B"/>
                                    </svg>
                                    <span class="text-[#0D4D8B] text-sm lg:text-base font-medium rounded-xl">Add New Profile</span>
                                </div>
                            </div>
                        @else
                            <span class="text-base font-medium text-[#09335D]">Others</span>
                            <div class="flex flex-col gap-2 items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.25 6.75C8.25 4.67893 9.92893 3 12 3C14.0711 3 15.75 4.67893 15.75 6.75C15.75 8.82107 14.0711 10.5 12 10.5C9.92893 10.5 8.25 8.82107 8.25 6.75Z" fill="url(#paint0_linear_5196_50573)"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M15.75 9.75C15.75 8.09315 17.0931 6.75 18.75 6.75C20.4069 6.75 21.75 8.09315 21.75 9.75C21.75 11.4069 20.4069 12.75 18.75 12.75C17.0931 12.75 15.75 11.4069 15.75 9.75Z" fill="url(#paint1_linear_5196_50573)"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M2.25 9.75C2.25 8.09315 3.59315 6.75 5.25 6.75C6.90685 6.75 8.25 8.09315 8.25 9.75C8.25 11.4069 6.90685 12.75 5.25 12.75C3.59315 12.75 2.25 11.4069 2.25 9.75Z" fill="url(#paint2_linear_5196_50573)"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.30986 15.1175C7.50783 13.2444 9.60835 12 12 12C14.3919 12 16.4927 13.2447 17.6906 15.1182C18.5187 16.4134 18.877 17.9752 18.709 19.4979C18.6827 19.7359 18.5444 19.947 18.3368 20.0662C16.4694 21.1376 14.3051 21.75 12 21.75C9.69492 21.75 7.53059 21.1376 5.66325 20.0662C5.45559 19.947 5.3173 19.7359 5.29103 19.4979C5.12293 17.9749 5.48141 16.4129 6.30986 15.1175Z" fill="url(#paint3_linear_5196_50573)"/>
                                    <path d="M5.08228 14.2537C5.07024 14.2722 5.05827 14.2907 5.04638 14.3093C4.08091 15.8189 3.63908 17.6167 3.77471 19.389C3.16667 19.2967 2.5767 19.1481 2.01043 18.9487L1.89547 18.9082C1.68576 18.8343 1.53923 18.6439 1.52159 18.4222L1.51192 18.3007C1.50402 18.2014 1.5 18.1011 1.5 18C1.5 15.9851 3.08905 14.3414 5.08228 14.2537Z" fill="url(#paint4_linear_5196_50573)"/>
                                    <path d="M20.2256 19.389C20.3612 17.617 19.9196 15.8196 18.9545 14.3102C18.9424 14.2913 18.9303 14.2725 18.9181 14.2537C20.9111 14.3416 22.5 15.9853 22.5 18C22.5 18.1011 22.496 18.2014 22.4881 18.3007L22.4784 18.4222C22.4608 18.6439 22.3142 18.8343 22.1045 18.9082L21.9896 18.9487C21.4234 19.1481 20.8336 19.2966 20.2256 19.389Z" fill="url(#paint5_linear_5196_50573)"/>
                                    <defs>
                                        <linearGradient id="paint0_linear_5196_50573" x1="1.45958" y1="-5.29327" x2="20.4591" y2="31.3759" gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#BDD8F2"/>
                                            <stop offset="1" stop-color="#F4FFE6" stop-opacity="0.5"/>
                                        </linearGradient>
                                        <linearGradient id="paint1_linear_5196_50573" x1="1.45958" y1="-5.29327" x2="20.4591" y2="31.3759" gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#BDD8F2"/>
                                            <stop offset="1" stop-color="#F4FFE6" stop-opacity="0.5"/>
                                        </linearGradient>
                                        <linearGradient id="paint2_linear_5196_50573" x1="1.45958" y1="-5.29327" x2="20.4591" y2="31.3759" gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#BDD8F2"/>
                                            <stop offset="1" stop-color="#F4FFE6" stop-opacity="0.5"/>
                                        </linearGradient>
                                        <linearGradient id="paint3_linear_5196_50573" x1="1.45958" y1="-5.29327" x2="20.4591" y2="31.3759" gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#BDD8F2"/>
                                            <stop offset="1" stop-color="#F4FFE6" stop-opacity="0.5"/>
                                        </linearGradient>
                                        <linearGradient id="paint4_linear_5196_50573" x1="1.45958" y1="-5.29327" x2="20.4591" y2="31.3759" gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#BDD8F2"/>
                                            <stop offset="1" stop-color="#F4FFE6" stop-opacity="0.5"/>
                                        </linearGradient>
                                        <linearGradient id="paint5_linear_5196_50573" x1="1.45958" y1="-5.29327" x2="20.4591" y2="31.3759" gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#BDD8F2"/>
                                            <stop offset="1" stop-color="#F4FFE6" stop-opacity="0.5"/>
                                        </linearGradient>
                                    </defs>
                                </svg>
                                <span class="text-sm font-light text-[#98A2B3]">You haven't added any profile for others</span>
                            </div>
                            <div class="py-4 border border-[#0D4D8B] flex flex-row gap-3
                        rounded-xl justify-center hover:cursor-pointer new-form" data-hs-overlay="#hs-large-modal">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M11.9996 6.00039C11.9996 7.98862 10.3878 9.60039 8.39961 9.60039C6.41138 9.60039 4.79961 7.98862 4.79961 6.00039C4.79961 4.01217 6.41138 2.40039 8.39961 2.40039C10.3878 2.40039 11.9996 4.01217 11.9996 6.00039Z" fill="#0D4D8B"/>
                                    <path d="M1.93794 19.714C1.46118 19.4087 1.18503 18.8656 1.25487 18.3039C1.6966 14.7508 4.72693 12.0004 8.39949 12.0004C12.072 12.0004 15.1024 14.75 15.5441 18.303C15.614 18.8648 15.3378 19.4078 14.861 19.7131C12.9959 20.9074 10.7785 21.6004 8.39949 21.6004C6.02045 21.6004 3.80312 20.9081 1.93794 19.714Z" fill="#0D4D8B"/>
                                    <path d="M19.4996 6.90039C19.4996 6.40333 19.0967 6.00039 18.5996 6.00039C18.1026 6.00039 17.6996 6.40333 17.6996 6.90039V9.30039H15.2996C14.8026 9.30039 14.3996 9.70333 14.3996 10.2004C14.3996 10.6974 14.8026 11.1004 15.2996 11.1004H17.6996V13.5004C17.6996 13.9974 18.1026 14.4004 18.5996 14.4004C19.0967 14.4004 19.4996 13.9974 19.4996 13.5004V11.1004H21.8996C22.3967 11.1004 22.7996 10.6974 22.7996 10.2004C22.7996 9.70333 22.3967 9.30039 21.8996 9.30039H19.4996V6.90039Z" fill="#0D4D8B"/>
                                </svg>
                                <span class="text-[#0D4D8B] text-base font-medium rounded-xl">Add New Profile</span>
                            </div>
                        @endif

                            <div class="{{ $patients->count() > 1 ? 'overflow-scroll h-[300px]' : 'h-[150px]' }} gap-2 flex flex-col">
                            @foreach($patients as $patient)
                                @if($patient->id == $user->patient->id)
                                    @continue
                                @endif
                                <div class="hover:cursor-pointer">
                                    <div id="card-patient-{{$patient->id}}" class="hover:cursor-pointer flex flex-row justify-between border rounded-xl p-4 items-center mt-1 hover:border-[#0D4D8B]"
                                         wire:click="onHandleSelectedPatient('{{ $patient->id }}')">
                                        <div class="flex flex-row gap-4 items-center">
                                            <div class="flex flex-col gap-1 items-center justify-center">
                                                @if($patient->image)
                                                    <img class="w-[56px] h-[56px] rounded-full object-cover"
                                                         src="{{asset_gcs($patient->image)}}" alt="Image">
                                                @else
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
                                                        <rect x="1.25" y="1.25" width="57.5" height="57.5" stroke="white" stroke-width="1.5"/>
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M45.5992 46.5599C50.0017 42.4113 52.75 36.5266 52.75 30C52.75 17.4355 42.5645 7.25 30 7.25C17.4355 7.25 7.25 17.4355 7.25 30C7.25 36.5266 9.99832 42.4113 14.4008 46.5599C18.4736 50.3979 23.9621 52.75 30 52.75C36.0379 52.75 41.5264 50.3979 45.5992 46.5599ZM16.3386 43.562C19.5458 39.5615 24.4737 37 30 37C35.5263 37 40.4542 39.5615 43.6614 43.562C40.174 47.0748 35.341 49.25 30 49.25C24.659 49.25 19.826 47.0748 16.3386 43.562ZM38.75 23C38.75 27.8325 34.8325 31.75 30 31.75C25.1675 31.75 21.25 27.8325 21.25 23C21.25 18.1675 25.1675 14.25 30 14.25C34.8325 14.25 38.75 18.1675 38.75 23Z" fill="#98A2B3"/>
                                                    </svg>
                                                @endif
                                                <div class="flex flex-row items-center gap-2">
                                                    <div data-hs-overlay="#hs-large-modal-{{$patient->uuid}}">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" class="hover:cursor-pointer">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M20.6705 3.3295C20.2312 2.89017 19.5188 2.89017 19.0795 3.3295L17.9223 4.48667L19.5133 6.07766L20.6705 4.9205C21.1098 4.48116 21.1098 3.76884 20.6705 3.3295ZM18.4527 7.13832L16.8617 5.54733L8.46085 13.9482C8.02029 14.3887 7.69644 14.9321 7.51857 15.5292L7.11458 16.8854L8.47078 16.4814C9.0679 16.3036 9.61129 15.9797 10.0519 15.5391L18.4527 7.13832ZM18.0188 2.26884C19.044 1.24372 20.706 1.24372 21.7312 2.26884C22.7563 3.29397 22.7563 4.95603 21.7312 5.98116L11.1125 16.5998C10.4957 17.2166 9.73498 17.67 8.899 17.919L6.21411 18.7188C5.95019 18.7974 5.6644 18.7251 5.46967 18.5303C5.27494 18.3356 5.20259 18.0498 5.28121 17.7859L6.08099 15.101C6.33001 14.265 6.7834 13.5043 7.40019 12.8875L18.0188 2.26884ZM5.25 6.74999C4.42157 6.74999 3.75 7.42156 3.75 8.24999V18.75C3.75 19.5784 4.42157 20.25 5.25 20.25H15.75C16.5784 20.25 17.25 19.5784 17.25 18.75V14C17.25 13.5858 17.5858 13.25 18 13.25C18.4142 13.25 18.75 13.5858 18.75 14V18.75C18.75 20.4068 17.4069 21.75 15.75 21.75H5.25C3.59315 21.75 2.25 20.4068 2.25 18.75V8.24999C2.25 6.59314 3.59315 5.24999 5.25 5.24999H10C10.4142 5.24999 10.75 5.58578 10.75 5.99999C10.75 6.4142 10.4142 6.74999 10 6.74999H5.25Z" fill="#0D4D8B"/>
                                                        </svg>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex flex-col gap-1">
                                                <div class="flex gap-1 items-center">
                                                    @if($patient->verified_at || $patient->mr_no)
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                                                        </svg>
                                                    @endif
                                                    <span class="text-base font-light text-[#344054] linguise_patient_name">{{ $patient->fullname }}</span>
                                                    <span class="text-base font-light text-[#344054] {{ $patient->relation_patient ? '' : 'hidden' }}">({{\App\Enums\Table\Patient\RelationPatient::getLabel($patient->relation_patient)}})</span>
                                                </div>
                                                @if($patient->dob)
                                                    <span class="text-sm text-[#475467]">{{ \Carbon\Carbon::parse($patient->dob)->format('d F Y') }}</span>
                                                @endif
                                                <span class="text-sm text-[#475467]">{{ $patient->contact_with_code }}</span>
                                                @if(!$patient->is_complete_data)
                                                    <div class="flex flex-row gap-2 py-1 px-3 bg-[#F79009] text-white items-center rounded-full hover:cursor-pointer">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M7 2.1875C4.34213 2.1875 2.1875 4.34213 2.1875 7C2.1875 9.65787 4.34213 11.8125 7 11.8125C9.65787 11.8125 11.8125 9.65787 11.8125 7C11.8125 4.34213 9.65787 2.1875 7 2.1875ZM1.3125 7C1.3125 3.85888 3.85888 1.3125 7 1.3125C10.1411 1.3125 12.6875 3.85888 12.6875 7C12.6875 10.1411 10.1411 12.6875 7 12.6875C3.85888 12.6875 1.3125 10.1411 1.3125 7ZM7 4.8125C7.24162 4.8125 7.4375 5.00838 7.4375 5.25V7.4375C7.4375 7.67912 7.24162 7.875 7 7.875C6.75838 7.875 6.5625 7.67912 6.5625 7.4375V5.25C6.5625 5.00838 6.75838 4.8125 7 4.8125ZM6.5625 9.1875C6.5625 8.94588 6.75838 8.75 7 8.75H7.00437C7.246 8.75 7.44187 8.94588 7.44187 9.1875V9.19187C7.44187 9.4335 7.246 9.62937 7.00437 9.62937H7C6.75838 9.62937 6.5625 9.4335 6.5625 9.19187V9.1875Z" fill="white"/>
                                                        </svg>
                                                        <span class="text-sm font-light">Data is incomplete</span>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="flex flex-row items-center gap-2">
                                            <div class="h-4 w-4 bg-[#F2F4F7] border border-primary-blue rounded-full hover:cursor-pointer" id="self-id-inactive-{{$patient->id}}">
                                            </div>
                                            <div class="hidden" id="self-id-active-{{$patient->id}}">
                                                <div class="h-4 w-4 bg-[#0D4D8B] rounded-full hover:cursor-pointer flex items-center justify-center">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10" fill="none" class="">
                                                        <path d="M8.33268 2.5L3.74935 7.08333L1.66602 5" stroke="white" stroke-width="1.66667" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>


                    </div>
                </div>
                <div x-data="{ PatientSelected: false }"
                    x-on:appointment-patient-selected.window="PatientSelected = $event.detail.PatientSelected">
                    <div
                        wire:click="continue"
                        class="py-4 flex flex-row gap-3 rounded-xl justify-center mt-8"
                        :class="PatientSelected ? 'bg-[#0D4D8B] cursor-pointer' : 'bg-gray-400 opacity-50 cursor-not-allowed'"
                        :disabled="!PatientSelected">
                        <span class="text-white text-base font-medium rounded-xl">Select</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="flex flex-col md:mt-10 md:pt-8 md:px-6 pb-10 gap-4 bg-white bg-opacity-50 rounded-3xl z-10 relative">
        <div class="text-sm md:text-base flex flex-row gap-1 items-center">
            <span>Patient Profile</span>
            <span class="text-red-500">*</span>
        </div>
        @if($isSelectedPatient)
            <div class="border rounded-xl p-4 items-center bg-white">
                <div class="flex flex-col md:flex-row justify-between gap-2 md:gap-0">
                    <div class="flex flex-col">
                        <span class="text-[#667085] text-base font-medium">{{$user->patient->id == $selectedPatient->id ? 'Myself' : 'Others'}}</span>
                        <div class="flex flex-row gap-4 items-center">
                            @if($selectedPatient->image)
                                <img class="w-[56px] h-[56px] rounded-full object-cover"
                                     src="{{asset_gcs($selectedPatient->image)}}" alt="Image">
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
                                    <rect x="1.25" y="1.25" width="57.5" height="57.5" stroke="white" stroke-width="1.5"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M45.5992 46.5599C50.0017 42.4113 52.75 36.5266 52.75 30C52.75 17.4355 42.5645 7.25 30 7.25C17.4355 7.25 7.25 17.4355 7.25 30C7.25 36.5266 9.99832 42.4113 14.4008 46.5599C18.4736 50.3979 23.9621 52.75 30 52.75C36.0379 52.75 41.5264 50.3979 45.5992 46.5599ZM16.3386 43.562C19.5458 39.5615 24.4737 37 30 37C35.5263 37 40.4542 39.5615 43.6614 43.562C40.174 47.0748 35.341 49.25 30 49.25C24.659 49.25 19.826 47.0748 16.3386 43.562ZM38.75 23C38.75 27.8325 34.8325 31.75 30 31.75C25.1675 31.75 21.25 27.8325 21.25 23C21.25 18.1675 25.1675 14.25 30 14.25C34.8325 14.25 38.75 18.1675 38.75 23Z" fill="#98A2B3"/>
                                </svg>
                            @endif
                            <div class="flex flex-col gap-1">
                                <div class="flex gap-1 items-center">
                                    @if($selectedPatient->verified_at || $selectedPatient->mr_no)
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                                        </svg>
                                    @endif
                                    <span class="text-base font-light text-[#344054] linguise_patient_name">{{ $selectedPatient->fullname }}</span>
                                    @if($user->patient->id != $selectedPatient->id)
                                        <span class="text-base font-light text-[#344054] {{ $selectedPatient->relation_patient ? '' : 'hidden' }}">({{\App\Enums\Table\Patient\RelationPatient::getLabel($selectedPatient->relation_patient)}})</span>
                                    @endif
                                </div>
                                @if($selectedPatient->dob)
                                    <span class="text-sm text-[#475467]">{{ \Carbon\Carbon::parse($selectedPatient->dob)->format('d F Y') }}</span>
                                @endif
                                @if($selectedPatient->contact_no)
                                    <span class="text-sm text-[#475467]">{{ $selectedPatient->contact_with_code ?? '-' }}</span>
                                @endif
                                @if(!$user->patient->is_complete_data)
                                    <div class="flex flex-row gap-2 py-1 px-3 bg-[#F79009] text-white items-center rounded-full hover:cursor-pointer">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M7 2.1875C4.34213 2.1875 2.1875 4.34213 2.1875 7C2.1875 9.65787 4.34213 11.8125 7 11.8125C9.65787 11.8125 11.8125 9.65787 11.8125 7C11.8125 4.34213 9.65787 2.1875 7 2.1875ZM1.3125 7C1.3125 3.85888 3.85888 1.3125 7 1.3125C10.1411 1.3125 12.6875 3.85888 12.6875 7C12.6875 10.1411 10.1411 12.6875 7 12.6875C3.85888 12.6875 1.3125 10.1411 1.3125 7ZM7 4.8125C7.24162 4.8125 7.4375 5.00838 7.4375 5.25V7.4375C7.4375 7.67912 7.24162 7.875 7 7.875C6.75838 7.875 6.5625 7.67912 6.5625 7.4375V5.25C6.5625 5.00838 6.75838 4.8125 7 4.8125ZM6.5625 9.1875C6.5625 8.94588 6.75838 8.75 7 8.75H7.00437C7.246 8.75 7.44187 8.94588 7.44187 9.1875V9.19187C7.44187 9.4335 7.246 9.62937 7.00437 9.62937H7C6.75838 9.62937 6.5625 9.4335 6.5625 9.19187V9.1875Z" fill="white"/>
                                        </svg>
                                        <span class="text-sm font-light">Data is incomplete</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div wire:click="onHandleSelectProfile" class="flex flex-row items-center gap-1 hover:cursor-pointer">
                        <span class="text-base font-medium text-[#0D4D8B]">Change Profile</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.21967 5.21967C8.51256 4.92678 8.98744 4.92678 9.28033 5.21967L13.5303 9.46967C13.8232 9.76256 13.8232 10.2374 13.5303 10.5303L9.28033 14.7803C8.98744 15.0732 8.51256 15.0732 8.21967 14.7803C7.92678 14.4874 7.92678 14.0126 8.21967 13.7197L11.9393 10L8.21967 6.28033C7.92678 5.98744 7.92678 5.51256 8.21967 5.21967Z" fill="#8FC640"/>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-6">
                <div class="flex flex-col">
                    <span class="text-xs font-light text-[#667085]">Patient Name</span>
                    <span class="text-base font-light text-[#101828] linguise_patient_name">{{ $selectedPatient->fullname ?? '-' }}</span>
                </div>
                <div class="flex flex-col">
                    <span class="text-xs font-light text-[#667085]">Identity Number (KTP)</span>
                    <span class="text-base font-light text-[#101828]">{{ $selectedPatient->ktp_number ?? '-' }}</span>
                </div>
                <div class="flex flex-col">
                    <span class="text-xs font-light text-[#667085]">Date of Birth</span>
                    <span class="text-base font-light text-[#101828]">{{ \Carbon\Carbon::parse($selectedPatient->dob)->format('d/m/Y') }}</span>
                </div>
                <div class="flex flex-col">
                    <span class="text-xs font-light text-[#667085]">Gender</span>
                    <span class="text-base font-light text-[#101828]">{{ $selectedPatient->gender_label ?? '-' }}</span>
                </div>
                <div class="flex flex-col gap-2">
                    <span class="text-sm font-light text-[#1D2939]">Phone Number</span>
                    <span class="text-base font-light text-[#101828]">{{ $selectedPatient->contact_with_code ?? '-' }}</span>
                </div>
                <div class="flex flex-col gap-2">
                    <span class="text-sm font-light text-[#1D2939]">Email</span>
                    <span class="text-base font-light text-[#101828]">{{ $selectedPatient->email ?? '-' }}</span>
                </div>
            </div>
            <div class="border border-[#B54708] rounded-xl py-3 px-4 bg-[#FFFAEB]">
                <div class="flex flex-row gap-4">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M14.4876 12.0005L9.15426 2.66714C9.03797 2.46194 8.86933 2.29127 8.66555 2.17252C8.46176 2.05378 8.23012 1.99121 7.99426 1.99121C7.7584 1.99121 7.52677 2.05378 7.32298 2.17252C7.11919 2.29127 6.95055 2.46194 6.83426 2.66714L1.50093 12.0005C1.38338 12.204 1.32175 12.4351 1.32227 12.6701C1.32279 12.9052 1.38545 13.136 1.50389 13.339C1.62234 13.5421 1.79236 13.7102 1.99673 13.8264C2.20109 13.9425 2.43253 14.0026 2.6676 14.0005H13.3343C13.5682 14.0002 13.7979 13.9385 14.0005 13.8213C14.203 13.7042 14.3711 13.5359 14.4879 13.3332C14.6048 13.1306 14.6663 12.9007 14.6662 12.6668C14.6662 12.4329 14.6046 12.2031 14.4876 12.0005Z" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 6V8.66667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 11.333H8.00667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="flex flex-col text-[#93370D] gap-1 text-sm md:text-base">
                        <span class="font-semibold text-[#B54708]">Cannot update the personal information</span>
                        <span class="">To ensure the protection of your data, please contact our hospital staff to update any personal information you’ve previously saved. </span>
                    </div>
                </div>
            </div>
{{--            @if($user->patient->id != $selectedPatient->id)--}}
{{--                <div wire:click="onHandleIsConsent" class="flex flex-row gap-3 items-center hover:cursor-pointer">--}}
{{--                    <div class="flex items-center justify-center border border-[#344054] h-[20px] w-[20px] p-1 rounded-md {{ $isConsent ? 'rounded-md bg-[#0D4D8B]' : '' }}">--}}
{{--                        <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">--}}
{{--                            <path d="M11.6673 3.5L5.25065 9.91667L2.33398 7" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>--}}
{{--                        </svg>--}}
{{--                    </div>--}}
{{--                    <span class="text-base font-light text-[#344054]">The patient consents to you viewing their medical resume and checkup results generated from this order.</span>--}}
{{--                </div>--}}
{{--                @if(Session::has('error_is_consent'))--}}
{{--                    <div class="text-xs text-red-500">{{ Session::get('error_is_consent') }}</div>--}}
{{--                @endif--}}
{{--            @endif--}}
            @if($appointment->type == \App\Enums\Table\AppointmentPatientSummary\Type::VISIT)
                <div class="flex flex-col gap-4">
                    <span class="text-base md:text-xl font-medium">Select Guarantor</span>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-2 md:gap-6">
                        <div wire:click="onHandleSelectedPayer('{{ \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::PERSONAL }}')" class="p-4 w-full rounded-xl border flex flex-row justify-between items-center hover:cursor-pointer
                        {{ $selectedPayer == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::PERSONAL ? 'bg-blue-50 border-[#0D4D8B] font-medium' : 'font-light'}}">
                            <span class="text-base">Private</span>
                            <div class="h-4 w-4 bg-[#F2F4F7] rounded-full justify-center flex items-center
                            {{ $selectedPayer == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::PERSONAL ? 'border border-[#0D4D8B]' : '' }}">
                                <div class="h-2 w-2 bg-[#0D4D8B] rounded-full {{ $selectedPayer == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::PERSONAL ? '' : 'hidden' }}">
                                </div>
                            </div>
                        </div>
                        <div wire:click="onHandleSelectedPayer('{{ \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::INSURANCE }}')" class="p-4 w-full rounded-xl border flex flex-row justify-between items-center hover:cursor-pointer
                        {{ $selectedPayer == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::INSURANCE ? 'bg-blue-50 border-[#0D4D8B] font-medium' : 'font-light'}}">
                            <span class="text-base">Insurance</span>
                            <div class="h-4 w-4 bg-[#F2F4F7] rounded-full justify-center flex items-center hover:cursor-pointer
                            {{ $selectedPayer == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::INSURANCE ? 'border border-[#0D4D8B]' : '' }}">
                                <div class="h-2 w-2 bg-[#0D4D8B] rounded-full {{ $selectedPayer == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::INSURANCE ? '' : 'hidden' }}">
                                </div>
                            </div>
                        </div>
                        <div wire:click="onHandleSelectedPayer('{{ \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::COMPANY }}')" class="p-4 w-full rounded-xl border flex flex-row justify-between items-center hover:cursor-pointer
                        {{ $selectedPayer == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::COMPANY ? 'bg-blue-50 border-[#0D4D8B] font-medium' : 'font-light'}}">
                            <span class="text-base">Company</span>
                            <div class="h-4 w-4 bg-[#F2F4F7] rounded-full justify-center flex items-center
                            {{ $selectedPayer == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::COMPANY ? 'border border-[#0D4D8B]' : '' }}">
                                <div class="h-2 w-2 bg-[#0D4D8B] rounded-full {{ $selectedPayer == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::COMPANY ? '' : 'hidden' }}">
                                </div>
                            </div>
                        </div>
                    </div>
{{--                    @if($selectedPayer == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::INSURANCE)--}}
{{--                        <div id="insurance-section" class="flex flex-col gap-4">--}}
{{--                            <div class="flex flex-col gap-2">--}}
{{--                                <div class="flex-row flex gap-1">--}}
{{--                                    <span class="text-base font-light">Insurance</span>--}}
{{--                                    <span class="text-base font-light text-[#F04438]">*</span>--}}
{{--                                </div>--}}
{{--                                <div class="flex flex-col">--}}
{{--                                    <div wire:click="onHandleModalInsurance" class="w-full py-[18px] px-6 border--}}
{{--                                rounded-xl flex flex-row justify-between items-center text-base font-light hover:cursor-pointer bg-white {{ Session::has('error_selected_insurance') ? 'border-red-500' : '' }}">--}}
{{--                                        {{ @$selectedInsurance['name'] ? @$selectedInsurance['name'] : 'Select Insurance'}}--}}
{{--                                        <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>--}}
{{--                                    </div>--}}
{{--                                    @if($isShowOptionInsurance)--}}
{{--                                        <div class="rounded-xl bg-white border flex flex-col p-4 gap-4 mt-2 overflow-auto h-52">--}}
{{--                                            @foreach($insurances as $insurance)--}}
{{--                                                @if($insurance->name == \App\Enums\General\GeneralString::INSURANCE_OTHER)--}}
{{--                                                    @php--}}
{{--                                                        $otherId = $insurance->id;--}}
{{--                                                        continue;--}}
{{--                                                    @endphp--}}
{{--                                                @endif--}}
{{--                                                <div wire:click="onHandleSelectedInsurance('{{$insurance->id}}','{{ $insurance->name }}')" class="hover:cursor-pointer font-light hover:font-bold">--}}
{{--                                                    <span class="text-base">{{$insurance->name}}</span>--}}
{{--                                                    <hr class="mt-2">--}}
{{--                                                </div>--}}
{{--                                            @endforeach--}}
{{--                                            <span wire:click="onHandleSelectedInsurance('{{ $otherId }}','{{\App\Enums\General\GeneralString::INSURANCE_OTHER}}')" class="text-base font-light hover:cursor-pointer hover:font-bold">Other</span>--}}
{{--                                        </div>--}}
{{--                                    @endif--}}
{{--                                </div>--}}
{{--                                @if(Session::has('error_selected_insurance'))--}}
{{--                                    <div class="text-xs text-red-500">{{ Session::get('error_selected_insurance') }}</div>--}}
{{--                                @endif--}}
{{--                            </div>--}}
{{--                            @if(@$selectedInsurance['name'] == \App\Enums\General\GeneralString::INSURANCE_OTHER)--}}
{{--                                <div class="flex flex-col gap-2">--}}
{{--                                    <div class="flex-row flex gap-1">--}}
{{--                                        <span class="text-base font-light">Other Insurance</span>--}}
{{--                                        <span class="text-base font-light text-[#F04438]">*</span>--}}
{{--                                    </div>--}}
{{--                                    <div class="flex flex-col">--}}
{{--                                        <div class="w-full items-center rounded-2xl border border-gray-200 py-[18px] px-6 flex flex-row justify-between bg-white--}}
{{--                                        {{ Session::has('error_other_insurance') ? 'border-red-500' : '' }}">--}}
{{--                                            <input type="text" class="w-full outline-none bg-transparent text-base font-light"--}}
{{--                                                   placeholder="Input Patient Insurance"--}}
{{--                                                   wire:model="otherInsuranceName">--}}
{{--                                        </div>--}}
{{--                                    </div>--}}
{{--                                    @if(Session::has('error_other_insurance'))--}}
{{--                                        <div class="text-xs text-red-500">{{ Session::get('error_other_insurance') }}</div>--}}
{{--                                    @endif--}}
{{--                                </div>--}}
{{--                            @endif--}}
{{--                            <div class="flex flex-col gap-2">--}}
{{--                                <div class="flex-row flex gap-1">--}}
{{--                                    <span class="text-base font-light">Insurance Number</span>--}}
{{--                                    <span class="text-base font-light text-[#F04438]">*</span>--}}
{{--                                </div>--}}
{{--                                <div class="flex flex-col">--}}
{{--                                    <div class="w-full items-center rounded-2xl border border-gray-200 py-[18px] px-6 flex flex-row justify-between bg-white--}}
{{--                                    {{ Session::has('error_insurance_number') ? 'border-red-500' : '' }}">--}}
{{--                                        <input type="text" class="w-full outline-none bg-transparent text-base font-light"--}}
{{--                                               placeholder="Input Patient Insurance Number"--}}
{{--                                               wire:model="insuranceNumber">--}}
{{--                                    </div>--}}
{{--                                </div>--}}
{{--                                @if(Session::has('error_insurance_number'))--}}
{{--                                    <div class="text-xs text-red-500">{{ Session::get('error_insurance_number') }}</div>--}}
{{--                                @endif--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    @endif--}}
{{--                    @if($selectedPayer == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::COMPANY)--}}
{{--                        <div id="company-section" class="flex flex-col gap-4">--}}
{{--                            <div class="flex flex-col gap-2">--}}
{{--                                <div class="flex-row flex gap-1">--}}
{{--                                    <span class="text-base font-light">Corporate Name</span>--}}
{{--                                    <span class="text-base font-light text-[#F04438]">*</span>--}}
{{--                                </div>--}}
{{--                                <div class="flex flex-col">--}}
{{--                                    <div wire:click="onHandleModalCompany" class="w-full py-[18px] px-6 border--}}
{{--                                rounded-xl flex flex-row justify-between items-center text-base font-light hover:cursor-pointer bg-white--}}
{{--                                {{Session::has('error_selected_company') ? 'border-red-500' : ''}}">--}}
{{--                                        {{ @$selectedCompany['name'] ? @$selectedCompany['name'] : 'Select Corporate'}}--}}
{{--                                        <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>--}}
{{--                                    </div>--}}
{{--                                    @if($isShowOptionCompany)--}}
{{--                                        <div class="rounded-xl bg-white border flex flex-col p-4 gap-4 mt-2 h-52 overflow-auto">--}}
{{--                                            @foreach($companies as $company)--}}
{{--                                                @if($company->name == \App\Enums\General\GeneralString::INSURANCE_OTHER)--}}
{{--                                                    @php--}}
{{--                                                        $otherId = $company->id;--}}
{{--                                                        continue;--}}
{{--                                                    @endphp--}}
{{--                                                @endif--}}
{{--                                                <div wire:click="onHandleSelectedCompany('{{$company->id}}','{{ $company->name }}')" class="hover:cursor-pointer font-light hover:font-bold">--}}
{{--                                                    <span class="text-base">{{$company->name}}</span>--}}
{{--                                                    <hr class="mt-2">--}}
{{--                                                </div>--}}
{{--                                            @endforeach--}}
{{--                                            <span wire:click="onHandleSelectedCompany('{{ $otherId }}','{{\App\Enums\General\GeneralString::INSURANCE_OTHER}}')" class="text-base font-light hover:cursor-pointer hover:font-bold">Other</span>--}}
{{--                                        </div>--}}
{{--                                    @endif--}}
{{--                                </div>--}}
{{--                                @if(Session::has('error_selected_company'))--}}
{{--                                    <div class="text-xs text-red-500">{{ Session::get('error_selected_company') }}</div>--}}
{{--                                @endif--}}
{{--                                @if(@$selectedCompany['name'] == \App\Enums\General\GeneralString::INSURANCE_OTHER)--}}
{{--                                    <div class="flex flex-col gap-2">--}}
{{--                                        <div class="flex-row flex gap-1">--}}
{{--                                            <span class="text-base font-light">Other Corporate Name</span>--}}
{{--                                            <span class="text-base font-light text-[#F04438]">*</span>--}}
{{--                                        </div>--}}
{{--                                        <div class="flex flex-col">--}}
{{--                                            <div class="w-full items-center rounded-2xl border border-gray-200 py-[18px] px-6 flex flex-row justify-between bg-white--}}
{{--                                            {{ Session::has('error_other_company') ? 'border-red-500' : '' }}">--}}
{{--                                                <input type="text" class="w-full outline-none bg-transparent text-base font-light"--}}
{{--                                                       placeholder="Input Other Corporate Name"--}}
{{--                                                       wire:model="otherCompanyName">--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
{{--                                        @if(Session::has('error_other_company'))--}}
{{--                                            <div class="text-xs text-red-500">{{ Session::get('error_other_company') }}</div>--}}
{{--                                        @endif--}}
{{--                                    </div>--}}
{{--                                @endif--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                    @endif--}}

                </div>
            @endif
        @else
            <div wire:click="onHandleSelectProfile" class="rounded-xl border flex flex-row
            justify-between p-4 items-center hover:cursor-pointer bg-white {{ Session::has('error_selected_patient') ? 'border-red-500' : '' }}">
                <div class="flex-row flex gap-4 items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
                        <rect x="1.25" y="1.25" width="57.5" height="57.5" stroke="white" stroke-width="1.5"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M45.5992 46.5599C50.0017 42.4113 52.75 36.5266 52.75 30C52.75 17.4355 42.5645 7.25 30 7.25C17.4355 7.25 7.25 17.4355 7.25 30C7.25 36.5266 9.99832 42.4113 14.4008 46.5599C18.4736 50.3979 23.9621 52.75 30 52.75C36.0379 52.75 41.5264 50.3979 45.5992 46.5599ZM16.3386 43.562C19.5458 39.5615 24.4737 37 30 37C35.5263 37 40.4542 39.5615 43.6614 43.562C40.174 47.0748 35.341 49.25 30 49.25C24.659 49.25 19.826 47.0748 16.3386 43.562ZM38.75 23C38.75 27.8325 34.8325 31.75 30 31.75C25.1675 31.75 21.25 27.8325 21.25 23C21.25 18.1675 25.1675 14.25 30 14.25C34.8325 14.25 38.75 18.1675 38.75 23Z" fill="#98A2B3"/>
                    </svg>
                    <span class="text-sm md:text-base font-light text-[Gray/600]">Select Profile</span>
                </div>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.21967 5.21967C8.51256 4.92678 8.98744 4.92678 9.28033 5.21967L13.5303 9.46967C13.8232 9.76256 13.8232 10.2374 13.5303 10.5303L9.28033 14.7803C8.98744 15.0732 8.51256 15.0732 8.21967 14.7803C7.92678 14.4874 7.92678 14.0126 8.21967 13.7197L11.9393 10L8.21967 6.28033C7.92678 5.98744 7.92678 5.51256 8.21967 5.21967Z" fill="#8FC640"/>
                </svg>
            </div>
            @if(Session::has('error_selected_patient'))
                <div class="text-xs text-red-500">{{ Session::get('error_selected_patient') }}</div>
            @endif
        @endif
    </div>
    <div class="flex flex-row justify-between gap-6 mx-auto z-10 relative">
        <div class="py-4 bg-white border-[#0D4D8B] border-2 w-full rounded-xl flex justify-center mx-auto mt-16 hover:cursor-pointer items-center"
             wire:click="onHandleBackState" wire:loading.remove wire:target="onHandleNextState">
            <span class="text-base font-medium text-[#0D4D8B]">Back</span>
        </div>
        <div class="w-full"
             wire:loading wire:target="onHandleNextState">

        </div>
        <div class="py-4 bg-[#0D4D8B] w-full rounded-xl flex justify-center mx-auto mt-16 hover:cursor-pointer items-center"
             wire:click="onHandleNextState" wire:loading.remove wire:target="onHandleNextState">
            <span class="text-base font-medium text-white">Next</span>
        </div>
        <div class="w-full"  wire:loading wire:target="onHandleNextState">
            <livewire:landing-page.component.loading-component text="Loading..." wire:key="{{ \Illuminate\Support\Str::uuid() }}"/>
        </div>
    </div>

    <div id="hs-large-modal" class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto pointer-events-none" role="dialog" tabindex="-1" aria-labelledby="hs-large-modal-label">
        <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all lg:max-w-5xl lg:w-full m-3 lg:mx-auto">
            <div class="flex flex-col bg-white border shadow-sm rounded-xl pointer-events-auto">
                <div class="flex justify-between items-center py-3 px-4">
                    <h3 id="hs-large-modal-label" class="font-bold text-gray-800">
                        Add New Profile
                    </h3>
                    <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none" aria-label="Close" data-hs-overlay="#hs-large-modal">
                        <span class="sr-only">Close</span>
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6 6 18"></path>
                            <path d="m6 6 12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="p-4 overflow-y-auto">
                    <livewire:landing-page.profile.new-profile :page="$page" wire:key="patient-{{\Illuminate\Support\Str::uuid()}}"/>
                </div>
            </div>
        </div>
    </div>

    <div id="hs-large-modal-{{$user->patient->uuid}}" class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto pointer-events-none" role="dialog" tabindex="-1" aria-labelledby="hs-large-modal-label">
        <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all lg:max-w-5xl lg:w-full m-3 lg:mx-auto">
            <div class="flex flex-col bg-white border shadow-sm rounded-xl pointer-events-auto">
                <div class="flex justify-between items-center py-3 px-4">
                    <h3 id="hs-large-modal-label" class="font-bold text-gray-800 linguise_patient_name">
                        Edit Profile {{ $user->patient->first_name . ' ' . $user->patient->last_name }}
                    </h3>
                    <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none" aria-label="Close" data-hs-overlay="#hs-large-modal-{{$user->patient->uuid}}">
                        <span class="sr-only">Close</span>
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6 6 18"></path>
                            <path d="m6 6 12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="p-4 overflow-y-auto">
                    <livewire:landing-page.profile.new-profile :page="$page" :patient_id="$user->patient->id" wire:key="patient-{{$user->patient->uuid}}"/>
                </div>
            </div>
        </div>
    </div>

    @foreach($patients as $patient)
        <div id="hs-large-modal-{{$patient->uuid}}" class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto pointer-events-none" role="dialog" tabindex="-1" aria-labelledby="hs-large-modal-label">
            <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all lg:max-w-5xl lg:w-full m-3 lg:mx-auto">
                <div class="flex flex-col bg-white border shadow-sm rounded-xl pointer-events-auto">
                    <div class="flex justify-between items-center py-3 px-4">
                        <h3 id="hs-large-modal-label" class="font-bold text-gray-800 linguise_patient_name">
                            Edit Profile {{ $patient->first_name . ' ' . $patient->last_name }}
                        </h3>
                        <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none" aria-label="Close" data-hs-overlay="#hs-large-modal-{{$patient->uuid}}">
                            <span class="sr-only">Close</span>
                            <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                        </button>
                    </div>
                    @php
                        $page = request()->fullUrl();
                    @endphp
                    <div class="p-4 overflow-y-auto">
                        <livewire:landing-page.profile.new-profile :page="$page" :patient_id="$patient->id" wire:key="patient-{{$patient->uuid}}"/>
                    </div>
                </div>
            </div>
        </div>
    @endforeach
</div>

@script
    <script>
        $wire.on('appointment-patient-selected', (event) => {
            event['allPatientIds'].forEach(function (id) {
                if(id === event['id']){
                    return;
                }
                document.getElementById('self-id-active-'+id).style.display = 'none';
                document.getElementById('self-id-inactive-'+id).style.display = 'block';
                var card = document.getElementById("card-patient-"+id);
                card.className = "flex flex-row justify-between border rounded-xl p-4 items-center hover:border-[#0D4D8B]";
            });
            document.getElementById('self-id-active-'+event['id']).style.display = 'block';
            document.getElementById('self-id-inactive-'+event['id']).style.display = 'none';
            var card = document.getElementById("card-patient-"+event['id']);
            card.className = "flex flex-row justify-between border rounded-xl p-4 items-center border-[#0D4D8B] bg-[#E5F2FF]";
        });
        $wire.on('clearance', (event) => {
            const hsOverlay = document.querySelectorAll('.hs-overlay.opened')
            HSOverlay.close(`#${hsOverlay[0].id}`);
        });
    </script>
@endscript

@push('script')
    <script>
        window.onload = function() {
            // Scroll to the top of the page
            window.scrollTo(0, 0);
        };

        // function onClickOpenFile(url){
        //     window.open(url, '_blank');
        // }
    </script>
@endpush
