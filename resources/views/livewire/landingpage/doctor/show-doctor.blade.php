<div>
    <div class="h-32">
    </div>
    <div class="rounded-b-3xl relative">
        <div class="absolute mx-auto blur-3xl opacity-50 w-1/2 h-full inset-0 z-1
            bg-gradient-to-b from-green-200 via-cyan-100 to-green-100 rounded-b-3xl"
             style="">
        </div>
        <div class="absolute mx-auto blur-3xl opacity-30 w-1/2 h-full inset-0 z-0
            bg-gradient-to-b from-blue-400 via-cyan-100 to-green-100 rounded-b-3xl"
             style="top: 30%; left: -20%">
        </div>
        <div class="relative z-1">
            <div class="flex flex-row px-4 md:px-40 py-4 gap-1 md:gap-4 items-center font-light text-xs md:text-base ">
                <a href="/" class="text-blue-800">Home</a>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <a href="{{route('doctors.index')}}" class="text-blue-800 hidden md:block">Find a Doctor</a>
                <svg class="hidden md:block" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="linguise_doctor_name"> {{ $doctor->name }} </span>
            </div>
        </div>
    </div>
    <div class="mx-4 md:mx-40 md:mt-10">
        <div class="md:flex-row flex-col flex gap-6">
            <div class="md:w-1/3 rounded-3xl flex flex-col gap-3">
                <div class="block md:hidden">
                    <h1 class="text-2xl md:text-3xl font-semibold linguise_doctor_name">{{ $doctor->name }}</h1>
                </div>
                <div class="flex w-full flex-col rounded-xl bg-gradient-to-b from-sky-50 via-transparent text-gray-700 mt-32 items-center">
                    <div class="relative mx-4 -mt-32 mb-4 grid h-[150px] w-[150px] md:h-[250px] md:w-[250px]
                        place-items-center overflow-hidden rounded-full">
                        <div class="">
                            @if(!$doctor->image)
                                <img class="w-[250px] object-cover" src="{{asset_gcs('public/assets/icon/318e26c9-a4f1-4301-be62-8edf42acaf4e.svg')}}" alt="Bali International Hospital">
                            @else
                                <img class="w-[250px] object-cover" src="{{asset_gcs($doctor->image)}}" alt="Image">
                            @endif
                        </div>
                    </div>
                    <div class="flex flex-col gap-2 md:gap-6 md:px-6 text-sm md:text-base font-light">
                        @if(@$doctor->profile_description)
                            <span>
                                {!! nl2br(e($doctor->profile_description)) !!}
                            </span>
                            <hr>
                        @endif
                        @if(count($doctor->languages) > 0)
                            <div class="flex flex-col gap-1 md:gap-4">
                                <span class="text-base md:text-xl font-medium">Language Spoken</span>
                                <div class="flex flex-wrap gap-3 items-center">
                                    @foreach($doctor->languages ?? [] as $language)
                                        <span class="text-sm md:text-base font-light">{{ $language->name }}</span>
                                        @if(!$loop->last)
                                            <svg xmlns="http://www.w3.org/2000/svg" width="4" height="4" viewBox="0 0 4 4" fill="none">
                                                <circle cx="2" cy="2" r="2" fill="#98A2B3"/>
                                            </svg>
                                        @endif
                                    @endforeach
                                </div>
                            </div>
                            <hr>
                        @endif
                        @if(count($doctor->medicalSchools) > 0)
                            <div class="flex flex-col gap-1 md:gap-4">
                                <span class="text-base md:text-xl font-medium">Medical School</span>
                                <div class="flex flex-col gap-3">
                                    @foreach($doctor->medicalSchools as $medicalSchool)
                                        <div class="flex items-center gap-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="4" height="4" viewBox="0 0 4 4" fill="none">
                                                <circle cx="2" cy="2" r="2" fill="#98A2B3"/>
                                            </svg>
                                            <span class="text-sm md:text-base font-light">{{ $medicalSchool->name }}, {{ $medicalSchool->year }} Years</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <hr>
                        @endif
                        @if(count($doctor->certificates) > 0)
                            <div class="flex flex-col gap-1 md:gap-4">
                                <span class="text-base md:text-xl font-medium">Board Certifications</span>
                                <div class="flex flex-col gap-3">

                                    @foreach($doctor->certificates as $certificate)
                                        <div class="flex items-center gap-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="4" height="4" viewBox="0 0 4 4" fill="none">
                                                <circle cx="2" cy="2" r="2" fill="#98A2B3"/>
                                            </svg>
                                            <span class="text-sm md:text-base font-light">{{ $certificate->name }}, {{$certificate->year}}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <hr>
                        @endif

                        @if(count($doctor->fellowships) > 0)
                            <div class="flex flex-col gap-1 md:gap-4">
                                <span class="text-base md:text-xl font-medium">Fellowships</span>
                                <div class="flex flex-col gap-3">
                                    @foreach($doctor->fellowships as $fellowship)
                                        <div class="flex items-center gap-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="4" height="4" viewBox="0 0 4 4" fill="none">
                                                <circle cx="2" cy="2" r="2" fill="#98A2B3"/>
                                            </svg>
                                            <span class="text-sm md:text-base font-light">{{ $fellowship->name }}, {{$fellowship->year}}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <hr>
                        @endif

                        @if(count($doctor->clinicalInterests) > 0)
                            <div class="flex flex-col gap-1 md:gap-4">
                                <span class="text-base md:text-xl font-medium">Clinical Interest</span>
                                <div class="flex flex-col gap-3">
                                    @foreach($doctor->clinicalInterests as $clinicalInterest)
                                        <div class="flex items-center gap-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="4" height="4" viewBox="0 0 4 4" fill="none">
                                                <circle cx="2" cy="2" r="2" fill="#98A2B3"/>
                                            </svg>
                                            <span class="text-sm md:text-base font-light">{{ $clinicalInterest->name }}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <div class="md:w-2/3">

                {{--personal information--}}
                <div class="flex flex-col gap-2">
                    <div class="hidden md:block">
                        <h1 class="text-2xl md:text-3xl font-semibold linguise_doctor_name">{{ $doctor->name }}</h1>
                    </div>
                    <div class="flex gap-2 items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="14" viewBox="0 0 12 14" fill="none">
                            <path d="M4.34717 10.0142C4.34717 13.417 10.1319 13.7573 10.1319 10.0142V7.29199" stroke="#667085"/>
                            <path d="M10.1318 6.26466H10.1386V6.27146H10.1318V6.26466Z" stroke="#667085" stroke-width="1.1" stroke-linejoin="round"/>
                            <path d="M11.1525 6.27083C11.1525 6.83462 10.6955 7.29167 10.1317 7.29167C9.56788 7.29167 9.11084 6.83462 9.11084 6.27083C9.11084 5.70704 9.56788 5.25 10.1317 5.25C10.6955 5.25 11.1525 5.70704 11.1525 6.27083Z" stroke="#667085"/>
                            <path d="M2.64572 1.84755V1.84755C1.70607 1.84755 0.944336 2.60928 0.944336 3.54894V6.61144C0.944336 8.49074 2.46781 10.0142 4.34711 10.0142V10.0142C6.22642 10.0142 7.74989 8.49074 7.74989 6.61144V3.20866C7.74989 2.45694 7.1405 1.84755 6.38878 1.84755V1.84755M2.64572 1.84755C2.64572 2.22341 2.95042 2.5281 3.32628 2.5281C3.70214 2.5281 4.00684 2.22341 4.00684 1.84755C4.00684 1.47169 3.70214 1.16699 3.32628 1.16699C2.95042 1.16699 2.64572 1.47169 2.64572 1.84755ZM6.38878 1.84755C6.38878 2.22341 6.08409 2.5281 5.70822 2.5281C5.33236 2.5281 5.02767 2.22341 5.02767 1.84755C5.02767 1.47169 5.33236 1.16699 5.70822 1.16699C6.08409 1.16699 6.38878 1.47169 6.38878 1.84755Z" stroke="#667085"/>
                        </svg>
                        <span class="text-sm md:text-base font-medium text-[#475467] linguise_doctor_specialty">{{ $doctor->specialty->group_name_en }}</span>
                    </div>
                    {{-- @if($doctor->experience_year)
                        <div class="flex gap-2 items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M4.1999 2.6252C4.1999 1.56205 5.06175 0.700195 6.1249 0.700195H7.8749C8.93805 0.700195 9.7999 1.56205 9.7999 2.6252V2.93508C10.2003 2.97385 10.5984 3.02086 10.9939 3.07594C11.9367 3.20724 12.5999 4.0245 12.5999 4.94935V7.37719C12.5999 8.16592 12.1139 8.91129 11.3192 9.15523C9.95286 9.57466 8.50224 9.80019 6.9999 9.80019C5.49757 9.80019 4.04694 9.57466 2.68056 9.15523C1.88587 8.91129 1.3999 8.16592 1.3999 7.37719V4.94935C1.3999 4.0245 2.06306 3.20724 3.00595 3.07594C3.40143 3.02086 3.79947 2.97385 4.1999 2.93508V2.6252ZM8.7499 2.6252V2.85269C8.17098 2.81786 7.58748 2.8002 6.9999 2.8002C6.41233 2.8002 5.82883 2.81786 5.2499 2.85269V2.6252C5.2499 2.14195 5.64165 1.7502 6.1249 1.7502H7.8749C8.35815 1.7502 8.7499 2.14195 8.7499 2.6252ZM6.9999 7.0002C6.6133 7.0002 6.2999 7.3136 6.2999 7.7002V7.7072C6.2999 8.09379 6.6133 8.4072 6.9999 8.4072H7.0069C7.3935 8.4072 7.7069 8.09379 7.7069 7.7072V7.7002C7.7069 7.3136 7.3935 7.0002 7.0069 7.0002H6.9999Z" fill="#667085"/>
                                <path d="M2.0999 10.5389V10.0602C2.18782 10.0972 2.2787 10.1302 2.37244 10.159C3.83754 10.6087 5.39197 10.8502 6.9999 10.8502C8.60783 10.8502 10.1623 10.6087 11.6274 10.159C11.7211 10.1302 11.812 10.0972 11.8999 10.0602V10.5389C11.8999 11.4819 11.2107 12.3099 10.2457 12.4186C9.18017 12.5386 8.09713 12.6002 6.9999 12.6002C5.90268 12.6002 4.81963 12.5386 3.75408 12.4186C2.78913 12.3099 2.0999 11.4819 2.0999 10.5389Z" fill="#667085"/>
                            </svg>
                            <span class="text-sm md:text-base font-medium text-[#475467]">{{ $doctor->experience_year }}+ years of experience</span>
                        </div>
                    @endif --}}
                    @if(featureFlag('show_doctor_filter_visit_tele'))
                        <div class="flex flex-wrap gap-1">
                            <span class="text-xs font-medium p-[2px] px-2 bg-[#F2F4F7] rounded-2xl text-[#475467] {{ $doctor->is_available_appointment ? '' : 'hidden' }}">Hospital Visit</span>
                            <span class="text-xs font-medium p-[2px] px-2 bg-[#F2F4F7] rounded-2xl text-[#475467] {{ $doctor->is_available_teleconsultation ? '' : 'hidden' }}">Teleconsultation</span>
                        </div>
                    @endif
                    @if($doctor->gender == 'F')
                        <div class="border border-[#16A34A] py-2 px-4 rounded-full flex items-center gap-2 bg-white w-fit">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M3.8 4.62743C3.8 2.78974 5.28974 1.3 7.12743 1.3C8.96512 1.3 10.4549 2.78974 10.4549 4.62743C10.4549 6.46512 8.96512 7.95486 7.12743 7.95486C5.28974 7.95486 3.8 6.46512 3.8 4.62743ZM7.12743 0C4.57177 0 2.5 2.07177 2.5 4.62743C2.5 6.94477 4.2034 8.86427 6.42639 9.2021C6.40919 9.26515 6.4 9.3315 6.4 9.4L6.4 11.066H5.15C4.73579 11.066 4.4 11.4018 4.4 11.816C4.4 12.2302 4.73579 12.566 5.15 12.566H6.4V13.3152C6.4 13.7294 6.73579 14.0652 7.15 14.0652C7.56421 14.0652 7.9 13.7294 7.9 13.3152V12.566H9.0652C9.47942 12.566 9.8152 12.2302 9.8152 11.816C9.8152 11.4018 9.47942 11.066 9.0652 11.066H7.9L7.9 9.4C7.9 9.32904 7.89014 9.26038 7.87173 9.19532C10.0735 8.83931 11.7549 6.92972 11.7549 4.62743C11.7549 2.07177 9.68309 0 7.12743 0Z" fill="#16A34A"></path>
                            </svg>
                            <span class="text-sm font-medium text-[#16A34A]">Female</span>
                        </div>
                    @elseif($doctor->gender == 'M')
                        <div class="border border-[#16A34A] py-2 px-4 rounded-full flex items-center gap-2 bg-white w-fit">
                            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="14" viewBox="0 0 15 14" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8.65677 1.75C8.65677 1.33579 8.99256 1 9.40677 1H13.2349C13.3366 1 13.4335 1.02024 13.522 1.05692C13.6104 1.09352 13.6933 1.14778 13.7652 1.21969C13.8857 1.34021 13.9567 1.49153 13.978 1.6483C13.983 1.68496 13.9853 1.72191 13.9849 1.75884V5.57808C13.9849 5.9923 13.6491 6.32808 13.2349 6.32808C12.8206 6.32808 12.4849 5.9923 12.4849 5.57808V3.56073L10.0649 5.98072C10.5023 6.68744 10.7549 7.5207 10.7549 8.41294C10.7549 10.9686 8.68309 13.0404 6.12743 13.0404C3.57177 13.0404 1.5 10.9686 1.5 8.41294C1.5 5.85728 3.57177 3.78551 6.12743 3.78551C7.24838 3.78551 8.27624 4.18409 9.07702 4.84725L11.4243 2.5L9.40677 2.5C8.99256 2.5 8.65677 2.16421 8.65677 1.75ZM2.8 8.41294C2.8 6.57525 4.28974 5.08551 6.12743 5.08551C7.96512 5.08551 9.45486 6.57525 9.45486 8.41294C9.45486 10.2506 7.96512 11.7404 6.12743 11.7404C4.28974 11.7404 2.8 10.2506 2.8 8.41294Z" fill="#16A34A"></path>
                            </svg>
                            <span class="text-sm font-medium text-[#16A34A]">Male</span>
                        </div>
                    @endif
                </div>
                @if($BooleanLinkAppoitmentExternal)
                <div class="flex md:flex-row flex-col gap-4 mt-8">
                <a href="{{ $BooleanLinkAppoitmentExternal ? $LinkAppoitmentExternal :route('appointments.book',['uuid'=>\Illuminate\Support\Str::uuid(), 'doctor_uuid'=>$doctor->uuid, 'type' => \App\Enums\Table\AppointmentPatientSummary\Type::VISIT])}}"
                    class="flex flex-row px-6 py-4 gap-3 bg-[#0D4D8B] rounded-xl w-full md:w-1/2 justify-center hover:cursor-pointer" {{$BooleanLinkAppoitmentExternal ? 'target="_blank"' : ''}}>
                     <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                         <path d="M13.25 12.75C13.25 13.1642 12.9142 13.5 12.5 13.5C12.0858 13.5 11.75 13.1642 11.75 12.75C11.75 12.3358 12.0858 12 12.5 12C12.9142 12 13.25 12.3358 13.25 12.75Z" fill="white"/>
                         <path d="M8 15.75C8.41421 15.75 8.75 15.4142 8.75 15C8.75 14.5858 8.41421 14.25 8 14.25C7.58579 14.25 7.25 14.5858 7.25 15C7.25 15.4142 7.58579 15.75 8 15.75Z" fill="white"/>
                         <path d="M8.75 17.25C8.75 17.6642 8.41421 18 8 18C7.58579 18 7.25 17.6642 7.25 17.25C7.25 16.8358 7.58579 16.5 8 16.5C8.41421 16.5 8.75 16.8358 8.75 17.25Z" fill="white"/>
                         <path d="M10.25 15.75C10.6642 15.75 11 15.4142 11 15C11 14.5858 10.6642 14.25 10.25 14.25C9.83579 14.25 9.5 14.5858 9.5 15C9.5 15.4142 9.83579 15.75 10.25 15.75Z" fill="white"/>
                         <path d="M11 17.25C11 17.6642 10.6642 18 10.25 18C9.83579 18 9.5 17.6642 9.5 17.25C9.5 16.8358 9.83579 16.5 10.25 16.5C10.6642 16.5 11 16.8358 11 17.25Z" fill="white"/>
                         <path d="M12.5 15.75C12.9142 15.75 13.25 15.4142 13.25 15C13.25 14.5858 12.9142 14.25 12.5 14.25C12.0858 14.25 11.75 14.5858 11.75 15C11.75 15.4142 12.0858 15.75 12.5 15.75Z" fill="white"/>
                         <path d="M13.25 17.25C13.25 17.6642 12.9142 18 12.5 18C12.0858 18 11.75 17.6642 11.75 17.25C11.75 16.8358 12.0858 16.5 12.5 16.5C12.9142 16.5 13.25 16.8358 13.25 17.25Z" fill="white"/>
                         <path d="M14.75 15.75C15.1642 15.75 15.5 15.4142 15.5 15C15.5 14.5858 15.1642 14.25 14.75 14.25C14.3358 14.25 14 14.5858 14 15C14 15.4142 14.3358 15.75 14.75 15.75Z" fill="white"/>
                         <path d="M15.5 17.25C15.5 17.6642 15.1642 18 14.75 18C14.3358 18 14 17.6642 14 17.25C14 16.8358 14.3358 16.5 14.75 16.5C15.1642 16.5 15.5 16.8358 15.5 17.25Z" fill="white"/>
                         <path d="M17 15.75C17.4142 15.75 17.75 15.4142 17.75 15C17.75 14.5858 17.4142 14.25 17 14.25C16.5858 14.25 16.25 14.5858 16.25 15C16.25 15.4142 16.5858 15.75 17 15.75Z" fill="white"/>
                         <path d="M15.5 12.75C15.5 13.1642 15.1642 13.5 14.75 13.5C14.3358 13.5 14 13.1642 14 12.75C14 12.3358 14.3358 12 14.75 12C15.1642 12 15.5 12.3358 15.5 12.75Z" fill="white"/>
                         <path d="M17 13.5C17.4142 13.5 17.75 13.1642 17.75 12.75C17.75 12.3358 17.4142 12 17 12C16.5858 12 16.25 12.3358 16.25 12.75C16.25 13.1642 16.5858 13.5 17 13.5Z" fill="white"/>
                         <path fill-rule="evenodd" clip-rule="evenodd" d="M7.25 2.25C7.66421 2.25 8 2.58579 8 3V4.5H17V3C17 2.58579 17.3358 2.25 17.75 2.25C18.1642 2.25 18.5 2.58579 18.5 3V4.5H19.25C20.9069 4.5 22.25 5.84315 22.25 7.5V18.75C22.25 20.4069 20.9069 21.75 19.25 21.75H5.75C4.09315 21.75 2.75 20.4069 2.75 18.75V7.5C2.75 5.84315 4.09315 4.5 5.75 4.5H6.5V3C6.5 2.58579 6.83579 2.25 7.25 2.25ZM20.75 11.25C20.75 10.4216 20.0784 9.75 19.25 9.75H5.75C4.92157 9.75 4.25 10.4216 4.25 11.25V18.75C4.25 19.5784 4.92157 20.25 5.75 20.25H19.25C20.0784 20.25 20.75 19.5784 20.75 18.75V11.25Z" fill="white"/>
                     </svg>
                     <span class="text-sm md:text-base font-medium text-white">Contact Icon Cancer Centre</span>
                 </a>
                </div>
                @elseif($doctor->is_available_appointment || $doctor->is_available_teleconsultation)
                    {{--appointment method--}}
                    <div class="flex md:flex-row flex-col gap-4 mt-8">
                        @if(featureFlag('show_doctor_filter_visit_tele'))
                            @if(@$doctor->is_available_teleconsultation)
                                <a href="{{route('appointments.book',['uuid'=>\Illuminate\Support\Str::uuid(), 'doctor_uuid'=>$doctor->uuid, 'type' => \App\Enums\Table\AppointmentPatientSummary\Type::TELECONSULTATION])}}"
                                class="flex flex-row px-6 py-4 gap-3 border-2 border-[#0D4D8B] rounded-xl w-full md:w-1/2 justify-center hover:cursor-pointer">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M4.5 4.5C2.84315 4.5 1.5 5.84315 1.5 7.5V16.5C1.5 18.1569 2.84315 19.5 4.5 19.5H12.75C14.4069 19.5 15.75 18.1569 15.75 16.5V7.5C15.75 5.84315 14.4069 4.5 12.75 4.5H4.5Z" fill="#0D4D8B"/>
                                        <path d="M19.9393 18.75L17.25 16.0606V7.93931L19.9393 5.24996C20.8843 4.30501 22.5 4.97427 22.5 6.31063V17.6893C22.5 19.0257 20.8843 19.6949 19.9393 18.75Z" fill="#0D4D8B"/>
                                    </svg>
                                    <span class="text-sm md:text-base font-medium text-[#0D4D8B]">Teleconsultation</span>
                                </a>
                            @endif
                        @endif
                        @if(@$doctor->is_available_appointment && $BooleanLinkAppoitmentExternal == false)
                            <a href="{{ $BooleanLinkAppoitmentExternal ? $LinkAppoitmentExternal :route('appointments.book',['uuid'=>\Illuminate\Support\Str::uuid(), 'doctor_uuid'=>$doctor->uuid, 'type' => \App\Enums\Table\AppointmentPatientSummary\Type::VISIT])}}"
                               class="flex flex-row px-6 py-4 gap-3 bg-[#0D4D8B] rounded-xl w-full md:w-1/2 justify-center hover:cursor-pointer" {{$BooleanLinkAppoitmentExternal ? 'target="_blank"' : ''}}>
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                                    <path d="M13.25 12.75C13.25 13.1642 12.9142 13.5 12.5 13.5C12.0858 13.5 11.75 13.1642 11.75 12.75C11.75 12.3358 12.0858 12 12.5 12C12.9142 12 13.25 12.3358 13.25 12.75Z" fill="white"/>
                                    <path d="M8 15.75C8.41421 15.75 8.75 15.4142 8.75 15C8.75 14.5858 8.41421 14.25 8 14.25C7.58579 14.25 7.25 14.5858 7.25 15C7.25 15.4142 7.58579 15.75 8 15.75Z" fill="white"/>
                                    <path d="M8.75 17.25C8.75 17.6642 8.41421 18 8 18C7.58579 18 7.25 17.6642 7.25 17.25C7.25 16.8358 7.58579 16.5 8 16.5C8.41421 16.5 8.75 16.8358 8.75 17.25Z" fill="white"/>
                                    <path d="M10.25 15.75C10.6642 15.75 11 15.4142 11 15C11 14.5858 10.6642 14.25 10.25 14.25C9.83579 14.25 9.5 14.5858 9.5 15C9.5 15.4142 9.83579 15.75 10.25 15.75Z" fill="white"/>
                                    <path d="M11 17.25C11 17.6642 10.6642 18 10.25 18C9.83579 18 9.5 17.6642 9.5 17.25C9.5 16.8358 9.83579 16.5 10.25 16.5C10.6642 16.5 11 16.8358 11 17.25Z" fill="white"/>
                                    <path d="M12.5 15.75C12.9142 15.75 13.25 15.4142 13.25 15C13.25 14.5858 12.9142 14.25 12.5 14.25C12.0858 14.25 11.75 14.5858 11.75 15C11.75 15.4142 12.0858 15.75 12.5 15.75Z" fill="white"/>
                                    <path d="M13.25 17.25C13.25 17.6642 12.9142 18 12.5 18C12.0858 18 11.75 17.6642 11.75 17.25C11.75 16.8358 12.0858 16.5 12.5 16.5C12.9142 16.5 13.25 16.8358 13.25 17.25Z" fill="white"/>
                                    <path d="M14.75 15.75C15.1642 15.75 15.5 15.4142 15.5 15C15.5 14.5858 15.1642 14.25 14.75 14.25C14.3358 14.25 14 14.5858 14 15C14 15.4142 14.3358 15.75 14.75 15.75Z" fill="white"/>
                                    <path d="M15.5 17.25C15.5 17.6642 15.1642 18 14.75 18C14.3358 18 14 17.6642 14 17.25C14 16.8358 14.3358 16.5 14.75 16.5C15.1642 16.5 15.5 16.8358 15.5 17.25Z" fill="white"/>
                                    <path d="M17 15.75C17.4142 15.75 17.75 15.4142 17.75 15C17.75 14.5858 17.4142 14.25 17 14.25C16.5858 14.25 16.25 14.5858 16.25 15C16.25 15.4142 16.5858 15.75 17 15.75Z" fill="white"/>
                                    <path d="M15.5 12.75C15.5 13.1642 15.1642 13.5 14.75 13.5C14.3358 13.5 14 13.1642 14 12.75C14 12.3358 14.3358 12 14.75 12C15.1642 12 15.5 12.3358 15.5 12.75Z" fill="white"/>
                                    <path d="M17 13.5C17.4142 13.5 17.75 13.1642 17.75 12.75C17.75 12.3358 17.4142 12 17 12C16.5858 12 16.25 12.3358 16.25 12.75C16.25 13.1642 16.5858 13.5 17 13.5Z" fill="white"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.25 2.25C7.66421 2.25 8 2.58579 8 3V4.5H17V3C17 2.58579 17.3358 2.25 17.75 2.25C18.1642 2.25 18.5 2.58579 18.5 3V4.5H19.25C20.9069 4.5 22.25 5.84315 22.25 7.5V18.75C22.25 20.4069 20.9069 21.75 19.25 21.75H5.75C4.09315 21.75 2.75 20.4069 2.75 18.75V7.5C2.75 5.84315 4.09315 4.5 5.75 4.5H6.5V3C6.5 2.58579 6.83579 2.25 7.25 2.25ZM20.75 11.25C20.75 10.4216 20.0784 9.75 19.25 9.75H5.75C4.92157 9.75 4.25 10.4216 4.25 11.25V18.75C4.25 19.5784 4.92157 20.25 5.75 20.25H19.25C20.0784 20.25 20.75 19.5784 20.75 18.75V11.25Z" fill="white"/>
                                </svg>
                                <span class="text-sm md:text-base font-medium text-white">Clinic Appointment</span>
                            </a>
                        @endif
                    </div>

                    @if($schedules)
                        <div class="flex flex-col gap-6 mt-[62px]">
                            <span class="text-xl font-medium text-[#0B4074]">Regular Schedule</span>
                            <div class="flex flex-col w-full">
                                <div class="grid grid-cols-3 bg-green-50 bg-opacity-50 py-3 text-center">
                                    <span class="text-base font-semibold">Day</span>
                                    <span class="text-base font-semibold">Time</span>
                                    <span class="text-base font-semibold">Location</span>
                                </div>
                                @foreach($schedules as $schedule)
                                    <div class="grid grid-cols-3 py-3 text-center">
                                        <span class="text-base font-light">{{ $schedule['day']}}</span>
                                        <span class="text-base font-light">{{ \Illuminate\Support\Carbon::parse($schedule['timefrom'])->format('H:s') . ' - ' . \Illuminate\Support\Carbon::parse($schedule['timeto'])->format('H:s') }}</span>
                                        <span class="text-base font-light linguise_schedule_location">{{ $schedule['clinic_desc'] }}</span>
                                    </div>
                                @endforeach

                            </div>
                        </div>
                    @endif
                
                @else
                    <div class="flex flex-col gap-4 mt-4">
                        <span class="text-base font-weight-400 text-[#475467]">Available for consultations by appointment only. To schedule, please contact our customer service team below.</span>
                        <a href="https://wa.me/{{ $doctorCSWALink }}" target="_blank"
                           class="flex flex-row py-3 px-4 rounded-xl border border-brand w-fit gap-2 items-center hover:cursor-pointer">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_9223_5562)">
                                    <path d="M0.51209 11.8563C0.511527 13.8728 1.04253 15.8417 2.05221 17.5771L0.415527 23.5066L6.53103 21.9156C8.22249 22.8292 10.1177 23.308 12.0435 23.3081H12.0486C18.4062 23.3081 23.5815 18.1748 23.5842 11.8653C23.5855 8.80792 22.3866 5.93295 20.2084 3.76997C18.0306 1.60718 15.1342 0.415458 12.0481 0.414062C5.68972 0.414062 0.514809 5.54709 0.512184 11.8563" fill="#0D4D8B"/>
                                    <path d="M0.100313 11.8527C0.0996563 13.9417 0.649687 15.981 1.69537 17.7786L0 23.9207L6.33478 22.2726C8.08022 23.2168 10.0454 23.7147 12.0451 23.7154H12.0502C18.636 23.7154 23.9972 18.3975 24 11.8621C24.0011 8.69488 22.7591 5.71656 20.5031 3.47609C18.2468 1.23591 15.2468 0.00130233 12.0502 0C5.46337 0 0.102938 5.31721 0.100313 11.8527ZM3.87291 17.469L3.63637 17.0965C2.64206 15.5277 2.11725 13.7149 2.118 11.8534C2.12006 6.4213 6.57544 2.00186 12.054 2.00186C14.7071 2.00298 17.2005 3.02921 19.0759 4.89116C20.9512 6.7533 21.9831 9.22865 21.9824 11.8614C21.98 17.2935 17.5245 21.7135 12.0502 21.7135H12.0463C10.2638 21.7126 8.51569 21.2376 6.99113 20.34L6.62831 20.1265L2.86912 21.1045L3.87291 17.469Z" fill="url(#paint0_linear_9223_5562)"/>
                                    <path d="M9.06334 6.89771C8.83965 6.4044 8.60425 6.39445 8.39153 6.3858C8.21734 6.37836 8.01821 6.37892 7.81928 6.37892C7.62015 6.37892 7.29662 6.45324 7.02315 6.74952C6.7494 7.04608 5.97803 7.76273 5.97803 9.22031C5.97803 10.678 7.048 12.0866 7.19715 12.2845C7.3465 12.482 9.26275 15.5689 12.2976 16.7564C14.8199 17.7433 15.3332 17.547 15.8806 17.4975C16.4281 17.4482 17.6472 16.7811 17.8959 16.0892C18.1448 15.3975 18.1448 14.8046 18.0702 14.6807C17.9956 14.5572 17.7964 14.4831 17.4978 14.335C17.1992 14.1868 15.7312 13.4701 15.4576 13.3712C15.1838 13.2724 14.9848 13.2231 14.7857 13.5198C14.5865 13.8159 14.0147 14.4831 13.8405 14.6807C13.6664 14.8787 13.4921 14.9034 13.1936 14.7552C12.8948 14.6065 11.9332 14.2941 10.7924 13.2849C9.90475 12.4996 9.3055 11.5298 9.13131 11.2331C8.95712 10.937 9.11265 10.7764 9.26237 10.6288C9.39653 10.496 9.56106 10.2828 9.7105 10.1099C9.85937 9.93687 9.90906 9.81343 10.0086 9.61585C10.1083 9.41808 10.0584 9.24506 9.98387 9.09687C9.90906 8.94868 9.32884 7.48347 9.06334 6.89771Z" fill="white"/>
                                </g>
                                <defs>
                                    <linearGradient id="paint0_linear_9223_5562" x1="1200" y1="2392.07" x2="1200" y2="0" gradientUnits="userSpaceOnUse">
                                        <stop stop-color="#F9F9F9"/>
                                        <stop offset="1" stop-color="white"/>
                                    </linearGradient>
                                    <clipPath id="clip0_9223_5562">
                                        <rect width="24" height="24" fill="white"/>
                                    </clipPath>
                                </defs>
                            </svg>

                            <span class="text-base font-weight-500 text-brand">
                                Chat with Our Customer Service
                            </span>
                        </a>
                    </div>
                @endif

            </div>
        </div>
    </div>
    <div class="mt-32">
{{--        @include('landing-page.partials.homepage.contact')--}}
    </div>
</div>
