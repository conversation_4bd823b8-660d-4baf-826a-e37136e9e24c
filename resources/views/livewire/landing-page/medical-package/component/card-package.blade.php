<div class="flex flex-col shadow-xl rounded-b-3xl">
    @if (@$package->image)
        <img class="rounded-t-2xl h-[185px] lg:h-[249px] object-cover" src="{{ asset_gcs($package->image) }}"
            alt="Bali International Hospital">
    @else
        <img class="rounded-t-2xl h-[185px] lg:h-[249px] object-cover"
            src="https://images.pexels.com/photos/806427/pexels-photo-806427.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2"
            alt="Bali International Hospital">
    @endif
    <div class="flex flex-col py-5 px-4 h-full">
        <div class="flex items-center gap-2">
            <span class="text-xl font-semibold line-clamp-2 linguise_package_name">{{ $package->title }}</span>
            @if ($package->title === 'Bali Classic')
                <span style="background-color: red;" class="text-white text-xs font-bold px-2 py-1 rounded linguise_package_promo">Promo 30%</span>
            @elseif ($package->title === 'Bali Deluxe')
                <span style="background-color: red;" class="text-white text-xs font-bold px-2 py-1 rounded linguise_package_promo">Promo 30%</span>
            @elseif ($package->title === 'Pearl Screening')
                <span style="background-color: red;" class="text-white text-xs font-bold px-2 py-1 rounded linguise_package_promo">Promo 15%</span>
            @elseif ($package->title === 'Jade Screening')
                <span style="background-color: red;" class="text-white text-xs font-bold px-2 py-1 rounded linguise_package_promo">Promo 15%</span>
            @elseif ($package->title === 'Silver Heart')
                <span style="background-color: red;" class="text-white text-xs font-bold px-2 py-1 rounded linguise_package_promo">Promo 30%</span>
            @endif
        </div>
        <span class="text-sm font-light text-[#6D7079] mt-3 line-clamp-3 ql-content">{!! nl2br(e(str_replace('|', "\n", $package->description_card))) !!}</span>
        <div class="mt-auto flex flex-col">
            <span
                class="text-2xl font-semibold text-brand mt-2 linguise_package_price">Rp{{ str_replace(',', '.', number_format($package->price)) }}</span>
            <a href="{{ route('medical_packages.show', [
                'slug_category' => $package->packageCategory->slug,
                'slug_package' => $package->slug,
            ]) }}"
                class="w-full bg-brand py-[18px] flex justify-center text-base text-white rounded-xl mt-5 hover:cursor-pointer">
                See Details
            </a>
        </div>
    </div>
</div>
