<div class="w-full">
    <div class="flex flex-col h-full max-h-screen">
        <div class="grid grid-cols-2 lg:grid-cols-3 gap-4 w-full p-6 rounded-t-3xl" style="background: linear-gradient(199.29deg, #3674B3 7.1%, #0B4074 93.71%);">
            <div class="w-full hidden lg:block">
                <span class="text-xl font-semibold text-white">Category</span>
            </div>
            <div class="w-full">
                <div class="relative">
                    <div wire:click="onHandleShowOriginalPackage" class="rounded-lg border p-4 bg-white hover:cursor-pointer">
                        <div class="flex flex-row justify-between items-center text-black">
                            <span class="text-base font-medium linguise_package_name">{{ $package->title }}</span>
                            <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M0.21967 0.21967C0.512563 -0.0732233 0.987437 -0.0732233 1.28033 0.21967L5 3.93934L8.71967 0.219671C9.01256 -0.0732226 9.48744 -0.0732225 9.78033 0.219671C10.0732 0.512564 10.0732 0.987438 9.78033 1.28033L5.53033 5.53033C5.38968 5.67098 5.19891 5.75 5 5.75C4.80109 5.75 4.61032 5.67098 4.46967 5.53033L0.21967 1.28033C-0.0732233 0.987437 -0.0732233 0.512563 0.21967 0.21967Z" fill="#8FC640"/>
                            </svg>
                        </div>
                    </div>
                    @if($isShowOriginalPackage)
                        <div class="rounded-lg border p-4 bg-white hover:cursor-pointer absolute w-full mt-2">
                            <div class="w-full items-center py-2 flex flex-row justify-between bg-white border-b gap-2">
                                <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.41667 11.5833C8.994 11.5833 11.0833 9.494 11.0833 6.91667C11.0833 4.33934 8.994 2.25 6.41667 2.25C3.83934 2.25 1.75 4.33934 1.75 6.91667C1.75 9.494 3.83934 11.5833 6.41667 11.5833Z" stroke="#6B7280" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M12.2505 12.75L9.74219 10.2417" stroke="#6B7280" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <input type="text" class="w-full outline-none bg-transparent text-base font-light text-black" placeholder="Search" wire:model.live.debounce.500ms="titlePackage">
                            </div>

                            <div class="flex flex-col gap-4 mt-5 mx-2 h-96 overflow-scroll">
                                <div wire:loading wire:target="titlePackage">
                                    Searching package...
                                </div>
                                <div class="flex flex-col gap-4" wire:loading.remove wire:target="titlePackage">
                                    @if($listPackages->count() == 0)
                                        <div class="flex flex-row justify-between items-center">
                                            <span class="text-base font-light text-[#344054]">No data found</span>
                                        </div>
                                    @else
                                        @foreach($listPackages as $listPackage)
                                            @if($listPackage->id == $package->id)
                                                <div class="flex flex-row justify-between items-center">
                                                    <span class="text-base font-medium text-[#16A34A]">{{ $listPackage->title }}</span>
                                                    <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M12.364 1.32272C12.6276 1.52355 12.6785 1.90003 12.4776 2.16361L6.07765 10.5636C5.97272 10.7013 5.81341 10.787 5.64066 10.7986C5.46791 10.8103 5.29856 10.7467 5.17613 10.6242L1.57613 7.02425C1.34181 6.78993 1.34181 6.41003 1.57613 6.17572C1.81044 5.9414 2.19034 5.9414 2.42465 6.17572L5.5392 9.29027L11.5231 1.43636C11.724 1.17277 12.1004 1.1219 12.364 1.32272Z" fill="#16A34A" stroke="#16A34A" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </div>
                                            @else
                                                @if($listPackage->id == @$comparePackage->id)
                                                    @continue
                                                @endif
                                                <div wire:click="onSelectPackage('{{$listPackage->id}}')" class="flex flex-row justify-between items-center hover:cursor-pointer hover:font-bold font-light" id="{{$listPackage->uuid}}">
                                                    <span class="text-base text-[#344054]">{{ $listPackage->title }}</span>
                                                </div>
                                            @endif
                                        @endforeach
                                    @endif

                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
            <div class="w-full">
                <div class="relative">
                    <div wire:click="onHandleShowComparePackage" class="rounded-lg border p-4 bg-white hover:cursor-pointer">
                        <div class="flex flex-row justify-between items-center text-black">
                            {{--                            <span class="text-base font-medium">Screening Golder Advance</span>--}}
                            <span class="text-base font-light linguise_package_name {{ @$comparePackage->title ? 'text-base font-medium' : '' }}">{{ @$comparePackage->title ? $comparePackage->title : 'Select Package' }}</span>
                            <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M0.21967 0.21967C0.512563 -0.0732233 0.987437 -0.0732233 1.28033 0.21967L5 3.93934L8.71967 0.219671C9.01256 -0.0732226 9.48744 -0.0732225 9.78033 0.219671C10.0732 0.512564 10.0732 0.987438 9.78033 1.28033L5.53033 5.53033C5.38968 5.67098 5.19891 5.75 5 5.75C4.80109 5.75 4.61032 5.67098 4.46967 5.53033L0.21967 1.28033C-0.0732233 0.987437 -0.0732233 0.512563 0.21967 0.21967Z" fill="#8FC640"/>
                            </svg>
                        </div>
                    </div>
                    @if($isShowComparePackage)
                        <div class="rounded-lg border p-4 bg-white hover:cursor-pointer absolute w-full mt-2">
                            <div class="w-full items-center py-2 flex flex-row justify-between bg-white border-b gap-2">
                                <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M6.41667 11.5833C8.994 11.5833 11.0833 9.494 11.0833 6.91667C11.0833 4.33934 8.994 2.25 6.41667 2.25C3.83934 2.25 1.75 4.33934 1.75 6.91667C1.75 9.494 3.83934 11.5833 6.41667 11.5833Z" stroke="#6B7280" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M12.2505 12.75L9.74219 10.2417" stroke="#6B7280" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                                <input type="text" class="w-full outline-none bg-transparent text-base font-light text-black" placeholder="Search" wire:model.live.debounce.500ms="titleComparePackage">
                            </div>

                            <div class="flex flex-col gap-4 mt-5 mx-2 h-96 overflow-scroll">
                                <div wire:loading wire:target="titleComparePackage">
                                    Searching package...
                                </div>
                                <div class="flex flex-col gap-4" wire:loading.remove wire:target="titleComparePackage">
                                    @if($listComparePackages->count() == 0)
                                        <div class="flex flex-row justify-between items-center">
                                            <span class="text-base font-light text-[#344054]">No data found</span>
                                        </div>
                                    @else
                                        @foreach($listComparePackages as $listPackage)
                                            @if($listPackage->id == @$comparePackage->id)
                                                <div class="flex flex-row justify-between items-center">
                                                    <span class="text-base font-medium text-[#16A34A]">{{ $listPackage->title }}</span>
                                                    <svg width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M12.364 1.32272C12.6276 1.52355 12.6785 1.90003 12.4776 2.16361L6.07765 10.5636C5.97272 10.7013 5.81341 10.787 5.64066 10.7986C5.46791 10.8103 5.29856 10.7467 5.17613 10.6242L1.57613 7.02425C1.34181 6.78993 1.34181 6.41003 1.57613 6.17572C1.81044 5.9414 2.19034 5.9414 2.42465 6.17572L5.5392 9.29027L11.5231 1.43636C11.724 1.17277 12.1004 1.1219 12.364 1.32272Z" fill="#16A34A" stroke="#16A34A" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </div>
                                            @else
                                                @if($listPackage->id == $package->id)
                                                    @continue
                                                @endif
                                                <div wire:click="onSelectComparePackage('{{$listPackage->id}}')" class="flex flex-row justify-between items-center hover:cursor-pointer hover:font-bold font-light" id="{{$listPackage->uuid}}">
                                                    <span class="text-base text-[#344054]">{{ $listPackage->title }}</span>
                                                </div>
                                            @endif
                                        @endforeach
                                    @endif

                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
        @foreach($data as $index => $packageData)
            @if($index == 0)
                <div class="grid grid-cols-2 lg:grid-cols-3 w-full border-b border-x">
                    <div class="w-full items-center py-4 px-4 hidden lg:flex">
                        <span class="text-base font-medium text-[#344054]">Price</span>
                    </div>
                    <div class="w-full py-4 px-2">
                        <span class="text-xs text-brand font-medium lg:hidden">Price</span>
                        <div class="flex flex-row gap-2 rounded-2xl lg:border lg:bg-gray-50 items-center p-4 lg:p-6">
                            <div class="h-10 items-center hidden lg:flex">
                                <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M0.125 18C0.125 8.12791 8.12791 0.125 18 0.125C27.8721 0.125 35.875 8.12791 35.875 18C35.875 27.8721 27.8721 35.875 18 35.875C8.12791 35.875 0.125 27.8721 0.125 18ZM24.6189 14.6742C25.0603 14.0563 24.9171 13.1975 24.2992 12.7561C23.6813 12.3147 22.8225 12.4579 22.3811 13.0758L16.4495 21.38L13.4723 18.4027C12.9353 17.8658 12.0647 17.8658 11.5277 18.4027C10.9908 18.9397 10.9908 19.8103 11.5277 20.3473L15.6527 24.4723C15.9385 24.7581 16.3356 24.9037 16.7384 24.8703C17.1412 24.837 17.509 24.6281 17.7439 24.2992L24.6189 14.6742Z" fill="#32D583"/>
                                </svg>
                            </div>
                            <div class="flex flex-col gap-1 w-full">
                                <span class="lg:text-base text-sm font-medium text-[#2C2F2E] text-center lg:text-start linguise_package_price">Rp{{str_replace(',', '.', number_format((int)$packageData['obbrivation']))}}</span>
                            </div>
                        </div>
                    </div>
                    @if((int)$packageData['obbrivation_compare'])
                        <div class="w-full py-4 px-2">
                            <span class="text-xs text-brand font-medium lg:hidden">&nbsp;</span>
                            <div class="flex flex-row gap-2 rounded-2xl lg:border lg:bg-gray-50 items-center p-4 lg:p-6">
                                <div class="h-10 items-center lg:flex hidden">
                                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.125 18C0.125 8.12791 8.12791 0.125 18 0.125C27.8721 0.125 35.875 8.12791 35.875 18C35.875 27.8721 27.8721 35.875 18 35.875C8.12791 35.875 0.125 27.8721 0.125 18ZM24.6189 14.6742C25.0603 14.0563 24.9171 13.1975 24.2992 12.7561C23.6813 12.3147 22.8225 12.4579 22.3811 13.0758L16.4495 21.38L13.4723 18.4027C12.9353 17.8658 12.0647 17.8658 11.5277 18.4027C10.9908 18.9397 10.9908 19.8103 11.5277 20.3473L15.6527 24.4723C15.9385 24.7581 16.3356 24.9037 16.7384 24.8703C17.1412 24.837 17.509 24.6281 17.7439 24.2992L24.6189 14.6742Z" fill="#32D583"/>
                                    </svg>
                                </div>
                                <div class="flex flex-col gap-1 w-full">
                                    <span class="lg:text-base text-sm font-medium text-[#2C2F2E] text-center lg:text-start linguise_package_price">Rp{{str_replace(',', '.', number_format((int)$packageData['obbrivation_compare']))}}</span>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="w-full lg:bg-gray-50 flex items-center justify-center flex-col">
                            <span class="text-xs lg:text-base text-center font-light lg:font-medium text-[#667085]">&nbsp;</span>
                            <span class="text-xs lg:text-base text-center font-light lg:font-medium text-[#667085]">Please Select a Package</span>
                        </div>
                    @endif
                </div>
            @else
                <div class="grid grid-cols-2 lg:grid-cols-3 w-full border-b border-x linguise_package_inclusion">
                    <div class="w-full hidden items-center py-4 px-4 lg:flex">
                        <span class="text-base font-medium text-[#344054]">{{ $packageData['section'] }}</span>
                    </div>
                    @if(count($packageData['obbrivation']) > 0)
                        <div class="w-full py-4 px-2 flex flex-col gap-4">
                            <span class="text-xs text-brand font-medium lg:hidden">{{ $packageData['section'] }}</span>
                            <div class="flex flex-row gap-2 rounded-2xl border border-[#6CE9A6] lg:border-hidden bg-[#ECFDF3] lg:bg-gray-50 items-center p-4 lg:p-6">
                                <div class="h-10 items-center hidden lg:flex">
                                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.125 18C0.125 8.12791 8.12791 0.125 18 0.125C27.8721 0.125 35.875 8.12791 35.875 18C35.875 27.8721 27.8721 35.875 18 35.875C8.12791 35.875 0.125 27.8721 0.125 18ZM24.6189 14.6742C25.0603 14.0563 24.9171 13.1975 24.2992 12.7561C23.6813 12.3147 22.8225 12.4579 22.3811 13.0758L16.4495 21.38L13.4723 18.4027C12.9353 17.8658 12.0647 17.8658 11.5277 18.4027C10.9908 18.9397 10.9908 19.8103 11.5277 20.3473L15.6527 24.4723C15.9385 24.7581 16.3356 24.9037 16.7384 24.8703C17.1412 24.837 17.509 24.6281 17.7439 24.2992L24.6189 14.6742Z" fill="#32D583"/>
                                    </svg>
                                </div>
                                <ul class="list-disc pl-5 hidden lg:block">
                                    @foreach($packageData['obbrivation'] as $key => $obbrivation)
                                        <li class="text-base font-medium text-[#2C2F2E]">{{ $obbrivation }}</li>
                                    @endforeach
                                </ul>
                                <div class="flex lg:hidden">
                                    <span class="text-sm font-light text-[#2C2F2E] break-words whitespace-normal" id="theoffside">{{ implode(', ', $packageData['obbrivation']) }}</span>
                                </div>
                            </div>
                        </div>
                    @else
                        <div class="w-full py-4 px-2 gap-4 flex flex-col">
                            <span class="text-xs text-brand font-medium lg:hidden">{{$packageData['section']}}</span>
                            <div class="flex flex-row gap-2 rounded-2xl border border-[#FDA29B] lg:border-hidden bg-[#FEE4E2] lg:bg-gray-50 items-center p-4 lg:p-6">
                                <div class="lg:flex hidden">
                                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M18 0.125C8.12791 0.125 0.125 8.12791 0.125 18C0.125 27.8721 8.12791 35.875 18 35.875C27.8721 35.875 35.875 27.8721 35.875 18C35.875 8.12791 27.8721 0.125 18 0.125ZM14.8473 12.9027C14.3103 12.3658 13.4397 12.3658 12.9027 12.9027C12.3658 13.4397 12.3658 14.3103 12.9027 14.8473L16.0555 18L12.9027 21.1527C12.3658 21.6897 12.3658 22.5603 12.9027 23.0973C13.4397 23.6342 14.3103 23.6342 14.8473 23.0973L18 19.9445L21.1527 23.0973C21.6897 23.6342 22.5603 23.6342 23.0973 23.0973C23.6342 22.5603 23.6342 21.6897 23.0973 21.1527L19.9445 18L23.0973 14.8473C23.6342 14.3103 23.6342 13.4397 23.0973 12.9027C22.5603 12.3658 21.6897 12.3658 21.1527 12.9027L18 16.0555L14.8473 12.9027Z" fill="#F04438"/>
                                    </svg>
                                </div>
                                <span class="text-sm font-light lg:text-base lg:font-medium text-[#2C2F2E]">Not available</span>
                            </div>
                        </div>
                    @endif
                    @if(count($packageData['obbrivation_compare']) > 0)

                        <div class="w-full py-4 px-2 flex flex-col gap-4">
                            <span class="text-xs text-brand font-medium lg:hidden">&nbsp;</span>
                            <div class="flex flex-row gap-2 rounded-2xl border border-[#6CE9A6] lg:border-hidden bg-[#ECFDF3] lg:bg-gray-50 items-center p-4 lg:p-6">
                                <div class="h-10 items-center hidden lg:flex">
                                    <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.125 18C0.125 8.12791 8.12791 0.125 18 0.125C27.8721 0.125 35.875 8.12791 35.875 18C35.875 27.8721 27.8721 35.875 18 35.875C8.12791 35.875 0.125 27.8721 0.125 18ZM24.6189 14.6742C25.0603 14.0563 24.9171 13.1975 24.2992 12.7561C23.6813 12.3147 22.8225 12.4579 22.3811 13.0758L16.4495 21.38L13.4723 18.4027C12.9353 17.8658 12.0647 17.8658 11.5277 18.4027C10.9908 18.9397 10.9908 19.8103 11.5277 20.3473L15.6527 24.4723C15.9385 24.7581 16.3356 24.9037 16.7384 24.8703C17.1412 24.837 17.509 24.6281 17.7439 24.2992L24.6189 14.6742Z" fill="#32D583"/>
                                    </svg>
                                </div>
                                <ul class="list-disc pl-5 hidden lg:block">
                                    @foreach($packageData['obbrivation_compare'] as $key => $obbrivation)
                                        <li class="text-base font-medium text-[#2C2F2E]">{{ $obbrivation }}</li>
                                    @endforeach
                                </ul>
                                <div class="flex lg:hidden">
                                    <span class="text-sm font-light text-[#2C2F2E] break-words whitespace-normal">
                                        {{ implode(', ', $packageData['obbrivation_compare']) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    @else
                        @if(@$comparePackage)
                            <div class="w-full py-4 px-2 gap-4 flex flex-col">
                                <span class="text-xs text-brand font-medium lg:hidden">&nbsp;</span>
                                <div class="flex flex-row gap-2 rounded-2xl border border-[#FDA29B] lg:border-hidden bg-[#FEE4E2] lg:bg-gray-50 items-center p-4 lg:p-6">
                                    <div class="lg:flex hidden">
                                        <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M18 0.125C8.12791 0.125 0.125 8.12791 0.125 18C0.125 27.8721 8.12791 35.875 18 35.875C27.8721 35.875 35.875 27.8721 35.875 18C35.875 8.12791 27.8721 0.125 18 0.125ZM14.8473 12.9027C14.3103 12.3658 13.4397 12.3658 12.9027 12.9027C12.3658 13.4397 12.3658 14.3103 12.9027 14.8473L16.0555 18L12.9027 21.1527C12.3658 21.6897 12.3658 22.5603 12.9027 23.0973C13.4397 23.6342 14.3103 23.6342 14.8473 23.0973L18 19.9445L21.1527 23.0973C21.6897 23.6342 22.5603 23.6342 23.0973 23.0973C23.6342 22.5603 23.6342 21.6897 23.0973 21.1527L19.9445 18L23.0973 14.8473C23.6342 14.3103 23.6342 13.4397 23.0973 12.9027C22.5603 12.3658 21.6897 12.3658 21.1527 12.9027L18 16.0555L14.8473 12.9027Z" fill="#F04438"/>
                                        </svg>
                                    </div>
                                    <span class="text-sm font-light lg:text-base lg:font-medium text-[#2C2F2E]">Not available</span>
                                </div>
                            </div>
                        @else
                            <div class="w-full lg:bg-gray-50 flex items-center justify-center flex-col">
                                <span class="text-xs lg:text-base text-center font-light lg:font-medium text-[#667085]">&nbsp;</span>
                                <span class="text-xs lg:text-base text-center font-light lg:font-medium text-[#667085]">Please Select a Package</span>
                            </div>
                        @endif
                    @endif
                </div>
            @endif
        @endforeach
        <div class="grid grid-cols-2 lg:grid-cols-3 pt-4 pb-8 border-x border-b rounded-b-3xl">
            <div class="lg:flex hidden">

            </div>
            <div class="w-full px-2">
                <a href="{{route('medical_packages.show',['slug_category' => $package->packageCategory->slug,'slug_package'=>$package->slug])}}" class="border border-brand rounded-xl p-4 flex items-center justify-center text-brand">
                    <span class="text-base">See Details</span>
                </a>
            </div>
            <div class="w-full px-2">
                @if($comparePackage)
                    <a href="{{route('medical_packages.show',['slug_category' => $comparePackage->packageCategory->slug,'slug_package'=>$comparePackage->slug])}}" class="border border-brand rounded-xl p-4 flex items-center justify-center text-brand">
                        <span class="text-base">See Details</span>
                    </a>
                @endif
            </div>
        </div>
    </div>
</div>
