<div class="flex flex-col md:p-6 bg-white bg-opacity-50 mt-4 md:mt-10 rounded-3xl">
    <div class="flex flex-col gap-4">
        <div class="border rounded-2xl p-4 flex flex-col md:flex-row md:items-center justify-between bg-white gap-2 md:gap-0">
            <div class="flex flex-col md:flex-row gap-4 items-center">
                @if(!$doctor->image)
                    <img class="h-16 w-16 rounded-full object-cover" src="{{ asset_gcs('public/assets/icon/318e26c9-a4f1-4301-be62-8edf42acaf4e.svg') }}" alt="Bali International Hospital">
                @else
                    <img class="rounded-full h-16 w-16 object-cover" src="{{asset_gcs($doctor->image)}}" alt="Bali International Hospital">
                @endif

                <div class="flex flex-col gap-1">
                    <span class="text-base font-semibold linguise_doctor_name">{{$doctor->name}}</span>
                    <span class="text-gray-400 font-medium text-sm linguise_doctor_specialty">{{ $doctor->specialty->group_name_en }}</span>
                    <div class="flex flex-wrap gap-1">
                        <span class="text-xs font-medium p-[2px] px-2 bg-[#F2F4F7] rounded-2xl text-[#475467]">{{ \App\Enums\Table\AppointmentPatientSummary\Type::getLabel($type) }}</span>
                    </div>
                </div>
            </div>
            <a href="{{route('doctors.index')}}" class="flex flex-row gap-2 items-center hover:cursor-pointer">
                <span class="text-base font-medium text-[#0D4D8B]">Change Doctor</span>
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.21967 5.21967C8.51256 4.92678 8.98744 4.92678 9.28033 5.21967L13.5303 9.46967C13.8232 9.76256 13.8232 10.2374 13.5303 10.5303L9.28033 14.7803C8.98744 15.0732 8.51256 15.0732 8.21967 14.7803C7.92678 14.4874 7.92678 14.0126 8.21967 13.7197L11.9393 10L8.21967 6.28033C7.92678 5.98744 7.92678 5.51256 8.21967 5.21967Z" fill="#8FC640"/>
                </svg>
            </a>
        </div>
        <div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-2 md:gap-6">
                <livewire:landing-page.appointment.component.form-date :appointment="$appointment" :schedules="$schedules" :simrs_doctor_id="$doctor->simrs_doctor_id" :type="$type" wire:key="{{ \Illuminate\Support\Str::uuid() }}"/>
                <livewire:landing-page.appointment.component.form-time :appointment="$appointment" wire:key="{{ \Illuminate\Support\Str::uuid() }}"/>
            </div>
        </div>
        <div class="flex flex-col gap-2">
            <span class="text-base font-light">Medical Concern or Request</span>
            <textarea wire:model="selectedMedicalConcern" id="concern" name="concern" rows="4"
                      placeholder="Type your concern"
                      class="border-2 w-full rounded-xl px-6 pt-[18px] text-base font-normal focus:outline-none">
            </textarea>
        </div>
        <livewire:landing-page.appointment.component.upload-image :uuid="$uuid" :appointment="$appointment"/>
        <div class="flex flex-row justify-between mt-4 md:mt-16">
            <div></div>
            <div class="py-4 bg-[#0D4D8B] w-1/2 rounded-xl flex justify-center hover:cursor-pointer gap-2"
                 wire:click="onHandleNextState" onclick="onClickNext()" id="onClickNextButtonSchedule">
                <span class="text-base font-medium text-white">Next</span>
            </div>

            <div class="w-1/2 hidden" id="loadingNextButtonSchedule">
                <div class="py-4 bg-[#0D4D8B] rounded-xl flex justify-center cursor-not-allowed gap-2">
                    <div>
                        @include('landing-page.component.spinner')
                    </div>
                    <span class="text-base font-medium text-white">Next</span>
                </div>
            </div>
{{--                @if($isSelectedDay && $isSelectedTime)--}}
{{--                @else--}}
{{--                    <div class="py-4 bg-gray-400 w-1/2 rounded-xl flex justify-center hover:cursor-not-allowed">--}}
{{--                        <span class="text-base font-medium text-white">Next</span>--}}
{{--                    </div>--}}
{{--                @endif--}}
        </div>
        </div>
</div>

@push('script')
    <script>

        function  onClickNext(){
            $('#onClickNextButtonSchedule').addClass('hidden');
            $('#loadingNextButtonSchedule').removeClass('hidden');
        }

    </script>
@endpush

@script
<script>
    document.getElementById('content').scrollIntoView({ behavior: 'smooth' });

    $wire.on('errorValidation', (event) => {
        document.getElementById('content').scrollIntoView({ behavior: 'smooth' });
        $('#loadingNextButtonSchedule').addClass('hidden');
        $('#onClickNextButtonSchedule').removeClass('hidden');
    });
</script>
@endscript
