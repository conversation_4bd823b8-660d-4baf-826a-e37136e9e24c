<a href="{{ route('profile.mybook.show', ['uuid' => $appointment['uuid'], 'type' => '1']) }}"
   class="flex flex-col p-4 bg-white rounded-xl gap-2 shadow-sm hover:cursor-pointer">
    <div class="flex flex-row justify-between">
        <div class="flex gap-3 items-center">
            @if($appointment['type'] == \App\Enums\Table\AppointmentPatientSummary\Type::TELECONSULTATION)
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M4.5 4.5C2.84315 4.5 1.5 5.84315 1.5 7.5V16.5C1.5 18.1569 2.84315 19.5 4.5 19.5H12.75C14.4069 19.5 15.75 18.1569 15.75 16.5V7.5C15.75 5.84315 14.4069 4.5 12.75 4.5H4.5Z" fill="url(#paint0_linear_6627_42350)"/>
                    <path d="M19.9393 18.75L17.25 16.0606V7.93931L19.9393 5.24996C20.8843 4.30501 22.5 4.97427 22.5 6.31063V17.6893C22.5 19.0257 20.8843 19.6949 19.9393 18.75Z" fill="url(#paint1_linear_6627_42350)"/>
                    <defs>
                        <linearGradient id="paint0_linear_6627_42350" x1="15.5003" y1="4.18751" x2="11.3734" y2="20.6948" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#3674B3"/>
                            <stop offset="1" stop-color="#0B4074"/>
                        </linearGradient>
                        <linearGradient id="paint1_linear_6627_42350" x1="15.5003" y1="4.18751" x2="11.3734" y2="20.6948" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#3674B3"/>
                            <stop offset="1" stop-color="#0B4074"/>
                        </linearGradient>
                    </defs>
                </svg>
            @else
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.75 2.25C7.16421 2.25 7.5 2.58579 7.5 3V4.5H16.5V3C16.5 2.58579 16.8358 2.25 17.25 2.25C17.6642 2.25 18 2.58579 18 3V4.5H18.75C20.4069 4.5 21.75 5.84315 21.75 7.5V18.75C21.75 20.4069 20.4069 21.75 18.75 21.75H5.25C3.59315 21.75 2.25 20.4069 2.25 18.75V7.5C2.25 5.84315 3.59315 4.5 5.25 4.5H6V3C6 2.58579 6.33579 2.25 6.75 2.25ZM20.25 11.25C20.25 10.4216 19.5784 9.75 18.75 9.75H5.25C4.42157 9.75 3.75 10.4216 3.75 11.25V18.75C3.75 19.5784 4.42157 20.25 5.25 20.25H18.75C19.5784 20.25 20.25 19.5784 20.25 18.75V11.25Z" fill="url(#paint0_linear_6627_41809)"/>
                    <defs>
                        <linearGradient id="paint0_linear_6627_41809" x1="15.2502" y1="1.84376" x2="8.14085" y2="22.1562" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#3674B3"/>
                            <stop offset="1" stop-color="#0B4074"/>
                        </linearGradient>
                    </defs>
                </svg>
            @endif

            <div class="flex flex-col gap-1">
                <span class="text-xs lg:text-sm font-light text-[#667085]">{{ $appointment['type'] == \App\Enums\Table\AppointmentPatientSummary\Type::TELECONSULTATION ? 'Teleconsultation' : 'Hospital Visit' }}</span>
                <span class="text-sm lg:text-base text-brand font-medium">{{ $appointment['bookDateTimeLabel'] }}</span>
            </div>
        </div>
        @if($appointment['payment_status'] == \App\Enums\Table\AppointmentPatientSummary\PaymentStatus::UNPAID && $appointment['type'] == \App\Enums\Table\AppointmentPatientSummary\Type::TELECONSULTATION)
            <div class="bg-[#FEF0C7] py-[6px] lg:px-3 h-fit rounded-lg flex items-center">
                <span class="text-xs lg:text-sm font-medium text-[#B54708] text-center">Payment Pending</span>
            </div>
        @elseif($appointment['payment_status'] == \App\Enums\Table\AppointmentPatientSummary\PaymentStatus::EXPIRED)
            <div class="bg-[#FEE4E2] py-[6px] lg:px-3 h-fit rounded-lg flex items-center">
                <span class="text-xs lg:text-sm font-medium text-[#D92D20] text-center">Expired</span>
            </div>
        @else
            @if($appointment['simrs_status'])
                <div class="py-[6px] px-3 h-fit rounded-lg
                {{ \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::getClassName($appointment['simrs_status'])['bg'] }}">
                    <span class="text-xs lg:text-sm font-medium
                    {{ \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::getClassName($appointment['simrs_status'])['text'] }}">
                        {{ \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::getLabel($appointment['simrs_status']) }}
                    </span>
                </div>
            @endif
        @endif

    </div>
    <hr>
    <div class="flex flex-row gap-1 items-center">
        @if($appointment['show_incomplete_icon'])
            <div class="py-1 px-2 bg-warning-60 flex flex-row gap-1 rounded-3xl items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7 2.1875C4.34213 2.1875 2.1875 4.34213 2.1875 7C2.1875 9.65787 4.34213 11.8125 7 11.8125C9.65787 11.8125 11.8125 9.65787 11.8125 7C11.8125 4.34213 9.65787 2.1875 7 2.1875ZM1.3125 7C1.3125 3.85888 3.85888 1.3125 7 1.3125C10.1411 1.3125 12.6875 3.85888 12.6875 7C12.6875 10.1411 10.1411 12.6875 7 12.6875C3.85888 12.6875 1.3125 10.1411 1.3125 7ZM7 4.8125C7.24162 4.8125 7.4375 5.00838 7.4375 5.25V7.4375C7.4375 7.67912 7.24162 7.875 7 7.875C6.75838 7.875 6.5625 7.67912 6.5625 7.4375V5.25C6.5625 5.00838 6.75838 4.8125 7 4.8125ZM6.5625 9.1875C6.5625 8.94588 6.75838 8.75 7 8.75H7.00437C7.246 8.75 7.44187 8.94588 7.44187 9.1875V9.19187C7.44187 9.4335 7.246 9.62937 7.00437 9.62937H7C6.75838 9.62937 6.5625 9.4335 6.5625 9.19187V9.1875Z" fill="white"/>
                </svg>
                <span class="text-xs font-light text-white">Incomplete</span>
            </div>
        @endif
        @if($appointment['show_verified_icon'])
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
            </svg>
        @endif
        <span class="text-base lg:text-xl font-semibold text-brand linguise_patient_name">{{ $appointment['patient']['fullname'] }}</span>
        <span class="text-base lg:text-xl font-light text-[#98A2B3]">({{\App\Enums\Table\Patient\RelationPatient::getLabel($appointment['patient']['relation_patient'])}})</span>
    </div>

    @if(@$appointment['patient']['mr_no'])
        <div class="flex flex-row gap-1 text-[#667085] text-base font-light">
            <span>MR ID: </span>
            <span>{{ @$appointment['patient']['mr_no'] }}</span>
        </div>
    @endif

    <span class="text-sm lg:text-base font-medium linguise_doctor_name">{{ $appointment['doctor']['name'] }}</span>
    <span class="text-sm lg:text-base font-light text-[#98A2B3] linguise_doctor_specialty">{{ $appointment['doctor']['specialty']['group_name_en'] }}</span>
    <div class="flex sm:flex-row flex-col justify-between text-sm lg:text-base">
        @if($appointment['type'] == \App\Enums\Table\AppointmentPatientSummary\Type::TELECONSULTATION)
            <div class="flex flex-row gap-1">
                <span class="font-weight-400 text-[#344054]">Doctor Fee:</span>
                <span class="font-weight-400 text-[#16A34A]">Rp{{str_replace(',', '.', number_format($appointment['amount']))}}</span>
            </div>

            <div class="flex flex-row gap-1">
                <span class="font-weight-400 text-[#344054]">Order Total:</span>
                <span class="font-weight-400 text-[#16A34A]">Rp{{str_replace(',', '.', number_format($appointment['amount']))}}</span>
            </div>
        @endif
    </div>
    @if($appointment['payment_status'] == \App\Enums\Table\AppointmentPatientSummary\PaymentStatus::UNPAID && $appointment['type'] == \App\Enums\Table\AppointmentPatientSummary\Type::TELECONSULTATION)
        <div class="rounded-lg p-2 px-3 flex flex-row bg-[#F2F4F7] justify-between">
            <div class="flex flex-col gap-1 w-1/2">
                <span class="text-xs lg:text-sm font-light">Total</span>
                <span class="text-base lg:text-lg font-bold text-[#16A34A]">Rp{{str_replace(',', '.', number_format($appointment['amount']))}}</span>
            </div>
            <a href="{{$appointment['payment_invoice_url']}}" class="rounded-xl py-3 lg:py-[18px] w-1/2 flex flex-row justify-center items-center hover:cursor-pointer bg-brand gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                    <path d="M11.2499 10.8176V13.4324C11.6816 13.3527 12.0745 13.2046 12.3876 12.9999C12.8698 12.6846 12.9999 12.352 12.9999 12.125C12.9999 11.898 12.8698 11.5654 12.3876 11.2501C12.0745 11.0454 11.6816 10.8973 11.2499 10.8176Z" fill="white"/>
                    <path d="M8.82961 8.61947C8.88337 8.67543 8.94464 8.73053 9.01404 8.78416C9.22197 8.94484 9.47355 9.06777 9.75 9.1469V6.60315C9.67545 6.62449 9.60271 6.64901 9.53215 6.6766C9.48721 6.69417 9.44315 6.71299 9.40007 6.73302C9.25996 6.79816 9.13019 6.87614 9.01404 6.96589C8.63658 7.25757 8.5 7.59253 8.5 7.87503C8.5 8.05887 8.55784 8.26493 8.70228 8.46683C8.73898 8.51812 8.78126 8.56915 8.82961 8.61947Z" fill="white"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M18.5 10C18.5 14.4183 14.9183 18 10.5 18C6.08172 18 2.5 14.4183 2.5 10C2.5 5.58172 6.08172 2 10.5 2C14.9183 2 18.5 5.58172 18.5 10ZM10.4999 4C10.9142 4 11.2499 4.33579 11.2499 4.75V5.06584C11.8423 5.17106 12.4182 5.40427 12.9031 5.77893C13.3293 6.10829 13.6467 6.51836 13.8282 6.97896C13.9801 7.36432 13.7908 7.79985 13.4055 7.95174C13.0201 8.10363 12.5846 7.91437 12.4327 7.52901C12.3599 7.34437 12.22 7.14675 11.9859 6.96586C11.778 6.80519 11.5264 6.68225 11.2499 6.60312V9.29944C11.948 9.39233 12.6327 9.61819 13.2085 9.99467C13.9955 10.5093 14.4999 11.2644 14.4999 12.125C14.4999 12.9856 13.9955 13.7407 13.2085 14.2553C12.6327 14.6318 11.948 14.8577 11.2499 14.9506V15.25C11.2499 15.6642 10.9142 16 10.4999 16C10.0857 16 9.74994 15.6642 9.74994 15.25V14.9506C9.05186 14.8577 8.3672 14.6318 7.79141 14.2553C7.30887 13.9398 6.9337 13.5376 6.71337 13.0672C6.5377 12.692 6.69937 12.2455 7.07449 12.0699C7.4496 11.8942 7.89611 12.0559 8.07178 12.431C8.15258 12.6035 8.31692 12.8067 8.61229 12.9999C8.92537 13.2046 9.3183 13.3526 9.74994 13.4324V10.6842C9.15762 10.5789 8.58167 10.3457 8.09681 9.97107C7.40033 9.43288 6.99994 8.68017 6.99994 7.875C6.99994 7.06983 7.40034 6.31712 8.09681 5.77893C8.58167 5.40427 9.15762 5.17106 9.74994 5.06584V4.75C9.74994 4.33579 10.0857 4 10.4999 4Z" fill="white"/>
                </svg>
                <span class="text-sm lg:text-base font-semibold text-white">Pay Now</span>
            </a>
        </div>
    @endif
{{--    @if($appointment['simrs_status'] == \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::COMPLETED)--}}
{{--        @if($appointment['show_incomplete_icon'])--}}
{{--            <div class="rounded-lg border border-brand py-[10px] flex items-center justify-center gap-2 hover:cursor-pointer">--}}
{{--                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">--}}
{{--                    <path fill-rule="evenodd" clip-rule="evenodd" d="M4.6875 2.5C4.51491 2.5 4.375 2.63991 4.375 2.8125V17.1875C4.375 17.3601 4.51491 17.5 4.6875 17.5H15.3125C15.4851 17.5 15.625 17.3601 15.625 17.1875V9.6875C15.625 8.47938 14.6456 7.5 13.4375 7.5H12.1875C11.3246 7.5 10.625 6.80044 10.625 5.9375V4.6875C10.625 3.47938 9.64562 2.5 8.4375 2.5H4.6875ZM4.6875 1.25C3.82456 1.25 3.125 1.94955 3.125 2.8125V17.1875C3.125 18.0504 3.82455 18.75 4.6875 18.75H15.3125C16.1754 18.75 16.875 18.0504 16.875 17.1875V9.375C16.875 4.88769 13.2373 1.25 8.75 1.25H4.6875ZM11.4642 3.05654C11.7263 3.54183 11.875 4.09729 11.875 4.6875V5.9375C11.875 6.11009 12.0149 6.25 12.1875 6.25H13.4375C14.0277 6.25 14.5832 6.39875 15.0685 6.6608C14.374 5.04624 13.0788 3.75102 11.4642 3.05654ZM6.25 12.5C6.25 12.1548 6.52982 11.875 6.875 11.875H13.125C13.4702 11.875 13.75 12.1548 13.75 12.5C13.75 12.8452 13.4702 13.125 13.125 13.125H6.875C6.52982 13.125 6.25 12.8452 6.25 12.5ZM6.25 15C6.25 14.6548 6.52982 14.375 6.875 14.375H10C10.3452 14.375 10.625 14.6548 10.625 15C10.625 15.3452 10.3452 15.625 10 15.625H6.875C6.52982 15.625 6.25 15.3452 6.25 15Z" fill="#0D4D8B"/>--}}
{{--                </svg>--}}
{{--                <span class="text-base text-brand font-semibold">View Medical Resume</span>--}}
{{--            </div>--}}
{{--        @endif--}}
{{--    @endif--}}
    @if($appointment['show_component_number_7'])
        <div class="rounded-lg p-2 px-3 flex flex-row justify-between items-center border border-[#B54708] bg-[#FFF9EB] text-[#B54708] gap-2"
             data-id="{{$appointment['patient_id']}}" data-uuid="{{ $appointment['patient']['uuid'] }}">
            <span class="text-xs lg:text-sm font-light w-1/2">Please complete your profile before visiting the hospital.</span>
            <a href="{{route('profile.show', $appointment['patient']['uuid'])}}"
               class="rounded-xl py-[10px] w-1/2 flex flex-row justify-center items-center hover:cursor-pointer gap-2 bg-brand">
                <span class="text-sm lg:text-base font-semibold text-white">Complete Profile</span>
            </a>
        </div>
    @endif
    @if($appointment['show_component_number_6'])
        <div class="rounded-lg p-2 px-3 flex flex-row justify-between items-center border border-[#B54708] bg-[#FFF9EB] text-[#B54708] gap-2"
             data-id="{{$appointment['patient_id']}}" data-uuid="{{ $appointment['patient']['uuid'] }}">
            <span class="text-sm font-light w-1/2">Please complete your profile to join the Teleconsultation.</span>
            <a href="{{route('profile.show', $appointment['patient']['uuid'])}}"
               class="rounded-xl py-[10px] w-1/2 flex flex-row justify-center items-center hover:cursor-pointer gap-2 bg-brand">
                <span class="text-sm lg:text-base font-semibold text-white">Complete Profile</span>
            </a>
        </div>
    @endif
    @if($appointment['show_component_number_8'])
        <div onclick="onClickUrl(event, '#')" class="bg-brand flex flex-row items-center justify-center py-3 rounded-xl gap-3 hover:cursor-not-allowed opacity-50">
            <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                <path d="M5 4.5C3.34315 4.5 2 5.84315 2 7.5V16.5C2 18.1569 3.34315 19.5 5 19.5H13.25C14.9069 19.5 16.25 18.1569 16.25 16.5V7.5C16.25 5.84315 14.9069 4.5 13.25 4.5H5Z" fill="white"/>
                <path d="M20.4393 18.75L17.75 16.0606V7.93931L20.4393 5.24996C21.3843 4.30501 23 4.97427 23 6.31063V17.6893C23 19.0257 21.3843 19.6949 20.4393 18.75Z" fill="white"/>
            </svg>
            <span class="text-sm lg:text-base font-semibold text-white">Join Teleconsultation</span>
        </div>
    @endif

    @if($appointment['show_component_number_9'])
        @if($messageUnavailableUrl)
            <span class="text-white text-xs p-2 rounded-xl bg-red-800">Apologies, the URL is currently unavailable. Please try again later.</span>
        @endif
        @if(@$appointment['url_link_teleconsultation'])
            <div onclick="onClickUrl(event, '{{$appointment['url_link_teleconsultation']}}')" class="bg-brand flex flex-row items-center justify-center py-3 rounded-xl gap-3 hover:cursor-pointer">
                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                    <path d="M5 4.5C3.34315 4.5 2 5.84315 2 7.5V16.5C2 18.1569 3.34315 19.5 5 19.5H13.25C14.9069 19.5 16.25 18.1569 16.25 16.5V7.5C16.25 5.84315 14.9069 4.5 13.25 4.5H5Z" fill="white"/>
                    <path d="M20.4393 18.75L17.75 16.0606V7.93931L20.4393 5.24996C21.3843 4.30501 23 4.97427 23 6.31063V17.6893C23 19.0257 21.3843 19.6949 20.4393 18.75Z" fill="white"/>
                </svg>
                <span class="text-sm lg:text-base font-semibold text-white">Join Teleconsultation</span>
            </div>
        @else
            <div wire:click="onClickCheckAppointment" onclick="onClickPreventPropagation(event)" class="bg-brand flex flex-row items-center justify-center py-3 rounded-xl gap-3 hover:cursor-pointer">
                <div class="flex flex-row gap-2" wire:loading.remove wire:target="onClickCheckAppointment">
                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                        <path d="M5 4.5C3.34315 4.5 2 5.84315 2 7.5V16.5C2 18.1569 3.34315 19.5 5 19.5H13.25C14.9069 19.5 16.25 18.1569 16.25 16.5V7.5C16.25 5.84315 14.9069 4.5 13.25 4.5H5Z" fill="white"/>
                        <path d="M20.4393 18.75L17.75 16.0606V7.93931L20.4393 5.24996C21.3843 4.30501 23 4.97427 23 6.31063V17.6893C23 19.0257 21.3843 19.6949 20.4393 18.75Z" fill="white"/>
                    </svg>
                    <span class="text-sm lg:text-base font-semibold text-white">Join Teleconsultation</span>
                </div>
                <div wire:loading wire:target="onClickCheckAppointment">
                    @include('landing-page.component.spinner')
                </div>
            </div>
        @endif
    @endif

</a>

@push('script')
    <script>
        function onHandleClickDetailAppointment(uuid) {
            // Construct the URL with the provided UUID and type parameters
            // Redirect to the constructed URL
            window.location.href = "{{ route('profile.mybook.show', ['uuid' => ':uuid', 'type' => ':type']) }}"
                .replace(':uuid', uuid)
                .replace(':type', 1);
        }

        function onClickPreventPropagation(event) {
            event.stopPropagation();
        }

        function onClickUrl(event, url) {
            event.stopPropagation();

            // Check if the URL starts with 'http://' or 'https://'
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                // If not, prepend 'http://'
                url = 'http://' + url;
            }

            // Open the URL in a new tab
            window.open(url, '_blank');
        }

        function onHandleChild(event) {
            // Prevent the default behavior of the event
            // event.preventDefault();
            // Prevent the event from bubbling up the DOM tree
            // event.stopPropagation();
        }
    </script>
@endpush

@script
    <script>

        $wire.on('redirectToUrl', (event) => {
            let url = event[0]['url'];
            // Check if the URL starts with 'http://' or 'https://'
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                // If not, prepend 'http://'
                url = 'http://' + url;
            }
            // Open the URL in a new tab
            window.open(url, '_blank');
        });

    </script>
@endscript
