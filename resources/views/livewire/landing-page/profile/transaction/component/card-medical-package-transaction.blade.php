<a href="{{ route('profile.mybook.show', ['uuid' => $packageSummary['uuid'], 'type' => $packageSummary['type_transaction']]) }}"
   class="hover:cursor-pointer flex flex-col p-4 bg-white rounded-xl gap-2 shadow-sm">
    @if(count($packageSummary['package_summary_details']) > 1)
        <div class="flex flex-row justify-between">
            <div class="flex gap-2 items-center">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none">
                    <path d="M7.1665 16.1667C7.16651 22 17.0832 22.5833 17.0832 16.1667V11.5" stroke="#0D4D8B" stroke-width="1.1"/>
                    <path d="M17.0832 9.73829H17.0949V9.74996H17.0832V9.73829Z" stroke="#0D4D8B" stroke-width="1.1" stroke-linejoin="round"/>
                    <path d="M18.8332 9.75C18.8332 10.7165 18.0497 11.5 17.0832 11.5C16.1167 11.5 15.3332 10.7165 15.3332 9.75C15.3332 8.7835 16.1167 8 17.0832 8C18.0497 8 18.8332 8.7835 18.8332 9.75Z" stroke="#0D4D8B" stroke-width="1.1"/>
                    <path d="M4.24986 2.16667V2.16667C2.63903 2.16667 1.33319 3.4725 1.33319 5.08333V10.3333C1.33319 13.555 3.94486 16.1667 7.16652 16.1667V16.1667C10.3882 16.1667 12.9999 13.555 12.9999 10.3333V4.5C12.9999 3.21134 11.9552 2.16667 10.6665 2.16667V2.16667M4.24986 2.16667C4.24986 2.811 4.77219 3.33333 5.41652 3.33333C6.06086 3.33333 6.58319 2.811 6.58319 2.16667C6.58319 1.52233 6.06086 1 5.41652 1C4.77219 1 4.24986 1.52233 4.24986 2.16667ZM10.6665 2.16667C10.6665 2.811 10.1442 3.33333 9.49986 3.33333C8.85553 3.33333 8.33319 2.811 8.33319 2.16667C8.33319 1.52233 8.85553 1 9.49986 1C10.1442 1 10.6665 1.52233 10.6665 2.16667Z" stroke="#0D4D8B" stroke-width="1.1"/>
                </svg>
                <div class="flex flex-col gap-1">
                    <span class="text-xs lg:text-sm font-light text-[#667085]">Multiple Packages</span>
                    <span class="text-sm lg:text-base text-brand font-medium">Multiple Dates</span>
                </div>
            </div>
            <div class="py-[6px] px-3 h-fit rounded-lg
                {{ \App\Enums\Table\PackageSummary\StatusPayment::getClassName($packageSummary['status_payment'])['bg'] }}">
                    <span class="text-xs lg:text-sm font-medium
                    {{ \App\Enums\Table\PackageSummary\StatusPayment::getClassName($packageSummary['status_payment'])['text'] }}">
                        {{ \App\Enums\Table\PackageSummary\StatusPayment::getLabel($packageSummary['status_payment']) }}
                    </span>
            </div>
        </div>
        <hr>
        @foreach($packageSummary['package_summary_details'] as $packageSummaryDetail)
            <div class="flex flex-row justify-between items-center text-sm lg:text-base">
                <span class="font-medium">{{ $packageSummaryDetail['title'] }}</span>
                <span class="font-medium text-[#667085]">{{ $packageSummaryDetail['visit_date_time_label'] }}</span>
            </div>
        @endforeach

        @if($packageSummary['status_payment'] == \App\Enums\Table\PackageSummary\StatusPayment::UNPAID)
            <div class="rounded-lg p-2 px-3 flex flex-row bg-[#F2F4F7] justify-between">
                <div class="flex flex-col gap-1 w-1/2 justify-center">
                    <span class="text-xs lg:text-sm font-light">Total</span>
                    <span class="text-base lg:text-lg font-bold text-[#16A34A]">Rp{{str_replace(',', '.', number_format((int)$packageSummary['amount_pay']))}}</span>
                </div>
                <a href="{{$packageSummary['payment_invoice_url']}}" class="rounded-xl py-3 lg:py-[18px] w-1/2 flex flex-row justify-center items-center hover:cursor-pointer bg-brand gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                        <path d="M11.2499 10.8176V13.4324C11.6816 13.3527 12.0745 13.2046 12.3876 12.9999C12.8698 12.6846 12.9999 12.352 12.9999 12.125C12.9999 11.898 12.8698 11.5654 12.3876 11.2501C12.0745 11.0454 11.6816 10.8973 11.2499 10.8176Z" fill="white"/>
                        <path d="M8.82961 8.61947C8.88337 8.67543 8.94464 8.73053 9.01404 8.78416C9.22197 8.94484 9.47355 9.06777 9.75 9.1469V6.60315C9.67545 6.62449 9.60271 6.64901 9.53215 6.6766C9.48721 6.69417 9.44315 6.71299 9.40007 6.73302C9.25996 6.79816 9.13019 6.87614 9.01404 6.96589C8.63658 7.25757 8.5 7.59253 8.5 7.87503C8.5 8.05887 8.55784 8.26493 8.70228 8.46683C8.73898 8.51812 8.78126 8.56915 8.82961 8.61947Z" fill="white"/>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M18.5 10C18.5 14.4183 14.9183 18 10.5 18C6.08172 18 2.5 14.4183 2.5 10C2.5 5.58172 6.08172 2 10.5 2C14.9183 2 18.5 5.58172 18.5 10ZM10.4999 4C10.9142 4 11.2499 4.33579 11.2499 4.75V5.06584C11.8423 5.17106 12.4182 5.40427 12.9031 5.77893C13.3293 6.10829 13.6467 6.51836 13.8282 6.97896C13.9801 7.36432 13.7908 7.79985 13.4055 7.95174C13.0201 8.10363 12.5846 7.91437 12.4327 7.52901C12.3599 7.34437 12.22 7.14675 11.9859 6.96586C11.778 6.80519 11.5264 6.68225 11.2499 6.60312V9.29944C11.948 9.39233 12.6327 9.61819 13.2085 9.99467C13.9955 10.5093 14.4999 11.2644 14.4999 12.125C14.4999 12.9856 13.9955 13.7407 13.2085 14.2553C12.6327 14.6318 11.948 14.8577 11.2499 14.9506V15.25C11.2499 15.6642 10.9142 16 10.4999 16C10.0857 16 9.74994 15.6642 9.74994 15.25V14.9506C9.05186 14.8577 8.3672 14.6318 7.79141 14.2553C7.30887 13.9398 6.9337 13.5376 6.71337 13.0672C6.5377 12.692 6.69937 12.2455 7.07449 12.0699C7.4496 11.8942 7.89611 12.0559 8.07178 12.431C8.15258 12.6035 8.31692 12.8067 8.61229 12.9999C8.92537 13.2046 9.3183 13.3526 9.74994 13.4324V10.6842C9.15762 10.5789 8.58167 10.3457 8.09681 9.97107C7.40033 9.43288 6.99994 8.68017 6.99994 7.875C6.99994 7.06983 7.40034 6.31712 8.09681 5.77893C8.58167 5.40427 9.15762 5.17106 9.74994 5.06584V4.75C9.74994 4.33579 10.0857 4 10.4999 4Z" fill="white"/>
                    </svg>
                    <span class="text-sm lg:text-base font-semibold text-white">Pay Now</span>
                </a>
            </div>
        @endif
    @else
        <div class="flex flex-row justify-between">
            <div class="flex gap-2 items-center">
                @if(@$packageSummary['package_summary_details'][0]['type']['icon'])
                    <img src="{{ asset_gcs($packageSummary['package_summary_details'][0]['type']['icon']) }}" alt="icon bithealth" class="h-[20px] w-[20px] object-cover">
                @else
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none">
                        <path d="M7.1665 16.1667C7.16651 22 17.0832 22.5833 17.0832 16.1667V11.5" stroke="#0D4D8B" stroke-width="1.1"/>
                        <path d="M17.0832 9.73829H17.0949V9.74996H17.0832V9.73829Z" stroke="#0D4D8B" stroke-width="1.1" stroke-linejoin="round"/>
                        <path d="M18.8332 9.75C18.8332 10.7165 18.0497 11.5 17.0832 11.5C16.1167 11.5 15.3332 10.7165 15.3332 9.75C15.3332 8.7835 16.1167 8 17.0832 8C18.0497 8 18.8332 8.7835 18.8332 9.75Z" stroke="#0D4D8B" stroke-width="1.1"/>
                        <path d="M4.24986 2.16667V2.16667C2.63903 2.16667 1.33319 3.4725 1.33319 5.08333V10.3333C1.33319 13.555 3.94486 16.1667 7.16652 16.1667V16.1667C10.3882 16.1667 12.9999 13.555 12.9999 10.3333V4.5C12.9999 3.21134 11.9552 2.16667 10.6665 2.16667V2.16667M4.24986 2.16667C4.24986 2.811 4.77219 3.33333 5.41652 3.33333C6.06086 3.33333 6.58319 2.811 6.58319 2.16667C6.58319 1.52233 6.06086 1 5.41652 1C4.77219 1 4.24986 1.52233 4.24986 2.16667ZM10.6665 2.16667C10.6665 2.811 10.1442 3.33333 9.49986 3.33333C8.85553 3.33333 8.33319 2.811 8.33319 2.16667C8.33319 1.52233 8.85553 1 9.49986 1C10.1442 1 10.6665 1.52233 10.6665 2.16667Z" stroke="#0D4D8B" stroke-width="1.1"/>
                    </svg>
                @endif
                <div class="flex flex-col gap-1">
                    <span class="text-xs lg:text-sm font-light text-[#667085] linguise_package_type">{{ $packageSummary['package_summary_details'][0]['type']['name'] }}</span>
                    <span class="text-sm lg:text-base text-brand font-medium">{{ $packageSummary['package_summary_details'][0]['visit_date_time_label'] }}</span>
                </div>
            </div>
            @if($packageSummary['type_transaction'] !== 3)
                @if((int)$packageSummary['status_payment'] == \App\Enums\Table\PackageSummary\StatusPayment::UNPAID)
                    <div class="py-1 lg:py-[6px] px-3 h-fit rounded-lg
                {{ \App\Enums\Table\PackageSummary\StatusPayment::getClassName($packageSummary['status_payment'])['bg'] }}">
                        <span class="text-xs lg:text-sm font-medium
                        {{ \App\Enums\Table\PackageSummary\StatusPayment::getClassName($packageSummary['status_payment'])['text'] }}">
                            {{ \App\Enums\Table\PackageSummary\StatusPayment::getLabel($packageSummary['status_payment']) }}
                        </span>
                    </div>
                @else
                    <div class="py-[6px] px-3 h-fit rounded-lg">
                        <span class="text-sm font-medium">
                            {{ \App\Enums\Table\PackageSummaryDetail\Status::getLabel($packageSummary['package_summary_details'][0]['status']) }}
                        </span>
                    </div>
                @endif
            @endif
            @if($packageSummary['type_transaction'] == 3)
                @if(in_array($packageSummary['package_summary_details'][0]['status'],[\App\Enums\Table\PackageSummaryDetail\Status::PAYMENT_PENDING]))
                    <div class="bg-[#FEF0C7] py-1 lg:py-[6px] px-3 h-fit rounded-lg">
                        <span class="text-xs lg:text-sm font-medium text-[#B54708]">Payment Pending</span>
                    </div>
                @elseif(in_array($packageSummary['package_summary_details'][0]['status'],[\App\Enums\Table\PackageSummaryDetail\Status::IN_PROCESS, \App\Enums\Table\PackageSummaryDetail\Status::UP_COMING]))
                    <div class="bg-[#E0EAFF] py-1 lg:py-[6px] px-3 h-fit rounded-lg">
                        <span class="text-xs lg:text-sm font-medium text-brand">{{ \App\Enums\Table\PackageSummaryDetail\Status::getLabel($packageSummary['package_summary_details'][0]['status']) }}</span>
                    </div>
                @elseif(in_array($packageSummary['package_summary_details'][0]['status'],[\App\Enums\Table\PackageSummaryDetail\Status::CANCEL]))
                    <div class="bg-[#FEE4E2] py-1 lg:py-[6px] px-3 h-fit rounded-lg">
                        <span class="text-xs lg:text-sm font-medium text-[#D92D20]">{{ \App\Enums\Table\PackageSummaryDetail\Status::getLabel($packageSummary['package_summary_details'][0]['status']) }}</span>
                    </div>
                @elseif(in_array($packageSummary['package_summary_details'][0]['status'],[\App\Enums\Table\PackageSummaryDetail\Status::COMPLETE]))
                    <div class="bg-[#D1FADF] py-1 lg:py-[6px] px-3 h-fit rounded-lg">
                        <span class="text-xs lg:text-sm font-medium text-[#027A48]">{{ \App\Enums\Table\PackageSummaryDetail\Status::getLabel($packageSummary['package_summary_details'][0]['status']) }}</span>
                    </div>
                @endif
            @endif

        </div>
        <hr>
        <div class="flex flex-row gap-1">
            @if($packageSummary['package_summary_details'][0]['show_incomplete_icon'])
                <div class="py-1 px-2 bg-warning-60 flex flex-row gap-1 rounded-3xl items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M7 2.1875C4.34213 2.1875 2.1875 4.34213 2.1875 7C2.1875 9.65787 4.34213 11.8125 7 11.8125C9.65787 11.8125 11.8125 9.65787 11.8125 7C11.8125 4.34213 9.65787 2.1875 7 2.1875ZM1.3125 7C1.3125 3.85888 3.85888 1.3125 7 1.3125C10.1411 1.3125 12.6875 3.85888 12.6875 7C12.6875 10.1411 10.1411 12.6875 7 12.6875C3.85888 12.6875 1.3125 10.1411 1.3125 7ZM7 4.8125C7.24162 4.8125 7.4375 5.00838 7.4375 5.25V7.4375C7.4375 7.67912 7.24162 7.875 7 7.875C6.75838 7.875 6.5625 7.67912 6.5625 7.4375V5.25C6.5625 5.00838 6.75838 4.8125 7 4.8125ZM6.5625 9.1875C6.5625 8.94588 6.75838 8.75 7 8.75H7.00437C7.246 8.75 7.44187 8.94588 7.44187 9.1875V9.19187C7.44187 9.4335 7.246 9.62937 7.00437 9.62937H7C6.75838 9.62937 6.5625 9.4335 6.5625 9.19187V9.1875Z" fill="white"/>
                    </svg>
                    <span class="text-xs font-light text-white">Incomplete</span>
                </div>
            @endif
            @if(@$packageSummary['package_summary_details'][0]['patient']['is_complete_data'] && @$packageSummary['package_summary_details'][0]['patient']['mr_no'])
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                </svg>
            @endif
            <span class="text-base lg:text-xl font-semibold text-brand linguise_patient_name">{{ $packageSummary['package_summary_details'][0]['patient']['fullname'] }}</span>
            <span class="text-base lg:text-xl font-light text-[#98A2B3]">({{\App\Enums\Table\Patient\RelationPatient::getLabel($packageSummary['package_summary_details'][0]['patient']['relation_patient'])}})</span>
        </div>
        @if(@$packageSummary['package_summary_details'][0]['patient']['mr_no'])
            <div class="flex flex-row gap-1 text-[#667085] text-base font-light">
                <span>MR ID: </span>
                <span>{{ @$packageSummary['package_summary_details'][0]['patient']['mr_no'] }}</span>
            </div>
        @endif
        <span class="text-sm lg:text-base font-medium linguise_package_name">{{ $packageSummary['package_summary_details'][0]['package']['title'] }}</span>
        <span class="text-sm lg:text-base font-light text-[#98A2B3] linguise_package_category">{{ $packageSummary['package_summary_details'][0]['category']['name'] }}</span>
        <div class="flex flex-col sm:flex-row justify-between text-sm lg:text-base">
            <div class="flex flex-row gap-1">
                <span class="font-weight-400 text-[#344054]">Package Price:</span>
                <span class="font-weight-400 text-[#16A34A] linguise_package_price">Rp{{str_replace(',', '.', number_format($packageSummary['package_summary_details'][0]['price']))}}</span>
            </div>
            <div class="flex flex-row gap-1">
                <span class="font-weight-400 text-[#344054]">Order Total:</span>
                <span class="font-weight-400 text-[#16A34A] linguise_package_price">Rp{{str_replace(',', '.', number_format($packageSummary['amount_pay']))}}</span>
            </div>
        </div>
        @if($packageSummary['type_transaction'] !== 3)
            @if($packageSummary['status_payment'] == \App\Enums\Table\PackageSummary\StatusPayment::UNPAID)
                <div class="rounded-lg p-2 px-3 flex flex-row bg-[#F2F4F7] justify-between">
                    <div class="flex flex-col gap-1 w-1/2">
                        <span class="text-xs lg:text-sm font-light">Total</span>
                        <span class="text-base lg:text-lg font-bold text-[#16A34A]">Rp{{str_replace(',', '.', number_format((int)$packageSummary['amount_pay']))}}</span>
                    </div>
                    <a href="{{$packageSummary['payment_invoice_url']}}" class="rounded-xl py-3 lg:py-[18px] w-1/2 flex flex-row justify-center items-center hover:cursor-pointer bg-brand gap-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                            <path d="M11.2499 10.8176V13.4324C11.6816 13.3527 12.0745 13.2046 12.3876 12.9999C12.8698 12.6846 12.9999 12.352 12.9999 12.125C12.9999 11.898 12.8698 11.5654 12.3876 11.2501C12.0745 11.0454 11.6816 10.8973 11.2499 10.8176Z" fill="white"/>
                            <path d="M8.82961 8.61947C8.88337 8.67543 8.94464 8.73053 9.01404 8.78416C9.22197 8.94484 9.47355 9.06777 9.75 9.1469V6.60315C9.67545 6.62449 9.60271 6.64901 9.53215 6.6766C9.48721 6.69417 9.44315 6.71299 9.40007 6.73302C9.25996 6.79816 9.13019 6.87614 9.01404 6.96589C8.63658 7.25757 8.5 7.59253 8.5 7.87503C8.5 8.05887 8.55784 8.26493 8.70228 8.46683C8.73898 8.51812 8.78126 8.56915 8.82961 8.61947Z" fill="white"/>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.5 10C18.5 14.4183 14.9183 18 10.5 18C6.08172 18 2.5 14.4183 2.5 10C2.5 5.58172 6.08172 2 10.5 2C14.9183 2 18.5 5.58172 18.5 10ZM10.4999 4C10.9142 4 11.2499 4.33579 11.2499 4.75V5.06584C11.8423 5.17106 12.4182 5.40427 12.9031 5.77893C13.3293 6.10829 13.6467 6.51836 13.8282 6.97896C13.9801 7.36432 13.7908 7.79985 13.4055 7.95174C13.0201 8.10363 12.5846 7.91437 12.4327 7.52901C12.3599 7.34437 12.22 7.14675 11.9859 6.96586C11.778 6.80519 11.5264 6.68225 11.2499 6.60312V9.29944C11.948 9.39233 12.6327 9.61819 13.2085 9.99467C13.9955 10.5093 14.4999 11.2644 14.4999 12.125C14.4999 12.9856 13.9955 13.7407 13.2085 14.2553C12.6327 14.6318 11.948 14.8577 11.2499 14.9506V15.25C11.2499 15.6642 10.9142 16 10.4999 16C10.0857 16 9.74994 15.6642 9.74994 15.25V14.9506C9.05186 14.8577 8.3672 14.6318 7.79141 14.2553C7.30887 13.9398 6.9337 13.5376 6.71337 13.0672C6.5377 12.692 6.69937 12.2455 7.07449 12.0699C7.4496 11.8942 7.89611 12.0559 8.07178 12.431C8.15258 12.6035 8.31692 12.8067 8.61229 12.9999C8.92537 13.2046 9.3183 13.3526 9.74994 13.4324V10.6842C9.15762 10.5789 8.58167 10.3457 8.09681 9.97107C7.40033 9.43288 6.99994 8.68017 6.99994 7.875C6.99994 7.06983 7.40034 6.31712 8.09681 5.77893C8.58167 5.40427 9.15762 5.17106 9.74994 5.06584V4.75C9.74994 4.33579 10.0857 4 10.4999 4Z" fill="white"/>
                        </svg>
                        <span class="text-sm lg:text-base font-semibold text-white">Pay Now</span>
                    </a>
                </div>
            @endif
        @endif
        @if($packageSummary['package_summary_details'][0]['show_component_number_8'])
            <div class="rounded-lg p-2 px-3 flex flex-row bg-[#F2F4F7] justify-between items-center">
                <span class="text-xs lg:text-sm font-light w-1/2">Please complete your profile before visiting the hospital.</span>
                <a href="{{route('profile.show', $packageSummary['package_summary_details'][0]['patient_uuid'])}}"
                   class="bg-white rounded-xl py-[10px] w-1/2 flex flex-row justify-center items-center hover:cursor-pointer gap-2 border border-brand">
                    <span class="text-sm lg:text-base font-semibold text-brand">Complete Profile</span>
                </a>
            </div>
        @endif
    @endif

</a>

@push('script')
    <script>
        function onHandleClickDetail(uuid, type) {
            // Construct the URL with the provided UUID and type parameters
            // Redirect to the constructed URL
            window.location.href = "{{ route('profile.mybook.show', ['uuid' => ':uuid', 'type' => ':type']) }}"
                .replace(':uuid', uuid)
                .replace(':type', type);
        }
    </script>
@endpush
