<div>
    <div class="border border-[#B54708] rounded-xl py-3 px-4 bg-[#FFFAEB] mb-4">
        <div class="flex flex-row gap-4">
            <div class="mt-1">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M14.4876 12.0005L9.15426 2.66714C9.03797 2.46194 8.86933 2.29127 8.66555 2.17252C8.46176 2.05378 8.23012 1.99121 7.99426 1.99121C7.7584 1.99121 7.52677 2.05378 7.32298 2.17252C7.11919 2.29127 6.95055 2.46194 6.83426 2.66714L1.50093 12.0005C1.38338 12.204 1.32175 12.4351 1.32227 12.6701C1.32279 12.9052 1.38545 13.136 1.50389 13.339C1.62234 13.5421 1.79236 13.7102 1.99673 13.8264C2.20109 13.9425 2.43253 14.0026 2.6676 14.0005H13.3343C13.5682 14.0002 13.7979 13.9385 14.0005 13.8213C14.203 13.7042 14.3711 13.5359 14.4879 13.3332C14.6048 13.1306 14.6663 12.9007 14.6662 12.6668C14.6662 12.4329 14.6046 12.2031 14.4876 12.0005Z" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M8 6V8.66667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round" />
                    <path d="M8 11.333H8.00667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
            </div>
            <div class="flex flex-col text-[#93370D] gap-1">
                <span class="text-sm font-light warning-description">{{ $process_message }}</span>
            </div>
        </div>
    </div>
    <div class="mb-4">
        @include('livewire.landing-page.profile.component.mandatory_icon_description')
    </div>
    <div class="flex flex-col gap-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-2 md:gap-4">
            @if($patient?->origin==null)
            <div class="flex flex-col gap-2 md:col-span-2">
                <span class="text-base text-[#475467]">Citizenship <span class="text-red-600">**</span></span>
                <div class="flex flex-row gap-5">
                    <div class="flex items-center gap-1 cursor-pointer" wire:click="onClickCitizenship(1)">
                        @if($citizenship == 1)
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                            <rect x="1" y="0.5" width="19" height="19" rx="9.5" fill="#E6F2FF" />
                            <rect x="1" y="0.5" width="19" height="19" rx="9.5" stroke="#0D4D8B" />
                            <circle cx="10.5" cy="10" r="4" fill="#0D4D8B" />
                        </svg>
                        @else
                        <div class="h-5 w-5 bg-white rounded-full border border-brand"></div>
                        @endif
                        <span class="text-base text-[#09335D] {{ $citizenship == 1 ? 'font-weight-500' : '' }}">Domestic</span>
                    </div>
                    <div class="flex items-center gap-1 cursor-pointer" wire:click="onClickCitizenship(2)">
                        @if($citizenship == 2)
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                            <rect x="1" y="0.5" width="19" height="19" rx="9.5" fill="#E6F2FF" />
                            <rect x="1" y="0.5" width="19" height="19" rx="9.5" stroke="#0D4D8B" />
                            <circle cx="10.5" cy="10" r="4" fill="#0D4D8B" />
                        </svg>
                        @else
                        <div class="h-5 w-5 bg-white rounded-full border border-brand"></div>
                        @endif
                        <span class="text-base text-[#09335D] {{ $citizenship == 2 ? 'font-weight-500' : '' }}">International</span>
                    </div>
                </div>
                @if(Session::get('error_citizenship'))
                <span class="text-xs text-red-600">{{ Session::get('error_citizenship') }}</span>
                @endif
            </div>
            @else
            <div class="flex flex-col gap-2 md:col-span-2">
                <span class="text-base text-[#475467]">Citizenship <span class="text-red-600">**</span></span>
                @if($citizenship)
                <div class="flex flex-row gap-5">
                    <div class="flex items-center gap-1 cursor-pointer">
                        @if($citizenship == 1)
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                            <rect x="1" y="0.5" width="19" height="19" rx="9.5" fill="#D3D3D3" />
                            <rect x="1" y="0.5" width="19" height="19" rx="9.5" stroke="#A9A9A9" />
                            <circle cx="10.5" cy="10" r="4" fill="#A9A9A9" />
                        </svg>
                        @else
                        <div class="h-5 w-5 bg-white rounded-full border border-gray-10">
                        </div>
                        @endif
                        <span class="text-base text-[#09335D] {{ $citizenship == 1 ? 'font-weight-500' : '' }}">Domestic</span>
                    </div>
                    <div class="flex items-center gap-1 cursor-pointer">
                        @if($citizenship == 2)
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                            <rect x="1" y="0.5" width="19" height="19" rx="9.5" fill="#D3D3D3" />
                            <rect x="1" y="0.5" width="19" height="19" rx="9.5" stroke="#A9A9A9" />
                            <circle cx="10.5" cy="10" r="4" fill="#A9A9A9" />
                        </svg>
                        @else
                        <div class="h-5 w-5 bg-white rounded-full border border-gray-10">
                        </div>
                        @endif
                        <span class="text-base {{ $citizenship == 2 ? 'font-weight-500' : '' }} text-[#09335D]">International</span>
                    </div>
                </div>
                @else
                <div class="flex flex-row gap-5">
                    <div class="flex items-center gap-1 cursor-pointer" wire:click="onClickCitizenship(1)">
                        @if($citizenship == 1)
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                            <rect x="1" y="0.5" width="19" height="19" rx="9.5" fill="#E6F2FF" />
                            <rect x="1" y="0.5" width="19" height="19" rx="9.5" stroke="#0D4D8B" />
                            <circle cx="10.5" cy="10" r="4" fill="#0D4D8B" />
                        </svg>
                        @else
                        <div class="h-5 w-5 bg-white rounded-full border border-brand">
                        </div>
                        @endif
                        <span class="text-base text-[#09335D] {{ $citizenship == 1 ? 'font-weight-500' : '' }}">Domestic</span>
                    </div>
                    <div class="flex items-center gap-1 cursor-pointer" wire:click="onClickCitizenship(2)">
                        @if($citizenship == 2)
                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                            <rect x="1" y="0.5" width="19" height="19" rx="9.5" fill="#E6F2FF" />
                            <rect x="1" y="0.5" width="19" height="19" rx="9.5" stroke="#0D4D8B" />
                            <circle cx="10.5" cy="10" r="4" fill="#0D4D8B" />
                        </svg>
                        @else
                        <div class="h-5 w-5 bg-white rounded-full border border-brand">
                        </div>
                        @endif
                        <span class="text-base {{ $citizenship == 2 ? 'font-weight-500' : '' }} text-[#09335D]">International</span>
                    </div>
                </div>
                @endif
                @if(Session::get('error_citizenship'))
                <span class="text-xs text-red-600">{{Session::get('error_citizenship')}}</span>
                @endif
            </div>
            @endif
            @if($patient_id)
            <div class="flex flex-col gap-2 md:col-span-2" wire:key="profile-{{\Illuminate\Support\Str::uuid()}}">
                <span class="text-base text-[#475467]">Photo Profile</span>
                <div class="flex flex-row gap-5 items-center">
                    @if($photo_profile_path)
                    <img src="{{ asset_gcs($photo_profile_path) }}" class="h-[62px] w-[62px] object-cover rounded-full">
                    @else
                    <div class="h-[63px] w-[63px] rounded-full bg-brand text-white flex items-center justify-center">
                        <span class="text-3xl font-medium">{{ initial_name($first_name) }}</span>
                    </div>
                    {{-- <svg xmlns="http://www.w3.org/2000/svg" width="63" height="63" viewBox="0 0 63 63" fill="none">--}}
                    {{-- <path fill-rule="evenodd" clip-rule="evenodd" d="M31.5 59.5C46.964 59.5 59.5 46.964 59.5 31.5C59.5 16.036 46.964 3.5 31.5 3.5C16.036 3.5 3.5 16.036 3.5 31.5C3.5 46.964 16.036 59.5 31.5 59.5ZM24.4043 20.4696V40.8333H27.4767V34.4995L29.883 31.7353L36.4654 40.8333H40.1642L31.9213 29.6373L40.1344 20.4696H36.2665L27.7253 30.1941H27.4767V20.4696H24.4043Z" fill="url(#paint0_linear_214_10216)"/>--}}
                    {{-- <defs>--}}
                    {{-- <linearGradient id="paint0_linear_214_10216" x1="40.834" y1="2.33335" x2="20.4173" y2="60.6666" gradientUnits="userSpaceOnUse">--}}
                    {{-- <stop stop-color="#3674B3"/>--}}
                    {{-- <stop offset="1" stop-color="#0B4074"/>--}}
                    {{-- </linearGradient>--}}
                    {{-- </defs>--}}
                    {{-- </svg>--}}
                    @endif
                    <div class="py-3 px-4 bg-[#0D4D8B] rounded-lg h-fit text-white cursor-pointer text-base" onclick="document.getElementById('fileInput-{{$patient_id}}').click()">
                        <span>{{ $photo_profile_path ? 'Upload New Picture' : 'Upload Picture' }}</span>
                        <input type="file" wire:model="photo_profile" id="fileInput-{{$patient_id}}" hidden>
                    </div>
                    @if($photo_profile_path)
                    <div class="py-3 px-4 bg-white border border-red-500 rounded-lg h-fit cursor-pointer text-base text-red-500" wire:click="onClickDeletePhotoProfile">
                        <span>Delete</span>
                    </div>
                    @else
                    <span class="text-sm font-medium text-[#667085]">No file chosen</span>
                    @endif
                </div>
                <div wire:loading wire:target="photo_profile" wire:key="photo-profile-{{\Illuminate\Support\Str::uuid()}}" class="p-4 border shadow-sm text-center rounded-xl">
                    <span>Uploading...</span>
                    <div class="flex w-full h-2 bg-gray-200 overflow-hidden dark:bg-gray-700" role="progressbar" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100">
                        <div class="flex flex-col justify-center overflow-hidden bg-blue-600 text-xs text-white text-center whitespace-nowrap transition duration-500 dark:bg-blue-500" style="width: 25%"></div>
                    </div>
                </div>
                @if(Session::get('error_photo_profile'))
                <span class="text-xs text-red-600">{{Session::get('error_photo_profile')}}</span>
                @endif
            </div>
            @endif
            <div class="flex flex-col gap-2">
                <span class="text-base text-[#475467]">First Name <span class="text-red-600">**</span></span>
                @php
                $isDisabled = isset($patient) && !empty($patient->first_name);
                @endphp
                <div class="w-full items-center rounded-xl border border-gray-200 py-[18px] px-6 flex flex-row justify-between focus-within:border-blue-500
                         {{ Session::has('error_first_name') ? 'border-red-500 focus-within:border-red-500' : '' }}
                         {{ $isDisabled ? 'bg-gray-50' : 'bg-white'  }}">
                    <input type="text" class="linguise_patient_name w-full outline-none bg-transparent text-base font-light text-[#98A2B3] focus:text-black {{ $first_name ? 'text-black' : ''}}"
                        placeholder="First Name"
                        name="first_name"
                        wire:model.live.debounce.500ms="first_name"
                        {{ $isDisabled ? 'disabled' : '' }}>
                </div>
                @if(Session::get('error_first_name'))
                <span class="text-xs text-red-600">{{Session::get('error_first_name')}}</span>
                @endif
            </div>
            <div class="flex flex-col gap-2">
                <span class="text-base text-[#475467]">Last Name <span class="text-red-600">**</span></span>
                <div class="w-full items-center rounded-xl border border-gray-200 py-[18px] px-6 flex flex-row justify-between focus-within:border-blue-500
                         {{ Session::has('error_last_name') ? 'border-red-500 focus-within:border-red-500' : '' }}
                         {{ $patient?->last_name ? 'bg-gray-50' : 'bg-white' }}">
                    <input type="text" class="linguise_patient_name w-full outline-none bg-transparent text-base font-light text-[#98A2B3] focus:text-black {{ $last_name ? 'text-black' : ''}}"
                        placeholder="Last Name"
                        name="last_name"
                        wire:model.live.debounce.500ms="last_name"
                        {{ $patient?->last_name ? 'disabled' : '' }}>
                </div>
                @if(Session::get('error_last_name'))
                <span class="text-xs text-red-600">{{Session::get('error_last_name')}}</span>
                @endif
            </div>
            <div class="flex flex-col gap-2">
                <label for="dateOfBirth" class="text-16-24">Date of Birth <span class="text-error-50">**</span></label>
                @php
                $isDisabledDob = isset($patient) && !empty($patient->dob);
                @endphp
                <div class="w-full items-center rounded-xl border border-gray-200 py-[18px] px-6 flex flex-row justify-between focus-within:border-blue-500
                         {{ Session::has('error_dob') ? 'border-red-500 focus-within:border-red-500' : '' }} cursor-pointer
                        {{ $isDisabledDob ? 'bg-gray-50' : 'bg-white' }}">
                    <input type="text" id="dateOfBirth" name="dateOfBirth"
                        class="focus:outline-none w-full" value="{{ old('dateOfBirth') }}" placeholder="dd/mm/yyyy"
                        wire:model.live.debounce.500ms="dob"
                        {{ $isDisabledDob ? 'disabled' : '' }} readonly>
                    <div id="dobClickAble">
                        {!! file_get_contents('assets/svg/calendar.svg') !!}
                    </div>
                </div>
                @if(Session::get('error_dob'))
                <span class="text-xs text-red-600">{{Session::get('error_dob')}}</span>
                @endif
            </div>
            <div class="flex flex-col gap-2 relative" wire:click="isModalGender">
                <span class="text-base text-[#475467]">Gender <span class="text-red-600">**</span></span>
                @php
                $isDisabledGender = isset($patient) && !empty($patient->gender);
                @endphp
                <div class="w-full items-center rounded-xl border py-[18px] px-6 flex flex-row justify-between focus-within:border-blue-500
                         {{ Session::has('error_gender') ? 'border-red-500 focus-within:border-red-500' : '' }} {{ $is_modal_gender ? 'border-blue-500' : 'border-gray-200' }} {{ $isDisabledGender ? 'border-gray-200' : '' }}
                         cursor-pointer {{ $isDisabledGender ? 'bg-gray-50' : 'bg-white' }}">
                    <span type="text" class="w-full outline-none bg-transparent text-base font-light text-[#98A2B3] focus:text-black {{ $gender ? 'text-black' : ''}}">
                        {{ @$gender['key'] ? $gender['key'] : 'Select Gender' }}
                    </span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.71967 8.21967C6.01256 7.92678 6.48744 7.92678 6.78033 8.21967L10.5 11.9393L14.2197 8.21967C14.5126 7.92678 14.9874 7.92678 15.2803 8.21967C15.5732 8.51256 15.5732 8.98744 15.2803 9.28033L11.0303 13.5303C10.8897 13.671 10.6989 13.75 10.5 13.75C10.3011 13.75 10.1103 13.671 9.96967 13.5303L5.71967 9.28033C5.42678 8.98744 5.42678 8.51256 5.71967 8.21967Z" fill="#667085" />
                    </svg>
                </div>
                @if($is_modal_gender && !$isDisabledGender)
                <div class="hover:cursor-pointer rounded-xl flex flex-col p-4 border shadow absolute w-full bg-white z-10 mt-24
                    {{ $isDisabled && $gender ? 'hidden' : '' }}">
                    @foreach($genders as $dataGender)
                    <div wire:click="onSelectGender('{{$dataGender}}')"
                        class="flex flex-row items-center gap-3 py-[10px] hover:border-b hover:border-t" id="parent">
                        <span class="text-base {{ @$gender['key'] == $dataGender->key ? 'font-bold' : 'font-light' }}">{{ $dataGender->key }}</span>
                    </div>
                    @endforeach
                </div>
                @endif
                @if(Session::get('error_gender'))
                <span class="text-xs text-red-600">{{Session::get('error_gender')}}</span>
                @endif
            </div>
            @if($citizenship == 1 || !$citizenship)
            <div class="flex flex-col gap-2">
                <span class="text-base text-[#475467]">Identity Card Number (NIK) <span class="text-red-600">**</span></span>
                @php
                $isDisabledNik = isset($patient) && !empty($patient->ktp_number);
                @endphp
                <div class="w-full items-center rounded-xl border border-gray-200 py-[18px] px-6 flex flex-row justify-between focus-within:border-blue-500
                         {{ Session::has('error_nik') ? 'border-red-500 focus-within:border-red-500' : '' }}
                        {{ $isDisabledNik ? 'bg-gray-50' : 'bg-white' }}">
                    <input type="number" class="w-full outline-none bg-transparent text-base font-light text-[#98A2B3] focus:text-black {{ $nik ? 'text-black' : ''}}"
                        placeholder="Identity Card Number"
                        wire:model.live.debounce.500ms="nik"
                        {{ $isDisabledNik ? 'disabled' : '' }}>
                </div>
                @if(Session::get('error_nik'))
                <span class="text-xs text-red-600">{{Session::get('error_nik')}}</span>
                @endif
            </div>
            @endif
            <div class="flex flex-col gap-2">
                <div class="flex flex-row gap-1">
                    <span class="text-base text-[#475467]">Passport Number <span class="text-red-600 {{ $citizenship == 2 ? '' : 'hidden' }}">**</span></span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M12.5 3.75C7.94365 3.75 4.25 7.44365 4.25 12C4.25 16.5563 7.94365 20.25 12.5 20.25C17.0563 20.25 20.75 16.5563 20.75 12C20.75 7.44365 17.0563 3.75 12.5 3.75ZM2.75 12C2.75 6.61522 7.11522 2.25 12.5 2.25C17.8848 2.25 22.25 6.61522 22.25 12C22.25 17.3848 17.8848 21.75 12.5 21.75C7.11522 21.75 2.75 17.3848 2.75 12ZM11.75 8.25C11.75 7.83579 12.0858 7.5 12.5 7.5H12.5075C12.9217 7.5 13.2575 7.83579 13.2575 8.25V8.2575C13.2575 8.67171 12.9217 9.0075 12.5075 9.0075H12.5C12.0858 9.0075 11.75 8.67171 11.75 8.2575V8.25ZM11.4561 10.5584C12.6023 9.98532 13.8929 11.0206 13.5821 12.2639L12.8731 15.0999L12.9146 15.0792C13.2851 14.8939 13.7356 15.0441 13.9208 15.4146C14.1061 15.7851 13.9559 16.2356 13.5854 16.4208L13.5439 16.4416C12.3977 17.0147 11.1071 15.9794 11.4179 14.7361L12.1269 11.9001L12.0854 11.9208C11.7149 12.1061 11.2644 11.9559 11.0792 11.5854C10.8939 11.2149 11.0441 10.7644 11.4146 10.5792L11.4561 10.5584Z" fill="#667085" />
                    </svg>
                </div>
                <div class="w-full items-center rounded-xl border border-gray-200 py-[18px] px-6 flex flex-row justify-between focus-within:border-blue-500
                         {{ Session::has('error_passport') ? 'border-red-500 focus-within:border-red-500' : '' }}
                         {{ $is_disabled_passport ? 'bg-gray-50' : 'bg-white' }}">
                    <input type="text" class="w-full outline-none bg-transparent text-base font-light text-[#98A2B3] focus:text-black {{ $passport ? 'text-black' : ''}}"
                        placeholder="Passport Number"
                        wire:model.live.debounce.500ms="passport"
                        {{ $is_disabled_passport ? 'disabled' : '' }}>
                </div>
                @if(Session::get('error_passport'))
                <span class="text-xs text-red-600">{{Session::get('error_passport')}}</span>
                @endif
            </div>
            @if($citizenship == 2)
            <div class="flex flex-col gap-2">
                <div class="flex flex-row gap-1">
                    <span class="text-base text-[#475467]">KITAS/KITAP Number</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M12.5 3.75C7.94365 3.75 4.25 7.44365 4.25 12C4.25 16.5563 7.94365 20.25 12.5 20.25C17.0563 20.25 20.75 16.5563 20.75 12C20.75 7.44365 17.0563 3.75 12.5 3.75ZM2.75 12C2.75 6.61522 7.11522 2.25 12.5 2.25C17.8848 2.25 22.25 6.61522 22.25 12C22.25 17.3848 17.8848 21.75 12.5 21.75C7.11522 21.75 2.75 17.3848 2.75 12ZM11.75 8.25C11.75 7.83579 12.0858 7.5 12.5 7.5H12.5075C12.9217 7.5 13.2575 7.83579 13.2575 8.25V8.2575C13.2575 8.67171 12.9217 9.0075 12.5075 9.0075H12.5C12.0858 9.0075 11.75 8.67171 11.75 8.2575V8.25ZM11.4561 10.5584C12.6023 9.98532 13.8929 11.0206 13.5821 12.2639L12.8731 15.0999L12.9146 15.0792C13.2851 14.8939 13.7356 15.0441 13.9208 15.4146C14.1061 15.7851 13.9559 16.2356 13.5854 16.4208L13.5439 16.4416C12.3977 17.0147 11.1071 15.9794 11.4179 14.7361L12.1269 11.9001L12.0854 11.9208C11.7149 12.1061 11.2644 11.9559 11.0792 11.5854C10.8939 11.2149 11.0441 10.7644 11.4146 10.5792L11.4561 10.5584Z" fill="#667085" />
                    </svg>
                </div>
                <div class="w-full items-center rounded-xl border border-gray-200 py-[18px] px-6 flex flex-row justify-between focus-within:border-blue-500
                         {{ Session::has('error_kitas') ? 'border-red-500 focus-within:border-red-500' : '' }}
                         {{ $is_disabled_kitas_number ? 'bg-gray-50' : 'bg-white' }}">
                    <input type="text" class="w-full outline-none bg-transparent text-base font-light text-[#98A2B3] focus:text-black {{ $passport ? 'text-black' : ''}}"
                        placeholder="KITAS/KITAP Number"
                        wire:model.live.debounce.500ms="kitas"
                        {{ $is_disabled_kitas_number ? 'disabled' : '' }}>
                </div>
                @if(Session::get('error_kitas'))
                <span class="text-xs text-red-600">{{Session::get('error_kitas')}}</span>
                @endif
            </div>
            @endif
            <div class="flex flex-col md:col-span-2 gap-2">
                <span class="text-base text-[#475467]">Email <span class="text-red-600">**</span></span>
                <div class="w-full items-center rounded-xl border border-gray-200 py-[18px] px-6 flex flex-row justify-between focus-within:border-blue-500
                         {{ Session::has('error_email') ? 'border-red-500 focus-within:border-red-500' : '' }}
                         {{ $patient?->email ? 'bg-gray-50' : 'bg-white' }}">
                    <input type="text" class="w-full outline-none bg-transparent text-base font-light text-[#98A2B3] focus:text-black {{ $first_name ? 'text-black' : ''}}"
                        placeholder="Email"
                        wire:model.live.debounce.500ms="email"
                        {{ $patient?->email ? 'disabled' : '' }}>
                </div>
                @if(Session::get('error_email'))
                <span class="text-xs text-red-600">{{Session::get('error_email')}}</span>
                @endif
            </div>
            <div class="flex flex-col gap-2 relative">
                <span class="text-base text-[#475467]">Phone Number <span class="text-red-600">**</span></span>
                @php
                $isDisabledContact = isset($patient) && !empty($patient->contact_no);
                @endphp
                <div class="w-full items-center rounded-xl border border-gray-200 py-[18px] px-6 flex flex-row justify-between focus-within:border-blue-500
                         {{ Session::has('error_phone_number') ? 'border-red-500 focus-within:border-red-500' : '' }}
                         {{ $isDisabledContact ? 'bg-gray-50 focus-within:border-gray-200': 'bg-white'}}">
                    <div wire:click="isModalCountryCode" class="flex flex-row gap-1 items-center w-1/3 cursor-pointer">
                        <img id="selected-country-flag" class="selected-country-code rounded-full h-[18px] w-[18px] object-cover"
                            src="{{ asset_gcs('vendor/blade-flags/country-'.strtolower($country_code['country_code']).'.svg') }}" />
                        <span class="text-[#101828] text-base">+{{ $country_code['extension'] }}</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M4.67574 6.57613C4.91005 6.34181 5.28995 6.34181 5.52426 6.57613L8.5 9.55186L11.4757 6.57613C11.7101 6.34181 12.0899 6.34181 12.3243 6.57613C12.5586 6.81044 12.5586 7.19034 12.3243 7.42466L8.92426 10.8247C8.81174 10.9372 8.65913 11.0004 8.5 11.0004C8.34087 11.0004 8.18826 10.9372 8.07574 10.8247L4.67574 7.42465C4.44142 7.19034 4.44142 6.81044 4.67574 6.57613Z" fill="#667085" />
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" width="1" height="18" viewBox="0 0 1 18" fill="none">
                            <path d="M1 1C1 0.723858 0.776142 0.5 0.5 0.5C0.223858 0.5 0 0.723858 0 1H1ZM0 17V17.5H1V17H0ZM0 1V17H1V1H0Z" fill="#98A2B3" />
                        </svg>
                    </div>
                    <input type="number" class="w-2/3 outline-none bg-transparent text-base font-light text-[#98A2B3] focus:text-black {{ $phone_number ? 'text-black' : ''}}"
                        placeholder="Phone Number"
                        name="first_name"
                        wire:model.live.debounce.500ms="phone_number"
                        {{ $isDisabledContact ? 'disabled' : '' }}>
                </div>
                @if($is_modal_country_code && !$patient_id)
                <div class="hover:cursor-pointer rounded-xl flex flex-col p-4 border shadow absolute w-full bg-white z-10 mt-24 h-44 overflow-auto">
                    <div class="w-full items-center rounded-xl border border-gray-200 p-4 flex flex-row justify-between bg-white">
                        <input type="text" class="w-full outline-none bg-transparent text-sm font-light text-[#98A2B3] focus:text-black {{ $search_country_code ? 'text-black' : ''}}"
                            placeholder="Search name or country code"
                            wire:model.live.debounce.500ms="search_country_code">
                    </div>
                    <div class="flex flex-row gap-2 mt-2" wire:loading wire:target="search_country_code">
                        @include('landing-page.component.spinner')
                        <span class="text-base text-brand">Searching...</span>
                    </div>
                    <div wire:loading.remove wire:target="search_country_code" class="mt-2">
                        @if($countryCodes->isEmpty())
                        <div class="text-center py-4">
                            <p>No country codes available.</p>
                        </div>
                        @else
                        @foreach($countryCodes as $code)
                        <div wire:click="onSelectCountryCode('{{$code}}')"
                            class="flex flex-row items-center gap-3 py-[10px] hover:border-b hover:border-t" id="parent">
                            @if(@$country_code['country_code'] == $code->country_code)
                            <div class="bg-[#16A34A] p-[2px] rounded">
                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                    <path d="M10 3L4.5 8.5L2 6" stroke="white" stroke-width="1.6666" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                            </div>
                            @else
                            <img id="selected-country-flag" class="selected-country-code rounded-full h-[18px] w-[18px] object-cover"
                                src="{{ asset_gcs('vendor/blade-flags/country-'.strtolower($code->country_code).'.svg') }}" />
                            @endif
                            <span class="text-base font-light">+{{ $code->extension }}</span>
                            <span class="text-base font-light">{{ $code->country_name }}</span>
                        </div>
                        @endforeach
                        @endif
                    </div>
                </div>
                @endif
                @if(Session::get('error_phone_number'))
                <span class="text-xs text-red-600">{{Session::get('error_phone_number')}}</span>
                @endif
            </div>
            <div class="flex flex-col gap-2 relative" wire:click="isModalRelation">
                <span class="text-base text-[#475467]">Relationship to Patient <span class="text-red-600">**</span></span>
                <div class="w-full items-center rounded-xl border py-[18px] px-6 flex border-gray-200 flex-row justify-between focus-within:border-blue-500 cursor-pointer
                         {{ Session::has('error_relation_to_patient') ? 'border-red-500 focus-within:border-red-500' : '' }} {{ $is_modal_relation ? 'border-blue-500' : 'border-gray-200' }}
                         {{ $is_modal_relation ? 'bg-gray-50 focus-within:border-gray-200': 'bg-white'}}">
                    <span type="text" class="w-full outline-none bg-transparent text-base font-light text-[#98A2B3] focus:text-black {{ $relation_to_patient ? 'text-black' : ''}}">
                        {{ @$relation_to_patient['key'] ? $relation_to_patient['key'] : 'Select Relationship' }}
                    </span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.71967 8.21967C6.01256 7.92678 6.48744 7.92678 6.78033 8.21967L10.5 11.9393L14.2197 8.21967C14.5126 7.92678 14.9874 7.92678 15.2803 8.21967C15.5732 8.51256 15.5732 8.98744 15.2803 9.28033L11.0303 13.5303C10.8897 13.671 10.6989 13.75 10.5 13.75C10.3011 13.75 10.1103 13.671 9.96967 13.5303L5.71967 9.28033C5.42678 8.98744 5.42678 8.51256 5.71967 8.21967Z" fill="#667085" />
                    </svg>
                </div>
                @if($is_modal_relation)
                <div class="hover:cursor-pointer rounded-xl flex flex-col p-4 border shadow absolute w-full bg-white z-10 mt-24">
                    @foreach($relations as $dataRelation)
                    @if($dataRelation->value == 1)
                    @continue
                    @endif
                    <div wire:click="onSelectRelation('{{$dataRelation}}')"
                        class="flex flex-row items-center gap-3 py-[10px] hover:border-b hover:border-t" id="parent">
                        <span class="text-base {{ @$relation_to_patient['key'] == $dataRelation->key ? 'font-bold' : 'font-light' }}">{{ $dataRelation->key }}</span>
                    </div>
                    @endforeach
                </div>
                @endif
                @if(Session::get('error_relation_to_patient'))
                <span class="text-xs text-red-600">{{Session::get('error_relation_to_patient')}}</span>
                @endif
            </div>
            {{-- @include('livewire.landing-page.profile.component.confirmation_button')--}}
        </div>
        <div class="w-full flex justify-end">
            <div class="w-full md:w-2/3 flex flex-col gap-2">
                @if($isError)
                <span class="text-red-600 text-end">{{ $isError }}</span>
                @endif
                @if($isConfirmation)
                <div class="flex md:flex-row flex-col gap-2 md:gap-4" id="buttonSubmitBasic">
                    <div class="w-full md:w-1/2 py-3 md:py-[18px] flex justify-center rounded-xl border border-[#0D4D8B] cursor-pointer" wire:click="nextSection(true, true)" onclick="onClickNextBasic()">
                        <span class="text-[#0D4D8B] font-weight-500">Submit</span>
                    </div>
                    <div class="w-full md:w-1/2 py-3 md:py-[18px] flex justify-center rounded-xl bg-[#0D4D8B] gap-3 cursor-pointer" wire:click="nextSection(false)">
                        <span class="text-white font-weight-500">Fill Out More</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.836 11.3636C19.1875 11.7151 19.1875 12.2849 18.836 12.6364L13.736 17.7364C13.3845 18.0879 12.8147 18.0879 12.4632 17.7364C12.1117 17.3849 12.1117 16.8151 12.4632 16.4636L16.9268 12L12.4632 7.5364C12.1117 7.18492 12.1117 6.61508 12.4632 6.2636C12.8147 5.91213 13.3845 5.91213 13.736 6.2636L18.836 11.3636ZM7.736 6.2636L12.836 11.3636C13.1875 11.7151 13.1875 12.2849 12.836 12.6364L7.736 17.7364C7.38453 18.0879 6.81468 18.0879 6.46321 17.7364C6.11174 17.3849 6.11174 16.8151 6.46321 16.4636L10.9268 12L6.46321 7.5364C6.11174 7.18492 6.11174 6.61508 6.46321 6.2636C6.81468 5.91213 7.38453 5.91213 7.736 6.2636Z" fill="white" />
                        </svg>
                    </div>
                </div>
                <div class="flex justify-center cursor-not-allowed hidden" id="loadingButtonSubmitBasic">
                    @include('landing-page.component.button_spinner_with_text',['text' => 'Submitting...'])
                </div>
                @else
                <div class="flex flex-row gap-4 opacity-50">
                    <div class="w-1/2 py-[18px] flex justify-center rounded-xl border border-[#0D4D8B] cursor-pointer">
                        <span class="text-[#0D4D8B] font-weight-500">Submit</span>
                    </div>
                    <div class="w-1/2 py-[18px] flex justify-center rounded-xl bg-[#0D4D8B] gap-3 cursor-pointer">
                        <span class="text-white font-weight-500">Fill Out More</span>
                        <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M18.836 11.3636C19.1875 11.7151 19.1875 12.2849 18.836 12.6364L13.736 17.7364C13.3845 18.0879 12.8147 18.0879 12.4632 17.7364C12.1117 17.3849 12.1117 16.8151 12.4632 16.4636L16.9268 12L12.4632 7.5364C12.1117 7.18492 12.1117 6.61508 12.4632 6.2636C12.8147 5.91213 13.3845 5.91213 13.736 6.2636L18.836 11.3636ZM7.736 6.2636L12.836 11.3636C13.1875 11.7151 13.1875 12.2849 12.836 12.6364L7.736 17.7364C7.38453 18.0879 6.81468 18.0879 6.46321 17.7364C6.11174 17.3849 6.11174 16.8151 6.46321 16.4636L10.9268 12L6.46321 7.5364C6.11174 7.18492 6.11174 6.61508 6.46321 6.2636C6.81468 5.91213 7.38453 5.91213 7.736 6.2636Z" fill="white" />
                        </svg>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

@push('script')
<script>
    function onClickNextBasic() {
        $('#buttonSubmitBasic').addClass('hidden');
        $('#loadingButtonSubmitBasic').removeClass('hidden');
    }

    function onClickNextAdvance() {
        $('#buttonSubmitAdvance').addClass('hidden');
        $('#loadingButtonSubmitAdvance').removeClass('hidden').addClass('flex');
    }

    function onClickNextAddress() {
        $('#buttonSubmitAddress').addClass('hidden');
        $('#loadingButtonSubmitAddress').removeClass('hidden').addClass('flex');
    }
</script>
@endpush
