@section('title', 'Bali International Hospital')
@extends('landing-page.layouts.masterTemplate')
@php
    $platform = session()->get('platform');
@endphp

@section('content')
    <div class="relative">
        <div class="absolute h-1/2 blur-3xl opacity-30 w-1/2 inset-0 z-10
            bg-gradient-to-b from-green-200 via-cyan-100 to-green-100 rounded-b-3xl"
             style="">
        </div>
        <div class="relative z-20">
            <div id="banner" class="relative">
                @if(@$package_category->image)
                    <img class="w-full h-[360px] object-cover"
                         src="{{ asset_gcs($package_category->image) }}"
                         alt="Bali International Hospital">
                @else
                    <img class="w-full h-[360px] object-cover"
                         src="{{ asset_default_gcs() }}"
                         alt="Bali International Hospital">
                @endif

                <div class="mx-4 md:mx-40 absolute inset-x-0 bottom-0 flex flex-col
        justify-between text-white text-4xl font-semibold mb-10 gap-2">
                    <h1 class="text-[36px] lg:text-[60px] font-semibold">{{ $package_category->name }}</h1>
                </div>
            </div>
            @if(!in_array($platform, ['mobile']))
                <div id="breadcrumb" class="">
                    <div class="relative z-1">
                        <div class="flex flex-row px-4 md:px-40 py-4 gap-4 items-center text-sm lg:text-base font-light">
                            <a href="/" class="text-primary-blue-60">Home</a>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <div class="hs-dropdown relative inline-flex lg:hidden">
                                <button id="hs-dropdown-default" type="button" class="hs-dropdown-toggle py-3">
                                    <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                        <circle cx="12" cy="12" r="1"></circle>
                                        <circle cx="12" cy="5" r="1"></circle>
                                        <circle cx="12" cy="19" r="1"></circle>
                                    </svg>
                                </button>
                                <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white shadow-md rounded-lg p-2 mt-2 after:h-4 after:absolute after:-bottom-4 after:start-0 after:w-full before:h-4 before:absolute before:-top-4 before:start-0 before:w-full" aria-labelledby="hs-dropdown-default">
                                    <a href="{{route('medical_packages.type',$package_category->packageType->slug)}}" class="text-[#667085]"> {{ $package_category->packageType->name }} </a>
                                </div>
                            </div>
                            <a href="{{route('medical_packages.type',$package_category->packageType->slug)}}" class="text-[#667085] hidden lg:block"> {{ $package_category->packageType->name }} </a>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span class="text-[#667085]"> {{ $package_category->name }} </span>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
    <div class="py-6 mx-4 md:mx-40">
        <div class="flex flex-col">
            <span class="text-xl lg:text-3xl font-semibold font-[#1D2939]">Choose from Our {{ $package_category->name }}</span>
        </div>
        <div class="flex flex-col mt-6 relative h-52 md:h-28">
            <div class="w-full lg:w-2/3 absolute z-10">
                <livewire:landing-page.medical-package.component.filter :isShowFilter="true" :type="2"/>
            </div>
        </div>
        <div class="relative mt-24">
            <livewire:landing-page.medical-package.component.list-package :$package_category/>
        </div>
    </div>

    {{--    <div class="mt-32 xl:mx-40">--}}
    {{--        @include('landing-page.partials.homepage.contact')--}}
    {{--    </div>--}}
@endsection
