@section('title', 'Bali International Hospital')
@section('seo_meta')
    <link rel="canonical" href="https://bih.id/medical-packages/detail/{{ $slug_category }}/{{ $slug_package }}" />
    <link rel="alternate" hreflang="en-id" href="https://bih.id/medical-packages/detail/{{ $slug_category }}/{{ $slug_package }}" />
@endsection
@extends('landing-page.layouts.masterTemplate')
<style>
    .ql-container {
        border: none !important;
        overflow: hidden;
    }
    /*.custom-quill-container .ql-editor * {*/
    /*    font-size: inherit !important; !* Ensure Quill's font sizes are respected *!*/
    /*    color: inherit !important; !* Respect Quill's inline colors *!*/
    /*}*/
    #editor {
        pointer-events: none; /* Disable interaction */
        opacity: 0.7; /* Optional: Visually indicate read-only */
    }

</style>
@php
    $platform = session()->get('platform');
@endphp

@section('content')
    <div>
        <div class="h-24 lg:h-32">
        </div>
        <div class="relative">
            <div class="absolute mx-auto w-1/3 h-1/3 inset-0
            bg-gradient-to-b from-green-200 via-cyan-100 to-green-100 rounded-b-3xl blur-3xl opacity-50"
                 style="right: -50%">
            </div>
            <div class="rounded-b-3xl z-20">
                <div class="flex flex-row mx-4 md:px-32 py-4 gap-4 items-center text-sm lg:text-base font-light">
                    <a href="/" class="text-primary-blue-60">Home</a>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <div class="hs-dropdown relative inline-flex lg:hidden">
                        <button id="hs-dropdown-default" type="button" class="hs-dropdown-toggle py-3">
                            <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="12" cy="12" r="1"></circle>
                                <circle cx="12" cy="5" r="1"></circle>
                                <circle cx="12" cy="19" r="1"></circle>
                            </svg>
                        </button>
                        <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white
                     shadow-md rounded-lg p-2 mt-2 after:h-4 after:absolute after:-bottom-4 after:start-0 after:w-full before:h-4
                     before:absolute before:-top-4 before:start-0 before:w-full z-20" aria-labelledby="hs-dropdown-default">
                            <a class="text-base font-light text-[#667085] linguise_package_category" href="{{route('medical_packages.category',['slug_category'=>$category->slug])}}"> {{ $category->name }} </a>
                        </div>
                    </div>
                    <a class="text-base font-light text-[#667085] hidden lg:block linguise_package_category" href="{{route('medical_packages.category',['slug_category'=>$category->slug])}}"> {{ $category->name }} </a>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                        <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <span class="text-base font-light text-[#667085] linguise_package_name"> {{ $package->title }} </span>
                </div>
            </div>

            <div class="lg:mt-10 mt-2 mx-4 md:mx-32 relative z-10">
                <div class="flex flex-col md:flex-row gap-6">
                    <div class="flex flex-col gap-6 w-full md:w-2/3 order-2 md:order-1">
                        <h1 class="text-3xl font-semibold text-natural-gray-20 md:block hidden linguise_package_name">{{ $package->title }}</h1>
                        <div class="custom-quill-container ql-container ql-snow" id="editor">
                            <div class="ql-editor">
                                {!! $package->content !!}
                            </div>
                        </div>
                        @if($package->glances->count() > 0)
                            <div class="flex flex-col gap-6">
                                <h2 class="text-2xl lg:text-3xl font-semibold text-natural-gray-20">At a Glance</h2>
                                <div class="grid grid-cols-2 lg:grid-cols-3 gap-4">
                                    @foreach($package->glances as $glance)
                                        <div class="flex flex-row items-center gap-1">
                                            <div class="h-2 flex items-center">
                                                <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9 2.125C5.20304 2.125 2.125 5.20304 2.125 9C2.125 12.797 5.20304 15.875 9 15.875C12.797 15.875 15.875 12.797 15.875 9C15.875 5.20304 12.797 2.125 9 2.125ZM0.875 9C0.875 4.51269 4.51269 0.875 9 0.875C13.4873 0.875 17.125 4.51269 17.125 9C17.125 13.4873 13.4873 17.125 9 17.125C4.51269 17.125 0.875 13.4873 0.875 9ZM11.8633 6.61642C12.1442 6.81705 12.2092 7.20739 12.0086 7.48827L8.88358 11.8633C8.7768 12.0128 8.60964 12.1077 8.42655 12.1229C8.24346 12.138 8.06297 12.0719 7.93306 11.9419L6.05806 10.0669C5.81398 9.82286 5.81398 9.42714 6.05806 9.18306C6.30214 8.93898 6.69786 8.93898 6.94194 9.18306L8.29525 10.5364L10.9914 6.76173C11.192 6.48084 11.5824 6.41579 11.8633 6.61642Z" fill="#16A34A"/>
                                                </svg>
                                            </div>
                                            <span class="font-light lg:font-medium text-base text-[#475467]">{{$glance->name}}</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endif
                        @if(count($package->formatted_tests ?? []) > 0)
                            <div class="flex gap-6 flex-col">
                                <h2 class="text-2xl lg:text-3xl font-semibold text-natural-gray-20">Package Inclusion</h2>
                                <table class="min-w-full bg-white">
                                    <thead>
                                    <tr class="bg-[#F4FFE5] text-left font-semibold">
                                        @if($checkPackageInclusion)
                                        <th class="w-1/2 p-4">Tests</th>
                                        @else
                                        <th class="w-1/2 p-4">Category</th>
                                        <th class="w-1/2 p-4">Tests</th>
                                        @endif
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($package->formatted_tests as $test)
                                        <tr class="border-b border-gray-200 linguise_package_inclusion">
                                            @if($checkPackageInclusion)
                                            <td class="py-2 px-4 text-gray-700">
                                                <ul class="list-disc list-inside">
                                                    @foreach($test['obbrivation'] as $key => $obbrivation)
                                                        <li class="text-[#344054] font-light text-base">{{$obbrivation}}</li>
                                                    @endforeach
                                                </ul>
                                            </td>
                                            @else
                                            <td class="py-2 px-4 text-[#344054] font-light text-base align-content-center">{{ $test['section'] }}</td>
                                            <td class="py-2 px-4 text-gray-700">
                                                <ul class="list-disc list-inside">
                                                    @foreach($test['obbrivation'] as $key => $obbrivation)
                                                        <li class="text-[#344054] font-light text-base">{{$obbrivation}}</li>
                                                    @endforeach
                                                </ul>
                                            </td>
                                            @endif
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif

                        @if($package->preparations)
                            <div class="flex flex-col gap-6">
                                <span class="text-2xl lg:text-3xl font-semibold text-natural-gray-20">Preparation</span>
                                <span class="font-light text-base text-[#344054]">{!! nl2br(e($package->preparations)) !!}</span>
                            </div>
                        @endif

                        @if($package->term_conditions)
                            <div class="flex flex-col gap-6">
                                <span class="text-2xl lg:text-3xl font-semibold text-natural-gray-20 md:block hidden">Terms & Conditions</span>
                                <span class="font-light text-base text-[#344054]">{!! nl2br(e($package->term_conditions)) !!}</span>
                            </div>
                        @endif

                    </div>
                    <div class="flex flex-col gap-6 w-full md:w-5/12 order-1 md:order-2">
                        <span class="text-xl lg:text-3xl font-semibold text-natural-gray-20 md:hidden block linguise_package_name">{{ $package->title }}</span>
                        <div class="bg-white rounded-2xl lg:rounded-[48px] pb-6 lg:p-6 shadow-sm">
                            <div class="flex flex-col gap-6">
                                @if(str_contains($package->image,"unsplash"))
                                    <img class="w-full object-cover rounded-t-3xl"
                                         src="{{ $package->image }}"
                                         alt="Image">
                                @else
                                    <img class="w-full object-cover rounded-t-3xl"
                                         src="{{ asset_gcs($package->image) }}"
                                         alt="Image">
                                @endif

                                <div class="flex flex-col gap-4 px-4">
                                    <div class="flex flex-row">
                                        <span class="text-base font-light text-[#667085]">Package Name: <span class="text-brand font-medium text-base linguise_package_name">{{ $package->title }}</span></span>
                                    </div>
                                    <div class="flex flex-row">
                                        <span class="text-base font-light text-[#667085]">Category: <a href="{{route('medical_packages.category',['slug_category'=>$category->slug])}}" class="text-brand font-medium text-base linguise_package_category">{{ $category->name }}</a></span>
                                    </div>
                                    <span class="text-2xl text-brand font-semibold linguise_package_price">Rp{{ str_replace(',', '.', number_format($package->price)) }}</span>
                                </div>
                                <div class="flex flex-row px-4 justify-between gap-3 items-center">
                                    @if($checkPackage)
                                    <div class="border border-brand py-3 w-full rounded-2xl flex justify-center hover:cursor-pointer hover:bg-blue-50" data-hs-overlay="#hs-large-modal">
                                        <span class="text-base font-medium text-brand">Compare</span>
                                    </div>
                                    @else
                                    <div onclick="redirectToInquiry('{{ $package->uuid }}')" class="bg-brand py-3 w-full rounded-2xl flex justify-center hover:cursor-pointer hover:bg-blue-900" >
                                        <span class="text-base font-medium text-white">Send an inquiry</span>
                                    </div>
                                    @endif
                                    @if($checkPackage)
                                    @if($isExists)
                                        <a href="{{route('medical_packages.carts.destroy',['slug_package' => $package->slug])}}" class="border border-red-500 py-3 w-full rounded-2xl flex justify-center hover:cursor-pointer hover:bg-red-50">
                                            <span class="text-sm font-medium text-red-500 text-center">Remove from Cart</span>
                                        </a>
                                    @else
                                        @if(session('platform') === 'mobile')
                                            <button onclick="addToCart('{{ $package->slug }}')" class="border border-brand py-3 w-full rounded-2xl flex justify-center hover:cursor-pointer hover:bg-blue-50">
                                                <span class="text-base font-medium text-brand">Add to Cart</span>
                                            </button>
                                        @else
                                            <a href="{{ route('medical_packages.carts.store', ['slug_package' => $package->slug]) }}" class="border border-brand py-3 w-full rounded-2xl flex justify-center hover:cursor-pointer hover:bg-blue-50">
                                                <span class="text-base font-medium text-brand">Add to Cart</span>
                                            </a>
                                        @endif
                                    @endif
                                    @else
                                        <button onclick="window.open('https://wa.me/6281211661127', '_blank')" class="bg-brand py-3 w-full rounded-2xl flex justify-center hover:cursor-pointer hover:bg-blue-900">
                                            <img class="" src="{{ asset('assets/common/whatsappSolid.svg') }}" alt="whatsapp">
                                            <span class="text-base font-medium text-white ml-1">WhatsApp</span>
                                        </button>
                                    @endif
                                </div>
                                @if($checkPackage)
                                    @if(session('platform') === 'mobile')
                                        <div class="flex flex-row px-4 justify-between gap-3">
                                            <button onclick="redirectToCheckout('{{ $package->uuid }}')" class="bg-brand py-3 w-full rounded-2xl flex justify-center hover:cursor-pointer hover:bg-blue-900">
                                            <span class="text-base font-medium text-white">
                                                Buy Now
                                            </span>
                                            </button>
                                        </div>
                                    @else
                                        <div class="flex flex-row px-4 justify-between gap-3">
                                            <a href="{{ route('medical_packages.checkout_package', ['package_uuid' => $package->uuid]) }}"
                                               class="bg-brand py-3 w-full rounded-2xl flex justify-center hover:cursor-pointer hover:bg-blue-900">
                                            <span class="text-base font-medium text-white">
                                                Buy Now
                                            </span>
                                            </a>
                                        </div>
                                    @endif
                                @endif
{{--                                <div class="flex flex-row px-4 justify-between gap-3">--}}
{{--                                    <button onclick="" class="bg-brand py-3 w-full rounded-2xl flex justify-center hover:cursor-pointer hover:bg-blue-900" data-hs-overlay="#pre-register-modal">--}}
{{--                                        <span class="text-sm font-medium text-white">--}}
{{--                                            Sign Up Now to Get the Early Bird Rate!--}}
{{--                                        </span>--}}
{{--                                    </button>--}}
{{--                                </div>--}}

                            </div>
                        </div>
                        <div class="p-6 flex gap-6 flex-col hidden lg:block">
                            {{--                            <div class="flex flex-col gap-1">--}}
                            {{--                                <span class="text-brand text-sm font-medium">Need an emergency check?</span>--}}
                            {{--                                <span class="text-[#667085] font-light text-sm">Telephone:<a href="https://api.whatsapp.com/send?phone=6289680988232&text=Hallo%20need%20help%20so%20bad!!" target="_blank" class="text-brand font-semibold text-sm hover:cursor-pointer"> +62 150 911</a></span>--}}
                            {{--                            </div>--}}
                            <div class="flex flex-col gap-1">
                                <span class="text-brand text-sm font-medium">{{ @$package->location }}</span>
                                <span class="text-[#667085] font-light text-sm">{{ @$package->building_floor }}</span>
                                <span class="text-[#667085] font-light text-sm">{{ @$package->formattedOpenTime }}</span>
                            </div>
                        </div>
                        @if($checkPackage)
                        <div class="max-w-md mx-auto bg-white shadow-md rounded-xl p-6 border border-gray-200">
                            <p class="text-gray-600 mb-4">
                                Explore our available Add-Ons to make your screening experience even more complete. 
                            </p>
                          
                            <a href="{{ asset_gcs('document/053b3d2b-de4b-4660-9d7d-afb860efc368.pdf') }}"
                               class="flex items-center justify-center gap-2 w-full bg-blue-700 hover:bg-blue-800 text-white font-medium py-3 rounded-lg transition" target= "_blank">
                              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                                   stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M4 16v2a2 2 0 002 2h12a2 2 0 002-2v-2M7 10l5 5 5-5M12 4v12"/>
                              </svg>
                              Download here
                            </a>
                          
                            <p class="text-sm text-gray-500 mt-4">
                              <strong>Note:</strong> Add-Ons can and will be added during registration at the hospital.
                            </p>
                        </div>
                        @endif
                    </div>
                </div>
                <div class="flex gap-6 flex-col mt-2 lg:hidden">
                    {{--                    <div class="flex flex-col gap-1">--}}
                    {{--                        <span class="text-brand text-sm font-medium">Need an emergency check?</span>--}}
                    {{--                        <span class="text-[#667085] font-light text-sm">Telephone:<a href="https://api.whatsapp.com/send?phone=6289680988232&text=Hallo%20need%20help%20so%20bad!!" target="_blank" class="text-brand font-semibold text-sm hover:cursor-pointer"> +62 150 911</a></span>--}}
                    {{--                    </div>--}}
                    <div class="flex flex-col gap-1">
                        <span class="text-brand text-sm font-medium">{{ @$package->location }}</span>
                        <span class="text-[#667085] font-light text-sm">{{ @$package->building_floor }}</span>
                        <span class="text-[#667085] font-light text-sm">{{ @$package->formattedOpenTime }}</span>
                    </div>
                </div>
                @if($related_packages->count() > 0)
                    <div class="flex flex-col mt-10 gap-6">
                        <span class="text-2xl lg:text-3xl font-semibold font-[#1D2939]">Related Packages</span>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            @foreach($related_packages as $relatedPackage)
                                @php
                                    $isLoading = false;
                                @endphp
                                <livewire:landing-page.medical-package.component.card-package :package="$relatedPackage" :isLoadStyle="$isLoading" wire:key="related-package-{{$relatedPackage->uuid}}"/>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <div id="hs-large-modal" class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto pointer-events-none">
            <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all lg:max-w-4xl lg:w-full m-3 lg:mx-auto">
                <div class="flex flex-col bg-white border shadow-sm rounded-xl pointer-events-auto">
                    <div class="flex justify-between items-center py-3 px-4 border-b">
                        <h3 class="text-xl font-semibold text-gray-800">
                            Compare Packages
                        </h3>
                        <button type="button" class="flex justify-center items-center size-7 text-sm font-semibold rounded-full border border-transparent text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none" data-hs-overlay="#hs-large-modal">
                            <span class="sr-only">Close</span>
                            <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M18 6 6 18"></path>
                                <path d="m6 6 12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="p-4 overflow-y-auto">
                        <livewire:landing-page.medical-package.compare-package :$package/>
                    </div>
                </div>
            </div>
        </div>
        {{--    <div class="mt-32 xl:mx-40">--}}
        {{--        @include('landing-page.partials.homepage.contact')--}}
        {{--    </div>--}}
    </div>
@endsection
@push('script')
    <script>
        const userId = @json(Session::get('user_id'));
        function postMessage(){
            try {
                console.log('UNAUTHORIZED 403')
                Check.postMessage('check session');
            } catch (error) {
                console.error('An error occurred while posting the message:', error);
            }

        }

        function redirectToCheckout(packageUuid) {
            if(!userId){
                postMessage();
            }
            const url = `/medical-packages/checkout-package/${packageUuid}`;
            window.location.href = url;
        }

        function redirectToInquiry(uuid) {
            const url = `/medical-packages/send-inquiry/${uuid}`;
            window.location.href = url;
        }

        function addToCart(slug) {
            if(!userId){
                postMessage();
            }
            const url = `/medical-packages/carts/store/${slug}`;
            window.location.href = url;
        }
    </script>
@endpush
