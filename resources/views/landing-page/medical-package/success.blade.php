@section('title', 'Bali International Hospital')
@extends('landing-page.layouts.masterTemplate')
@include('landing-page.partials.bg_move_style_screen')
@php
    $isPatientComplete = true;
@endphp

@section('content')
    <div class="h-32">
    </div>
    <div class="relative z-10">
        <div class="absolute mx-auto w-1/3 h-1/3 inset-0
            bg-gradient-to-b from-green-200 via-cyan-100 to-green-100 rounded-b-3xl blur-3xl opacity-50"
             style="right: -50%">
        </div>
        <div class="rounded-b-3xl z-20 {{ Session::get('platform') == 'mobile' ? 'hidden' : '' }}">
            <div class="flex flex-row mx-4 md:px-40 py-4 gap-4 items-center">
                <a href="/" class="text-primary-blue-60">Home</a>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <a class="text-base font-light text-[#667085]"> Cart </a>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <a class="text-base font-light text-[#667085]"> Checkout</a>
            </div>
        </div>
    </div>
    <div class="movingColors hidden lg:block">
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
    </div>
    <div class="movingColors2 hidden lg:block">
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
        <p></p>
    </div>
    <div class="relative mx-4 md:mx-40 z-10">
        <div class="flex flex-col gap-4 items-center">
            <img src="{{ asset_gcs('asset/public/icon/9580d6dc-0c6a-4441-9933-279b574fa206.png') }}"
                 alt="Payment success" class="h-[110px] w-[110px]"/>
            <span class="text-[28px] text-[#0D4D8B] font-semibold mx-auto text-center md:w-1/2">Congratulations! Your reservation has been confirmed.</span>

            @foreach($packageSummary->packageSummaryDetails as $packageSummaryDetail)
                @if(!$packageSummaryDetail->patient->is_complete_data)
                    <div class="w-full md:w-1/2 flex flex-row mx-auto mt-4">
                        <div class="border border-[#B54708] rounded-xl py-3 px-4 bg-[#FFFAEB]">
                            <div class="flex flex-row gap-4">
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <path d="M14.4866 12.0005L9.15329 2.66714C9.037 2.46194 8.86836 2.29127 8.66457 2.17252C8.46078 2.05378 8.22915 1.99121 7.99329 1.99121C7.75743 1.99121 7.52579 2.05378 7.322 2.17252C7.11822 2.29127 6.94958 2.46194 6.83329 2.66714L1.49995 12.0005C1.38241 12.204 1.32077 12.4351 1.32129 12.6701C1.32181 12.9052 1.38447 13.136 1.50292 13.339C1.62136 13.5421 1.79138 13.7102 1.99575 13.8264C2.20011 13.9425 2.43156 14.0026 2.66662 14.0005H13.3333C13.5672 14.0002 13.797 13.9385 13.9995 13.8213C14.202 13.7042 14.3701 13.5359 14.487 13.3332C14.6038 13.1306 14.6653 12.9007 14.6653 12.6668C14.6652 12.4329 14.6036 12.2031 14.4866 12.0005Z" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M8 6V8.66667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M8 11.333H8.00667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="flex flex-col text-[#93370D] gap-1">
                                    <span class="text-base font-semibold text-[#B54708]">Patient data is still incomplete</span>
                                    <span class="">To ensure a smoother experience during your visit to the hospital, please complete your profile before visiting the hospital.</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    @php
                        break;
                    @endphp
                @endif
            @endforeach


            <div class="w-full md:w-1/2">
                <span class="self-start text-lg font-medium text-brand">{{ $packageSummary->packageSummaryDetails->count() > 1 ? 'Packages' : 'Package' }}</span>
            </div>

            @foreach($packageSummary->packageSummaryDetails as $packageSummaryDetail)
                @php
                    if(!$packageSummaryDetail->patient->is_complete_data){
                        $isPatientComplete = false;
                    }
                @endphp
                <div class="w-full md:w-1/2 flex flex-row">
                    <div class="rounded-xl p-6 bg-white w-full">
                        <div class="flex flex-col gap-2">

                            {{-- package type--}}
                            <div class="flex gap-1">
                                <div class="flex items-center gap-1">
                                    @if($packageSummaryDetail->packageType->icon)
                                        <img class="w-[24px] h-[24px] rounded-full object-cover"
                                             src="{{asset_gcs($packageSummaryDetail->packageType->icon)}}" alt="Image">
                                    @endif
                                    <span class="text-sm font-light text-primary-blue"> {{ $packageSummaryDetail->packageType->name }}</span>
                                </div>
                            </div>

                            {{-- package--}}
                            <div class="flex flex-row justify-between items-center">
                                <span class="text-2xl font-medium">{{ $packageSummaryDetail->package->title }}</span>
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" class="hover:cursor-pointer">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M9.86321 6.2636C10.2147 5.91213 10.7845 5.91213 11.136 6.2636L16.236 11.3636C16.5875 11.7151 16.5875 12.2849 16.236 12.6364L11.136 17.7364C10.7845 18.0879 10.2147 18.0879 9.86321 17.7364C9.51174 17.3849 9.51174 16.8151 9.86321 16.4636L14.3268 12L9.86321 7.5364C9.51174 7.18492 9.51174 6.61508 9.86321 6.2636Z" fill="#667085"/>
                                </svg>
                            </div>

                            {{-- price--}}
                            <span class="text-base font-light text-[#475467]">Rp{{ str_replace(',', '.', number_format($packageSummaryDetail->package->price)) }}</span>

                            {{-- show hide section--}}
                            @if(@Auth::guard('public')->id() == $packageSummary->public_user_id)
                            <div class="flex flex-row gap-1 items-center hover:cursor-pointer" onclick="onHandleStatus('{{$packageSummaryDetail->uuid}}',true)"
                                 id="more-button-{{$packageSummaryDetail->uuid}}">
                                <span class="text-sm font-medium text-[#0D4D8B]">View Patient</span>
                            </div>
                            @endif
                            <div class="flex flex-row gap-1 items-center hover:cursor-pointer hidden" onclick="onHandleStatus('{{$packageSummaryDetail->uuid}}',false)"
                                 id="hide-button-{{$packageSummaryDetail->uuid}}">
                                <span class="text-sm font-medium text-[#0D4D8B]">Hide Patients
                                </span>
                            </div>
                            <div class="hidden" id="more-{{$packageSummaryDetail->uuid}}">
                                <hr class="-mx-6">
                                <div class="flex flex-row px-4 py-2">
                                    <span class="text-[#667085] text-sm">Registration No <span class="text-sm font-medium text-[#344054]">MPX001</span></span>
                                </div>
                                {{-- patient information section--}}
                                <div class="flex flex-row gap-2 py-2 px-4">
                                    <div class="w-[26px] h-[26px]">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M11.9998 3C10.343 3 8.99984 4.34315 8.99984 6C8.99984 7.65685 10.343 9 11.9998 9C13.6567 9 14.9998 7.65685 14.9998 6C14.9998 4.34315 13.6567 3 11.9998 3ZM7.49984 6C7.49984 3.51472 9.51456 1.5 11.9998 1.5C14.4851 1.5 16.4998 3.51472 16.4998 6C16.4998 8.48528 14.4851 10.5 11.9998 10.5C9.51456 10.5 7.49984 8.48528 7.49984 6ZM5.27693 19.6409C7.34216 20.5158 9.61373 21 12.0002 21C14.3864 21 16.6577 20.5159 18.7228 19.6412C18.4151 16.1987 15.5226 13.5 11.9998 13.5C8.47721 13.5 5.58472 16.1985 5.27693 19.6409ZM3.75109 20.1053C3.82843 15.6156 7.49183 12 11.9998 12C16.508 12 20.1714 15.6157 20.2486 20.1056C20.2537 20.4034 20.0822 20.676 19.8115 20.8002C17.4326 21.8918 14.7864 22.5 12.0002 22.5C9.2137 22.5 6.56728 21.8917 4.18816 20.7999C3.91749 20.6757 3.74596 20.4031 3.75109 20.1053Z" fill="#3674B3"/>
                                        </svg>
                                    </div>
                                    <div class="flex flex-col gap-2">
                                        <span class="text-base font-medium text-[#1D2939]">Patient Information</span>
                                        <div class="flex flex-col gap-1">
                                            <div class="flex gap-1 items-center">
                                                @if($packageSummaryDetail->patient->verified_at)
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                                                    </svg>
                                                @endif
                                                <span class="text-base text-[#344054] font-medium linguise_patient_name">{{ $packageSummaryDetail->patient->fullname }}</span>
                                            </div>
                                            @if($packageSummaryDetail->patient->dob)
                                                <span class="text-base text-[#475467]">{{ \Carbon\Carbon::parse($packageSummaryDetail->patient->dob)->format('d F Y') }}</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                                {{-- visit date section--}}
                                <div class="flex flex-row gap-2 py-2 px-4">
                                    <div class="w-[26px] h-[26px]">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M6.75 2.25C7.16421 2.25 7.5 2.58579 7.5 3V4.5H16.5V3C16.5 2.58579 16.8358 2.25 17.25 2.25C17.6642 2.25 18 2.58579 18 3V4.5H18.75C20.4069 4.5 21.75 5.84315 21.75 7.5V18.75C21.75 20.4069 20.4069 21.75 18.75 21.75H5.25C3.59315 21.75 2.25 20.4069 2.25 18.75V7.5C2.25 5.84315 3.59315 4.5 5.25 4.5H6V3C6 2.58579 6.33579 2.25 6.75 2.25ZM5.25 6C4.42157 6 3.75 6.67157 3.75 7.5V8.65135C4.19126 8.39609 4.70357 8.25 5.25 8.25H18.75C19.2964 8.25 19.8087 8.39609 20.25 8.65135V7.5C20.25 6.67157 19.5784 6 18.75 6H5.25ZM20.25 11.25C20.25 10.4216 19.5784 9.75 18.75 9.75H5.25C4.42157 9.75 3.75 10.4216 3.75 11.25V18.75C3.75 19.5784 4.42157 20.25 5.25 20.25H18.75C19.5784 20.25 20.25 19.5784 20.25 18.75V11.25ZM11.25 12.75C11.25 12.3358 11.5858 12 12 12H12.0075C12.4217 12 12.7575 12.3358 12.7575 12.75V12.7575C12.7575 13.1717 12.4217 13.5075 12.0075 13.5075H12C11.5858 13.5075 11.25 13.1717 11.25 12.7575V12.75ZM13.5 12.75C13.5 12.3358 13.8358 12 14.25 12H14.2575C14.6717 12 15.0075 12.3358 15.0075 12.75V12.7575C15.0075 13.1717 14.6717 13.5075 14.2575 13.5075H14.25C13.8358 13.5075 13.5 13.1717 13.5 12.7575V12.75ZM15.75 12.75C15.75 12.3358 16.0858 12 16.5 12H16.5075C16.9217 12 17.2575 12.3358 17.2575 12.75V12.7575C17.2575 13.1717 16.9217 13.5075 16.5075 13.5075H16.5C16.0858 13.5075 15.75 13.1717 15.75 12.7575V12.75ZM6.75 15C6.75 14.5858 7.08579 14.25 7.5 14.25H7.5075C7.92171 14.25 8.2575 14.5858 8.2575 15V15.0075C8.2575 15.4217 7.92171 15.7575 7.5075 15.7575H7.5C7.08579 15.7575 6.75 15.4217 6.75 15.0075V15ZM9 15C9 14.5858 9.33579 14.25 9.75 14.25H9.7575C10.1717 14.25 10.5075 14.5858 10.5075 15V15.0075C10.5075 15.4217 10.1717 15.7575 9.7575 15.7575H9.75C9.33579 15.7575 9 15.4217 9 15.0075V15ZM11.25 15C11.25 14.5858 11.5858 14.25 12 14.25H12.0075C12.4217 14.25 12.7575 14.5858 12.7575 15V15.0075C12.7575 15.4217 12.4217 15.7575 12.0075 15.7575H12C11.5858 15.7575 11.25 15.4217 11.25 15.0075V15ZM13.5 15C13.5 14.5858 13.8358 14.25 14.25 14.25H14.2575C14.6717 14.25 15.0075 14.5858 15.0075 15V15.0075C15.0075 15.4217 14.6717 15.7575 14.2575 15.7575H14.25C13.8358 15.7575 13.5 15.4217 13.5 15.0075V15ZM15.75 15C15.75 14.5858 16.0858 14.25 16.5 14.25H16.5075C16.9217 14.25 17.2575 14.5858 17.2575 15V15.0075C17.2575 15.4217 16.9217 15.7575 16.5075 15.7575H16.5C16.0858 15.7575 15.75 15.4217 15.75 15.0075V15ZM6.75 17.25C6.75 16.8358 7.08579 16.5 7.5 16.5H7.5075C7.92171 16.5 8.2575 16.8358 8.2575 17.25V17.2575C8.2575 17.6717 7.92171 18.0075 7.5075 18.0075H7.5C7.08579 18.0075 6.75 17.6717 6.75 17.2575V17.25ZM9 17.25C9 16.8358 9.33579 16.5 9.75 16.5H9.7575C10.1717 16.5 10.5075 16.8358 10.5075 17.25V17.2575C10.5075 17.6717 10.1717 18.0075 9.7575 18.0075H9.75C9.33579 18.0075 9 17.6717 9 17.2575V17.25ZM11.25 17.25C11.25 16.8358 11.5858 16.5 12 16.5H12.0075C12.4217 16.5 12.7575 16.8358 12.7575 17.25V17.2575C12.7575 17.6717 12.4217 18.0075 12.0075 18.0075H12C11.5858 18.0075 11.25 17.6717 11.25 17.2575V17.25ZM13.5 17.25C13.5 16.8358 13.8358 16.5 14.25 16.5H14.2575C14.6717 16.5 15.0075 16.8358 15.0075 17.25V17.2575C15.0075 17.6717 14.6717 18.0075 14.2575 18.0075H14.25C13.8358 18.0075 13.5 17.6717 13.5 17.2575V17.25Z" fill="#3674B3"/>
                                        </svg>
                                    </div>
                                    <div class="flex flex-col gap-2">
                                        <span class="text-base font-medium text-[#1D2939]">Visit Date</span>
                                        <span class="text-base text-[#344054] font-medium">{{ \Carbon\Carbon::parse($packageSummaryDetail->visit_date)->format('l, d M Y') }}</span>
                                    </div>
                                </div>

                                {{-- attachment section--}}
                                @if($packageSummaryDetail->attachment_url)
                                    <div class="flex flex-row gap-2 py-2 px-4">
                                        <div class="w-[26px] h-[26px]">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5.625 3C5.41789 3 5.25 3.16789 5.25 3.375V20.625C5.25 20.8321 5.41789 21 5.625 21H18.375C18.5821 21 18.75 20.8321 18.75 20.625V11.625C18.75 10.1753 17.5747 9 16.125 9H14.625C13.5895 9 12.75 8.16053 12.75 7.125V5.625C12.75 4.17525 11.5747 3 10.125 3H5.625ZM5.625 1.5C4.58947 1.5 3.75 2.33947 3.75 3.375V20.625C3.75 21.6605 4.58947 22.5 5.625 22.5H18.375C19.4105 22.5 20.25 21.6605 20.25 20.625V11.25C20.25 5.86522 15.8848 1.5 10.5 1.5H5.625ZM13.757 3.66785C14.0715 4.25019 14.25 4.91675 14.25 5.625V7.125C14.25 7.33211 14.4179 7.5 14.625 7.5H16.125C16.8332 7.5 17.4998 7.6785 18.0822 7.99296C17.2488 6.05549 15.6945 4.50123 13.757 3.66785ZM7.5 15C7.5 14.5858 7.83579 14.25 8.25 14.25H15.75C16.1642 14.25 16.5 14.5858 16.5 15C16.5 15.4142 16.1642 15.75 15.75 15.75H8.25C7.83579 15.75 7.5 15.4142 7.5 15ZM7.5 18C7.5 17.5858 7.83579 17.25 8.25 17.25H12C12.4142 17.25 12.75 17.5858 12.75 18C12.75 18.4142 12.4142 18.75 12 18.75H8.25C7.83579 18.75 7.5 18.4142 7.5 18Z" fill="#3674B3"/>
                                            </svg>
                                        </div>
                                        <div class="flex flex-col gap-2">
                                            <span class="text-base font-medium text-[#1D2939]">Attachment File <span class="text-[#667085] font-light">(.jpg, .png, .pdf, .zip)</span></span>
                                            <a href="{{ route('static-page-gcs', ['path' => $packageSummaryDetail->attachment_url]) }}"
                                               target="_blank" class="hover:cursor-pointer text-base font-medium text-[#0D4D8B]">
                                                {{ get_file_name_from_path($packageSummaryDetail->attachment_url) }}
                                            </a>
                                        </div>
                                    </div>

                                @endif

                            </div>

                        </div>
                    </div>
                </div>
            @endforeach

        </div>

        <div class="w-full md:w-1/2 flex flex-row justify-between gap-6 mx-auto mt-8">
            @if($isPatientComplete)
                <a href="/" class="py-4 bg-white border-[#0D4D8B] border-2 w-full rounded-xl flex justify-center mx-auto hover:cursor-pointer items-center"
                >
                    <span class="text-base font-medium text-[#0D4D8B] text-center">Back to Home</span>
                </a>
            @else
                @if($packageSummary->packageSummaryDetails->count() > 1)
                    <a href="{{route('profile.index')}}" onclick="callCrossPlatformCompleteProfile()" class="py-4 bg-white border-[#0D4D8B] border-2 w-full rounded-xl flex justify-center mx-auto hover:cursor-pointer items-center"
                    >
                        <span class="text-base font-medium text-[#0D4D8B] text-center">Complete Profile</span>
                    </a>
                @else
                    <a href="{{route('profile.show', $packageSummaryDetail->patient->uuid)}}" onclick="callCrossPlatformCompleteProfile()" class="py-4 bg-white border-[#0D4D8B] border-2 w-full rounded-xl flex justify-center mx-auto hover:cursor-pointer items-center"
                    >
                        <span class="text-base font-medium text-[#0D4D8B] text-center">Complete Profile</span>
                    </a>
                @endif
            @endif

            <a href="{{ route('profile.mybook.index') }}" onclick="callCrossPlatformViewMyBooking()" class="py-4 bg-[#0D4D8B] w-full rounded-xl flex justify-center mx-auto hover:cursor-pointer items-center">
                <span class="text-base font-medium text-white text-center">View My Bookings</span>
            </a>
        </div>

    </div>

    {{--    <div class="mt-32 xl:mx-40">--}}
    {{--        @include('landing-page.partials.homepage.contact')--}}
    {{--    </div>--}}
@endsection

@push('script')
    <script>

        function callCrossPlatformViewMyBooking(){
            try {
                console.log('clicked : View My Bookings');
                ListTransaction.postMessage('List Transaction');
            } catch (error) {
                console.error('An error occurred while posting the message:', error);
            }
        }

        function callCrossPlatformCompleteProfile(){
            try {
                console.log('clicked : Complete Profile');
                CompleteProfile.postMessage('Complete Profile');
            } catch (error) {
                console.error('An error occurred while posting the message:', error);
            }
        }

        window.onload = function(){
            try {
                console.log('PAYMENT SUCCESS')
                PaymentResult.postMessage('payment success');
            } catch (error) {
                console.error('An error occurred while posting the message:', error);
            }
        }

        function onHandleStatus(uuid,status){
            if(status){
                document.getElementById('more-'+uuid).style.display = 'block';
                document.getElementById('hide-button-'+uuid).style.display = 'block';
                document.getElementById('more-button-'+uuid).style.display = 'none';
            }else{
                document.getElementById('more-'+uuid).style.display = 'none';
                document.getElementById('hide-button-'+uuid).style.display = 'none';
                document.getElementById('more-button-'+uuid).style.display = 'block';
            }
        }
    </script>
@endpush

