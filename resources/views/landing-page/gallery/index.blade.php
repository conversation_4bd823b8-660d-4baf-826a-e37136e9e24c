@section('title', 'Bali International Hospital')
@extends('landing-page.layouts.masterTemplate')

<style>
    .sync1 .item {
        /*background: #008cff;*/
        margin: 5px;
        color: #fff;
        text-align: center;
        border-radius: 24px;
    }

    .sync2 .item {
        /*background: #c9c9c9;*/
        margin: 5px;
        color: #fff;
        text-align: center;
        cursor: pointer;
        border-radius: 24px;
    }

    .sync2 .item h1 {
        font-size: 18px;
    }

    .sync2 .current .item {
        /*background: #0c83e7;*/
    }

    .owl-theme .owl-nav {
        /*default owl-theme theme reset .disabled:hover links */
    }

    .owl-theme .owl-nav [class*=owl-] {
        transition: all 0.3s ease;
    }

    .owl-theme .owl-nav [class*=owl-].disabled:hover {
        background-color: #d6d6d6;
    }

    .sync1.owl-theme {
        position: relative;
    }

    .sync1.owl-theme .owl-next,
    .sync1.owl-theme .owl-prev {
        width: 22px;
        height: 40px;
        margin-top: -20px;
        position: absolute;
        top: 50%;
    }

    .sync1.owl-theme .owl-prev {
        left: 10px;
    }

    .sync1.owl-theme .owl-next {
        right: 10px;
    }
</style>
@section('content')
    <div class="xl:mx-40 flex flex-col mx-10">
        {{-- BANNER --}}
        <div class="relative xl:-mx-40 -mx-10">
            <img src="{{ asset_gcs('public/assets/coe/810356a0-6707-4f89-98d7-f441b604e98c.png') }}" alt="Gallery Banner Image"
                class="inset-0 w-full object-cover "
                style="background: radial-gradient(77.36% 77.36% at 45.71% 50%, rgba(7, 39, 70, 0.33) 0%, rgba(7, 39, 70, 0.72) 100%);">
            <div class="absolute inset-0"
                style="background: radial-gradient(77.36% 77.36% at 45.71% 50%, rgba(7, 39, 70, 0.33) 0%, rgba(7, 39, 70, 0.72) 100%);">
            </div>
            <div class="absolute inset-0 flex flex-col justify-end xl:mx-40 mx-44 mb-11">
                <h1 class="text-white xl:text-6xl text-4xl font-weight-600">Gallery</h1>
            </div>
        </div>
        {{-- BREADCRUMB --}}
        <div class="flex flex-row flex-wrap gap-4 items-center py-4">
            <a href="/" class="text-primary-blue-60">Home</a>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5"
                    stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <span> Gallery </span>
        </div>
        {{-- CONTENT --}}
        <div class="flex flex-col gap-8 items-center justify-center mb-20">
            <div class="flex p-2 bg-white bg-opacity-40">
                <div class="flex gap-4">
                    <div class="flex">
                        <div class="flex bg-gray-200 rounded-lg transition p-1">
                            <nav class="flex space-x-2" aria-label="Tabs" role="tablist">
                                <button type="button"
                                    class="hs-tab-active:bg-white hs-tab-active:text-[#16A34A] py-2 px-4 inline-flex items-center gap-x-2 bg-transparent text-sm text-gray-500 hover:text-gray-700 font-medium rounded-lg hover:hover:text-[#16A34A] disabled:opacity-50 disabled:pointer-events-none active"
                                    id="segment-item-1" data-hs-tab="#segment-1" aria-controls="segment-1" role="tab">
                                    <span>
                                        <svg width="32" height="28" viewBox="0 0 32 28" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M16 9.5C12.8934 9.5 10.375 12.0184 10.375 15.125C10.375 18.2316 12.8934 20.75 16 20.75C19.1066 20.75 21.625 18.2316 21.625 15.125C21.625 12.0184 19.1066 9.5 16 9.5Z"
                                                fill="#1D2939" />
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M12.0164 0.60669C13.3354 0.535863 14.6636 0.5 16 0.5C17.3364 0.5 18.6646 0.535863 19.9836 0.60669C21.435 0.684629 22.7281 1.48418 23.4823 2.69276L24.7141 4.66678C25.0727 5.24149 25.6817 5.63197 26.3793 5.73111C26.9574 5.81326 27.5333 5.90218 28.1069 5.99777C30.2547 6.35567 31.75 8.23678 31.75 10.361V23C31.75 25.4853 29.7353 27.5 27.25 27.5H4.75C2.26472 27.5 0.25 25.4853 0.25 23V10.361C0.25 8.23676 1.74529 6.35565 3.89308 5.99776C4.46673 5.90216 5.04264 5.81325 5.62072 5.7311C6.31834 5.63196 6.92728 5.24148 7.28591 4.66677L8.51772 2.69276C9.2719 1.48418 10.565 0.684629 12.0164 0.60669ZM8.125 15.125C8.125 10.7758 11.6508 7.25 16 7.25C20.3492 7.25 23.875 10.7758 23.875 15.125C23.875 19.4742 20.3492 23 16 23C11.6508 23 8.125 19.4742 8.125 15.125ZM26.125 12.875C26.7463 12.875 27.25 12.3713 27.25 11.75C27.25 11.1287 26.7463 10.625 26.125 10.625C25.5037 10.625 25 11.1287 25 11.75C25 12.3713 25.5037 12.875 26.125 12.875Z"
                                                fill="#1D2939" />
                                        </svg>
                                    </span>
                                    Photo
                                </button>
                                <button type="button"
                                    class="hs-tab-active:bg-white hs-tab-active:text-[#16A34A] py-2 px-4 inline-flex items-center gap-x-2 bg-transparent text-sm text-gray-500 hover:text-gray-700 font-medium rounded-lg hover:hover:text-[#16A34A] disabled:opacity-50 disabled:pointer-events-none"
                                    id="segment-item-2" data-hs-tab="#segment-2" aria-controls="segment-2" role="tab">
                                    <span>
                                        <svg width="36" height="36" viewBox="0 0 36 36" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M18 5.625C11.1655 5.625 5.625 11.1655 5.625 18C5.625 24.8345 11.1655 30.375 18 30.375C24.8345 30.375 30.375 24.8345 30.375 18C30.375 11.1655 24.8345 5.625 18 5.625ZM3.375 18C3.375 9.92284 9.92284 3.375 18 3.375C26.0772 3.375 32.625 9.92284 32.625 18C32.625 26.0772 26.0772 32.625 18 32.625C9.92284 32.625 3.375 26.0772 3.375 18ZM15.75 14.287V21.713L22.4335 18L15.75 14.287ZM13.5 13.331C13.5 12.0443 14.8823 11.231 16.007 11.8558L24.4113 16.5249C25.5686 17.1678 25.5686 18.8322 24.4113 19.4751L16.007 24.1442C14.8823 24.769 13.5 23.9557 13.5 22.669V13.331Z"
                                                fill="#1D2939" stroke="#667085" stroke-width="1.5" stroke-linecap="round"
                                                stroke-linejoin="round" />
                                        </svg>
                                    </span>
                                    Video
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
            <div class="mt-3 w-full">
                {{-- PHOTO --}}
                <div id="segment-1" role="tabpanel" aria-labelledby="segment-item-1"
                    class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-8">
                    <div class="flex flex-col gap-3" data-hs-overlay="#hs-subscription-with-image-1">
                        <img class="w-full h-[18.75rem] object-cover rounded-2xl"
                            src="{{ asset_gcs('public/assets/gallery/51b36831-0c89-43ee-a7c7-5eea58b46783.jpg') }}"
                            alt="Image Description">
                        <p class="text-[#101828] font-weight-400 text-16-24">President Suite</p>
                        <div class="text-center">
                        </div>
                    </div>
                    <div class="flex flex-col gap-3" data-hs-overlay="#hs-subscription-with-image-2">
                        <img class="w-full h-[18.75rem] object-cover rounded-2xl"
                             src="{{ asset_gcs('public/assets/coe/7d64af7a-f16f-46f5-b2c6-2ee7c58de08e.jpg') }}"
                             alt="Image Description">
                        <p class="text-[#101828] font-weight-400 text-16-24">Single Room</p>
                        <div class="text-center">
                        </div>
                    </div>
                    <div class="flex flex-col gap-3" data-hs-overlay="#hs-subscription-with-image-3">
                        <img class="w-full h-[18.75rem] object-cover rounded-2xl"
                             src="{{ asset_gcs('public/assets/coe/6bb92e28-ff98-40bd-b166-cd13d574a879.jpg') }}"
                             alt="Image Description">
                        <p class="text-[#101828] font-weight-400 text-16-24">Operating Theater</p>
                        <div class="text-center">
                        </div>
                    </div>
                    <div class="flex flex-col gap-3" data-hs-overlay="#hs-subscription-with-image-4">
                        <img class="w-full h-[18.75rem] object-cover rounded-2xl"
                             src="{{ asset_gcs('public/assets/coe/0d7bdc7f-95a9-4489-aa51-2a4eadff59d4.jpg') }}"
                             alt="Image Description">
                        <p class="text-[#101828] font-weight-400 text-16-24">MRI 3 Tesla</p>
                        <div class="text-center">
                        </div>
                    </div>
                    <div class="flex flex-col gap-3" data-hs-overlay="#hs-subscription-with-image-5">
                        <img class="w-full h-[18.75rem] object-cover rounded-2xl"
                             src="{{ asset_gcs('public/assets/coe/00a4b85f-5b09-4b56-9174-386f90c22163.jpg') }}"
                             alt="Image Description">
                        <p class="text-[#101828] font-weight-400 text-16-24">Compassionate Care</p>
                        <div class="text-center">
                        </div>
                    </div>
                    <div class="flex flex-col gap-3" data-hs-overlay="#hs-subscription-with-image-6">
                        <img class="w-full h-[18.75rem] object-cover rounded-2xl"
                             src="{{ asset_gcs('public/assets/coe/c23f848a-6032-4f64-bd1f-d4d9ab36e2e7.jpg') }}"
                             alt="Image Description">
                        <p class="text-[#101828] font-weight-400 text-16-24">Lobby</p>
                        <div class="text-center">
                        </div>
                    </div>
                </div>
                {{-- VIDEO --}}
                <div id="segment-2" class="hidden w-full" role="tabpanel" aria-labelledby="segment-item-2">
                    <iframe class="inset-0 object-cover rounded-3xl mb-2 w-full" height="667" src="https://www.youtube.com/embed/MBMBNDVXjj0" allowfullscreen></iframe>
                    <h2 class="text-20-30 text-neutral-10 font-weight-600">Bali International Hospital</h2>
                    <p class="text-16-24 text-neutral-10 font-weight-400">Teaser Video Bali International Hospital.</p>
                </div>
            </div>
        </div>

        {{-- Modal --}}
        <div id="hs-subscription-with-image-1"
             class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto">
            <div
                class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all
                sm:max-w-3xl sm:w-full m-3 sm:mx-auto">
                <div class="relative flex flex-col bg-white shadow-lg rounded-xl text-center w-full">
                    <div class="text-24-32 font-weight-600 text-[#1D2939] mt-2">President Suite</div>
                    <div class="absolute top-2 end-2 z-[10]">
                        <button type="button"
                                class="inline-flex justify-center items-center size-8 text-sm font-semibold rounded-lg border border-transparent bg-white/10 text-white hover:bg-white/20 disabled:opacity-50 disabled:pointer-events-none dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-gray-600"
                                data-hs-overlay="#hs-subscription-with-image-1">
                            <span class="sr-only">Close</span>
                            <svg width="36" height="36" viewBox="0 0 36 36" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M11.3046 9.39541C10.7774 8.8682 9.92261 8.8682 9.39541 9.39541C8.8682 9.92261 8.8682 10.7774 9.39541 11.3046L16.0908 18L9.39541 24.6954C8.8682 25.2226 8.8682 26.0774 9.39541 26.6046C9.92261 27.1318 10.7774 27.1318 11.3046 26.6046L18 19.9092L24.6954 26.6046C25.2226 27.1318 26.0774 27.1318 26.6046 26.6046C27.1318 26.0774 27.1318 25.2226 26.6046 24.6954L19.9092 18L26.6046 11.3046C27.1318 10.7774 27.1318 9.92261 26.6046 9.39541C26.0774 8.8682 25.2226 8.8682 24.6954 9.39541L18 16.0908L11.3046 9.39541Z"
                                    fill="#667085" />
                            </svg>
                        </button>
                    </div>
                    <div class="p-2 xl:p-3 text-center overflow-y-auto">
                        <img class="w-full object-fill"
                             src="{{ asset_gcs('public/assets/gallery/51b36831-0c89-43ee-a7c7-5eea58b46783.jpg') }}"
                             alt="Image Description">
                    </div>
                </div>
            </div>
        </div>

        {{-- Modal --}}
        <div id="hs-subscription-with-image-2"
             class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto">
            <div
                class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all sm:max-w-3xl sm:w-full m-3 sm:mx-auto">
                <div class="relative flex flex-col bg-white shadow-lg rounded-xl text-center w-full">
                    <div class="text-24-32 font-weight-600 text-[#1D2939] mt-2">Single Room</div>
                    <div class="absolute top-2 end-2 z-[10]">
                        <button type="button"
                                class="inline-flex justify-center items-center size-8 text-sm font-semibold rounded-lg border border-transparent bg-white/10 text-white hover:bg-white/20 disabled:opacity-50 disabled:pointer-events-none dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-gray-600"
                                data-hs-overlay="#hs-subscription-with-image-2">
                            <span class="sr-only">Close</span>
                            <svg width="36" height="36" viewBox="0 0 36 36" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M11.3046 9.39541C10.7774 8.8682 9.92261 8.8682 9.39541 9.39541C8.8682 9.92261 8.8682 10.7774 9.39541 11.3046L16.0908 18L9.39541 24.6954C8.8682 25.2226 8.8682 26.0774 9.39541 26.6046C9.92261 27.1318 10.7774 27.1318 11.3046 26.6046L18 19.9092L24.6954 26.6046C25.2226 27.1318 26.0774 27.1318 26.6046 26.6046C27.1318 26.0774 27.1318 25.2226 26.6046 24.6954L19.9092 18L26.6046 11.3046C27.1318 10.7774 27.1318 9.92261 26.6046 9.39541C26.0774 8.8682 25.2226 8.8682 24.6954 9.39541L18 16.0908L11.3046 9.39541Z"
                                    fill="#667085" />
                            </svg>

                        </button>
                    </div>
                    <div class="p-2 xl:p-3 text-center overflow-y-auto">
                        <img class="w-full object-fill"
                             src="{{ asset_gcs('public/assets/coe/7d64af7a-f16f-46f5-b2c6-2ee7c58de08e.jpg') }}"
                             alt="Image Description">
                    </div>
                </div>
            </div>
        </div>

        {{-- Modal --}}
        <div id="hs-subscription-with-image-3"
             class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto">
            <div
                class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all sm:max-w-3xl sm:w-full m-3 sm:mx-auto">
                <div class="relative flex flex-col bg-white shadow-lg rounded-xl text-center w-full">
                    <div class="text-24-32 font-weight-600 text-[#1D2939] mt-2">Operating Theater</div>
                    <div class="absolute top-2 end-2 z-[10]">
                        <button type="button"
                                class="inline-flex justify-center items-center size-8 text-sm font-semibold rounded-lg border border-transparent bg-white/10 text-white hover:bg-white/20 disabled:opacity-50 disabled:pointer-events-none dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-gray-600"
                                data-hs-overlay="#hs-subscription-with-image-3">
                            <span class="sr-only">Close</span>
                            <svg width="36" height="36" viewBox="0 0 36 36" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M11.3046 9.39541C10.7774 8.8682 9.92261 8.8682 9.39541 9.39541C8.8682 9.92261 8.8682 10.7774 9.39541 11.3046L16.0908 18L9.39541 24.6954C8.8682 25.2226 8.8682 26.0774 9.39541 26.6046C9.92261 27.1318 10.7774 27.1318 11.3046 26.6046L18 19.9092L24.6954 26.6046C25.2226 27.1318 26.0774 27.1318 26.6046 26.6046C27.1318 26.0774 27.1318 25.2226 26.6046 24.6954L19.9092 18L26.6046 11.3046C27.1318 10.7774 27.1318 9.92261 26.6046 9.39541C26.0774 8.8682 25.2226 8.8682 24.6954 9.39541L18 16.0908L11.3046 9.39541Z"
                                    fill="#667085" />
                            </svg>

                        </button>
                    </div>
                    <div class="p-2 xl:p-3 text-center overflow-y-auto">
                        <img class="w-full object-fill"
                             src="{{ asset_gcs('public/assets/coe/6bb92e28-ff98-40bd-b166-cd13d574a879.jpg') }}"
                             alt="Image Description">
                    </div>
                </div>
            </div>
        </div>

        {{-- Modal --}}
        <div id="hs-subscription-with-image-4"
             class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto">
            <div
                class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all sm:max-w-3xl sm:w-full m-3 sm:mx-auto">
                <div class="relative flex flex-col bg-white shadow-lg rounded-xl text-center w-full">
                    <div class="text-24-32 font-weight-600 text-[#1D2939] mt-2">MRI 3 Tesla</div>
                    <div class="absolute top-2 end-2 z-[10]">
                        <button type="button"
                                class="inline-flex justify-center items-center size-8 text-sm font-semibold rounded-lg border border-transparent bg-white/10 text-white hover:bg-white/20 disabled:opacity-50 disabled:pointer-events-none dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-gray-600"
                                data-hs-overlay="#hs-subscription-with-image-4">
                            <span class="sr-only">Close</span>
                            <svg width="36" height="36" viewBox="0 0 36 36" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M11.3046 9.39541C10.7774 8.8682 9.92261 8.8682 9.39541 9.39541C8.8682 9.92261 8.8682 10.7774 9.39541 11.3046L16.0908 18L9.39541 24.6954C8.8682 25.2226 8.8682 26.0774 9.39541 26.6046C9.92261 27.1318 10.7774 27.1318 11.3046 26.6046L18 19.9092L24.6954 26.6046C25.2226 27.1318 26.0774 27.1318 26.6046 26.6046C27.1318 26.0774 27.1318 25.2226 26.6046 24.6954L19.9092 18L26.6046 11.3046C27.1318 10.7774 27.1318 9.92261 26.6046 9.39541C26.0774 8.8682 25.2226 8.8682 24.6954 9.39541L18 16.0908L11.3046 9.39541Z"
                                    fill="#667085" />
                            </svg>

                        </button>
                    </div>
                    <div class="p-2 xl:p-3 text-center overflow-y-auto">
                        <img class="w-full object-fill"
                             src="{{ asset_gcs('public/assets/coe/0d7bdc7f-95a9-4489-aa51-2a4eadff59d4.jpg') }}"
                             alt="Image Description">
                    </div>
                </div>
            </div>
        </div>

        {{-- Modal --}}
        <div id="hs-subscription-with-image-5"
            class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto">
            <div
                class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all sm:max-w-3xl sm:w-full m-3 sm:mx-auto">
                <div class="relative flex flex-col bg-white shadow-lg rounded-xl text-center w-full">
                    <div class="text-24-32 font-weight-600 text-[#1D2939] mt-2">Compassionate Care</div>
                    <div class="absolute top-2 end-2 z-[10]">
                        <button type="button"
                            class="inline-flex justify-center items-center size-8 text-sm font-semibold rounded-lg border border-transparent bg-white/10 text-white hover:bg-white/20 disabled:opacity-50 disabled:pointer-events-none dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-gray-600"
                            data-hs-overlay="#hs-subscription-with-image-5">
                            <span class="sr-only">Close</span>
                            <svg width="36" height="36" viewBox="0 0 36 36" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M11.3046 9.39541C10.7774 8.8682 9.92261 8.8682 9.39541 9.39541C8.8682 9.92261 8.8682 10.7774 9.39541 11.3046L16.0908 18L9.39541 24.6954C8.8682 25.2226 8.8682 26.0774 9.39541 26.6046C9.92261 27.1318 10.7774 27.1318 11.3046 26.6046L18 19.9092L24.6954 26.6046C25.2226 27.1318 26.0774 27.1318 26.6046 26.6046C27.1318 26.0774 27.1318 25.2226 26.6046 24.6954L19.9092 18L26.6046 11.3046C27.1318 10.7774 27.1318 9.92261 26.6046 9.39541C26.0774 8.8682 25.2226 8.8682 24.6954 9.39541L18 16.0908L11.3046 9.39541Z"
                                    fill="#667085" />
                            </svg>

                        </button>
                    </div>
                    <div class="p-2 xl:p-3 text-center overflow-y-auto">
                        <div id="sync1" class="sync1 owl-carousel owl-carousel-gallery-main owl-theme">
                            <div class="item h-[400] bg-black">
                                <img class="w-full h-[400] object-cover"
                                     src="{{ asset_gcs('public/assets/coe/00a4b85f-5b09-4b56-9174-386f90c22163.jpg') }}"
                                     alt="Image Description">
                            </div>
                            <div class="item h-[400]">
                                <img class="w-full h-[400] object-cover"
                                     src="{{ asset_gcs('public/assets/gallery/489c060a-2f1a-442e-88ed-1fb3515cf6cd.jpg') }}"
                                     alt="Image Description">
                            </div>
                            <div class="item h-[400]">
                                <img class="w-full h-[400] object-cover"
                                     src="{{ asset_gcs('public/assets/gallery/335b3f46-d274-4bfe-ab2d-439a457c57e5.jpg') }}"
                                     alt="Image Description">
                            </div>
                            <div class="item h-[400]">
                                <img class="w-full h-[400] object-cover"
                                     src="{{ asset_gcs('public/assets/gallery/d49042d3-8795-401b-bd73-d38ca5cfbbe7.jpg') }}"
                                     alt="Image Description">
                            </div>
                        </div>

                        <div id="sync2" class="sync2 owl-carousel owl-carousel-gallery owl-theme no-navigation">
                            <div class="item h-[100]">
                                <img class="item w-full h-[100]"
                                     src="{{ asset_gcs('public/assets/coe/00a4b85f-5b09-4b56-9174-386f90c22163.jpg') }}"
                                     alt="Image Description">
                            </div>
                            <div class="item h-[100]">
                                <img class="item w-full h-[100]"
                                     src="{{ asset_gcs('public/assets/gallery/489c060a-2f1a-442e-88ed-1fb3515cf6cd.jpg') }}"
                                     alt="Image Description">
                            </div>
                            <div class="item h-[100]">
                                <img class="item w-full h-[100]"
                                     src="{{ asset_gcs('public/assets/gallery/335b3f46-d274-4bfe-ab2d-439a457c57e5.jpg') }}"
                                     alt="Image Description">
                            </div>
                            <div class="item h-[100]">
                                <img class="item w-full h-[100]"
                                     src="{{ asset_gcs('public/assets/gallery/d49042d3-8795-401b-bd73-d38ca5cfbbe7.jpg') }}"
                                     alt="Image Description">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {{-- Modal --}}
        <div id="hs-subscription-with-image-6"
             class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto">
            <div
                class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all sm:max-w-3xl sm:w-full m-3 sm:mx-auto">
                <div class="relative flex flex-col bg-white shadow-lg rounded-xl text-center w-full">
                    <div class="text-24-32 font-weight-600 text-[#1D2939] mt-2">Bali International Hospital</div>
                    <div class="absolute top-2 end-2 z-[10]">
                        <button type="button"
                                class="inline-flex justify-center items-center size-8 text-sm font-semibold rounded-lg border border-transparent bg-white/10 text-white hover:bg-white/20 disabled:opacity-50 disabled:pointer-events-none dark:focus:outline-none dark:focus:ring-1 dark:focus:ring-gray-600"
                                data-hs-overlay="#hs-subscription-with-image-6">
                            <span class="sr-only">Close</span>
                            <svg width="36" height="36" viewBox="0 0 36 36" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M11.3046 9.39541C10.7774 8.8682 9.92261 8.8682 9.39541 9.39541C8.8682 9.92261 8.8682 10.7774 9.39541 11.3046L16.0908 18L9.39541 24.6954C8.8682 25.2226 8.8682 26.0774 9.39541 26.6046C9.92261 27.1318 10.7774 27.1318 11.3046 26.6046L18 19.9092L24.6954 26.6046C25.2226 27.1318 26.0774 27.1318 26.6046 26.6046C27.1318 26.0774 27.1318 25.2226 26.6046 24.6954L19.9092 18L26.6046 11.3046C27.1318 10.7774 27.1318 9.92261 26.6046 9.39541C26.0774 8.8682 25.2226 8.8682 24.6954 9.39541L18 16.0908L11.3046 9.39541Z"
                                    fill="#667085" />
                            </svg>

                        </button>
                    </div>
                    <div class="p-2 xl:p-3 text-center overflow-y-auto">
                        <div id="sync3" class="sync1 owl-carousel owl-carousel-gallery-main owl-theme">
                            <div class="item h-[400] bg-black">
                                <img class="w-full h-[400] object-cover"
                                     src="{{ asset_gcs('public/assets/coe/c23f848a-6032-4f64-bd1f-d4d9ab36e2e7.jpg') }}"
                                     alt="Image Description">
                            </div>
                            <div class="item h-[400]">
                                <img class="w-full h-[400] object-cover"
                                     src="{{ asset_gcs('public/assets/gallery/28c1105a-53cc-4455-8452-6a299730a87d.jpg') }}"
                                     alt="Image Description">
                            </div>
                            <div class="item h-[400]">
                                <img class="w-full h-[400] object-cover"
                                     src="{{ asset_gcs('public/assets/gallery/a4785ac1-76a1-4f12-98b6-3d33ecd865c6.jpg') }}"
                                     alt="Image Description">
                            </div>
                            <div class="item h-[400]">
                                <img class="w-full h-[400] object-cover"
                                     src="{{ asset_gcs('public/assets/gallery/1749d38e-ad20-4f41-8817-50b10e7dac50.jpg') }}"
                                     alt="Image Description">
                            </div>
                        </div>

                        <div id="sync4" class="sync2 owl-carousel owl-carousel-gallery owl-theme no-navigation">
                            <div class="item h-[100]">
                                <img class="item w-full h-[100]"
                                     src="{{ asset_gcs('public/assets/coe/c23f848a-6032-4f64-bd1f-d4d9ab36e2e7.jpg') }}"
                                     alt="Image Description">
                            </div>
                            <div class="item h-[100]">
                                <img class="item w-full h-[100]"
                                     src="{{ asset_gcs('public/assets/gallery/28c1105a-53cc-4455-8452-6a299730a87d.jpg') }}"
                                     alt="Image Description">
                            </div>
                            <div class="item h-[100]">
                                <img class="item w-full h-[100]"
                                     src="{{ asset_gcs('public/assets/gallery/a4785ac1-76a1-4f12-98b6-3d33ecd865c6.jpg') }}"
                                     alt="Image Description">
                            </div>
                            <div class="item h-[100]">
                                <img class="item w-full h-[100]"
                                     src="{{ asset_gcs('public/assets/gallery/1749d38e-ad20-4f41-8817-50b10e7dac50.jpg') }}"
                                     alt="Image Description">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>
@endsection

@push('script')
    <script>
        $(document).ready(function() {
            var sync1 = $("#sync1");
            var sync2 = $("#sync2");
            var sync3 = $("#sync3");
            var sync4 = $("#sync4");
            var slidesPerPage = 4;
            var syncedSecondary = true;

            $('.owl-carousel-gallery-main').owlCarousel({
                loop: true,
                dots: false,
                nav: true,
                responsive: {
                    0: {
                        items: 1
                    },
                },
            })

            sync1
                .owlCarousel({
                    items: 1,
                    slideSpeed: 2000,
                    nav: true,
                    autoplay: false,
                    dots: true,
                    loop: true,
                    responsiveRefreshRate: 200,
                    navText: [
                        '<svg width="100%" height="100%" viewBox="0 0 11 20"><path style="fill:none;stroke-width: 1px;stroke: #000;" d="M9.554,1.001l-8.607,8.607l8.607,8.606"/></svg>',
                        '<svg width="100%" height="100%" viewBox="0 0 11 20" version="1.1"><path style="fill:none;stroke-width: 1px;stroke: #000;" d="M1.054,18.214l8.606,-8.606l-8.606,-8.607"/></svg>'
                    ]
                })
                .on("changed.owl.carousel", syncPosition);

            sync2
                .on("initialized.owl.carousel", function() {
                    sync2.find(".owl-item").eq(0).addClass("current");
                })
                .owlCarousel({
                    items: slidesPerPage,
                    dots: true,
                    nav: true,
                    smartSpeed: 200,
                    slideSpeed: 500,
                    slideBy: slidesPerPage, //alternatively you can slide by 1, this way the active slide will stick to the first item in the second carousel
                    responsiveRefreshRate: 100
                })
                .on("changed.owl.carousel", syncPosition2);

            sync3
                .owlCarousel({
                    items: 1,
                    slideSpeed: 2000,
                    nav: true,
                    autoplay: false,
                    dots: true,
                    loop: true,
                    responsiveRefreshRate: 200,
                    navText: [
                        '<svg width="100%" height="100%" viewBox="0 0 11 20"><path style="fill:none;stroke-width: 1px;stroke: #000;" d="M9.554,1.001l-8.607,8.607l8.607,8.606"/></svg>',
                        '<svg width="100%" height="100%" viewBox="0 0 11 20" version="1.1"><path style="fill:none;stroke-width: 1px;stroke: #000;" d="M1.054,18.214l8.606,-8.606l-8.606,-8.607"/></svg>'
                    ]
                })
                .on("changed.owl.carousel", syncPosition3);

            sync4
                .on("initialized.owl.carousel", function() {
                    sync4.find(".owl-item").eq(0).addClass("current");
                })
                .owlCarousel({
                    items: slidesPerPage,
                    dots: true,
                    nav: true,
                    smartSpeed: 200,
                    slideSpeed: 500,
                    slideBy: slidesPerPage, //alternatively you can slide by 1, this way the active slide will stick to the first item in the second carousel
                    responsiveRefreshRate: 100
                })
                .on("changed.owl.carousel", syncPosition4);

            function syncPosition(el) {
                //if you set loop to false, you have to restore this next line
                //var current = el.item.index;
                //if you disable loop you have to comment this block
                var count = el.item.count - 1;
                var current = Math.round(el.item.index - el.item.count / 2 - 0.5);
                if (current < 0) {
                    current = count;
                }
                if (current > count) {
                    current = 0;
                }
                //end block
                sync2
                    .find(".owl-item")
                    .removeClass("current")
                    .eq(current)
                    .addClass("current");
                var onscreen = sync2.find(".owl-item.active").length - 1;
                var start = sync2.find(".owl-item.active").first().index();
                var end = sync2.find(".owl-item.active").last().index();
                if (current > end) {
                    sync2.data("owl.carousel").to(current, 100, true);
                }
                if (current < start) {
                    sync2.data("owl.carousel").to(current - onscreen, 100, true);
                }
            }

            function syncPosition2(el) {
                if (syncedSecondary) {
                    var number = el.item.index;
                    sync1.data("owl.carousel").to(number, 100, true);
                }
            }

            sync2.on("click", ".owl-item", function(e) {
                e.preventDefault();
                var number = $(this).index();
                sync1.data("owl.carousel").to(number, 300, true);
            });

            function syncPosition3(el) {
                //if you set loop to false, you have to restore this next line
                //var current = el.item.index;
                //if you disable loop you have to comment this block
                var count = el.item.count - 1;
                var current = Math.round(el.item.index - el.item.count / 2 - 0.5);
                if (current < 0) {
                    current = count;
                }
                if (current > count) {
                    current = 0;
                }
                //end block
                sync4
                    .find(".owl-item")
                    .removeClass("current")
                    .eq(current)
                    .addClass("current");
                var onscreen = sync4.find(".owl-item.active").length - 1;
                var start = sync4.find(".owl-item.active").first().index();
                var end = sync4.find(".owl-item.active").last().index();
                if (current > end) {
                    sync4.data("owl.carousel").to(current, 100, true);
                }
                if (current < start) {
                    sync4.data("owl.carousel").to(current - onscreen, 100, true);
                }
            }

            function syncPosition4(el) {
                if (syncedSecondary) {
                    var number = el.item.index;
                    sync3.data("owl.carousel").to(number, 100, true);
                }
            }

            sync4.on("click", ".owl-item", function(e) {
                e.preventDefault();
                var number = $(this).index();
                sync3.data("owl.carousel").to(number, 300, true);
            });
        });
    </script>
@endpush
