<div class="live-chat relative z-50">
    <!-- Start Chat Button -->
    <button class="start-chat fixed bg-gradient-to-r from-[#3674B3] to-[#0B4074] w-[130px] h-[56px] text-white right-4 bottom-4 rounded-lg gap-1 font-normal text-sm leading-5 hover:cursor-pointer flex items-center justify-center sm:w-[100px] sm:h-[48px] sm:text-xs md:w-[120px] md:h-[50px] md:text-sm">
        <img src="{{ asset('assets/svg/chat.svg') }}" alt="" class="inline">
        Let's Chat!
    </button>

    <!-- Channel Options -->
    <div class="channel-options hidden fixed right-4 bottom-[95px] w-full max-w-[395px] sm:max-w-[320px] md:max-w-[460px] px-4 pl-10 sm:pl-0">
        <!-- Title Section -->
        <div class="title-section w-full h-[83px] p-6 gap-20 rounded-t-2xl bg-gradient-to-r from-[#3674B3] to-[#0B4074] flex items-center justify-center">
            <img src="{{ asset('assets/svg/bih-logo-white.svg') }}" alt="">
        </div>

        <!-- Content Section -->
        <div class="content-section w-full p-6 pb-8 pt-6 gap-8 rounded-b-2xl bg-white shadow-lg">
            <h1 class="title text-lg leading-7 font-semibold text-[#170F49]">Hello!</h1>
            <h2 class="subtitle text-base leading-5 font-normal text-[#170F49] mb-3.5">Talk to us on the available channel(s):</h2>

            <!-- WhatsApp Button -->
            <a href="https://wa.me/{{ bih_config_value(\App\Enums\Table\BihConfig\Key::CUSTOMER_SERVICE, \App\Enums\Table\BihConfig\Group::CONTACT_INFORMATION) }}" target="_blank"
               class="whatsapp-button flex items-center gap-2 text-[#475467] w-full h-12 border border-[#16A34A] rounded-md mb-2.5 p-2 cursor-pointer hover:bg-[#60D76A] hover:text-white sm:h-10 md:h-11">
                <img src="{{ asset('assets/svg/whatsapp.svg') }}" alt="" class="inline w-6 h-6">
                Whatsapp
            </a>

            <!-- Telegram Button -->
            <a href="{{ bih_config_value(\App\Enums\Table\BihConfig\Key::CHAT_WITH_US_TELEGRAM, \App\Enums\Table\BihConfig\Group::CONTACT_INFORMATION) }}" target="_blank" class="telegram-button flex items-center gap-2 text-[#475467] w-full h-12 border border-[#16A34A] rounded-md p-2 cursor-pointer hover:bg-[#60D76A] hover:text-white sm:h-10 md:h-11">
                <img src="{{ asset('assets/svg/telegram.svg') }}" alt="" class="inline w-6 h-6">
                Telegram
            </a>
        </div>
    </div>

    <!-- Close Chat Button -->
    <button class="close-chat hidden w-[56px] h-[56px] fixed right-4 bottom-4 p-4 gap-1 rounded-lg bg-gradient-to-r from-[#3674B3] to-[#0B4074] flex items-center justify-center sm:w-10 sm:h-10 md:w-12 md:h-12">
        <img src="{{ asset('assets/svg/cross-white.svg') }}" alt="">
    </button>
</div>

@push('script')
    <script>
        $(document).ready(function (){
            $('.start-chat').on('click', function (){
                $(this).css('display', 'none');
                $('.channel-options').css('display', 'block');
                $('.close-chat').css('display', 'block');
            });

            $('.close-chat').on('click', function (){
                $(this).css('display', 'none');
                $('.channel-options').css('display', 'none');
                $('.start-chat').css('display', 'block');
            });

            {{--$('.whatsapp-button').on('click', function (){--}}
            {{--    window.open('https://wa.me/{{ env('CONTACT_WHATSAPP') }}', '_blank');--}}
            {{--});--}}

            {{--$('.telegram-button').on('click', function (){--}}
            {{--    window.open('https://t.me/{{ env('USERNAME_TELEGRAM') }}', '_blank');--}}
            {{--});--}}
        });
    </script>
@endpush


