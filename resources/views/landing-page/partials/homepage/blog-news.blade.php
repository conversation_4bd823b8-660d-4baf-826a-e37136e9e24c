<link href="https://cdn.jsdelivr.net/npm/quill@2.0.2/dist/quill.snow.css" rel="stylesheet" />
<style>
    /* Remove border from the editor */
    .ql-container {
        border: none !important;
        overflow: hidden;
    }
    .ql-editor {
        display: -webkit-box;
        overflow: hidden;
        -webkit-line-clamp: 5;
        -webkit-box-orient: vertical;
        display: block; /* Ensure block display */
        line-height: 1.6; /* Adjust line height as necessary */
        height: calc(1.2em * 5); /* Adjust height according to line height and number of lines */
    }
</style>
<div class="flex flex-col lg:gap-8 gap-4">
    <div class="flex flex-col gap-2 -ml-10">
        <div class="flex items-center">
{{--            <div class="w-10 bg-[#16A34A] h-[1px]"></div>--}}
            <div class="text-sm text-[#16A34A] ml-10">Read Top Articles</div>
        </div>
        <div class="flex">
            <div class="text-4xl font-bold mx-10">Blogs & News</div>
        </div>
    </div>
    <div class="grid grid-cols-1 lg:grid-cols-2 lg:gap-10 gap-4">
        @if(@$articles['highlight'])
            <div class="flex flex-col">
                <a href="{{ route('articles.show', @$articles['highlight']->slug) }}">
                    <div class="h-[240px] bg-cover bg-center rounded-lg cursor-pointer"
                         style="background-image: url('{{ asset_gcs(@$articles['highlight']->image) }}');"
                    loading="lazy">
                    </div>
                </a>
                <div class="flex gap-2 items-center lg:mt-6 mt-2">
                    @if(@$articles['highlight']->user->image)
                        <img src="{{ $articles['highlight']->user->image }}" alt="Bali International Hospital" loading="lazy"
                             class="inset-0 object-cover h-5 rounded-full w-5"
                             style="background: radial-gradient(77.36% 77.36% at 45.71% 50%, rgba(7, 39, 70, 0.33) 0%, rgba(7, 39, 70, 0.72) 100%);">
                    @else
                        <div class="rounded-full bg-[#f7faff] items-center flex justify-center p-2">
                            <span class="text-xs font-bold text-gray-700">{{ @$articles['highlight']->user->initials }}</span>
                        </div>
                    @endif
                    <div class="text-sm">{{ @$articles['highlight']->user->name }}</div>
                </div>
                <a href="{{ route('articles.show', @$articles['highlight']->slug) }}"
                   class="hover:cursor-pointer text-[#09335D] font-semibold text-xl lg:text-2xl mt-4 lg:mt-3 line-clamp-2">
                    {{ @$articles['highlight']->title }}
                </a>
                <div class="-ml-4 line-clamp-3 overflow-hidden">
                    <div class="ql-container" id="editor">
                        {!! @$articles['highlight']->content !!}
                    </div>
                </div>

                <div class="flex text-xs lg:text-sm text-[#667085] items-center lg:gap-2 gap-1 mt-2">
                    <svg class="object-cover" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                         fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M12 2.25C6.61522 2.25 2.25 6.61522 2.25 12C2.25 17.3848 6.61522 21.75 12 21.75C17.3848 21.75 21.75 17.3848 21.75 12C21.75 6.61522 17.3848 2.25 12 2.25ZM12.75 6C12.75 5.58579 12.4142 5.25 12 5.25C11.5858 5.25 11.25 5.58579 11.25 6V12C11.25 12.4142 11.5858 12.75 12 12.75H16.5C16.9142 12.75 17.25 12.4142 17.25 12C17.25 11.5858 16.9142 11.25 16.5 11.25H12.75V6Z"
                              fill="#667085" />
                    </svg>
                    <div>{{ @$articles['highlight']->time_to_read }} min read </div>
                    <div> • </div>
                    <span>{{ \Carbon\Carbon::parse(@$articles['highlight']->created_at)->format('d M Y') }}</span>
                </div>
            </div>
        @endif
        <div class="flex flex-col lg:gap-10 gap-4">
            @foreach ($articles['articles'] as $key => $article)
                <a href="{{ route('articles.show', $article->slug) }}" class="flex flex-row gap-3 lg:gap-6 hover:cursor-pointer h-[126px]">
                    <div class="w-2/5">
                        <div class="h-[126px]">
                            <img src="{{ asset_gcs($article->image) }}" alt="" loading="lazy"
                                 class="rounded-lg object-cover h-full w-full"
                            loading="lazy">
                        </div>
                    </div>
                    <div class="w-3/5 flex flex-col {{ strlen($article->title) < 25 ? 'gap-2' : 'justify-between gap-0' }}">
                        <div class="flex gap-2 items-center">
                            @if(@$article->user->image)
                                <img src="{{ $article->user->image }}" alt="Bali International Hospital" loading="lazy"
                                     class="inset-0 object-cover h-4 rounded-full w-4"
                                     style="background: radial-gradient(77.36% 77.36% at 45.71% 50%, rgba(7, 39, 70, 0.33) 0%, rgba(7, 39, 70, 0.72) 100%);">
                            @else
                                <div class="rounded-full bg-[#f7faff] items-center flex justify-center p-2">
                                    <span class="text-[10px] lg:text-xs font-bold text-gray-700">{{ @$article->user->initials }}</span>
                                </div>
                            @endif
                            <div class="text-xs lg:text-sm">{{ @$article->user->name }}</div>
                        </div>
                        <div class="text-[#09335D] font-weight-500 text-lg line-clamp-2">{{ $article->title }}</div>
                        <div class="flex text-xs lg:text-sm text-[#667085] items-center gap-2">
                            <svg class="object-cover" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24"
                                 fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M12 2.25C6.61522 2.25 2.25 6.61522 2.25 12C2.25 17.3848 6.61522 21.75 12 21.75C17.3848 21.75 21.75 17.3848 21.75 12C21.75 6.61522 17.3848 2.25 12 2.25ZM12.75 6C12.75 5.58579 12.4142 5.25 12 5.25C11.5858 5.25 11.25 5.58579 11.25 6V12C11.25 12.4142 11.5858 12.75 12 12.75H16.5C16.9142 12.75 17.25 12.4142 17.25 12C17.25 11.5858 16.9142 11.25 16.5 11.25H12.75V6Z"
                                      fill="#667085" />
                            </svg>
                            <div>{{ @$article->time_to_read }} min read </div>
                            <div> • </div>
                            <span>{{ \Carbon\Carbon::parse($article->created_at)->format('d M Y') }}</span>
                        </div>
                    </div>
                </a>
            @endforeach
        </div>
    </div>
    <div class="flex justify-center hover:cursor-pointer">
        <a href="{{ route('articles.index') }}" class="button-homepage">
            More Articles
        </a>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/quill@2.0.2/dist/quill.js"></script>
<script>
    var toolbarOptions = [
        [{ 'header': [1, 2, false] }],
        ['bold', 'italic', 'underline'],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        ['link']
    ];

    const containerNews = document.getElementById('editor');
    const quill = new Quill(containerNews,{
        readOnly: true,
        modules: {
            toolbar: null
        },
        theme: 'snow'
    });
</script>
