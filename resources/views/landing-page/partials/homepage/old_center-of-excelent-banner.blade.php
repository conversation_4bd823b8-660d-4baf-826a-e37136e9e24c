<style>
    .card-coe:hover .learn-more {
        visibility: visible;
        position: absolute;
        background-color: #0D4D8B;
        color: white;
        height: 48px;
        bottom: 0;
        left: 0;
        width: 100%;
        border-bottom-right-radius: 1.5rem;
        border-bottom-left-radius: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: opacity 0.5s ease;
        opacity: 100;
    }
</style>

<div class="">
    @foreach ($coeData as $item)
        <img class="hidden" src="{{ $item['bg'] }}">
        {{-- <img class="hidden" src="assets/homepage/coe/{{ $item['id'] }}.jpg"> --}}
    @endforeach
</div>

<div id="bg-banner-coe" class="bg-cover bg-center py-8 xl:h-[580px] -mx-4 xl:mx-0"
    style="background-image: url('{{ Storage::disk('gcs')->url('public/homepage/4071d652-1833-4d98-9794-c5fa0c8d22c4.jpeg') }}')">
    <div class="flex flex-col xl:flex-row gap-7 justify-between mx-4 xl:mx-40">
        <div class="order-1 container-icon-coe grid grid-cols-2 xl:w-[392px] gap-x-8 gap-y-[30.5px]">
            @foreach ($coeData as $item)
                <a href="{{ $item['nav'] }}" id="{{ $item['id'] }}" data-title="{{ $item['title'] }}"
                    data-description="{{ $item['description'] }}" data-bg="{{ $item['bg'] }}"
                    class="card-coe hover:cursor-pointer bg-opacity-70 bg-white backdrop-filter backdrop-blur-xl rounded-3xl flex flex-col justify-between gap-4 items-center py-6">
                    <img src="{{ $item['image'] }}" alt="{{ $item['alt'] }}" class="w-[60px]">
                    <p class="text-12-18 xl:text-">
                        @if(strlen($item['title']) > 22)
                            {{ substr($item['title'], 0, 15) . '...'}}
                        @else
                            {{ $item['title'] }}
                        @endif
                    </p>
                    <div class="learn-more opacity-0 absolute read-more" data-link="{{ $item['nav'] }}">
                        Read More
                    </div>
                </a>
            @endforeach
        </div>
        {{-- CARD --}}
        <div
            class="order-0 xl:order-2 xl:flex flex-col gap-4 bg-white backdrop-filter backdrop-blur-xl bg-opacity-40 h-[560px] xl:w-1/2 xl:h-[511px] rounded-3xl p-4 xl:p-10">
            <div class="flex flex-col h-full justify-between">
                <div class="flex flex-col gap-3">
                    <div class="flex flex-col gap-2">
                        <p class="text-16-24 font-weight-400 text-[#16A34A]">✦ World-Class Specialty Care</p>
                        <p class="card-title text-36-44 font-weight-600 text-primary-blue">Centers of Excellence</p>
                    </div>
                    <p class="card-content font-light text-18-28 max-h-[200px] text-[#475467]">Our Centers of Excellence focus on Cardiology, Oncology, Neurology, Gastroenterology, Orthopedics, and Medical Check-Ups. We offer one-stop services in a healing Balinese ambience. We constantly innovate our medical services with a holistic approach to patient care.</p>
                </div>
                <div class="flex flex-col gap-3 mt-2">
                    <a href="/center-of-excellence" class="button-card-coe flex justify-center">
                        <button class="button-homepage w-full">
                            View All Centers
                        </button>
                    </a>
                    <div class="flex flex-col gap-2" id="parentPartnershipContainer">
{{--                        <p class="text-18-28 text-[#475467] font-weight-400 mb-2">Our Partners</p>--}}
{{--                        <div class="grid grid-cols-2 xl:grid-cols-4 gap-4 items-center" id="partnershipContainer">--}}
{{--                                <img src="{{asset("assets/homepage/partnership/innoquest.png")}}"--}}
{{--                                    alt="Image description" class="h-16 xl:h-full xl:w-full object-contain rounded-md">--}}
{{--                            <img src="{{asset_gcs("public/assets/homepage/partnership/328e4b8a-135f-4884-9649-b57ea8f6aa3c.png")}}"--}}
{{--                                alt="Image description" class="h-16 xl:h-full xl:w-full object-contain rounded-md">--}}
{{--                            <img src="{{asset_gcs("public/assets/homepage/partnership/a347ef87-ccbb-4404-95b4-e9c1cf45b081.png")}}"--}}
{{--                                 alt="Image description" class="h-16 xl:h-full xl:w-full object-contain rounded-md">--}}
{{--                            <img src="{{asset_gcs("public/assets/homepage/partnership/8cf82ab1-6b56-4f3f-a419-f8b3fa6bcd8b.png")}}"--}}
{{--                                 alt="Image description" class="h-16 xl:h-full xl:w-full object-contain rounded-md">--}}
{{--                            <img src="{{asset_gcs("public/assets/homepage/partnership/bc722320-6bb7-46cb-9480-5e6aba7a39bb.png")}}"--}}
{{--                                 alt="Image description" class="h-16 xl:h-full xl:w-full object-contain rounded-md">--}}
{{--                        </div>--}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('script')
    <script>
        let container = $('.card-coe');
        let baner = $('#bg-banner-coe')
        var paragraph = $('.card-content')
        let cardTitle = $('.card-title')
        const data = @json($coeData);


        let description =
            "Our Centers of Excellence focus on Cardiology, Oncology, Neurology, Gastroenterology, Orthopedics, and Medical Check-Ups. We offer one-stop services in a healing Balinese ambience. We constantly innovate our medical services with a holistic approach to patient care."

        let title = "Centers of Excellence"

        if ($(window).width() < 600) {
            container.on('click', function (event) {
                event.preventDefault();

                let hoveredId = $(this).attr('id');
                let title = $(this).data('title')
                let bg = $(this).data('bg');
                // console.log(bg);

                let contentDescription = $(this).data('description')
                baner.css({
                    // 'background-image': `url('{{ asset('assets/homepage/coe/${hoveredId}.jpg') }}')`,
                    'background-image': `url(` + bg + `)`,
                    'transition': 'background-image 0.3s ease'
                });
                paragraph.text(contentDescription)
                cardTitle.text(title)
                // $('.button-card-coe button').remove();
                // $('.button-card-coe').remove();
                $('.button-card-coe').hide();
                // $('#partnershipContainer').html('');
                document.getElementById('parentPartnershipContainer').style.display = 'none';

                if (data !== undefined) {
                    data.forEach(item => {
                        if (item.id === hoveredId) {
                            item.partnership.forEach(item2 => {
                                let imageElement = $('<img>', {
                                    src: item2,
                                    alt: 'Image description',
                                    class: 'h-16 xl:h-full xl:w-full object-contain rounded-md'
                                });
                                $('#partnershipContainer').append(imageElement);
                            })
                        }
                    });
                }
            })

            $('.read-more').on('click', function () {
                const href =  $(this).data('link');
                window.location.replace(href);
            });
        }

        container.on('mouseenter', function() {
            let hoveredId = $(this).attr('id');
            let title = $(this).data('title')
            let bg = $(this).data('bg');
            // console.log(bg);

            let contentDescription = $(this).data('description')
            baner.css({
                // 'background-image': `url('{{ asset('assets/homepage/coe/${hoveredId}.jpg') }}')`,
                'background-image': `url(` + bg + `)`,
                'transition': 'background-image 0.3s ease'
            });
            paragraph.text(contentDescription)
            cardTitle.text(title)
            // $('.button-card-coe button').remove();
            // $('.button-card-coe').remove();
            $('.button-card-coe').hide();
            // $('#partnershipContainer').html('');
            document.getElementById('parentPartnershipContainer').style.display = 'none';

            if (data !== undefined) {
                data.forEach(item => {
                    if (item.id === hoveredId) {
                        item.partnership.forEach(item2 => {
                            let imageElement = $('<img>', {
                                src: item2,
                                alt: 'Image description',
                                class: 'h-16 xl:h-full xl:w-full object-contain rounded-md'
                            });
                            $('#partnershipContainer').append(imageElement);
                        })
                    }
                });
            }
        });
        container.on('mouseleave', function() {

            document.getElementById('parentPartnershipContainer').style.display = 'block';
            // baner.css('background-image', `url('{{ asset('assets/homepage/coe/coe-bg.jpg') }}')`);
            baner.css('background-image',
                {{--`url('{{ Storage::disk('gcs')->url('public/assets/coe/a22b1b1f-153d-41b9-b138-5d0c4bdb6526.jpg') }}')`--}}
                `url('{{ Storage::disk('gcs')->url('public/assets/coe/63dba25e-bcd5-40d9-ac3c-3fa658a398c4.png') }}')`
            );
            paragraph.text(description);
            cardTitle.text(title)
            {{--$('#partnershipContainer').html($('<img>', {--}}
            {{--    // src: "{{ asset('assets/homepage/partnership/lippo.png') }}",--}}
            {{--    src: "{{ Storage::disk('gcs')->url('/public/assets/homepage/partnership/7b55af5b-d1d8-43dc-8f08-060e74185a3b.png') }}",--}}
            {{--    alt: 'Image description',--}}
            {{--    class: 'h-16 xl:h-full xl:w-full object-contain rounded-md'--}}
            {{--}));--}}

            // var newButton = $('<a>').addClass('button-homepage w-full').attr('href',
            //     '/patient-centered-care/center-of-excellence').text('View All Centers');
            // $('.button-card-coe').append(newButton);
            $('.button-card-coe').show();
        });
    </script>
@endpush()
