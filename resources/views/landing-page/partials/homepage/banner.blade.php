<div class="relative">
    <video id="bg-video" autoplay loop muted playsinline
           class="custom-video inset-0 w-full h-screen object-cover"
           poster="{{ asset_gcs('public/assets/general/1acdd8e3-5a26-46e4-a19c-503f2be1808b.webp') }}"
           preload="none">
    </video>
    {{-- <video autoplay loop muted playsinline class="custom-video inset-0 w-full h-screen object-cover"
           poster="{{ asset_gcs('public/assets/d3687068-baab-4983-9471-fa7709d5adbb.png') }}" preload="none">
        <source src="{{ Storage::disk('gcs')->url('videos/videobanner.mp4') }}"
                type="video/mp4">
        Your browser does not support the video tag.
    </video> --}}
    <div class="absolute inset-0" style="background-image: linear-gradient(0deg, #000000 1.35%, rgba(117, 153, 136, 0) 70.24%);">
    </div>
    <div class="absolute inset-0 flex-col justify-center flex">
        <div class="flex flex-col mx-4 xl:w-[32.063rem] xl:mx-40 gap-4 text-white mt-24">
            <p class="text-base lg:text-lg">
                Opening Soon
            </p>
            <p class="text-4xl lg:text-5xl font-weight-700">A Culture Of Care</p>
            {{-- <span id="element1" class="text-white text-4xl font-bold"></span> --}}
        </div>
    </div>
    <div class="absolute inset-x-0 bottom-0 mb-8 flex flex-col justify-center">
        <div class="flex bg-white bg-opacity-20 rounded-3xl mx-4 xl:mx-40 p-6 gap-4">
            <a href="{{ route('doctors.index') }}"
               class="flex flex-col xl:flex-row items-center py-2 xl:py-4 w-1/3 h-auto xl:w-1/3 bg-white xl:h-24 rounded-2xl shadow-xl gap-2 xl:gap-0">
                <div class="h-10 w-16 items-center justify-center flex">
                    <img src="{{ Storage::disk('gcs')->url('public/assets/homepage/banner/58524af8-8d94-4caf-a1cd-3e3c4c067d58.png') }}"
                         alt="findDoctor"
                         class="object-cover" />
                </div>
                <div class="flex flex-col">
                    <p
                        class="text-12-18 xl:text-16-22 w-[4.688rem] xl:w-full text-center xl:text-left font-weight-400 xl:font-weight-600 font-[#1F2937]">
                        Find a Doctor</p>
                    <p class="text-sm font-light font-[#6D7079] hidden xl:block">Choose by name, specialty & more
                    </p>
                </div>
            </a>
            <div
                class="flex flex-col xl:flex-row items-center py-2 xl:py-4 w-1/3 h-auto xl:w-1/3 bg-white xl:h-24 rounded-2xl shadow-xl gap-2 xl:gap-0">
                <div class="h-10 w-16 items-center justify-center flex">
                    <img src="{{ Storage::disk('gcs')->url('public/assets/homepage/banner/8ce0d5de-9ba5-481d-b6ed-4c31a3a587a4.png') }}"
                         alt="findDoctor"
                         class="object-cover" />
                </div>
                <a href="{{ route('appointments.book', [
                    'uuid' => \Illuminate\Support\Str::uuid(),
                    'doctor_uuid' => 'NO',
                    'type' => 1,
                ]) }}"
                   class="flex flex-col">
                    <p
                        class="text-12-18 xl:text-16-22 w-[4.688rem] xl:w-full text-center xl:text-left font-weight-400 xl:font-weight-600 font-[#1F2937]">
                        Clinic Appointment</p>
                    <p class="text-sm font-light font-[#6D7079] hidden xl:block">Schedule your visit online</p>
                </a>
            </div>
            <a href="{{route('medical_packages.category', ['slug_category' => \App\Enums\General\GeneralString::MCU_BASIC_CATEGORY])}}"
               class="hover:cursor-pointer flex flex-col xl:flex-row items-center py-2 xl:py-4 w-1/3 h-auto xl:w-1/3 bg-white xl:h-24 rounded-2xl shadow-xl gap-2 xl:gap-0">
                <div class="h-10 w-16 items-center justify-center flex">
                    <img src="{{ Storage::disk('gcs')->url('public/assets/homepage/banner/3f7cd5cf-93e6-462d-8f2c-b77ed149e7b0.png') }}"
                         alt="findDoctor"
                         class="object-cover" />
                </div>
                <div class="flex flex-col">
                    <p
                        class="text-12-18 xl:text-16-22 w-[4.688rem] xl:w-full text-center xl:text-left font-weight-400 xl:font-weight-600 font-[#1F2937]">
                        Buy a Package</p>
                    <p class="text-sm font-light font-[#6D7079] hidden xl:block">See our Health Screening package
                    </p>
                </div>
            </a>
        </div>

    </div>
</div>

<script>
    window.addEventListener('load', () => {
        const video = document.getElementById('bg-video');
        const source = document.createElement('source');
        source.src = "{{ asset_gcs('public/assets/general/da281669-2d5d-4889-ba59-bcac575ef402.mp4') }}";
        source.type = "video/mp4";
        video.appendChild(source);
        video.load();
    });
</script>
