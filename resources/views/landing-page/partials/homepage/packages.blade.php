<style>
    .hide-scrollbar {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;     /* Firefox */
    }
    .hide-scrollbar::-webkit-scrollbar {
        display: none; /* Chrome, Safari, and Opera */
    }
</style>

@php
    $maxType = 3;
@endphp
<div class="lg:-mr-40">
    <div class="flex flex-col lg:flex-row gap-4 lg:gap-[82px]">
        @foreach(@$packageTypes ?? [] as $packageType)
            <div class="flex flex-col w-full lg:w-1/2 hidden package-type-preview-section justify-between z-10" id="package-type-preview-{{$packageType->uuid}}">
                <div class="flex flex-col">
                    <span class="text-base font-inter font-light text-[#16A34A]">✦ Your Health, Our Priority</span>
                    <h2 class="text-5xl font-brand font-semibold mt-3">Discover Our {{$packageType->name}}</h2>
                    <hr class="mt-6">
                    @if($packageType->id == 1)
                        <span class="text-[18px] font-light text-[#475467] mt-4 line-clamp-[13]">Results in just 24 hours</span>
                    @endif
                    {{-- <span class="text-[18px] font-light text-[#475467] mt-4 line-clamp-[13]">{!! nl2br(e($packageType->description)) !!}</span> --}}
                </div>
                <a href="/medical-packages/category/mcu-basic" class="mt-6 py-4 px-6 bg-brand text-white flex justify-center rounded-xl w-fit hover:cursor-pointer">
                    <span class="text-base font-light">More Health Screening Packages</span>
                </a>
                {{-- <a href="{{route('medical_packages.type', ['slug_type' => $packageType->slug])}}" class="mt-6 py-4 px-6 bg-brand text-white flex justify-center rounded-xl w-fit hover:cursor-pointer">
                    <span class="text-base font-light">More {{ $packageType->name }} Packages</span>
                </a> --}}
            </div>
        @endforeach

        <div class="flex flex-col w-full lg:w-3/5 gap-6 z-10">
            <div class="flex lg:flex-row gap-4 overflow-x-auto whitespace-nowrap hide-scrollbar">
                @foreach(@$packageTypes ?? [] as $index => $packageType)
                    @php
                        if ($index >= $maxType) break;
                    @endphp
                    <div class="flex flex-row gap-2 py-3 px-6 rounded-full items-center hover:cursor-pointer bg-white text-[#667085] border package-type-button"
                         onclick="onClickPackageType('{{$packageType->uuid}}','{{$packageType->slug}}')" id="package-type-{{$packageType->uuid}}"
                         data-route="{{ route('ajax.package_types.show', ['uuid' => $packageType->uuid]) }}">
                        @if($packageType->icon)
                            <img src="{{ asset_gcs($packageType->icon) }}" alt="icon bithealth" loading="lazy" class="h-[20px] w-[20px] object-cover">
                        @endif
                        <span class="text-base font-light">{{$packageType->name}}</span>
                    </div>
                @endforeach
                @if($packageTypes)
                        @if(@$packageTypes->count() > $maxType)
                            <div class="hs-dropdown relative inline-flex z-10">
                                <button id="hs-dropdown-with-icons" type="button" class="hs-dropdown-toggle py-3 px-4 inline-flex items-center gap-x-2 text-sm font-medium rounded-full border border-gray-200 bg-white text-gray-800 shadow-sm hover:bg-gray-50 disabled:opacity-50 disabled:pointer-events-none">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                        <path d="M10 3C10.8284 3 11.5 3.67157 11.5 4.5C11.5 5.32843 10.8284 6 10 6C9.17157 6 8.5 5.32843 8.5 4.5C8.5 3.67157 9.17157 3 10 3Z" fill="#1D2939"/>
                                        <path d="M10 8.5C10.8284 8.5 11.5 9.17157 11.5 10C11.5 10.8284 10.8284 11.5 10 11.5C9.17157 11.5 8.5 10.8284 8.5 10C8.5 9.17157 9.17157 8.5 10 8.5Z" fill="#1D2939"/>
                                        <path d="M11.5 15.5C11.5 14.6716 10.8284 14 10 14C9.17157 14 8.5 14.6716 8.5 15.5C8.5 16.3284 9.17157 17 10 17C10.8284 17 11.5 16.3284 11.5 15.5Z" fill="#1D2939"/>
                                    </svg>
                                </button>

                                <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white shadow-md rounded-lg p-2 mt-2 divide-y divide-gray-200" aria-labelledby="hs-dropdown-with-icons">
                                    <div class="py-2 first:pt-0 last:pb-0">
                                        @foreach(@$packageTypes ?? [] as $index => $packageType)
                                            @php
                                                if ($index < $maxType) continue;
                                            @endphp
                                            <div
                                                onclick="onClickPackageTypeDropdown('{{$packageType->uuid}}','{{$packageType->slug}}')"
                                                class="flex flex-row gap-2 items-center hover:cursor-pointer">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M4.25 2C3.00736 2 2 3.00736 2 4.25V6.75C2 7.99264 3.00736 9 4.25 9H6.75C7.99264 9 9 7.99264 9 6.75V4.25C9 3.00736 7.99264 2 6.75 2H4.25ZM4.25 11C3.00736 11 2 12.0074 2 13.25V15.75C2 16.9926 3.00736 18 4.25 18H6.75C7.99264 18 9 16.9926 9 15.75V13.25C9 12.0074 7.99264 11 6.75 11H4.25ZM13.25 2C12.0074 2 11 3.00736 11 4.25V6.75C11 7.99264 12.0074 9 13.25 9H15.75C16.9926 9 18 7.99264 18 6.75V4.25C18 3.00736 16.9926 2 15.75 2H13.25ZM13.25 11C12.0074 11 11 12.0074 11 13.25V15.75C11 16.9926 12.0074 18 13.25 18H15.75C16.9926 18 18 16.9926 18 15.75V13.25C18 12.0074 16.9926 11 15.75 11H13.25Z" fill="#667085"/>
                                                </svg>
                                                <span class="text-[#475467] text-base font-inter hover:font-medium">{{ $packageType->name }}</span>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        @endif
                @endif

            </div>
            @foreach(@$packageTypes ?? [] as $packageType)
                <div class="hidden package-type-slider-section" id="package-type-slider-section-{{$packageType->uuid}}">
                    <div class="flex flex-row gap-6 owl-carousel owl-carousel-second owl-theme">
                        @foreach($packageType->recommendedPackages as $package)
                            <div class="flex flex-col h-fit">
                                @if(@$package->image)
                                    <img class="rounded-2xl object-cover h-[146px] w-[260px]" src="{{asset_gcs($package->image)}}" alt="medical package bithealth"
                                         loading="lazy">
                                @else
                                    <img class="rounded-2xl object-cover h-[146px] w-[260px]" src="https://images.pexels.com/photos/806427/pexels-photo-806427.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" loading="lazy" alt="medical package bithealth">
                                @endif
                                <span class="text-2xl font-semibold line-clamp-2 mt-6 h-16">{{ $package->title }}</span>
                                <span class="line-clamp-2 text-base font-light text-[#667085] mt-2 hidden md:block">{!! $package->description_card !!}</span>
                                <span class="text-xl font-medium text-brand mt-4">Rp{{ str_replace(',', '.', number_format($package->price)) }}</span>
                                <a href="{{route('medical_packages.show',['slug_category' => $package->packageCategory->slug, 'slug_package'=>$package->slug])}}" class="border border-brand py-3 rounded-xl flex justify-center w-fit px-6 hover:cursor-pointer mt-6">
                                    <span class="text-base font-medium text-brand">See Details</span>
                                </a>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach

            <div class="flex flex-row gap-4 justify-between lg:mr-40">
                <button id="customNextBtnPackage"
                        class="bg-white shadow-lg rounded-full size-12 flex justify-center items-center ">
                    {!! file_get_contents('assets/common/chevron-left.svg') !!}
                </button>
                <button id="customPrevBtnPackage"
                        class="bg-white shadow-lg rounded-full size-12 flex justify-center items-center">
                    {!! file_get_contents('assets/common/chevron-right.svg') !!}
                </button>
            </div>
        </div>
    </div>
</div>

@push('script')
    <script>
        var owl2 = $('.owl-carousel-second').owlCarousel({
            // loop: true,
            margin: 24,
            dots: false,
            responsive: {
                0: {
                    items: 1
                },
                600: {
                    items: 3
                },
            },
        })

        $('#customNextBtnPackage').click(function() {
            owl2.trigger('prev.owl.carousel');
        })
        $('#customPrevBtnPackage').click(function() {
            owl2.trigger('next.owl.carousel', [300]);
        })

        function onClickPackageType(uuid, slug) {
            processButton(uuid);
            processPreviewSection(uuid);
            processSliderSection(uuid);
        }
        function onClickPackageTypeDropdown(uuid, slug) {
            window.location.href = "medical-packages/type/" + slug;
        }

        function processButton(uuid){
            // Reset class for all buttons
            var packageTypes = document.querySelectorAll('.package-type-button');
            if(packageTypes){
                packageTypes.forEach(function(button) {
                    button.className = "flex flex-row gap-2 py-3 px-6 rounded-full items-center hover:cursor-pointer bg-white text-[#667085] border package-type-button";
                });
            }

            // Apply specific class to the clicked button
            var card = document.getElementById("package-type-"+uuid);
            if(card){
                card.className = "flex flex-row gap-2 py-3 px-6 rounded-full items-center hover:cursor-pointer bg-[#E5FFEF] text-[#16A34A] package-type-button";
            }
        }

        function processPreviewSection(uuid) {
            // Reset visibility for all preview sections
            var packageTypes = document.querySelectorAll('.package-type-preview-section');
            packageTypes.forEach(function(preview) {
                preview.classList.add('hidden'); // Hide all preview sections
            });

            // Show the preview section corresponding to the clicked UUID
            var card = document.getElementById("package-type-preview-" + uuid);
            if (card) {
                card.classList.remove('hidden'); // Show the selected preview section
            }
        }

        function processSliderSection(uuid) {
            // Reset visibility for all preview sections
            var packageTypes = document.querySelectorAll('.package-type-slider-section');
            packageTypes.forEach(function(preview) {
                preview.classList.add('hidden'); // Hide all preview sections
            });

            // Show the preview section corresponding to the clicked UUID
            var card = document.getElementById("package-type-slider-section-" + uuid);
            console.log(card);
            if (card) {
                card.classList.remove('hidden'); // Show the selected preview section
            }
        }

        // preview section condition
        var packageTypes = @json($packageTypes);
        var defaultUuid = packageTypes[0].uuid;
        processPreviewSection(defaultUuid);
        processButton(defaultUuid);
        processSliderSection(defaultUuid);

    </script>
@endpush
