@section('title', 'Bali International Hospital')
@section('seo_meta')
    <link rel="canonical" href="https://bih.id/career">
    <link rel="alternate" hreflang="en-id" href="https://bih.id/career">
@endsection
@extends('landing-page.layouts.masterTemplate')

@section('content')
    <div class="mx-4 xl:mx-40 flex flex-col">
        {{-- BANNER --}}
        <div class="relative xl:-mx-40 -mx-4">
            <img src="{{ asset_gcs('public/career/1435376c-5a5a-4130-bfc2-2e5ffcf67640.png') }}" alt="Career"
                 class="inset-0 w-full object-cover h-64 xl:h-max"
                 style="background: radial-gradient(77.36% 77.36% at 45.71% 50%, rgba(7, 39, 70, 0.33) 0%, rgba(7, 39, 70, 0.72) 100%);">
            <div class="absolute inset-0"
                 style="background: radial-gradient(77.36% 77.36% at 45.71% 50%, rgba(7, 39, 70, 0.33) 0%, rgba(7, 39, 70, 0.72) 100%);">
            </div>
            <div class="absolute inset-0 flex flex-col justify-end xl:mx-40 mx-4 mb-11">
                <h1 class="text-white xl:text-6xl text-4xl font-weight-600">Career</h1>
            </div>
        </div>
        <div class="flex flex-col mb-6">
            <div class="mb-6">
                <div class="flex flex-row gap-4 items-center py-4">
                    <a href="/" class="text-primary-blue-60">Home</a>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                        fill="none">
                        <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5"
                            stroke-linecap="round" stroke-linejoin="round" />
                    </svg>
                    <p class="breadcrumb-inactive">Career</p>
                </div>
            </div>
            <div class="text-center py-4 lg:py-10 flex flex-col gap-6">
                <h1 class="text-30-38 font-weight-600 text-[#09335D]">Why Join Bali International Hospital?
                </h1>
                <p class="text-left text-base font-light text-[#344054]">At Bali International Hospital, we recognize that employees are our most important asset. That's why we foster a work environment that empowers you to grow personally and professionally. We offer:</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 pb-10">
                <div class="flex flex-col xl:flex-row">
                    <img src="{{ asset_gcs('public/career/45eaff1e-f646-4358-a607-73bca0059e45.jpeg') }}" alt="Banner Image"
                        class="inset-0 size-60 xl:size-40 mb-4 xl:mb-0 object-cover rounded-2xl w-full">
                    <div class="xl:px-4 w-full flex flex-col gap-2">
                        <p class="font-weight-600 text-18-28 text-[#09335D]">Career Development Opportunities</p>
                        <p class="text-base text-[#475467] font-light">We provide training and development opportunities to help you reach your full
                            potential in a medical and administrative career.</p>
                    </div>
                </div>

                <div class="flex flex-col xl:flex-row">
                    <img src="{{ asset_gcs('public/career/62c78517-10b1-45d0-b47e-8ff2c9ad2055.jpeg') }}" alt="Banner Image"
                         class="inset-0 size-60 xl:size-40 mb-4 xl:mb-0 object-cover rounded-2xl w-full">
                    <div class="xl:px-4 w-full flex flex-col gap-2">
                        <p class="font-weight-600 text-18-28 text-[#09335D]">Patient-Oriented Work Culture</p>
                        <p class="text-base text-[#475467] font-light">At Bali International Hospital, patient well-being is our top priority. We're looking for passionate individuals to join our team and contribute to delivering exceptional healthcare. Here, you'll play a vital role in creating a positive and healing environment for every patient.</p>
                    </div>
                </div>

                <div class="flex flex-col xl:flex-row">
                    <img src="{{ asset_gcs('public/career/b00f4836-39d0-4c84-8725-78044c83bc9e.jpeg') }}" alt="Banner Image"
                         class="inset-0 size-60 xl:size-40 mb-4 xl:mb-0 object-cover rounded-2xl w-full">
                    <div class="xl:px-4 w-full flex flex-col gap-2">
                        <p class="font-weight-600 text-18-28 text-[#09335D]">Collaborative Work Environment</p>
                        <p class="text-base text-[#475467] font-light">We value collaboration and teamwork. Here, you will work alongside a committed, friendly, and professional team.</p>
                    </div>
                </div>

                <div class="flex flex-col xl:flex-row">
                    <img src="{{ asset_gcs('public/career/6339da1b-d53c-4cb4-8ed2-943d015b5dda.jpeg') }}" alt="Banner Image"
                         class="inset-0 size-60 xl:size-40 mb-4 xl:mb-0 object-cover rounded-2xl w-full">
                    <div class="xl:px-4 w-full flex flex-col gap-2">
                        <p class="font-weight-600 text-18-28 text-[#09335D]">Living and Working in Bali</p>
                        <p class="text-base text-[#475467] font-light">Experience the chance to work in Bali, renowned for its picturesque scenery, lively culture, and outstanding standard of living.</p>
                    </div>
                </div>

            </div>
            <div class="py-10 flex flex-col gap-10">
                <h2 class="text-30-38 font-weight-600 text-[#09335D] text-center">Discover Fulfillment and Success in Your Career Journey</h2>
                <div class="flex flex-col xl:flex-row gap-4 w-full">
                    <div class="flex gap-2 min-w-60">
                        <div id="type" class="flex flex-col gap-2 lg:gap-0">
                            <span>Type</span>
                            <div class="h-full justify-center flex gap-6 items-center">
                                <label class="cursor-pointer text-[#344054] text-base">
                                    <input type="radio" name="type" value="medic"
                                        {{ request()->type === 'medic' ? 'checked' : '' }}> Medical
                                </label>
                                <label class="cursor-pointer text-[#344054] text-base">
                                    <input type="radio" name="type" value="non-medic"
                                        {{ request()->type === 'non-medic' ? 'checked' : '' }}> Non-Medical
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col gap-2 w-full xl:w-60">
                        <label>Department</label>
                        <select id="departments-career"
                            class="text-sm xl:text-base border border-gray-200 rounded-xl h-14 xl:w-60 px-3">
                            <option value="all" class="text-sm font-[#344054]">All Department</option>
                            @foreach ($departments as $department)
                                <option value="{{ $department->uuid }}"
                                    {{ $department->uuid === request()->departmentUUID ? 'selected' : '' }}>
                                    {{ $department->name }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="w-full lg:mt-8">
                        <form class="relative " onsubmit="searchTitle(this)">
                            <input type="text" hidden name="type" value="{{ request()->type }}" id="">
                            <input type="text" hidden name="departmentUUID" value="{{ request()->departmentUUID }}"
                                id="">
                            <input type="search" name="title" value="{{ request()->title }}"
                                class="block w-full px-6 py-4 text-base text-gray-900 border border-gray-200 rounded-xl focus:outline-none"
                                placeholder="Search here" />
                            <button type="submit"
                                class="text-white absolute end-2.5 bottom-2.5 bg-[#0D4D8B] hover:bg-[#4b8dd0fe] focus:outline-none font-medium rounded-xl text-sm p-2">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M9 3.5C5.96243 3.5 3.5 5.96243 3.5 9C3.5 12.0376 5.96243 14.5 9 14.5C10.519 14.5 11.893 13.8852 12.8891 12.8891C13.8852 11.893 14.5 10.519 14.5 9C14.5 5.96243 12.0376 3.5 9 3.5ZM2 9C2 5.13401 5.13401 2 9 2C12.866 2 16 5.13401 16 9C16 10.6625 15.4197 12.1906 14.4517 13.3911L17.7803 16.7197C18.0732 17.0126 18.0732 17.4874 17.7803 17.7803C17.4874 18.0732 17.0126 18.0732 16.7197 17.7803L13.3911 14.4517C12.1906 15.4197 10.6625 16 9 16C5.13401 16 2 12.866 2 9Z"
                                        fill="white" />
                                </svg>
                            </button>
                        </form>
                    </div>
                </div>
{{--                <div class="w-full xl:hidden -mt-2">--}}
{{--                    <form class="relative" onsubmit="searchTitle(this)">--}}
{{--                        <input type="text" hidden name="type" value="{{ request()->type }}" id="">--}}
{{--                        <input type="text" hidden name="departmentUUID" value="{{ request()->departmentUUID }}"--}}
{{--                            id="">--}}
{{--                        <input type="search" name="title" value="{{ request()->title }}"--}}
{{--                            class="block w-full px-6 py-4 text-sm text-gray-900 border border-gray-300 rounded-xl focus:outline-none"--}}
{{--                            placeholder="Search here" />--}}
{{--                        <button type="submit"--}}
{{--                            class="text-white absolute end-2.5 bottom-2.5 bg-[#0D4D8B] hover:bg-[#4b8dd0fe] focus:outline-none font-medium rounded-xl text-sm px-4 py-2">--}}
{{--                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none"--}}
{{--                                xmlns="http://www.w3.org/2000/svg">--}}
{{--                                <path fill-rule="evenodd" clip-rule="evenodd"--}}
{{--                                    d="M9 3.5C5.96243 3.5 3.5 5.96243 3.5 9C3.5 12.0376 5.96243 14.5 9 14.5C10.519 14.5 11.893 13.8852 12.8891 12.8891C13.8852 11.893 14.5 10.519 14.5 9C14.5 5.96243 12.0376 3.5 9 3.5ZM2 9C2 5.13401 5.13401 2 9 2C12.866 2 16 5.13401 16 9C16 10.6625 15.4197 12.1906 14.4517 13.3911L17.7803 16.7197C18.0732 17.0126 18.0732 17.4874 17.7803 17.7803C17.4874 18.0732 17.0126 18.0732 16.7197 17.7803L13.3911 14.4517C12.1906 15.4197 10.6625 16 9 16C5.13401 16 2 12.866 2 9Z"--}}
{{--                                    fill="white" />--}}
{{--                            </svg>--}}
{{--                        </button>--}}
{{--                    </form>--}}
{{--                </div>--}}
            </div>
            <div class="border border-[#BDD8F2] min-h-sceen rounded-2xl mb-10">
                <div class="grid divide-y divide-[#BDD8F2] mx-auto ">
                    @foreach ($careers as $index => $career)
                        <div class="item">
                            <details
                                class="group @if ($index === $careers->count() - 1) rounded-b-2xl @endif @if ($index === 0) rounded-t-2xl @endif" onclick="onClickDetail('{{$career->id}}')"
                                id="patient_detail_{{$career->id}}">
                                <summary
                                    class="flex justify-between items-center font-medium cursor-pointer list-none py-4 px-4 ">
                                    <div class="flex flex-col gap-2">
                                        <p class="text-[#0D4D8B] text-lg font-semibold linguise_career_name"> {{ $career->title }}</p>
                                        <div class="flex flex-row gap-6">
                                            <div class="flex gap-2 items-center">
                                            <span>
                                                <svg width="14" height="18" viewBox="0 0 14 18" fill="none"
                                                     xmlns="http://www.w3.org/2000/svg">
                                                    <path
                                                        d="M9.5 7.75C9.5 9.13071 8.38071 10.25 7 10.25C5.61929 10.25 4.5 9.13071 4.5 7.75C4.5 6.36929 5.61929 5.25 7 5.25C8.38071 5.25 9.5 6.36929 9.5 7.75Z"
                                                        stroke="#667085" stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                    <path
                                                        d="M13.25 7.75C13.25 13.7018 7 17.125 7 17.125C7 17.125 0.75 13.7018 0.75 7.75C0.75 4.29822 3.54822 1.5 7 1.5C10.4518 1.5 13.25 4.29822 13.25 7.75Z"
                                                        stroke="#667085" stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                </svg>
                                            </span>
                                                <p class="text-[#667085] text-sm font-weight-400 linguise_career_location">{{ $career->location }}</p>
                                            </div>
                                            <div class="flex gap-2 items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                  <path fill-rule="evenodd" clip-rule="evenodd" d="M10 3.125C6.20304 3.125 3.125 6.20304 3.125 10C3.125 13.797 6.20304 16.875 10 16.875C13.797 16.875 16.875 13.797 16.875 10C16.875 6.20304 13.797 3.125 10 3.125ZM1.875 10C1.875 5.51269 5.51269 1.875 10 1.875C14.4873 1.875 18.125 5.51269 18.125 10C18.125 14.4873 14.4873 18.125 10 18.125C5.51269 18.125 1.875 14.4873 1.875 10ZM10 4.375C10.3452 4.375 10.625 4.65482 10.625 5V9.375H13.75C14.0952 9.375 14.375 9.65482 14.375 10C14.375 10.3452 14.0952 10.625 13.75 10.625H10C9.65482 10.625 9.375 10.3452 9.375 10V5C9.375 4.65482 9.65482 4.375 10 4.375Z" fill="#667085"/>
                                                </svg>
                                                <p class="text-[#667085] text-sm font-weight-400">{{ \Carbon\Carbon::parse($career->created_at)->format('d M Y') }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <span class="transition group-open:rotate-90">
                                        <svg width="10" height="18" viewBox="0 0 10 18" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path d="M1.25 1.5L8.75 9L1.25 16.5" stroke="#0D4D8B" stroke-width="1.5"
                                                stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                    </span>
                                </summary>
                                <div
                                    class="text-neutral-600 group-open:animate-fadeIn bg-white px-4 py-2 @if ($index === $careers->count() - 1) rounded-b-2xl @endif ql-content">
                                    {!! $career->content !!}

                                    <a href="{{ $career->link }}" target="_blank" rel="noopener noreferrer"
                                        class="my-4 w-full flex items-center">
                                        <div
                                            class="border px-6 py-3 border-[#0B4074] rounded-xl text-[#0B4074] hover:text-white hover:bg-[#0B4074]">
                                            Apply Now
                                        </div>
                                    </a>
                                </div>
                            </details>
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="mt-4">
                {{ $careers->links() }}
            </div>
        </div>
        <div class="bg-warning-90 xl:h-56 xl:my-10 rounded-2xl flex flex-col py-6 md:flex-row items-center px-8 gap-2 xl:gap-6 yx">
            <img src="{{ asset('assets/career/warning.png') }}" alt="Banner Image"
                 class="object-cover rounded-2xl size-28 xl:size-44">
            <div class="flex flex-col gap-3 xl:py-0">
                <p class="font-weight-600 xl:text-30-38 text-24-32 text-warning-10">Beware of Recruitment Scams!</p>
                <p class="text-warning-10 font-weight-400 xl:text-16-24 text-14-20">It has come to our attention that fake job offers are being made, claiming to be from Bali International Hospital. Please be aware that Bali International Hospital does not require any payment from applicants seeking employment with us.</p>
            </div>
        </div>
    </div>
@endsection

@push('script')
    <script>
        function updateURLParams(params) {
            let searchParams = new URLSearchParams(window.location.search);
            for (const [key, value] of Object.entries(params)) {
                if (value === null || value === undefined || value === '') {
                    searchParams.delete(key);
                } else {
                    searchParams.set(key, value);
                }
            }
            window.location.href = window.location.pathname + '?' + searchParams.toString();
        }

        document.getElementById("departments-career").addEventListener("change", function() {
            var departmentUUID = this.value;
            updateURLParams({
                departmentUUID
            });
        });

        $('#type input[type="radio"]').change(function() {
            var departmentType = $(this).val();
            updateURLParams({
                'type': departmentType
            });
        });

        function searchTitle(params) {
            var careerTitle = params;
            updateURLParams({
                'title': careerTitle
            });
        }

        var careers = @json($careers);


        function onClickDetail(id){
            var element = document.getElementById('patient_detail_' + id);
            if (element) {
                // or if you want to add a class:
                // element.classList.add('bg-[#E5F2FF80]');
                element.classList.toggle('bg-[#E5F2FF80]');
            }
            // iterate over the careers
            careers.data.forEach(function(career) {
                var otherElement = document.getElementById('patient_detail_' + career.id);
                if(id !== career.id){
                    otherElement.classList.remove('bg-[#E5F2FF80]');
                }
            });
        }
    </script>
@endpush
