@section('title', 'Bali International Hospital')
@extends("landing-page.layouts.landingPageTemplate")

<style>
    /* Hide the spinners */
    .no-spinners::-webkit-inner-spin-button,
    .no-spinners::-webkit-outer-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }

    /* Firefox */
    .no-spinners {
        -moz-appearance: textfield;
    }
</style>

@section("content")
    <div class="mx-4 md:mx-14 my-16 z-10 h-screen md:min-h-[785px]">
        <div class="grid grid-cols-1 md:grid-cols-2">
            <div class="z-10 hidden md:block">
                <div class="w-60 h-[93px]">
                    <img src="{{ asset('assets/svg/bih-logo.png') }}" alt="">
                </div>
                <div class="mt-[3.087rem] ml-4">
                    <h1 class="text-30-38">Welcome to</h1>
                    <h1 class="text-30-38">Bali International Hospital</h1>
                    <p class="text-typo-grey">Caring for You, every step of the way.</p>
                </div>
            </div>

            {{-- This is section for OTP diplay on login.public page --}}
            <div id="OTPForm" class="z-10">
                <form action="{{ route('login.public.otp-verification.store') }}" method="POST" id="otp-form">
                    @csrf
                    <input type="hidden" name="referenceId" value="{{ $reference_id }}">
                    <input type="hidden" name="otpId" value="{{ $otp_id }}">
                    <div class="md:w-599 md:h-618 py-8 md:px-20 flex flex-col gap-6 md:gap-12 bg-white rounded-3xl">
                        <div class="mt-7 flex flex-col gap-2 items-center md:items-start">
                            <span><b class="text-2xl md:text-3xl">OTP Verification</b></span>
                            <span class="text-typo-grey text-sm md:text-base font-light text-center md:text-start" id="used-contact">Enter the verification code we just sent to your {{ $reference_contact_type == \App\Enums\Table\Otp\Method::PHONE_NUMBER ? "phone number" : "email" }} {{ $reference_contact }}.</span>
                        </div>

                        <div class="flex flex-col gap-7">
                            <div class="flex space-x-3 justify-center" data-hs-pin-input="">
                                <input type="number" name="otp[]" id="otp1" class="border rounded-lg w-10 md:w-16 h-10 md:h-16 text-center text-xl md:text-3xl text-primary-blue-60 font-weight-500 p-1 md:py-4 md:px-4 no-spinners" data-hs-pin-input-item="" autofocus="">
                                <input type="number" name="otp[]" id="otp2" class="border rounded-lg w-10 md:w-16 h-10 md:h-16 text-center text-xl md:text-3xl text-primary-blue-60 font-weight-500 p-1 md:py-4 md:px-4 no-spinners" data-hs-pin-input-item="">
                                <input type="number" name="otp[]" id="otp3" class="border rounded-lg w-10 md:w-16 h-10 md:h-16 text-center text-xl md:text-3xl text-primary-blue-60 font-weight-500 p-1 md:py-4 md:px-4 no-spinners" data-hs-pin-input-item="">
                                <input type="number" name="otp[]" id="otp4" class="border rounded-lg w-10 md:w-16 h-10 md:h-16 text-center text-xl md:text-3xl text-primary-blue-60 font-weight-500 p-1 md:py-4 md:px-4 no-spinners" data-hs-pin-input-item="">
                                <input type="number" name="otp[]" id="otp5" class="border rounded-lg w-10 md:w-16 h-10 md:h-16 text-center text-xl md:text-3xl text-primary-blue-60 font-weight-500 p-1 md:py-4 md:px-4 no-spinners" data-hs-pin-input-item="">
                                <input type="number" name="otp[]" id="otp6" class="border rounded-lg w-10 md:w-16 h-10 md:h-16 text-center text-xl md:text-3xl text-primary-blue-60 font-weight-500 p-1 md:py-4 md:px-4 no-spinners" data-hs-pin-input-item="">
                            </div>
                            <div class="text-center">
                                <p id="countingDown" class="text-brand-light"></p>
                                <p id="attemptsLeft" class="text-typo-grey mt-2"></p>
                                <div id="sending-state" class="w-full items-center justify-center hidden">
                                    @include('landing-page.component.spinner_with_text', ['utility' => 'bg-transparent flex flex-row gap-1 items-center text-brand','text'=>'Sending...'])
                                </div>
                            </div>
                        </div>

                        <div class="flex flex-col gap-6 text-center px-4 md:px-0">
                            <button type="submit" class="w-full py-3 md:w-456 md:h-14 rounded-xl bg-brand-light text-white" disabled id="submit-button">Verify</button>
                            <div href="#" class="w-full md:w-[28.5rem] h-[3.5rem] rounded-lg border px-4 items-center flex hidden" id="loading-button">
                                @include('landing-page.component.spinner_with_text', ['utility' => 'bg-transparent flex flex-row gap-1 items-center text-brand','text'=>'Verifying...'])
                            </div>
                            @if ($reference_contact_type == \App\Enums\Table\Otp\Method::PHONE_NUMBER)
                                <p class="text-brand">Didn't get OTP? <a href="{{ route('login.public.email') }}" class="text-brand w-48 text-sm mx-auto font-weight-600 cursor-pointer">Change to Email</a></p>
                            @else
                                <p class="text-brand">Didn't get OTP? <a href="{{ route('login.public.phone') }}" class="text-brand w-48 text-sm mx-auto font-weight-600 cursor-pointer">Change to Phone Number</a></p>
                            @endif
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="z-0 fixed bottom-0 left-0 hidden md:block">
        <img src="{{ asset('assets/svg/gradient-green.svg') }}" alt="">
    </div>
    <div class="z-0 absolute top-0 right-0 hidden md:block">
        <img src="{{ asset('assets/svg/gradient-blue.svg') }}" alt="">
    </div>
@endsection
@push('script')
    <script>
        $(document).ready(function (){
            // For QA Test
            console.log('OTP', "{{ $otp_value }}")

            const token = $('meta[name="csrf-token"]').attr('content');
            const referenceId = "{{ $reference_id }}"
            const otpType = "{{ $otp_type }}";
            const platform = "{{ $platform }}";
            const contact = "{{ $reference_contact }}";
            const contactType = "{{ $reference_contact_type }}";
            const otpAbleType = "{{ $otpable_type }}";

            // Set the date we're counting down to
            const countingDown = $('#countingDown');
            const attemptsLeft = $('#attemptsLeft');
            const attemptsLeftCount = "{{ $attempts_left }}"

            let otpDuration = attemptsLeftCount > 0 ? "{{ $otp_duration }}" : "{{ $otp_retry_duration }}";
            let countDownDate = new Date(otpDuration).getTime();

            // Update the count-down every 1 second
            let x = setInterval(function() {

                // Get today's date and time
                let dateNow = new Date().toLocaleString("en-US", {timeZone:"{{ config('app.timezone') }}"});

                let timeNow = new Date(dateNow).getTime();

                // Find the distance between now and the count-down date
                let distance = countDownDate - timeNow;

                // Time calculations for minutes and seconds
                let minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                let seconds = Math.floor((distance % (1000 * 60)) / 1000);

                // Display the result in the element
                countingDown.text("Resend Code in 0" + minutes + " : " + seconds);
                if (attemptsLeftCount > 0) {
                    attemptsLeft.text("You have " + attemptsLeftCount + " attempts left to resend code");
                } else {
                    attemptsLeft.text("You have reached the limit to resend code. Please wait for 10 minutes to resend code.");
                }

                // If the count-down is finished, write some text
                if (distance < 0) {
                    clearInterval(x);

                    if (attemptsLeftCount <= 0) {
                        //ajax
                        $.ajax({
                            url: "{{ route('login.public.otp-reset-attempts.store') }}",
                            type: "POST",
                            cache: false,
                            data: {
                                "referenceId": referenceId,
                                "_token": token
                            },
                            success: function (response) {
                                if (response.success) {
                                    location.reload()
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        text: 'Reset OTP was failed',
                                    });
                                }
                            },
                            error: function (error) {
                                console.log(error)
                            }
                        });
                    }

                    countingDown.removeClass("text-brand-light");
                    countingDown.addClass("text-brand cursor-pointer");
                    countingDown.text("Resend Code");
                    attemptsLeft.hide();
                }
            }, 1000);

            countingDown.on('click', function () {
                const sendingState = $('#sending-state').removeClass("hidden").addClass("flex");
                countingDown.addClass("hidden");
                //ajax
                $.ajax({
                    url: "{{ route('login.public.send-otp') }}",
                    type: "POST",
                    cache: false,
                    data: {
                        "referenceId": referenceId,
                        "otpAbleType": otpAbleType,
                        "contact": contact,
                        "contactType": contactType,
                        "otpType": otpType,
                        "platform": platform,
                        "_token": token
                    },
                    success: function (response) {
                        if (response.success) {
                            location.reload()
                        } else {
                            Swal.fire({
                                icon: 'error',
                                text: 'Send OTP was failed',
                            });
                        }
                    },
                    error: function (error) {
                        console.log(error)
                    }
                });
            });

            $('#otp6').on('keyup', function (){
                const otp1 = $('#otp1').val();
                const otp2 = $('#otp2').val();
                const otp3 = $('#otp3').val();
                const otp4 = $('#otp4').val();
                const otp5 = $('#otp5').val();
                const otp6 = $('#otp6').val();
                if (otp1 !== "" && otp2 !== "" && otp2 !== "" && otp3 !== "" && otp4 !== "" && otp5 !== "" && otp6 !== "") {
                    $('#otp-form button[type=submit]').prop('disabled', false).removeClass('bg-brand-light').addClass('bg-brand');
                }
            }).on('keydown', function (e){
                if (e.which === 13) {
                    $('#otp-form').submit();
                }
            });
        });

        document.getElementById('otp-form').addEventListener('submit', function() {
            $('#loading-button').removeClass('hidden');
            $('#submit-button').addClass('hidden');
        });

    </script>
@endpush
