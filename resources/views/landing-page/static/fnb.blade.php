@section('title', 'Bali International Hospital')
@section('seo_meta')
    <link rel="canonical" href="https://bih.id/shops-services" />
    <link rel="alternate" hreflang="en-id" href="https://bih.id/shops-services" />
@endsection
@extends('landing-page.layouts.masterTemplate')

@include('landing-page.partials.bg_move_style')

@section('content')
    <div>
        <div class="movingColors hidden lg:block">
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
        </div>

        <div class="movingColors2 hidden lg:block">
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
        </div>
        <div class="relative">
            <div class="relative z-20">
                <div id="banner" class="relative">
                    <img class="w-full h-[460px] object-cover"
                        src="https://images.unsplash.com/photo-1611688370466-0e415a5c922e?q=80&w=2940&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
                        alt="Bali International Hospital">
                    <div
                        class="mx-4 lg:mx-40 absolute inset-x-0 bottom-0 flex flex-col justify-between text-white text-4xl font-semibold mb-10 gap-2">
                        <span class="tex-sm lg:text-[60px] font-semibold">Shops & Services at Our Hospital
                        </span>
                    </div>
                </div>

                <div id="breadcrumb" class="">
                    <div class="relative z-1">
                        <div class="flex flex-row mx-4 lg:px-40 py-4 gap-4 items-center text-sm lg:text-base font-light">
                            <a href="/" class="text-blue-800">Home</a>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                fill="none">
                                <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3"
                                    stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <span> Site Services </span>
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"
                                fill="none">
                                <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3"
                                    stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <span> Shops & Services at Our Hospital </span>
                        </div>
                    </div>
                </div>

            </div>

            <div class="mx-4 lg:px-40 flex flex-col gap-4 my-8 text-base text-[#344054] font-weight-400">
                <span>Enjoy added comfort during your visit with our on-site shops and services. From fresh flowers and
                    daily essentials to delightful treats, our retail partners are here to brighten your day and make your
                    time at the hospital more convenient, thoughtful, and welcoming — for you and your loved ones.</span>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 my-10 lg:my-[120px] mx-4 lg:px-40 gap-6">
                @foreach ($fnbs as $fnb)
                    <div
                        class="flex flex-col shadow-lg rounded-3xl overflow-hidden bg-white transition duration-300 hover:shadow-2xl">
                        {{-- Gambar --}}
                        <div class="h-[224px]">
                            <img class="w-full h-full object-cover" src="{{ $fnb['image'] }}" alt="{{ $fnb['name'] }}">
                        </div>

                        {{-- Konten --}}
                        <div class="py-5 px-4 flex flex-col gap-3">
                            {{-- Nama --}}
                            <span class="text-xl font-semibold text-[#1D2939]">{{ $fnb['name'] }}</span>

                            {{-- Deskripsi --}}
                            <span class="text-sm text-[#667085]">{{ $fnb['desc'] }}</span>

                            {{-- Lokasi --}}
                            @if (!empty($fnb['location']))
                                <div class="flex items-start gap-2 text-sm text-[#475467]">
                                    <svg class="w-5 h-5 text-[#0D4D8B]" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M10 2a6 6 0 016 6c0 4.418-6 10-6 10S4 12.418 4 8a6 6 0 016-6zm0 8a2 2 0 100-4 2 2 0 000 4z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    <span>{{ $fnb['location'] }}</span>
                                </div>
                            @endif

                            {{-- Jam Operasional --}}
                            @if (!empty($fnb['open_hours']))
                                <div class="flex items-start gap-2 text-sm text-[#475467]">
                                    <svg class="w-5 h-5 text-[#0D4D8B]" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd"
                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm.75-12a.75.75 0 00-1.5 0v4.25c0 .414.336.75.75.75h3a.75.75 0 000-1.5H10.75V6z"
                                            clip-rule="evenodd" />
                                    </svg>
                                    <span>{{ $fnb['open_hours'] }}</span>
                                </div>
                            @endif

                            {{-- Visit Website --}}
                            @if ($fnb['url'] != '#')
                                <a href="{{ $fnb['url'] }}" target="_blank"
                                    class="flex items-center gap-2 mt-2 text-base text-[#475467] hover:text-[#0D4D8B] transition">
                                    <span>Visit Website</span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#0D4D8B"
                                        viewBox="0 0 20 20">
                                        <path
                                            d="M12.293 2.293a1 1 0 011.414 0L18 6.586V4a1 1 0 112 0v7a1 1 0 01-1 1h-7a1 1 0 110-2h2.586L12.293 3.707a1 1 0 010-1.414zM2 5a2 2 0 012-2h6a1 1 0 010 2H4v10h10v-6a1 1 0 112 0v6a2 2 0 01-2 2H4a2 2 0 01-2-2V5z" />
                                    </svg>
                                </a>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>

        </div>
        <div class="z-10 -mb-10 mt-6">
            @include('landing-page.partials.homepage.contact')
        </div>
    </div>



@endsection
