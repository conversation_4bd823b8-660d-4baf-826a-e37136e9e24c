@section('title', 'Bali International Hospital')
@section('seo_meta')
    <link rel="canonical" href="https://bih.id/contact-us">
    <link rel="alternate" hreflang="en-id" href="https://bih.id/contact-us">
@endsection
@extends('landing-page.layouts.masterTemplate')

<style>
    .modal {
        display: none;
        position: fixed;
        z-index: 999;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.7);
        justify-content: center;
        align-items: center;
        transition: opacity 0.3s ease;
    }

    /* Aktifkan flex saat modal muncul */
    .modal.show {
        display: flex;
    }

    /* Konten modal */
    .modal-content {
        background: white;
        padding: 30px;
        border-radius: 12px;
        max-width: 600px;
        width: 90%;
        position: relative;
        animation: scaleIn 0.3s ease;
    }

    /* Tombol close berbentuk bulat */
    .close {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 30px;
        height: 30px;
        background-color: #eee;
        color: #333;
        border-radius: 50%;
        text-align: center;
        line-height: 30px;
        font-size: 20px;
        font-weight: bold;
        cursor: pointer;
        transition: background-color 0.3s ease;
    }

    .close:hover {
        background-color: #ddd;
    }

    /* Animasi masuk */
    @keyframes scaleIn {
        from {
            transform: scale(0.9);
            opacity: 0;
        }

        to {
            transform: scale(1);
            opacity: 1;
        }
    }

    body {
        margin: 0;
    }

    .movingColors {
        /*width: 100vw;*/
        /*height: 100vh;*/
        background: white;
    }

    .movingColors p {
        width: 400px;
        height: 400px;
        border-radius: 400px;
        backface-visibility: hidden;
        position: absolute;
        animation-name: move;
        animation-duration: 6s;
        animation-timing-function: linear;
        animation-iteration-count: infinite;
    }

    .movingColors p:nth-child(1) {
        color: #c9e1ff;
        left: 0;
        animation-duration: 13.7s;
        animation-delay: -14s;
        transform-origin: -16vw 2vh;
        box-shadow: -800px 0 214.0438363536px currentColor;
    }

    .movingColors p:nth-child(2) {
        color: #e7ffd5;
        left: 0;
        animation-duration: 13s;
        animation-delay: -14.3s;
        transform-origin: 25vw -7vh;
        box-shadow: 800px 0 135.6046803315px currentColor;
    }

    .movingColors p:nth-child(3) {
        color: #e7ffd5;
        left: 0;
        animation-duration: 14.1s;
        animation-delay: -10.2s;
        transform-origin: -16vw -17vh;
        box-shadow: -800px 0 183.0183599821px currentColor;
    }

    .movingColors p:nth-child(4) {
        color: #fffadf;
        left: 0;
        animation-duration: 12.6s;
        animation-delay: -10.9s;
        transform-origin: -20vw -8vh;
        box-shadow: 800px 0 245.4538106578px currentColor;
    }

    @keyframes move {
        100% {
            transform: translate3d(0, 0, 1px) rotate(360deg);
        }
    }
</style>


@section('content')
    <div class="xl:mx-40 flex flex-col mx-4">
        <div class="xl:h-32 h-24">
        </div>
        <div class="movingColors hidden lg:block">
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
            <p></p>
        </div>

        <div class="flex flex-row gap-4 items-center py-4 z-10">
            <a href="/" class="text-primary-blue-60">Home</a>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5"
                    stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <span> Contact Us </span>
        </div>
        {{-- Location & Sitemap --}}
        <div class="flex flex-col gap-10 justify-center items-center pt-6 xl:pb-10 z-10">
            <h1 class="text-2xl xl:text-4xl font-weight-600 text-h1-homepage">Contact Bali International Hospital</h1>
            <div
                class="flex flex-col gap-3 xl:gap-0 xl:flex-row h-[20rem] xl:h-[38rem] w-full xl:rounded-3xl xl:shadow-2xl xl:shadow-right xl:shadow-bottom">
                <div class="flex flex-col w-full order-2 xl:order-1 h-1/2 xl:h-full xl:w-1/2">
                    <div class="map h-full relative">
                        <iframe class="absolute xl:inset-0 w-full h-full xl:rounded-tl-3xl"
                            src="https://www.google.com/maps/embed/v1/place?q=Proyek+PT+PP+Bali+Internasional+Hospital,+Jalan+Bypass+Ngurah+Rai,+Sanur,+Denpasar+City,+Bali,+Indonesia&key=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8"></iframe>
                    </div>
                    <div class="direction bottom-0 xl:h-[9.625rem] bg-neutral-40/70 w-full xl:rounded-bl-3xl px-6 py-4">
                        <h2 class="text-24-32 text-white font-weight-600 mb-2">Bali International Hospital</h2>
                        <div class="grid grid-cols-1 xl:grid-cols-2 gap-2 pb-1">
                            <div class="address text-16-24 text-white font-weight-400">
                                Jl. Bypass Ngurah Rai, Sanur, <br />
                                Denpasar Selatan, Kota <br />
                                Denpasar, Bali
                            </div>
                            <div class="direction-button content-end overflow-hidden">
                                <a href="https://www.google.com/maps/dir//Proyek+PT+PP+Bali+Internasional+Hospital+87C5%2BPH6+Jl.+Bypass+Ngurah+Rai+Sanur,+Denpasar+Selatan,+Kota+Denpasar,+Bali/@-8.6782017,115.2588967,16z/data=!4m8!4m7!1m0!1m5!1m1!1s0x2dd241f5fdbf70cd:0xbbeeb3b4709906ba!2m2!1d115.2588183!2d-8.6782013?entry=ttu"
                                    target="_blank"
                                    class="bg-white rounded-lg flex py-3 w-[11.625rem] justify-center items-center gap-3 xl:float-right">
                                    <div class="text-16-24 text-primary-blue-30 font-weight-600">Get Direction</div>
                                    <img class="" src="{{ asset('assets/common/arrow-up-right.svg') }}"
                                        alt="whatsapp">
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <div
                    class="xl:w-1/2 order-1 xl:order-2 xl:py-24 xl:rounded-r-3xl justify-center items-center flex flex-col gap-6 xl:px-16">
                    {{-- <div class="flex justify-between w-full gap-5"> --}}
                    {{-- <a href="https://wa.me/{{ @$whatsappNumber->value }}"  target="_blank"
                           class="bg-white rounded-xl flex py-3 w-full justify-center items-center shadow gap-4">
                            <img class="" src="{{ asset('assets/common/whatsapp.svg') }}" alt="whatsapp">
                            <div>WhatsApp</div>
                        </a> --}}
                    {{--                        <a href="https://wa.me/{{ @$whatsappNumber->value }}"  target="_blank" --}}
                    {{--                           class="bg-white rounded-xl flex py-3 w-1/2 justify-center items-center shadow gap-4"> --}}
                    {{--                            <img class="" src="{{ asset('assets/common/whatsapp.svg') }}" alt="whatsapp"> --}}
                    {{--                            <div>WhatsApp</div> --}}
                    {{--                        </a> --}}
                    {{--                        <a href="tel:{{ @$callCenter->value }}" class="bg-white rounded-xl flex py-3 w-1/2 justify-center items-center shadow gap-4"> --}}
                    {{--                            <img class="" src="{{ asset('assets/common/phone.svg') }}" alt="phone"> --}}
                    {{--                            <div> Call BIH {{ @$callCenter->value }} </div> --}}
                    {{--                        </a> --}}
                    {{-- </div> --}}
                    {{--                    <button class="bg-white rounded-xl flex py-3 w-full justify-center items-center shadow gap-4"> --}}
                    {{--                        <img class="" src="{{ asset('assets/common/emergency-phone.svg') }}" alt="phone"> --}}
                    {{--                        <div> Emergency {{ @$emergencyCall->value }} </div> --}}
                    {{--                    </button> --}}
                    <a href="mailto:{{ @$email->value }}"
                        class="bg-white rounded-xl flex py-3 w-full justify-center items-center shadow gap-4">
                        <img class="" src="{{ asset('assets/common/envelope.svg') }}" alt="phone">
                        <div> {{ @$email->value }} </div>
                    </a>
                    <a href="tel:{{ @$callCenter->value }}"
                        class="bg-white rounded-xl flex py-3 w-full justify-center items-center shadow gap-4">
                        <img class="" src="{{ asset('assets/common/phone.svg') }}" alt="phone">
                        <div> Call Us {{ @$callCenter->value }} </div>
                    </a>
                    <a href="https://wa.me/{{ @$whatsappNumber->value }}" target="_blank"
                        class="bg-white rounded-xl flex py-3 w-full justify-center items-center shadow gap-4">
                        <img class="" src="{{ asset('assets/common/whatsapp.svg') }}" alt="phone">
                        <div> {{ @$whatsappNumber->value }} </div>
                    </a>
                    <h5 class="fw-bolder" style="font-weight: bold;">Transportation Partner</h5>
                    <button onclick="openModal()">
                        <img src="https://storage.googleapis.com/web-bih-bh-devel/518e1d68-bd32-40b2-bdae-efa78837286e.png"
                            style="max-width: 220px;height:auto;">
                    </button>

                    <div id="myModal" class="modal">
                        <div class="modal-content" style="margin-top: 25px;">
                            <span class="close" onclick="closeModal()">&times;</span>
                            <img src="https://storage.googleapis.com/web-bih-bh-devel/21a78fff-2393-4e6e-ba28-2a92edb3a8b9.jpg"
                                style="width: 100%;height:auto;">
                        </div>
                    </div>


                </div>
            </div>
        </div>

    </div>
    <br><br><br>
    <script>
        function openModal() {
            document.getElementById("myModal").classList.add("show");
        }

        function closeModal() {
            document.getElementById("myModal").classList.remove("show");
        }

        window.onclick = function(event) {
            const modal = document.getElementById("myModal");
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>

@endsection
