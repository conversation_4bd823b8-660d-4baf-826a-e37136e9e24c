@section('title', 'Bali International Hospital')
@section('seo_meta')
    <link rel="canonical" href="https://bih.id/ihc-hospitals">
    <link rel="alternate" hreflang="en-id" href="https://bih.id/ihc-hospitals">
@endsection
@extends('landing-page.layouts.masterTemplate')

@section('content')
    <div class="xl:mx-40 flex flex-col mx-4">
        {{-- BANNER --}}
        <div class="relative xl:-mx-40 -mx-4">
            <img src="https://storage.googleapis.com/web-bih-bh-devel/public/assets/about-us/904e33a8-81e0-4af0-bc3b-86c4397213cd.webp" alt="Banner Image"
                 class="inset-0 w-full object-cover h-64 xl:h-max"
                 style="background: radial-gradient(77.36% 77.36% at 45.71% 50%, rgba(7, 39, 70, 0.33) 0%, rgba(7, 39, 70, 0.72) 100%);">
            {{-- <img src="{{ asset('assets/ihcProfile/ihcProfileBanner.png') }}" alt="Banner Image"
                 class="inset-0 w-full object-cover h-64 xl:h-max"
                 style="background: radial-gradient(77.36% 77.36% at 45.71% 50%, rgba(7, 39, 70, 0.33) 0%, rgba(7, 39, 70, 0.72) 100%);"> --}}
            <div class="absolute inset-0"
                 style="background: radial-gradient(77.36% 77.36% at 45.71% 50%, rgba(7, 39, 70, 0.33) 0%, rgba(7, 39, 70, 0.72) 100%);">
            </div>
            <div class="absolute inset-0 flex flex-col justify-end xl:mx-40 mx-4 mb-11">
                <h1 class="text-white xl:text-6xl text-4xl font-weight-600">IHC Hospitals</h1>
            </div>
        </div>
        {{-- BREADCRUMB --}}
        <div class="flex flex-row flex-wrap gap-4 items-center py-4">
            <a href="/" class="text-primary-blue-60">Home</a>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5"
                      stroke-linecap="round" stroke-linejoin="round" />
            </svg>
            <span class="text-base font-light text-[#667085]"> IHC Hospitals </span>
        </div>
        <div class="flex flex-col gap-10 pt-5 pb-10">
            <span>Indonesia Healthcare Corporation (IHC) is one of Indonesia's largest hospital chains with an established network. It is a major state-owned hospital chain in Indonesia, including 37 hospitals and 68 clinics.</span>
            <div class="flex flex-col md:flex-row gap-4 justify-between">
                <div class="hs-dropdown relative inline-flex z-10 w-full lg:w-1/2">
                    <button id="hs-dropdown-default" type="button" class="hs-dropdown-toggle w-full items-center rounded-2xl border border-gray-200 py-2 pl-6 pr-2 flex flex-row justify-between bg-white focus-within:border-blue-500 focus-within:ring focus-within:ring-blue-200">
                        <span class="text-base font-light text-[#98A2B3] {{ request('location') ? 'text-black' : '' }}">{{ request('location') ? request('location') : 'Select Hospital Location'  }}</span>
                        <svg class="hs-dropdown-open:rotate-180 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m6 9 6 6 6-6"/></svg>
                    </button>

                    <div class="hs-dropdown-menu transition-[opacity,margin] duration hs-dropdown-open:opacity-100 opacity-0 hidden min-w-60 bg-white shadow-md rounded-lg p-2 mt-2 after:h-4 after:absolute after:-bottom-4 after:start-0 after:w-full before:h-4 before:absolute before:-top-4 before:start-0 before:w-full overflow-scroll h-96 w-1/3" aria-labelledby="hs-dropdown-default">
                        <span onclick="onClickLocation('')" class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-100" href="#">
                            --- All ---
                        </span>
                        @foreach($locations as $key => $location)
                            <span onclick="onClickLocation('{{$key}}')" class="flex items-center gap-x-3.5 py-2 px-3 rounded-lg text-sm text-gray-800 hover:bg-gray-50 focus:outline-none focus:bg-gray-100" href="#">
                                {{ $key }}
                            </span>
                        @endforeach
                    </div>
                </div>
                <div class="w-full lg:w-1/2">
                    <form onsubmit="onClickSearch(); return false;" class="w-full items-center rounded-2xl border border-gray-200 py-2 pl-6 pr-2 flex flex-row justify-between bg-white focus-within:border-blue-500 focus-within:ring focus-within:ring-blue-200">
                        <input type="text" class="w-full outline-none bg-transparent text-base font-light text-black" placeholder="Search Hospital Name" name="keyword" id="searchInput" value="{{ request('keyword') }}">
                        <div onclick="onClickSearch()" class="flex rounded-xl bg-blue-900 py-2 px-3 text-white text-sm gap-2 items-center hover:cursor-pointer">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M9 3.5C5.96243 3.5 3.5 5.96243 3.5 9C3.5 12.0376 5.96243 14.5 9 14.5C10.519 14.5 11.893 13.8852 12.8891 12.8891C13.8852 11.893 14.5 10.519 14.5 9C14.5 5.96243 12.0376 3.5 9 3.5ZM2 9C2 5.13401 5.13401 2 9 2C12.866 2 16 5.13401 16 9C16 10.6625 15.4197 12.1906 14.4517 13.3911L17.7803 16.7197C18.0732 17.0126 18.0732 17.4874 17.7803 17.7803C17.4874 18.0732 17.0126 18.0732 16.7197 17.7803L13.3911 14.4517C12.1906 15.4197 10.6625 16 9 16C5.13401 16 2 12.866 2 9Z" fill="white"></path>
                            </svg>
                            <span class="text-sm font-light">Search</span>
                        </div>
                    </form>
                </div>
            </div>
            <div class="">
                @foreach($hospitals as $key => $hospital)
                    <div class="flex flex-col gap-8">
                        <span class="text-center text-2xl font-weight-600 text-brand mt-10">{{ucwords($key)}}</span>
                        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                            @foreach($hospital as $subHospital)
                                <a href="{{$subHospital['link']}}" target="_blank" class="border border-[#BDD8F2] rounded-xl bg-[#E5F2FF33] p-6 gap-4 flex flex-col hover:bg-blue-50 hover:cursor-pointer">
                                    <span class="text-xl font-weight-600 text-[#1D2939]"> {{ $subHospital['name'] }}</span>
                                    <div class="flex flex-col gap-2">
                                        <div class="flex flex-row gap-2">
                                            <svg width="20" height="20" viewBox="0 0 12 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M6 1.5C3.51472 1.5 1.5 3.51472 1.5 6C1.5 8.19732 2.65378 9.95354 3.8577 11.1862C4.4565 11.7993 5.05673 12.2711 5.50724 12.5893C5.7059 12.7297 5.87457 12.8395 6 12.9179C6.12543 12.8395 6.2941 12.7297 6.49276 12.5893C6.94327 12.2711 7.5435 11.7993 8.1423 11.1862C9.34622 9.95354 10.5 8.19732 10.5 6C10.5 3.51472 8.48528 1.5 6 1.5ZM6 13.5C5.75981 13.9385 5.75963 13.9384 5.75944 13.9383L5.75764 13.9373L5.75365 13.9351L5.74034 13.9277C5.72913 13.9214 5.7133 13.9124 5.69316 13.9007C5.6529 13.8774 5.59535 13.8433 5.523 13.7988C5.37836 13.7097 5.17417 13.5784 4.93026 13.4061C4.44327 13.0621 3.7935 12.5516 3.1423 11.8849C1.84622 10.5579 0.5 8.56411 0.5 6C0.5 2.96243 2.96243 0.5 6 0.5C9.03757 0.5 11.5 2.96243 11.5 6C11.5 8.56411 10.1538 10.5579 8.8577 11.8849C8.2065 12.5516 7.55673 13.0621 7.06974 13.4061C6.82583 13.5784 6.62164 13.7097 6.477 13.7988C6.40465 13.8433 6.34711 13.8774 6.30684 13.9007C6.2867 13.9124 6.27087 13.9214 6.25966 13.9277L6.24635 13.9351L6.24236 13.9373L6.24105 13.9381C6.24085 13.9382 6.24019 13.9385 6 13.5ZM6 13.5L6.24019 13.9385C6.09055 14.0205 5.90908 14.0203 5.75944 13.9383L6 13.5ZM6 4.5C5.17157 4.5 4.5 5.17157 4.5 6C4.5 6.82843 5.17157 7.5 6 7.5C6.82843 7.5 7.5 6.82843 7.5 6C7.5 5.17157 6.82843 4.5 6 4.5ZM3.5 6C3.5 4.61929 4.61929 3.5 6 3.5C7.38071 3.5 8.5 4.61929 8.5 6C8.5 7.38071 7.38071 8.5 6 8.5C4.61929 8.5 3.5 7.38071 3.5 6Z" fill="#667085"/>
                                            </svg>
                                            <span class="text-sm text-[#667085] font-light">{{$subHospital['address']}}</span>
                                        </div>
                                        <div class="flex flex-row gap-2">
                                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M0 2C0 0.895431 0.895431 0 2 0H2.91442C3.488 0 3.98798 0.390372 4.1271 0.94683L4.86429 3.89562C4.98627 4.38353 4.80396 4.89703 4.40162 5.19879L3.5392 5.8456C3.44965 5.91277 3.43018 6.01112 3.45533 6.07979C4.21234 8.14632 5.85368 9.78766 7.92021 10.5447C7.98888 10.5698 8.08724 10.5504 8.1544 10.4608L8.80121 9.59839C9.10297 9.19604 9.61647 9.01373 10.1044 9.13571L13.0532 9.8729C13.6096 10.012 14 10.512 14 11.0856V12C14 13.1046 13.1046 14 12 14H10.5C4.70101 14 0 9.29899 0 3.5V2ZM2 1C1.44772 1 1 1.44772 1 2V3.5C1 8.74671 5.25329 13 10.5 13H12C12.5523 13 13 12.5523 13 12V11.0856C13 10.9709 12.9219 10.8709 12.8106 10.843L9.86185 10.1059C9.76426 10.0815 9.66156 10.1179 9.60121 10.1984L8.9544 11.0608C8.64612 11.4718 8.09513 11.6737 7.57625 11.4836C5.23351 10.6255 3.37453 8.76649 2.51635 6.42376C2.32627 5.90487 2.52817 5.35388 2.9392 5.0456L3.80161 4.39879C3.88208 4.33844 3.91855 4.23574 3.89415 4.13816L3.15695 1.18937C3.12913 1.07807 3.02913 1 2.91442 1H2Z" fill="#667085"/>
                                            </svg>
                                            <span class="text-sm text-[#667085] font-light">{{$subHospital['telephone']}}</span>
                                        </div>
                                    </div>
                                </a>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>
            @if(count($hospitals) < 1)
                <span class="text-center text-3xl">Data not found</span>
            @endif
        </div>

    </div>
@endsection


@push('script')
    <script>
        function onClickLocation(location) {
            // Get the current URL
            const currentUrl = new URL(window.location.href);

            // Set the 'location' query parameter
            currentUrl.searchParams.set('location', location);

            // Redirect to the new URL
            window.location.href = currentUrl.toString();
        }

        function onClickSearch() {
            // Get the current URL
            const currentUrl = new URL(window.location.href);

            // Set the 'keyword' query parameter
            currentUrl.searchParams.set('keyword', document.getElementById('searchInput').value);

            // Redirect to the new URL
            window.location.href = currentUrl.toString();
        }
    </script>
@endpush
