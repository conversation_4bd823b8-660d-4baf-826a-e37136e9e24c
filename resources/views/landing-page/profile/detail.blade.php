@section('title', 'Bali International Hospital')
@extends('landing-page.layouts.masterTemplate')

@section('content')
    <div class="h-32">
    </div>
    <div class="rounded-b-3xl relative">
        <div class="absolute mx-auto blur-3xl opacity-50 w-1/2 h-full inset-0 z-1
            bg-gradient-to-b from-green-200 via-cyan-100 to-green-100 rounded-b-3xl"
             style="">
        </div>
        <div class="absolute mx-auto blur-3xl opacity-30 w-1/2 h-full inset-0 z-0
            bg-gradient-to-b from-blue-400 via-cyan-100 to-green-100 rounded-b-3xl"
             style="top: 30%; left: -20%">
        </div>
        <div class="relative z-1">
            <div class="flex flex-row px-4 lg:px-40 py-4 gap-4 items-center text-base font-light">
                <a href="/" class="text-primary-blue-60">Home</a>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="text-[#667085]">My Account</span>
            </div>
        </div>
    </div>
    <div class="w-full bg-light-gray content-center">
        <div class="flex items-center justify-center">
            <div class="w-full lg:w-680 flex-none drop-shadow-sm px-4 lg:px-16 py-8 rounded-3xl">
                <div class="h-8 text-24-32 font-weight-600 mb-8">
                    <a href="{{ route('profile.index') }}">{!! file_get_contents('assets/profile/arrow-left.svg') !!}</a>
                    Patient Details
                </div>
                <div class="h-16 flex bg-white mb-8 py-2 px-2 rounded-xl">
                    <div class="flex-none mr-4 hover:cursor-pointer">
                        <div class="w-[213px] h-12 text-16-24 border-b-2 border-primary-green-111 text-primary-green-111 text-center rounded-xl py-2.5" id="personal-info-nav">
                            {!! file_get_contents('assets/profile/user.svg') !!}
                            Personal Information
                        </div>
                    </div>
                    <div class="flex-none mr-4 hover:cursor-pointer">
                        <div class="w-[115px] h-12 text-16-24 text-neutral-50 text-center py-2.5" id="addresses-info-nav">
                            {!! file_get_contents('assets/profile/map-marker.svg') !!}
                            Address
                        </div>
                    </div>
                </div>
                <div id="personal-info">
                    <div class="bg-white rounded-3xl px-8 lg:px-12 py-12 mb-6 w-full">
                        <div class="flex lg:flex-row flex-col mb-8 gap-2">
                            <div class="flex flex-row gap-2">
                                <div class="flex-none w-20 px-2 py-2 mr-2">
                                    @php
                                        $gcs = new \App\Services\GCS\GoogleCloudService();
                                    @endphp
                                    @if (!empty($detailPatient->image))
                                        <img class="shadow rounded-full h-16 w-16 align-middle border-none" src="{{ $gcs->getStaticUrl($detailPatient->image) }}" alt="profile">
                                    @else
                                        <div class="shadow rounded-full h-16 w-16 align-middle border-none text-36-44 py-2.5 px-5 bg-primary-blue-60 text-white">{{ strtoupper(substr($detailPatient->fullname, 0, 1)) }}</div>
                                    @endif
                                </div>
                                <div class="flex-grow-1">
                                    <div class="text-primary-blue-20 mb-1  flex flex-row gap-1 items-center">
                                        @if ($detailPatient->mr_no)
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                                            </svg>
                                        @endif
                                        <div class="linguise_patient_name">
                                            {{ $detailPatient->fullname }}
                                            @if (!empty($relationToPatient))
                                                <span class="text-neutral-50">({{ $relationToPatient->key }})</span>
                                            @endif
                                        </div>
                                    </div>
                                    @if(@$detailPatient->mr_no)
                                        <div class="flex flex-row gap-1 text-[#667085] text-sm font-light">
                                            <span>MR ID: </span>
                                            <span>{{ @$detailPatient->mr_no }}</span>
                                        </div>
                                    @endif
                                    @if ($detailPatient->percentage_progress == \App\Enums\Table\Patient\PercentageProgress::COMPLETE_ADDRESS_INFO)
                                        <p class="text-neutral-50 mb-1">{{ date("d F Y", strtotime($detailPatient->dob)) }}</p>
                                    @endif
                                    <p class="text-neutral-50 mb-1">+{{ count($detailPatient->phoneCountryCodes) > 0 ? $detailPatient->phoneCountryCodes[0]['extension'] : '' }}{{ $detailPatient->contact_no }}</p>
                                    @if (!$detailPatient->is_complete_data)
                                        <div class="bg-warning-50 inline-block py-1 px-2 rounded-full text-14-20 text-white">
                                            {!! file_get_contents('assets/profile/exclamation-circle.svg') !!}
                                            Data is incomplete
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <button class="bg-brand gap-2 h-10 w-fit rounded-lg text-white text-16-22 text-center flex flex-row items-center lg:float-end edit-profile px-4" data-hs-overlay="#hs-large-modal-{{$detailPatient->uuid}}">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M17.2254 2.77459C16.8593 2.40847 16.2657 2.40847 15.8996 2.77459L14.9353 3.73889L16.2611 5.06472L17.2254 4.10041C17.5915 3.7343 17.5915 3.1407 17.2254 2.77459ZM15.3772 5.9486L14.0514 4.62278L7.05071 11.6235C6.68357 11.9906 6.4137 12.4434 6.26547 12.941L5.92882 14.0712L7.05898 13.7345C7.55658 13.5863 8.00941 13.3164 8.37654 12.9493L15.3772 5.9486ZM15.0157 1.8907C15.87 1.03643 17.255 1.03643 18.1093 1.8907C18.9636 2.74498 18.9636 4.13002 18.1093 4.9843L9.26043 13.8332C8.74644 14.3472 8.11248 14.725 7.41584 14.9325L5.17843 15.599C4.95849 15.6645 4.72033 15.6042 4.55806 15.4419C4.39578 15.2797 4.33549 15.0415 4.40101 14.8216L5.06749 12.5842C5.27501 11.8875 5.65284 11.2536 6.16683 10.7396L15.0157 1.8907ZM4.375 5.62499C3.68464 5.62499 3.125 6.18464 3.125 6.87499V15.625C3.125 16.3154 3.68464 16.875 4.375 16.875H13.125C13.8154 16.875 14.375 16.3154 14.375 15.625V11.6667C14.375 11.3215 14.6548 11.0417 15 11.0417C15.3452 11.0417 15.625 11.3215 15.625 11.6667V15.625C15.625 17.0057 14.5057 18.125 13.125 18.125H4.375C2.99429 18.125 1.875 17.0057 1.875 15.625V6.87499C1.875 5.49428 2.99429 4.37499 4.375 4.37499H8.33333C8.67851 4.37499 8.95833 4.65482 8.95833 4.99999C8.95833 5.34517 8.67851 5.62499 8.33333 5.62499H4.375Z" fill="white"/>
                                </svg>
                                <span>Update</span>
                            </button>
                        </div>

                        <div class="flex flex-row flex-wrap">
                            @if (!empty($detailPatient->ktp_number))
                                <div class="basis-1/2 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >Identity Card Number (KTP)</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ $detailPatient->ktp_number }}</h3>
                                </div>
                            @endif
                            @if (!empty($detailPatient->passport_number))
                                <div class="basis-1/2 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >KITAS/KITAP Number</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ $detailPatient->passport_number }}</h3>
                                </div>
                            @endif
                            @if (!empty($detailPatient->passport_number))
                                <div class="basis-1/2 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >Passport Number</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ $detailPatient->passport_number }}</h3>
                                </div>
                            @endif
                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >Sex</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ \App\Enums\Table\Patient\Gender::getLabel($detailPatient->gender) }}</h3>
                            </div>
                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >Place of Birth</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ $detailPatient->place_of_birth }}</h3>
                            </div>
                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >Date of Birth</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ date("d F Y", strtotime($detailPatient->dob)) }}</h3>
                            </div>

                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >Religion</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ !empty($religion) ? $religion->key : '' }}</h3>
                            </div>
                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >Ethnicity</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ $detailPatient->ethnicityLabel }}</h3>
                            </div>
                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >Marital Status</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ !empty($maritalStatus) ? $maritalStatus->key : '' }}</h3>
                            </div>
                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >Education</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ !empty($education) ? $education->key : '' }}</h3>
                            </div>
                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >Employment</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ !empty($employment) ? $employment->key : '' }}</h3>
                            </div>
                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >Email</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ $detailPatient->email }}</h3>
                            </div>
                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >WhatsApp Number</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ $detailPatient->whatsapp_no }}</h3>
                            </div>
                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >Spoken Language</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ !empty($spokenLanguage) ? $spokenLanguage->key : '' }}</h3>
                            </div>
                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >Mother's Name</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ $detailPatient->mother_name }}</h3>
                            </div>
                            <div class="basis-1/2 mb-3">
                                <h2 class="text-12-18 text-neutral-50" >Vaccine Status</h2>
                                <h3 class="text-16-24 text-primary-blue-30">{{ \App\Enums\Table\Patient\VaccineStatus::getLabel($detailPatient->covid_vaccine) }}</h3>
                            </div>
                        </div>

                        @if(!empty($detailPatient->caregiver_name))
                            <h1 class="text-18-28 text-primary-blue-40 font-weight-600 mt-4 mb-3">Person In Charge of Patient</h1>

                            <div class="flex flex-row flex-wrap">
                                <div class="basis-1/2 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >Name</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ $detailPatient->caregiver_name }}</h3>
                                </div>
                                <div class="basis-1/2 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >Phone Number</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ $detailPatient->caregiver_contact }}</h3>
                                </div>
                                <div class="basis-1/2 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >Relationship</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ !empty($caregiverRelationToPatient) ? $caregiverRelationToPatient->key : '' }}</h3>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                <div id="addresses-info" class="hidden">
                    @if(!empty($patientIDCardAddress) && !empty($patientResidenceAddress))
                        <div class="bg-white rounded-3xl px-8 lg:px-12 py-8 mb-6 lg:w-full">
                            <h1 class="text-18-28 text-primary-blue-30 mb-4 font-weight-600">Address as per Identity Card</h1>
                            <div class="flex flex-row flex-wrap">
                                <div class="basis-full mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >Address</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ @$patientIDCardAddress['address'] ?? '-' }}</h3>
                                </div>
                                <div class="basis-1/2 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >Country</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ @$patientIDCardAddress['country_name'] ?? '-' }}</h3>
                                </div>
                                <div class="basis-1/2 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >Province</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ @$patientIDCardAddress['province_name'] ?? '-' }}</h3>
                                </div>
                                <div class="basis-1/2 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >City</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ @$patientIDCardAddress['city_name'] ?? '-' }}</h3>
                                </div>
                                <div class="basis-1/2 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >Subdistrict</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ @$patientIDCardAddress['subdistrict_name'] ?? '-' }}</h3>
                                </div>
                                <div class="basis-1/2 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >Ward</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ @$patientIDCardAddress['ward'] ?? '-' }}</h3>
                                </div>
                                <div class="basis-1/4 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >RT</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ @$patientIDCardAddress['rt'] ?? '-' }}</h3>
                                </div>
                                <div class="basis-1/4 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >Rw</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ @$patientIDCardAddress['rw'] ?? '-' }}</h3>
                                </div>
                                <div class="basis-1/2 mb-3">
                                    <h2 class="text-12-18 text-neutral-50" >Postal Code</h2>
                                    <h3 class="text-16-24 text-primary-blue-30">{{ @$patientIDCardAddress['postal_code'] ?? '-' }}</h3>
                                </div>
                            </div>

                            @if(!empty($patientResidenceAddress))
                                <h1 class="text-18-28 text-primary-blue-30 mt-4 mb-4 font-weight-600">Current Address in Indonesia</h1>
                                <div class="flex flex-row flex-wrap">
                                    <div class="basis-full mb-3">
                                        <h2 class="text-12-18 text-neutral-50" >Address</h2>
                                        <h3 class="text-16-24 text-primary-blue-30">{{ $patientResidenceAddress['address'] ?? '-' }}</h3>
                                    </div>
                                    <div class="basis-1/2 mb-3">
                                        <h2 class="text-12-18 text-neutral-50" >Country</h2>
                                        <h3 class="text-16-24 text-primary-blue-30">{{ $patientResidenceAddress['country_name'] ?? '-' }}</h3>
                                    </div>
                                    <div class="basis-1/2 mb-3">
                                        <h2 class="text-12-18 text-neutral-50" >Province</h2>
                                        <h3 class="text-16-24 text-primary-blue-30">{{ $patientResidenceAddress['province_name'] ?? '-' }}</h3>
                                    </div>
                                    <div class="basis-1/2 mb-3">
                                        <h2 class="text-12-18 text-neutral-50" >City</h2>
                                        <h3 class="text-16-24 text-primary-blue-30">{{ $patientResidenceAddress['city_name'] ?? '-' }}</h3>
                                    </div>
                                    <div class="basis-1/2 mb-3">
                                        <h2 class="text-12-18 text-neutral-50" >Subdistrict</h2>
                                        <h3 class="text-16-24 text-primary-blue-30">{{ $patientResidenceAddress['subdistrict_name'] ?? '-' }}</h3>
                                    </div>
                                    <div class="basis-1/2 mb-3">
                                        <h2 class="text-12-18 text-neutral-50" >Ward</h2>
                                        <h3 class="text-16-24 text-primary-blue-30">{{ $patientResidenceAddress['ward'] ?? '-' }}</h3>
                                    </div>
                                    <div class="basis-1/4 mb-3">
                                        <h2 class="text-12-18 text-neutral-50" >RT</h2>
                                        <h3 class="text-16-24 text-primary-blue-30">{{ $patientResidenceAddress['rt'] ?? '-' }}</h3>
                                    </div>
                                    <div class="basis-1/4 mb-3">
                                        <h2 class="text-12-18 text-neutral-50" >Rw</h2>
                                        <h3 class="text-16-24 text-primary-blue-30">{{ $patientResidenceAddress['rw'] ?? '-' }}</h3>
                                    </div>
                                    <div class="basis-1/2 mb-3">
                                        <h2 class="text-12-18 text-neutral-50" >Postal Code</h2>
                                        <h3 class="text-16-24 text-primary-blue-30">{{ $patientResidenceAddress['postal_code'] ?? '-' }}</h3>
                                    </div>
                                </div>
                            @endif
                        </div>
                    @else
                        <div class="bg-white rounded-3xl px-12 py-8 mb-6 w-[630px] text-center">
                            {!! file_get_contents('assets/profile/map-marker-not-found.svg') !!}
                            <p class="text-14-20 text-neutral-60">You haven't added an address. Please add your address by updating your personal information.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div id="hs-large-modal-{{$detailPatient->uuid}}" class="hs-overlay hidden size-full fixed top-0 start-0 z-[80] overflow-x-hidden overflow-y-auto pointer-events-none" role="dialog" tabindex="-1" aria-labelledby="hs-large-modal-label">
        <div class="hs-overlay-open:mt-7 hs-overlay-open:opacity-100 hs-overlay-open:duration-500 mt-0 opacity-0 ease-out transition-all lg:max-w-5xl lg:w-full m-3 lg:mx-auto">
            <div class="flex flex-col bg-white border shadow-sm rounded-xl pointer-events-auto">
                <div class="flex justify-between items-center py-3 px-4">
                    <h3 id="hs-large-modal-label" class="font-bold text-gray-800">
                        Edit Profile {{ $detailPatient->first_name . ' ' . $detailPatient->last_name }}
                    </h3>
                    <button type="button" class="size-8 inline-flex justify-center items-center gap-x-2 rounded-full border border-transparent text-gray-800 hover:bg-gray-200 focus:outline-none focus:bg-gray-200 disabled:opacity-50 disabled:pointer-events-none" aria-label="Close" data-hs-overlay="#hs-large-modal-{{$detailPatient->uuid}}">
                        <span class="sr-only">Close</span>
                        <svg class="shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M18 6 6 18"></path>
                            <path d="m6 6 12 12"></path>
                        </svg>
                    </button>
                </div>
                @php
                    $page = request()->fullUrl();
                @endphp
                <div class="p-4 overflow-y-auto">
                    <livewire:landing-page.profile.new-profile :page="$page" :patient_id="$detailPatient->id" wire:key="patient-{{$detailPatient->uuid}}"/>
                </div>
            </div>
        </div>
    </div>
@endsection


@push('script')
    <script>
        $(document).ready(function (){
            const personalInfoNav = $('#personal-info-nav');
            const addressInfoNav = $('#addresses-info-nav');
            const insuranceInfoNav = $('#insurance-info-nav');

            const personalInfo = $('#personal-info');
            const addressInfo = $('#addresses-info');
            const insuranceInfo = $('#insurance-info');

            personalInfoNav.on('click', function (){
                $(this).removeClass('text-neutral-50').addClass('border-b-2 border-primary-green-111 text-primary-green-111 rounded-xl');
                $(this).find('.user-icon').css('fill', '#16A34A');
                personalInfo.show();

                addressInfoNav.removeClass('border-b-2 border-primary-green-111 text-primary-green-111 rounded-xl').addClass('text-neutral-50');
                addressInfoNav.find('.map-pin-icon').css('fill', '#667085');
                addressInfo.hide();

                insuranceInfoNav.removeClass('border-b-2 border-primary-green-111 text-primary-green-111 rounded-xl').addClass('text-neutral-50');
                insuranceInfoNav.find('.credit-card-icon').css('fill', '#667085');
                insuranceInfo.hide();
            });

            addressInfoNav.on('click', function (){
                $(this).removeClass('text-neutral-50').addClass('border-b-2 border-primary-green-111 text-primary-green-111 rounded-xl');
                $(this).find('.map-pin-icon').css('fill', '#16A34A');
                addressInfo.show();

                personalInfoNav.removeClass('border-b-2 border-primary-green-111 text-primary-green-111 rounded-xl').addClass('text-neutral-50');
                personalInfoNav.find('.user-icon').css('fill', '#667085');
                personalInfo.hide();

                insuranceInfoNav.removeClass('border-b-2 border-primary-green-111 text-primary-green-111 rounded-xl').addClass('text-neutral-50');
                insuranceInfoNav.find('.credit-card-icon').css('fill', '#667085');
                insuranceInfo.hide();
            });

            insuranceInfoNav.on('click', function (){
                $(this).removeClass('text-neutral-50').addClass('border-b-2 border-primary-green-111 text-primary-green-111 rounded-xl');
                $(this).find('.credit-card-icon').css('fill', '#16A34A');
                insuranceInfo.show();

                personalInfoNav.removeClass('border-b-2 border-primary-green-111 text-primary-green-111 rounded-xl').addClass('text-neutral-50');
                personalInfoNav.find('.user-icon').css('fill', '#667085');
                personalInfo.hide();

                addressInfoNav.removeClass('border-b-2 border-primary-green-111 text-primary-green-111 rounded-xl').addClass('text-neutral-50');
                addressInfoNav.find('.map-pin-icon').css('fill', '#667085');
                addressInfo.hide();
            });
        });

        // function onClickOpenFile(url){
        //     window.open(url, '_blank');
        // }
    </script>
@endpush
