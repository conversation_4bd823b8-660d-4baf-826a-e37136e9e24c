@section('title', 'Bali International Hospital')
@extends('landing-page.layouts.masterTemplate')

@section('content')
    <div class="h-32">
    </div>
    <div class="rounded-b-3xl relative">
        <div class="absolute mx-auto blur-3xl opacity-50 w-1/2 h-full inset-0 z-1
            bg-gradient-to-b from-green-200 via-cyan-100 to-green-100 rounded-b-3xl"
             style="">
        </div>
        <div class="absolute mx-auto blur-3xl opacity-30 w-1/2 h-full inset-0 z-0
            bg-gradient-to-b from-blue-400 via-cyan-100 to-green-100 rounded-b-3xl"
             style="top: 30%; left: -20%">
        </div>
        <div class="relative z-1">
            <div class="flex flex-row px-4 md:px-40 py-4 gap-4 items-center text-sm md:text-base font-light">
                <a href="/" class="text-primary-blue-60">Home</a>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <a href="{{route('profile.mybook.index')}}" class="text-blue-800">My Bookings</a>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="text-[#667085]">Appointment Details</span>
            </div>
        </div>
    </div>
    <div class="content-center flex flex-col justify-center mx-4 md:mx-40 mt-4 md:mt-10 gap-3 md:gap-6">
        @if($appointment['type'] == \App\Enums\Table\AppointmentPatientSummary\Type::VISIT)
            <div class="flex flex-row items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.90002 2.3999C7.39708 2.3999 7.80002 2.80285 7.80002 3.2999V4.7999H16.2V3.2999C16.2 2.80285 16.603 2.3999 17.1 2.3999C17.5971 2.3999 18 2.80285 18 3.2999V4.7999H18.3C20.1226 4.7999 21.6 6.27736 21.6 8.0999V18.2999C21.6 20.1224 20.1226 21.5999 18.3 21.5999H5.70002C3.87749 21.5999 2.40002 20.1224 2.40002 18.2999V8.0999C2.40002 6.27736 3.87748 4.7999 5.70002 4.7999H6.00002V3.2999C6.00002 2.80285 6.40297 2.3999 6.90002 2.3999ZM5.70002 8.9999C4.8716 8.9999 4.20002 9.67148 4.20002 10.4999V18.2999C4.20002 19.1283 4.8716 19.7999 5.70003 19.7999H18.3C19.1285 19.7999 19.8 19.1283 19.8 18.2999V10.4999C19.8 9.67148 19.1285 8.9999 18.3 8.9999H5.70002Z" fill="url(#paint0_linear_6339_112385)"/>
                    <defs>
                        <linearGradient id="paint0_linear_6339_112385" x1="15.2003" y1="1.99991" x2="8.20025" y2="21.9999" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#3674B3"/>
                            <stop offset="1" stop-color="#0B4074"/>
                        </linearGradient>
                    </defs>
                </svg>
                <span class="text-xl font-medium text-brand">Hospital Visit</span>
            </div>
        @else
            <div class="flex flex-row items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M4.5 4.5C2.84315 4.5 1.5 5.84315 1.5 7.5V16.5C1.5 18.1569 2.84315 19.5 4.5 19.5H12.75C14.4069 19.5 15.75 18.1569 15.75 16.5V7.5C15.75 5.84315 14.4069 4.5 12.75 4.5H4.5Z" fill="url(#paint0_linear_6439_37246)"/>
                    <path d="M19.9393 18.75L17.25 16.0606V7.93931L19.9393 5.24996C20.8843 4.30501 22.5 4.97427 22.5 6.31063V17.6893C22.5 19.0257 20.8843 19.6949 19.9393 18.75Z" fill="url(#paint1_linear_6439_37246)"/>
                    <defs>
                        <linearGradient id="paint0_linear_6439_37246" x1="15.5003" y1="4.18751" x2="11.3734" y2="20.6948" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#3674B3"/>
                            <stop offset="1" stop-color="#0B4074"/>
                        </linearGradient>
                        <linearGradient id="paint1_linear_6439_37246" x1="15.5003" y1="4.18751" x2="11.3734" y2="20.6948" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#3674B3"/>
                            <stop offset="1" stop-color="#0B4074"/>
                        </linearGradient>
                    </defs>
                </svg>
                <span class="text-xl font-medium text-brand">Teleconsultation</span>
            </div>
        @endif

        <div class="flex flex-col md:flex-row gap-6">
            <div class="md:w-2/3 flex flex-col gap-4">
                <div class="flex flex-col p-6 rounded-3xl bg-gradient-to-br from-blue-25 via-transparent  gap-4">
                    <span class="text-lg font-bold text-brand">Patient’s Information</span>
                    @if($appointment->show_in_complete_icon_in_detail)
                        <div class="flex flex-row">
                            <div class="border border-[#B54708] rounded-xl py-3 px-4 bg-[#FFFAEB]">
                                <div class="flex flex-row gap-4">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M14.4866 12.0005L9.15329 2.66714C9.037 2.46194 8.86836 2.29127 8.66457 2.17252C8.46078 2.05378 8.22915 1.99121 7.99329 1.99121C7.75743 1.99121 7.52579 2.05378 7.322 2.17252C7.11822 2.29127 6.94958 2.46194 6.83329 2.66714L1.49995 12.0005C1.38241 12.204 1.32077 12.4351 1.32129 12.6701C1.32181 12.9052 1.38447 13.136 1.50292 13.339C1.62136 13.5421 1.79138 13.7102 1.99575 13.8264C2.20011 13.9425 2.43156 14.0026 2.66662 14.0005H13.3333C13.5672 14.0002 13.797 13.9385 13.9995 13.8213C14.202 13.7042 14.3701 13.5359 14.487 13.3332C14.6038 13.1306 14.6653 12.9007 14.6653 12.6668C14.6652 12.4329 14.6036 12.2031 14.4866 12.0005Z" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M8 6V8.66667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M8 11.333H8.00667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div class="flex flex-col text-[#93370D] gap-1">
                                        <span class="text-base font-semibold text-[#B54708]">Patient data is still incomplete</span>
                                        <span class="">Please complete your profile before the appointment date. This will reduce your waiting time in the hospital.</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                    @if($appointment->showComponentDetail(3))
                        <div class="flex flex-row">
                            <div class="border border-[#B54708] rounded-xl py-3 px-4 bg-[#FFFAEB]">
                                <div class="flex flex-row gap-4">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M14.4866 12.0005L9.15329 2.66714C9.037 2.46194 8.86836 2.29127 8.66457 2.17252C8.46078 2.05378 8.22915 1.99121 7.99329 1.99121C7.75743 1.99121 7.52579 2.05378 7.322 2.17252C7.11822 2.29127 6.94958 2.46194 6.83329 2.66714L1.49995 12.0005C1.38241 12.204 1.32077 12.4351 1.32129 12.6701C1.32181 12.9052 1.38447 13.136 1.50292 13.339C1.62136 13.5421 1.79138 13.7102 1.99575 13.8264C2.20011 13.9425 2.43156 14.0026 2.66662 14.0005H13.3333C13.5672 14.0002 13.797 13.9385 13.9995 13.8213C14.202 13.7042 14.3701 13.5359 14.487 13.3332C14.6038 13.1306 14.6653 12.9007 14.6653 12.6668C14.6652 12.4329 14.6036 12.2031 14.4866 12.0005Z" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M8 6V8.66667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M8 11.333H8.00667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div class="flex flex-col text-[#93370D] gap-1">
                                        <span class="text-base font-semibold text-[#B54708]">You will get a refund in 7 business days</span>
                                        <span class="">If you have yet to receive your refund, contact support: 150-442.</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    @if($appointment->showComponentDetail(4))
                        <div class="flex flex-row">
                            <div class="border border-[#B54708] rounded-xl py-3 px-4 bg-[#FFFAEB]">
                                <div class="flex flex-row gap-4">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                                            <path d="M14.4866 12.0005L9.15329 2.66714C9.037 2.46194 8.86836 2.29127 8.66457 2.17252C8.46078 2.05378 8.22915 1.99121 7.99329 1.99121C7.75743 1.99121 7.52579 2.05378 7.322 2.17252C7.11822 2.29127 6.94958 2.46194 6.83329 2.66714L1.49995 12.0005C1.38241 12.204 1.32077 12.4351 1.32129 12.6701C1.32181 12.9052 1.38447 13.136 1.50292 13.339C1.62136 13.5421 1.79138 13.7102 1.99575 13.8264C2.20011 13.9425 2.43156 14.0026 2.66662 14.0005H13.3333C13.5672 14.0002 13.797 13.9385 13.9995 13.8213C14.202 13.7042 14.3701 13.5359 14.487 13.3332C14.6038 13.1306 14.6653 12.9007 14.6653 12.6668C14.6652 12.4329 14.6036 12.2031 14.4866 12.0005Z" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M8 6V8.66667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                            <path d="M8 11.333H8.00667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                    </div>
                                    <div class="flex flex-col text-[#93370D] gap-1">
                                        <span class="text-base font-semibold text-[#B54708]">Did not remember cancelling?:</span>
                                        <span class="">Contact support: 150-442. Our team will resolve the issue promptly.</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="flex flex-col bg-white p-4 rounded-2xl">
                        <div class="flex flex-row gap-4 items-center">
                            @if($appointment->patient->image)
                                <img class="w-[56px] h-[56px] rounded-full object-cover"
                                     src="{{asset_gcs($appointment->patient->image)}}" alt="Image">
                            @else
                                <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
                                    <rect x="1.25" y="1.25" width="57.5" height="57.5" stroke="white" stroke-width="1.5"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M45.5992 46.5599C50.0017 42.4113 52.75 36.5266 52.75 30C52.75 17.4355 42.5645 7.25 30 7.25C17.4355 7.25 7.25 17.4355 7.25 30C7.25 36.5266 9.99832 42.4113 14.4008 46.5599C18.4736 50.3979 23.9621 52.75 30 52.75C36.0379 52.75 41.5264 50.3979 45.5992 46.5599ZM16.3386 43.562C19.5458 39.5615 24.4737 37 30 37C35.5263 37 40.4542 39.5615 43.6614 43.562C40.174 47.0748 35.341 49.25 30 49.25C24.659 49.25 19.826 47.0748 16.3386 43.562ZM38.75 23C38.75 27.8325 34.8325 31.75 30 31.75C25.1675 31.75 21.25 27.8325 21.25 23C21.25 18.1675 25.1675 14.25 30 14.25C34.8325 14.25 38.75 18.1675 38.75 23Z" fill="#98A2B3"/>
                                </svg>
                            @endif
                            <div class="flex flex-col gap-1">
                                <div class="flex gap-1 items-center">
                                    @if($appointment->patient->verified_at)
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                            <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                                        </svg>
                                    @endif
                                    <span class="text-base font-light text-[#344054] linguise_patient_name">{{ $appointment->patient->fullname }}</span>
                                    <span class="text-base font-light text-[#344054] {{ $appointment->patient->relation_patient ? '' : 'hidden' }}">({{\App\Enums\Table\Patient\RelationPatient::getLabel($appointment->patient->relation_patient)}})</span>
                                </div>
                                @if(@$appointment->patient->mr_no)
                                    <div class="flex flex-row gap-1 text-[#667085] text-sm font-light">
                                        <span>MR ID: </span>
                                        <span>{{ @$appointment->patient->mr_no }}</span>
                                    </div>
                                @endif
                                <span class="text-sm text-[#475467]">{{ \Carbon\Carbon::parse($appointment->patient->dob)->format('d F Y') }}</span>
                                <span class="text-sm text-[#475467]">{{ $appointment->patient->contact_with_code }}</span>
                            </div>
                        </div>
                    </div>
                    @if($appointment->type == \App\Enums\Table\AppointmentPatientSummary\Type::VISIT)
                        <div class="flex flex-row p-4 gap-4 rounded-xl bg-white">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M4.5 3.75C2.84315 3.75 1.5 5.09315 1.5 6.75V7.5H22.5V6.75C22.5 5.09315 21.1569 3.75 19.5 3.75H4.5Z" fill="#3674B3"/>
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M22.5 9.75H1.5V17.25C1.5 18.9069 2.84315 20.25 4.5 20.25H19.5C21.1569 20.25 22.5 18.9069 22.5 17.25V9.75ZM4.5 13.5C4.5 13.0858 4.83579 12.75 5.25 12.75H11.25C11.6642 12.75 12 13.0858 12 13.5C12 13.9142 11.6642 14.25 11.25 14.25H5.25C4.83579 14.25 4.5 13.9142 4.5 13.5ZM5.25 15.75C4.83579 15.75 4.5 16.0858 4.5 16.5C4.5 16.9142 4.83579 17.25 5.25 17.25H8.25C8.66421 17.25 9 16.9142 9 16.5C9 16.0858 8.66421 15.75 8.25 15.75H5.25Z" fill="#3674B3"/>
                            </svg>
                            <div class="flex flex-col gap-2">
                                <span class="text-base font-medium">Guarantor</span>
                                <span class="text-base font-light text-[#667085]">{{ $appointment->payment_method_label }}</span>
                                @if($appointment->payment_method == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::INSURANCE)
                                    <span class="text-base font-light text-[#667085]">{{ $appointment->insurance_name }}</span>
                                    <span class="text-base font-light text-[#667085]">{{ $appointment->insurance_number }}</span>
                                @elseif($appointment->payment_method == \App\Enums\Table\AppointmentPatientSummary\PaymentMethod::COMPANY)
                                    <span class="text-base font-light text-[#667085]">{{ $appointment->company_name }}</span>
                                @endif
                            </div>
                        </div>
                    @endif

                </div>

                <div class="flex flex-col p-6 rounded-3xl bg-gradient-to-br from-blue-25 via-transparent gap-4">
                    <span class="text-lg font-bold text-brand">Appointment Details</span>
                    <div class="flex flex-col gap-4 px-4 bg-white py-4 rounded-2xl">
                        <div class="flex flex-row gap-4">
                            <div class="w-6 h-6">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none">
                                    <path d="M15.4375 5.4442C15.4375 2.55292 13.0855 0.200195 10.194 0.200195C7.3025 0.200195 4.95026 2.55292 4.95026 5.4442C4.95074 8.33524 7.30274 11.4044 10.194 11.4044C13.0855 11.4044 15.4375 8.33524 15.4375 5.4442Z" fill="#3674B3"/>
                                    <path d="M0.399963 18.3461C0.399963 20.2536 4.78476 21.8004 10.1939 21.8004C15.6028 21.8004 19.9878 20.2536 19.9878 18.3461C19.9878 15.0596 17.7152 12.3063 14.6562 11.5656C15.4708 12.0946 16.0319 12.979 16.119 13.9985C16.8152 14.1545 17.3377 14.7768 17.3377 15.5196C17.3377 16.3798 16.6379 17.0796 15.7777 17.0796C14.9176 17.0796 14.2177 16.3798 14.2177 15.5196C14.2177 14.7905 14.7215 14.1783 15.3985 14.0081C15.2984 13.0649 14.6735 12.278 13.8224 11.9415L10.2976 15.2446L6.72588 11.8976C5.75772 12.2244 5.05404 13.1256 5.01996 14.1946C5.15892 14.2752 5.27124 14.4 5.34012 14.5512C5.853 14.7932 6.30564 15.3024 6.65244 16.0275C6.71052 16.1496 6.71796 16.2874 6.67548 16.4132C6.88332 16.9481 7.00212 17.5328 7.00212 18.0238C7.00212 18.7119 7.00212 19.3625 6.25212 19.5296C6.17076 19.5972 6.06924 19.6342 5.96244 19.6342H5.46108C5.2098 19.6342 5.00532 19.4295 5.00532 19.1784L5.0058 19.1607C5.0154 18.9185 5.2182 18.7227 5.46108 18.7227H5.9622C6.01356 18.7227 6.06372 18.7313 6.11196 18.7484C6.14244 18.7397 6.15204 18.7325 6.15204 18.7325C6.20772 18.6336 6.20772 18.2218 6.20772 18.024C6.20772 17.6252 6.10788 17.1435 5.93292 16.6959C5.84196 16.6443 5.76876 16.5663 5.72364 16.472C5.41956 15.8357 5.00652 15.4248 4.67076 15.4248C4.32732 15.4248 3.89364 15.8691 3.59124 16.5298C3.5418 16.6373 3.45516 16.7252 3.34836 16.7775C3.1902 17.2059 3.10332 17.6468 3.10332 18.0238C3.10332 18.1899 3.10332 18.6308 3.16692 18.7342C3.16764 18.7342 3.1818 18.7428 3.22164 18.7527C3.27348 18.7328 3.32916 18.7224 3.38484 18.7224H3.88692C4.12116 18.7224 4.31628 18.9008 4.34004 19.1321L4.34268 19.1604C4.34268 19.4295 4.1382 19.6342 3.88716 19.6342H3.38508C3.28548 19.6342 3.18924 19.6011 3.11004 19.5404C2.82324 19.4864 2.62116 19.3599 2.49324 19.154C2.3382 18.9051 2.30988 18.5782 2.30988 18.0238C2.30988 17.5373 2.4222 16.9774 2.6262 16.4424C2.5962 16.328 2.60628 16.2084 2.65596 16.1007C2.8686 15.636 3.13884 15.2381 3.4374 14.9504C3.60156 14.7922 3.77796 14.665 3.96252 14.5721C4.03212 14.4099 4.1526 14.2769 4.3002 14.1927C4.32828 13.0815 4.91844 12.1097 5.79828 11.5498C2.7054 12.2667 0.399963 15.0358 0.399963 18.3461ZM11.3939 18.2304C11.3939 18.1474 11.4692 18.0804 11.5616 18.0804H12.4739V17.1684C12.4739 17.0756 12.5408 17.0004 12.6239 17.0004H13.5239C13.6064 17.0004 13.6739 17.0756 13.6739 17.1684V18.0804H14.5861C14.679 18.0804 14.7539 18.1474 14.7539 18.2304V19.1304C14.7539 19.2135 14.6788 19.2804 14.5861 19.2804H13.6739V20.1924C13.6739 20.2853 13.6064 20.3604 13.5239 20.3604H12.6239C12.5408 20.3604 12.4739 20.2853 12.4739 20.1924V19.2804H11.5616C11.4692 19.2804 11.3939 19.2135 11.3939 19.1304V18.2304Z" fill="#3674B3"/>
                                    <path d="M15.7777 16.2398C16.1754 16.2398 16.4977 15.9174 16.4977 15.5198C16.4977 15.1222 16.1754 14.7998 15.7777 14.7998C15.3801 14.7998 15.0577 15.1222 15.0577 15.5198C15.0577 15.9174 15.3801 16.2398 15.7777 16.2398Z" fill="#3674B3"/>
                                </svg>
                            </div>

                            <div class="flex flex-col gap-2">
                                <span class="text-base text-[#1D2939] font-medium">Doctor</span>
                                <span class="text-base text-[#667085] font-medium linguise_doctor_name">{{ $appointment->doctor->name }}</span>
                                <span class="text-base text-[#667085] font-light linguise_doctor_specialty">{{ $appointment->doctor->specialty->group_name_en }}</span>
                            </div>
                        </div>
                        <livewire:landing-page.profile.transaction.component.reschedule-appointment :appointment="$appointment" wireKey="reschedule-{{\Illuminate\Support\Str::uuid()}}"/>

                        @if($appointment->type == \App\Enums\Table\AppointmentPatientSummary\Type::VISIT)
                            <div class="flex flex-row gap-4">
                                <div class="w-6 h-6">
                                    <svg width="18" height="21" viewBox="0 0 18 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8.53975 20.351C8.56995 20.3685 8.59369 20.3821 8.6105 20.3915L8.63843 20.4071C8.8613 20.5294 9.13776 20.5285 9.36084 20.4075L9.3895 20.3915C9.40631 20.3821 9.43005 20.3685 9.46025 20.351C9.52066 20.316 9.60697 20.265 9.7155 20.1982C9.93246 20.0646 10.2388 19.8676 10.6046 19.6091C11.3351 19.0931 12.3097 18.3274 13.2865 17.3273C15.2307 15.3368 17.25 12.3462 17.25 8.5C17.25 3.94365 13.5563 0.25 9 0.25C4.44365 0.25 0.75 3.94365 0.75 8.5C0.75 12.3462 2.76932 15.3368 4.71346 17.3273C5.69025 18.3274 6.66491 19.0931 7.39539 19.6091C7.76125 19.8676 8.06754 20.0646 8.2845 20.1982C8.39303 20.265 8.47934 20.316 8.53975 20.351ZM9 11.5C10.6569 11.5 12 10.1569 12 8.5C12 6.84315 10.6569 5.5 9 5.5C7.34315 5.5 6 6.84315 6 8.5C6 10.1569 7.34315 11.5 9 11.5Z" fill="#3674B3"/>
                                    </svg>
                                </div>
                                <div class="flex flex-col gap-2">
                                    <span class="text-base text-[#1D2939] font-medium">Consulting Room</span>
                                    <span class="text-base text-[#667085] font-medium">Bali International Hospital</span>
                                </div>
                            </div>
                        @endif

                        <div class="flex flex-row gap-4">
                            <div class="w-6 h-6">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M13.5664 10.0763V6.47168H10.4324V10.0763C10.4324 10.2736 10.2726 10.4334 10.0753 10.4334H6.4707V13.5673H10.0753C10.2726 13.5673 10.4324 13.7272 10.4324 13.9245V17.5291H13.5664V13.9245C13.5664 13.7272 13.7262 13.5673 13.9235 13.5673H17.5288V10.4334H13.9235C13.7262 10.4334 13.5664 10.2736 13.5664 10.0763Z" fill="white"/>
                                    <path d="M12 2C6.48571 2 2 6.48571 2 12C2 17.5143 6.48571 22 12 22C17.5143 22 22 17.5143 22 12C22 6.48571 17.5143 2 12 2ZM18.2427 13.9241C18.2427 14.1214 18.0829 14.2813 17.8856 14.2813H14.2803V17.8858C14.2803 18.0832 14.1205 18.243 13.9232 18.243H10.075C9.87765 18.243 9.71783 18.0832 9.71783 17.8858V14.2813H6.11417C5.91685 14.2813 5.75703 14.1214 5.75703 13.9241V10.0759C5.75703 9.87856 5.91685 9.71874 6.11417 9.71874H9.71874V6.11417C9.71874 5.91685 9.87856 5.75703 10.0759 5.75703H13.9241C14.1214 5.75703 14.2813 5.91685 14.2813 6.11417V9.71874H17.8865C18.0838 9.71874 18.2437 9.87856 18.2437 10.0759V13.9241H18.2427Z" fill="#3674B3"/>
                                </svg>
                            </div>
                            <div class="flex flex-col gap-2">
                                <span class="text-base text-[#1D2939] font-medium">Medical Concern or Request</span>
                                <span class="text-base text-[#667085] font-medium">{{ $appointment->medical_concern }}</span>
                            </div>
                        </div>

                        @if($appointment->attachment_url)
                            <div class="flex flex-row gap-4">
                                <div class="w-6 h-6">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.625 1.5C4.58947 1.5 3.75 2.33947 3.75 3.375V20.625C3.75 21.6605 4.58947 22.5 5.625 22.5H18.375C19.4105 22.5 20.25 21.6605 20.25 20.625V12.75C20.25 10.6789 18.5711 9 16.5 9H14.625C13.5895 9 12.75 8.16053 12.75 7.125V5.25C12.75 3.17893 11.0711 1.5 9 1.5H5.625ZM7.5 15C7.5 14.5858 7.83579 14.25 8.25 14.25H15.75C16.1642 14.25 16.5 14.5858 16.5 15C16.5 15.4142 16.1642 15.75 15.75 15.75H8.25C7.83579 15.75 7.5 15.4142 7.5 15ZM8.25 17.25C7.83579 17.25 7.5 17.5858 7.5 18C7.5 18.4142 7.83579 18.75 8.25 18.75H12C12.4142 18.75 12.75 18.4142 12.75 18C12.75 17.5858 12.4142 17.25 12 17.25H8.25Z" fill="#3674B3"/>
                                        <path d="M12.9712 1.8159C13.768 2.73648 14.25 3.93695 14.25 5.25V7.125C14.25 7.33211 14.4179 7.5 14.625 7.5H16.5C17.8131 7.5 19.0135 7.98204 19.9341 8.77881C19.0462 5.37988 16.3701 2.70377 12.9712 1.8159Z" fill="#3674B3"/>
                                    </svg>
                                </div>
                                <div class="flex flex-col gap-2">
                                    <span class="text-base text-[#1D2939] font-medium">Attachment File (.jpg, .png, .pdf, .zip)</span>
                                    <a href="{{ route('static-page-gcs', ['path' => $appointment->attachment_url]) }}"
                                       target="_blank" class="hover:cursor-pointer text-base font-medium text-[#0D4D8B]">{{ get_file_name_from_path($appointment->attachment_url) }}</a>
                                </div>
                            </div>
                        @endif

                    </div>
                </div>

                @if($appointment['type'] == \App\Enums\Table\AppointmentPatientSummary\Type::TELECONSULTATION)
                    <div class="flex flex-col p-6 rounded-3xl bg-gradient-to-br from-blue-25 via-transparent gap-4">
                        <span class="text-lg font-bold text-brand">Payment Summary</span>

                        @if(@$appointment->payment_channel)
                            <div class="flex flex-col gap-4 px-4 bg-white py-4 rounded-2xl">
                                <div class="flex flex-row gap-4">
                                    <svg width="22" height="18" viewBox="0 0 22 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3.5 0.75C1.84315 0.75 0.5 2.09315 0.5 3.75V4.5H21.5V3.75C21.5 2.09315 20.1569 0.75 18.5 0.75H3.5Z" fill="#3674B3"/>
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M21.5 6.75H0.5V14.25C0.5 15.9069 1.84315 17.25 3.5 17.25H18.5C20.1569 17.25 21.5 15.9069 21.5 14.25V6.75ZM3.5 10.5C3.5 10.0858 3.83579 9.75 4.25 9.75H10.25C10.6642 9.75 11 10.0858 11 10.5C11 10.9142 10.6642 11.25 10.25 11.25H4.25C3.83579 11.25 3.5 10.9142 3.5 10.5ZM4.25 12.75C3.83579 12.75 3.5 13.0858 3.5 13.5C3.5 13.9142 3.83579 14.25 4.25 14.25H7.25C7.66421 14.25 8 13.9142 8 13.5C8 13.0858 7.66421 12.75 7.25 12.75H4.25Z" fill="#3674B3"/>
                                    </svg>
                                    <div class="flex flex-col gap-2">
                                        <span class="text-base text-[#1D2939] font-medium">Payment Method</span>
                                        <span class="text-base text-[#667085] font-light">{{ $appointment->xendit_payment_method_label ?? 'n/a' }}</span>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="flex flex-col bg-white rounded-xl p-4 gap-4">
                            <div class="flex flex-row justify-between items-center">
                                <span class="text-base font-light">Teleconsultation Fee</span>
                                <span class="text-base font-light">Rp{{str_replace(',', '.', number_format((int)$appointment['amount']))}}</span>
                            </div>
                            <hr>
                            <div class="flex flex-row justify-between items-center">
                                <span class="text-[18px] font-semibold text-[#0D4D8B]">Total</span>
                                <span class="text-[18px] font-semibold text-[#0D4D8B]">Rp{{str_replace(',', '.', number_format((int)$appointment['amount']))}}</span>
                            </div>
                        </div>
                    </div>
                @endif

            </div>
            <div class="md:w-2/4 p-6 bg-gradient-to-br bg-blue-50 bg-opacity-40 h-fit rounded-3xl">
                <div class="flex flex-col p-6 bg-white gap-4 rounded-3xl">
                    <div class="flex flex-row justify-between items-center">
                        <span class="text-sm font-light text-[#667085]">Status</span>
                        @if($appointment['payment_status'] == \App\Enums\Table\AppointmentPatientSummary\PaymentStatus::UNPAID && $appointment['type'] == \App\Enums\Table\AppointmentPatientSummary\Type::TELECONSULTATION)
                            <div class="bg-[#FEF0C7] py-[6px] px-3 h-fit rounded-lg">
                                <span class="text-sm font-medium text-[#B54708]">Payment Pending</span>
                            </div>
                        @else
                            @if($appointment['simrs_status'])
                                <div class="py-[6px] px-3 rounded-lg
                                {{ \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::getClassName($appointment['simrs_status'])['bg'] }}">
                                    <span class="text-sm font-medium
                                    {{ \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::getClassName($appointment['simrs_status'])['text'] }}">
                                        {{ \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::getLabel($appointment['simrs_status']) }}
                                    </span>
                                </div>
                            @endif
                        @endif
                    </div>
                    @if($appointment['cancel_reason'])
                        <div class="flex py-3 px-4 rounded-lg border bg-[#F2F4F7]">
                            <span class="text-[#667085] text-sm">{{ $appointment['cancel_reason'] }}</span>
                        </div>
                    @endif

                    <div class="flex flex-col gap-2">
                        <span class="text-sm-center font-light text-[#667085]">Registration No</span>
                        <div class="flex flex-row justify-between">
                            <span class="text-lg text-[#16A34A] font-semibold" id="code">{{ @$appointment->registration_no ?? 'n/a'  }}</span>
                            <div class="flex flex-row gap-2 items-center hover:cursor-pointer" onclick="copyToClipboard()">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M2.5 5C2.5 3.61929 3.61929 2.5 5 2.5H11.875C13.2557 2.5 14.375 3.61929 14.375 5V6.25H15C16.3807 6.25 17.5 7.36929 17.5 8.75V15C17.5 16.3807 16.3807 17.5 15 17.5H8.75C7.36929 17.5 6.25 16.3807 6.25 15V14.375H5C3.61929 14.375 2.5 13.2557 2.5 11.875V5ZM6.25 13.125V8.75C6.25 7.36929 7.36929 6.25 8.75 6.25H13.125V5C13.125 4.30964 12.5654 3.75 11.875 3.75H5C4.30964 3.75 3.75 4.30964 3.75 5V11.875C3.75 12.5654 4.30964 13.125 5 13.125H6.25ZM8.75 7.5C8.05964 7.5 7.5 8.05964 7.5 8.75V15C7.5 15.6904 8.05964 16.25 8.75 16.25H15C15.6904 16.25 16.25 15.6904 16.25 15V8.75C16.25 8.05964 15.6904 7.5 15 7.5H8.75Z" fill="#0D4D8B"/>
                                </svg>
                                <span class="text-base font-semibold text-brand" id="copy-button">Copy</span>
                                <span class="font-bold text-sm text-[#0D4D8B] hidden" id="copy-tooltip">Copied!</span>
                            </div>
                        </div>

{{--                        <div class="border border-brand bg-white flex flex-row items-center justify-center py-3 rounded-xl gap-3 hover:cursor-pointer">--}}
{{--                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">--}}
{{--                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5.625 3C5.41789 3 5.25 3.16789 5.25 3.375V20.625C5.25 20.8321 5.41789 21 5.625 21H18.375C18.5821 21 18.75 20.8321 18.75 20.625V11.625C18.75 10.1753 17.5747 9 16.125 9H14.625C13.5895 9 12.75 8.16053 12.75 7.125V5.625C12.75 4.17525 11.5747 3 10.125 3H5.625ZM5.625 1.5C4.58947 1.5 3.75 2.33947 3.75 3.375V20.625C3.75 21.6605 4.58947 22.5 5.625 22.5H18.375C19.4105 22.5 20.25 21.6605 20.25 20.625V11.25C20.25 5.86522 15.8848 1.5 10.5 1.5H5.625ZM13.757 3.66785C14.0715 4.25019 14.25 4.91675 14.25 5.625V7.125C14.25 7.33211 14.4179 7.5 14.625 7.5H16.125C16.8332 7.5 17.4998 7.6785 18.0822 7.99296C17.2488 6.05549 15.6945 4.50123 13.757 3.66785ZM7.5 15C7.5 14.5858 7.83579 14.25 8.25 14.25H15.75C16.1642 14.25 16.5 14.5858 16.5 15C16.5 15.4142 16.1642 15.75 15.75 15.75H8.25C7.83579 15.75 7.5 15.4142 7.5 15ZM7.5 18C7.5 17.5858 7.83579 17.25 8.25 17.25H12C12.4142 17.25 12.75 17.5858 12.75 18C12.75 18.4142 12.4142 18.75 12 18.75H8.25C7.83579 18.75 7.5 18.4142 7.5 18Z" fill="#0D4D8B"/>--}}
{{--                            </svg>--}}
{{--                            <span class="text-base font-semibold text-brand">View Medical Resume</span>--}}
{{--                        </div>--}}

                        @if($appointment->show_in_complete_icon_in_detail)
                            {{--                            <div class="edit-profile bg-white border border-brand flex flex-row items-center justify-center py-3 rounded-xl gap-3 hover:cursor-pointer"--}}
                            {{--                                 data-id="{{ $appointment['patient_id'] }}" data-uuid="{{ $appointment['patient']['uuid'] }}">--}}
                            {{--                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">--}}
                            {{--                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M11.9998 3C10.343 3 8.99984 4.34315 8.99984 6C8.99984 7.65685 10.343 9 11.9998 9C13.6567 9 14.9998 7.65685 14.9998 6C14.9998 4.34315 13.6567 3 11.9998 3ZM7.49984 6C7.49984 3.51472 9.51456 1.5 11.9998 1.5C14.4851 1.5 16.4998 3.51472 16.4998 6C16.4998 8.48528 14.4851 10.5 11.9998 10.5C9.51456 10.5 7.49984 8.48528 7.49984 6ZM5.27693 19.6409C7.34216 20.5158 9.61373 21 12.0002 21C14.3864 21 16.6577 20.5159 18.7228 19.6412C18.4151 16.1987 15.5226 13.5 11.9998 13.5C8.47721 13.5 5.58472 16.1985 5.27693 19.6409ZM3.75109 20.1053C3.82843 15.6156 7.49183 12 11.9998 12C16.508 12 20.1714 15.6157 20.2486 20.1056C20.2537 20.4034 20.0822 20.676 19.8115 20.8002C17.4326 21.8918 14.7864 22.5 12.0002 22.5C9.2137 22.5 6.56728 21.8917 4.18816 20.7999C3.91749 20.6757 3.74596 20.4031 3.75109 20.1053Z" fill="#0D4D8B"/>--}}
                            {{--                                </svg>--}}
                            {{--                                <span class="text-base font-semibold text-brand">Complete Profile</span>--}}
                            {{--                            </div>--}}

                            <a class="bg-white border border-brand flex flex-row items-center justify-center py-3 rounded-xl gap-3 hover:cursor-pointer"
                               href="{{route('profile.show', $appointment->patient->uuid)}}">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M11.9998 3C10.343 3 8.99984 4.34315 8.99984 6C8.99984 7.65685 10.343 9 11.9998 9C13.6567 9 14.9998 7.65685 14.9998 6C14.9998 4.34315 13.6567 3 11.9998 3ZM7.49984 6C7.49984 3.51472 9.51456 1.5 11.9998 1.5C14.4851 1.5 16.4998 3.51472 16.4998 6C16.4998 8.48528 14.4851 10.5 11.9998 10.5C9.51456 10.5 7.49984 8.48528 7.49984 6ZM5.27693 19.6409C7.34216 20.5158 9.61373 21 12.0002 21C14.3864 21 16.6577 20.5159 18.7228 19.6412C18.4151 16.1987 15.5226 13.5 11.9998 13.5C8.47721 13.5 5.58472 16.1985 5.27693 19.6409ZM3.75109 20.1053C3.82843 15.6156 7.49183 12 11.9998 12C16.508 12 20.1714 15.6157 20.2486 20.1056C20.2537 20.4034 20.0822 20.676 19.8115 20.8002C17.4326 21.8918 14.7864 22.5 12.0002 22.5C9.2137 22.5 6.56728 21.8917 4.18816 20.7999C3.91749 20.6757 3.74596 20.4031 3.75109 20.1053Z" fill="#0D4D8B"/>
                                </svg>
                                <span class="text-base font-semibold text-brand">Complete Profile</span>
                            </a>
                        @endif
{{--                        @if($appointment['simrs_status'] == \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::COMPLETED)--}}
{{--                            @if($appointment['patient']['is_complete_data'])--}}
{{--                                <div class="border border-brand bg-white flex flex-row items-center justify-center py-3 rounded-xl gap-3 hover:cursor-pointer">--}}
{{--                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">--}}
{{--                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.625 3C5.41789 3 5.25 3.16789 5.25 3.375V20.625C5.25 20.8321 5.41789 21 5.625 21H18.375C18.5821 21 18.75 20.8321 18.75 20.625V11.625C18.75 10.1753 17.5747 9 16.125 9H14.625C13.5895 9 12.75 8.16053 12.75 7.125V5.625C12.75 4.17525 11.5747 3 10.125 3H5.625ZM5.625 1.5C4.58947 1.5 3.75 2.33947 3.75 3.375V20.625C3.75 21.6605 4.58947 22.5 5.625 22.5H18.375C19.4105 22.5 20.25 21.6605 20.25 20.625V11.25C20.25 5.86522 15.8848 1.5 10.5 1.5H5.625ZM13.757 3.66785C14.0715 4.25019 14.25 4.91675 14.25 5.625V7.125C14.25 7.33211 14.4179 7.5 14.625 7.5H16.125C16.8332 7.5 17.4998 7.6785 18.0822 7.99296C17.2488 6.05549 15.6945 4.50123 13.757 3.66785ZM7.5 15C7.5 14.5858 7.83579 14.25 8.25 14.25H15.75C16.1642 14.25 16.5 14.5858 16.5 15C16.5 15.4142 16.1642 15.75 15.75 15.75H8.25C7.83579 15.75 7.5 15.4142 7.5 15ZM7.5 18C7.5 17.5858 7.83579 17.25 8.25 17.25H12C12.4142 17.25 12.75 17.5858 12.75 18C12.75 18.4142 12.4142 18.75 12 18.75H8.25C7.83579 18.75 7.5 18.4142 7.5 18Z" fill="#0D4D8B"/>--}}
{{--                                    </svg>--}}
{{--                                    <span class="text-base font-semibold text-brand">View Medical Resume</span>--}}
{{--                                </div>--}}
{{--                            @endif--}}
{{--                        @endif--}}
                        @if($appointment['payment_status'] == \App\Enums\Table\AppointmentPatientSummary\PaymentStatus::UNPAID && $appointment['type'] == \App\Enums\Table\AppointmentPatientSummary\Type::TELECONSULTATION)
                            <div class="rounded-lg p-2 px-3 flex flex-row bg-[#F2F4F7] justify-between">
                                <div class="flex flex-col gap-1 w-1/2">
                                    <span class="text-sm font-light">Total</span>
                                    <span class="text-lg font-bold text-[#16A34A]">Rp{{str_replace(',', '.', number_format($appointment['amount']))}}</span>
                                </div>
                                <a href="{{$appointment['payment_invoice_url']}}" class="rounded-xl py-[18px] w-1/2 flex flex-row justify-center items-center hover:cursor-pointer bg-brand gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                                        <path d="M11.2499 10.8176V13.4324C11.6816 13.3527 12.0745 13.2046 12.3876 12.9999C12.8698 12.6846 12.9999 12.352 12.9999 12.125C12.9999 11.898 12.8698 11.5654 12.3876 11.2501C12.0745 11.0454 11.6816 10.8973 11.2499 10.8176Z" fill="white"/>
                                        <path d="M8.82961 8.61947C8.88337 8.67543 8.94464 8.73053 9.01404 8.78416C9.22197 8.94484 9.47355 9.06777 9.75 9.1469V6.60315C9.67545 6.62449 9.60271 6.64901 9.53215 6.6766C9.48721 6.69417 9.44315 6.71299 9.40007 6.73302C9.25996 6.79816 9.13019 6.87614 9.01404 6.96589C8.63658 7.25757 8.5 7.59253 8.5 7.87503C8.5 8.05887 8.55784 8.26493 8.70228 8.46683C8.73898 8.51812 8.78126 8.56915 8.82961 8.61947Z" fill="white"/>
                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M18.5 10C18.5 14.4183 14.9183 18 10.5 18C6.08172 18 2.5 14.4183 2.5 10C2.5 5.58172 6.08172 2 10.5 2C14.9183 2 18.5 5.58172 18.5 10ZM10.4999 4C10.9142 4 11.2499 4.33579 11.2499 4.75V5.06584C11.8423 5.17106 12.4182 5.40427 12.9031 5.77893C13.3293 6.10829 13.6467 6.51836 13.8282 6.97896C13.9801 7.36432 13.7908 7.79985 13.4055 7.95174C13.0201 8.10363 12.5846 7.91437 12.4327 7.52901C12.3599 7.34437 12.22 7.14675 11.9859 6.96586C11.778 6.80519 11.5264 6.68225 11.2499 6.60312V9.29944C11.948 9.39233 12.6327 9.61819 13.2085 9.99467C13.9955 10.5093 14.4999 11.2644 14.4999 12.125C14.4999 12.9856 13.9955 13.7407 13.2085 14.2553C12.6327 14.6318 11.948 14.8577 11.2499 14.9506V15.25C11.2499 15.6642 10.9142 16 10.4999 16C10.0857 16 9.74994 15.6642 9.74994 15.25V14.9506C9.05186 14.8577 8.3672 14.6318 7.79141 14.2553C7.30887 13.9398 6.9337 13.5376 6.71337 13.0672C6.5377 12.692 6.69937 12.2455 7.07449 12.0699C7.4496 11.8942 7.89611 12.0559 8.07178 12.431C8.15258 12.6035 8.31692 12.8067 8.61229 12.9999C8.92537 13.2046 9.3183 13.3526 9.74994 13.4324V10.6842C9.15762 10.5789 8.58167 10.3457 8.09681 9.97107C7.40033 9.43288 6.99994 8.68017 6.99994 7.875C6.99994 7.06983 7.40034 6.31712 8.09681 5.77893C8.58167 5.40427 9.15762 5.17106 9.74994 5.06584V4.75C9.74994 4.33579 10.0857 4 10.4999 4Z" fill="white"/>
                                    </svg>
                                    <span class="text-base font-semibold text-white">Pay Now</span>
                                </a>
                            </div>
                        @endif


                        @if($appointment->showComponent(8))
                            <div onclick="onClickUrl('{{$appointment['url_link_teleconsultation']}}')" class="bg-brand flex flex-row items-center justify-center py-3 rounded-xl gap-3 hover:cursor-not-allowed opacity-50">
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                                    <path d="M5 4.5C3.34315 4.5 2 5.84315 2 7.5V16.5C2 18.1569 3.34315 19.5 5 19.5H13.25C14.9069 19.5 16.25 18.1569 16.25 16.5V7.5C16.25 5.84315 14.9069 4.5 13.25 4.5H5Z" fill="white"/>
                                    <path d="M20.4393 18.75L17.75 16.0606V7.93931L20.4393 5.24996C21.3843 4.30501 23 4.97427 23 6.31063V17.6893C23 19.0257 21.3843 19.6949 20.4393 18.75Z" fill="white"/>
                                </svg>
                                <span class="text-base font-semibold text-white">Join Teleconsultation</span>
                            </div>
                        @endif

                        @if($appointment->showComponent(9))
                            <div onclick="onClickUrl('{{$appointment['url_link_teleconsultation']}}')" class="bg-brand flex flex-row items-center justify-center py-3 rounded-xl gap-3 hover:cursor-pointer">
                                <svg xmlns="http://www.w3.org/2000/svg" width="25" height="24" viewBox="0 0 25 24" fill="none">
                                    <path d="M5 4.5C3.34315 4.5 2 5.84315 2 7.5V16.5C2 18.1569 3.34315 19.5 5 19.5H13.25C14.9069 19.5 16.25 18.1569 16.25 16.5V7.5C16.25 5.84315 14.9069 4.5 13.25 4.5H5Z" fill="white"/>
                                    <path d="M20.4393 18.75L17.75 16.0606V7.93931L20.4393 5.24996C21.3843 4.30501 23 4.97427 23 6.31063V17.6893C23 19.0257 21.3843 19.6949 20.4393 18.75Z" fill="white"/>
                                </svg>
                                <span class="text-base font-semibold text-white">Join Teleconsultation</span>
                            </div>
                        @endif

                        @if(in_array($appointment['simrs_status'], \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::cancelableTransaction()))
                            <div class="p-3 text-center border rounded-xl border-[#344054] mt-2 hover:cursor-pointer" data-hs-overlay="#hs-basic-modal">
                                <span class="text-base text-[#344054] font-semibold">Cancel</span>
                            </div>

                            <div id="hs-basic-modal" class="hs-overlay hs-overlay-open:opacity-100 hs-overlay-open:duration-500 hidden size-full fixed top-0 start-0 z-[80] opacity-0 overflow-x-hidden transition-all overflow-y-auto pointer-events-none inset-0 flex justify-center items-center bg-black bg-opacity-50">
                                <div class="sm:max-w-lg sm:w-full m-3 sm:mx-auto">
                                    <div class="flex flex-col bg-white border shadow-sm rounded-xl pointer-events-auto">
                                        <div class="flex justify-between items-center py-3 px-4">
                                            <div></div>
                                            <button type="button" class="flex justify-center items-center size-7 text-sm font-semibold rounded-full border border-transparent text-gray-800 hover:bg-gray-100 disabled:opacity-50 disabled:pointer-events-none" data-hs-overlay="#hs-basic-modal">
                                                <span class="sr-only">Close</span>
                                                <svg class="flex-shrink-0 size-4" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                    <path d="M18 6 6 18"></path>
                                                    <path d="m6 6 12 12"></path>
                                                </svg>
                                            </button>
                                        </div>
{{--                                        <div class="flex flex-col items-center justify-center gap-2 text-center p-6">--}}
{{--                                            <svg xmlns="http://www.w3.org/2000/svg" width="60" height="62" viewBox="0 0 60 62" fill="none">--}}
{{--                                                <g filter="url(#filter0_b_7053_62859)">--}}
{{--                                                    <rect x="0.84668" width="50.9193" height="50.9193" rx="25.4597" fill="url(#paint0_linear_7053_62859)"/>--}}
{{--                                                </g>--}}
{{--                                                <g filter="url(#filter1_b_7053_62859)">--}}
{{--                                                    <rect x="8.23389" y="11.0806" width="50.9193" height="50.9193" rx="25.4597" fill="#FEDF89" fill-opacity="0.6"/>--}}
{{--                                                    <rect x="8.53983" y="11.3865" width="50.3075" height="50.3075" rx="25.1537" stroke="url(#paint1_linear_7053_62859)" stroke-width="0.611895"/>--}}
{{--                                                </g>--}}
{{--                                                <path d="M38.1385 22.8844C38.0627 21.9836 37.7271 21.1354 37.2157 20.4395C36.4338 19.3761 35.2393 18.6679 33.9273 18.6699C32.6541 18.6683 31.4922 19.3346 30.7099 20.345C30.158 21.0578 29.795 21.9421 29.7156 22.8844C29.6945 23.1332 29.6969 23.4029 29.7156 23.6448L31.4593 44.2938C31.5121 44.9171 31.7766 45.4676 32.1725 45.8774C32.6252 46.3458 33.2494 46.6303 33.9273 46.6295C34.5372 46.6303 35.1039 46.3996 35.5407 46.0124C36.0144 45.5928 36.3361 44.9877 36.3949 44.2934L38.1385 23.6444C38.1576 23.4025 38.1596 23.1332 38.1385 22.8844Z" fill="url(#paint2_linear_7053_62859)" fill-opacity="0.9"/>--}}
{{--                                                <path d="M37.5037 51.6287C37.5037 53.4241 36.0474 54.8792 34.2522 54.8792C32.4565 54.8792 31.0015 53.4241 31.0015 51.6287C31.0015 49.8316 32.4565 48.377 34.2522 48.377C36.0474 48.3774 37.5037 49.832 37.5037 51.6287Z" fill="url(#paint3_linear_7053_62859)" fill-opacity="0.9"/>--}}
{{--                                                <defs>--}}
{{--                                                    <filter id="filter0_b_7053_62859" x="-12.2101" y="-13.0567" width="77.0329" height="77.0329" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">--}}
{{--                                                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>--}}
{{--                                                        <feGaussianBlur in="BackgroundImageFix" stdDeviation="6.52837"/>--}}
{{--                                                        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_7053_62859"/>--}}
{{--                                                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_7053_62859" result="shape"/>--}}
{{--                                                    </filter>--}}
{{--                                                    <filter id="filter1_b_7053_62859" x="-4.82285" y="-1.97617" width="77.0329" height="77.0329" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">--}}
{{--                                                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>--}}
{{--                                                        <feGaussianBlur in="BackgroundImageFix" stdDeviation="6.52837"/>--}}
{{--                                                        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_7053_62859"/>--}}
{{--                                                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_7053_62859" result="shape"/>--}}
{{--                                                    </filter>--}}
{{--                                                    <linearGradient id="paint0_linear_7053_62859" x1="15.535" y1="11.1386" x2="54.6049" y2="24.5744" gradientUnits="userSpaceOnUse">--}}
{{--                                                        <stop stop-color="#FEC84B"/>--}}
{{--                                                        <stop offset="1" stop-color="#DC6803"/>--}}
{{--                                                    </linearGradient>--}}
{{--                                                    <linearGradient id="paint1_linear_7053_62859" x1="4.59679" y1="15.7096" x2="76.5498" y2="52.8609" gradientUnits="userSpaceOnUse">--}}
{{--                                                        <stop stop-color="white"/>--}}
{{--                                                        <stop offset="1" stop-color="white" stop-opacity="0"/>--}}
{{--                                                    </linearGradient>--}}
{{--                                                    <linearGradient id="paint2_linear_7053_62859" x1="31.3324" y1="32.7052" x2="39.7348" y2="33.0505" gradientUnits="userSpaceOnUse">--}}
{{--                                                        <stop stop-color="white"/>--}}
{{--                                                        <stop offset="1" stop-color="white" stop-opacity="0"/>--}}
{{--                                                    </linearGradient>--}}
{{--                                                    <linearGradient id="paint3_linear_7053_62859" x1="32.2566" y1="51.641" x2="38.6135" y2="52.5051" gradientUnits="userSpaceOnUse">--}}
{{--                                                        <stop stop-color="white"/>--}}
{{--                                                        <stop offset="1" stop-color="white" stop-opacity="0"/>--}}
{{--                                                    </linearGradient>--}}
{{--                                                </defs>--}}
{{--                                            </svg>--}}
{{--                                            <span class="text-xl font-semibold">Cancel Booking</span>--}}
{{--                                            <span class="text-base font-medium text-[#667085]">Are you sure you would like to cancel this appointment booking?</span>--}}
{{--                                            <div class="flex flex-row gap-4 text-center mt-4">--}}
{{--                                                <a href="{{route('profile.mybook.cancel',['uuid'=>$appointment['uuid'],'type'=>1])}}" class="h-fit bg-brand text-white py-2 px-4 rounded-lg hover:cursor-pointer">--}}
{{--                                                    <span class="text-base">Cancel Booking</span>--}}
{{--                                                </a>--}}
{{--                                                <div class="h-fit border border-brand text-brand py-2 px-4 rounded-lg hover:cursor-pointer" data-hs-overlay="#hs-basic-modal">--}}
{{--                                                    <span class="text-base">Not Now</span>--}}
{{--                                                </div>--}}
{{--                                            </div>--}}
{{--                                        </div>--}}
                                        <div>
                                            <livewire:landing-page.appointment.component.cancel
                                                :packageSummary="@$packageSummary"
                                                :packageSummaryDetail="@$packageSummaryDetail"
                                                :type="$type"
                                                :appointment="$appointment"
                                                wire:key="cancel-{{\Illuminate\Support\Str::uuid()}}"
                                            />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        @if($appointment->simrs_status == \App\Enums\Table\AppointmentPatientSummary\SimrsStatus::COMPLETED)
                            @include('landing-page.component.appointment.download_app')
                        @endif

                        <div class="bg-white flex flex-row items-center justify-center py-3 rounded-xl gap-1 hover:cursor-pointer">
                            <span class="text-base font-light text-brand">Need help?</span>
                            <a href="tel:{{ bih_config_value(\App\Enums\Table\BihConfig\Key::CALL_CENTER,\App\Enums\Table\BihConfig\Group::CONTACT_INFORMATION) }}" class="text-base font-semibold text-brand">Click here</a>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>

@endsection
@push('script')
    <script>
        function copyToClipboard() {
            var code = document.getElementById('code').innerText;
            var copyText = document.createElement('textarea');
            copyText.value = code;
            document.body.appendChild(copyText);
            copyText.select();
            document.execCommand('copy');
            document.body.removeChild(copyText);

            // Show tooltip
            var tooltip = document.getElementById('copy-tooltip');
            tooltip.classList.remove('hidden');
            var currentTooltip = document.getElementById('copy-button');
            currentTooltip.classList.add('hidden');
            setTimeout(function() {
                tooltip.classList.add('hidden');
                currentTooltip.classList.remove('hidden')
            }, 2000); // Hide tooltip after 2 seconds
        }

        function onClickUrl(url){
            // Check if the URL starts with 'http://' or 'https://'
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                // If not, prepend 'http://'
                url = 'http://' + url;
            }

            // Open the URL in a new tab
            window.open(url, '_blank');
        }
    </script>
@endpush
