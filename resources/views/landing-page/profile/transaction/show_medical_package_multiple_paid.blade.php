@section('title', 'Bali International Hospital')
@extends('landing-page.layouts.masterTemplate')

@section('content')
    <div class="h-32">
    </div>
    <div class="rounded-b-3xl relative">
        <div class="absolute mx-auto blur-3xl opacity-50 w-1/2 h-full inset-0 z-1
            bg-gradient-to-b from-green-200 via-cyan-100 to-green-100 rounded-b-3xl"
             style="">
        </div>
        <div class="absolute mx-auto blur-3xl opacity-30 w-1/2 h-full inset-0 z-0
            bg-gradient-to-b from-blue-400 via-cyan-100 to-green-100 rounded-b-3xl"
             style="top: 30%; left: -20%">
        </div>
        <div class="relative z-1">
            <div class="flex flex-row px-4 md:px-40 py-4 gap-4 items-center text-sm md:text-base font-light">
                <a href="/" class="text-primary-blue-60">Home</a>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5"
                          stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <a href="{{route('profile.mybook.index')}}" class="text-blue-800">My Bookings</a>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M5.66675 3.33325L10.3334 7.99992L5.66675 12.6666" stroke="#98A2B3" stroke-width="1.5"
                          stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span class="text-[#667085]">Appointment Details</span>
            </div>
        </div>
    </div>
    <div class="content-center flex flex-col justify-center mx-4 md:mx-40 mt-10 gap-6">
        {{--        @if($appointment['type'] == \App\Enums\Table\AppointmentPatientSummary\Type::VISIT)--}}
        {{--            <div class="flex flex-row items-center gap-2">--}}
        {{--                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">--}}
        {{--                    <path fill-rule="evenodd" clip-rule="evenodd" d="M6.90002 2.3999C7.39708 2.3999 7.80002 2.80285 7.80002 3.2999V4.7999H16.2V3.2999C16.2 2.80285 16.603 2.3999 17.1 2.3999C17.5971 2.3999 18 2.80285 18 3.2999V4.7999H18.3C20.1226 4.7999 21.6 6.27736 21.6 8.0999V18.2999C21.6 20.1224 20.1226 21.5999 18.3 21.5999H5.70002C3.87749 21.5999 2.40002 20.1224 2.40002 18.2999V8.0999C2.40002 6.27736 3.87748 4.7999 5.70002 4.7999H6.00002V3.2999C6.00002 2.80285 6.40297 2.3999 6.90002 2.3999ZM5.70002 8.9999C4.8716 8.9999 4.20002 9.67148 4.20002 10.4999V18.2999C4.20002 19.1283 4.8716 19.7999 5.70003 19.7999H18.3C19.1285 19.7999 19.8 19.1283 19.8 18.2999V10.4999C19.8 9.67148 19.1285 8.9999 18.3 8.9999H5.70002Z" fill="url(#paint0_linear_6339_112385)"/>--}}
        {{--                    <defs>--}}
        {{--                        <linearGradient id="paint0_linear_6339_112385" x1="15.2003" y1="1.99991" x2="8.20025" y2="21.9999" gradientUnits="userSpaceOnUse">--}}
        {{--                            <stop stop-color="#3674B3"/>--}}
        {{--                            <stop offset="1" stop-color="#0B4074"/>--}}
        {{--                        </linearGradient>--}}
        {{--                    </defs>--}}
        {{--                </svg>--}}
        {{--                <span class="text-xl font-medium text-brand">Hospital Visit</span>--}}
        {{--            </div>--}}
        {{--        @else--}}
        {{--            <div class="flex flex-row items-center gap-2">--}}
        {{--                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">--}}
        {{--                    <path d="M4.5 4.5C2.84315 4.5 1.5 5.84315 1.5 7.5V16.5C1.5 18.1569 2.84315 19.5 4.5 19.5H12.75C14.4069 19.5 15.75 18.1569 15.75 16.5V7.5C15.75 5.84315 14.4069 4.5 12.75 4.5H4.5Z" fill="url(#paint0_linear_6439_37246)"/>--}}
        {{--                    <path d="M19.9393 18.75L17.25 16.0606V7.93931L19.9393 5.24996C20.8843 4.30501 22.5 4.97427 22.5 6.31063V17.6893C22.5 19.0257 20.8843 19.6949 19.9393 18.75Z" fill="url(#paint1_linear_6439_37246)"/>--}}
        {{--                    <defs>--}}
        {{--                        <linearGradient id="paint0_linear_6439_37246" x1="15.5003" y1="4.18751" x2="11.3734" y2="20.6948" gradientUnits="userSpaceOnUse">--}}
        {{--                            <stop stop-color="#3674B3"/>--}}
        {{--                            <stop offset="1" stop-color="#0B4074"/>--}}
        {{--                        </linearGradient>--}}
        {{--                        <linearGradient id="paint1_linear_6439_37246" x1="15.5003" y1="4.18751" x2="11.3734" y2="20.6948" gradientUnits="userSpaceOnUse">--}}
        {{--                            <stop stop-color="#3674B3"/>--}}
        {{--                            <stop offset="1" stop-color="#0B4074"/>--}}
        {{--                        </linearGradient>--}}
        {{--                    </defs>--}}
        {{--                </svg>--}}
        {{--                <span class="text-xl font-medium text-brand">Teleconsultation</span>--}}
        {{--            </div>--}}
        {{--        @endif--}}

        <div class="flex flex-col md:flex-row gap-6">
            <div class="md:w-2/3 flex flex-col gap-4">

                @include('landing-page.profile.transaction.component-medical-package.single', ['packageSummaryDetail' => $packageSummaryDetail])

                {{-- payment summary --}}
                <div class="flex flex-col p-6 rounded-3xl bg-gradient-to-br from-blue-25 via-transparent gap-4">
                    <span class="text-lg font-bold text-brand">Payment Summary</span>

                    {{-- payment method section--}}
                    @if(@$packageSummaryDetail->packageSummary->payment_channel)
                        <div class="flex flex-col gap-4 px-4 bg-white py-4 rounded-2xl">
                            <div class="flex flex-row gap-4">
                                <svg width="22" height="18" viewBox="0 0 22 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M3.5 0.75C1.84315 0.75 0.5 2.09315 0.5 3.75V4.5H21.5V3.75C21.5 2.09315 20.1569 0.75 18.5 0.75H3.5Z" fill="#3674B3"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M21.5 6.75H0.5V14.25C0.5 15.9069 1.84315 17.25 3.5 17.25H18.5C20.1569 17.25 21.5 15.9069 21.5 14.25V6.75ZM3.5 10.5C3.5 10.0858 3.83579 9.75 4.25 9.75H10.25C10.6642 9.75 11 10.0858 11 10.5C11 10.9142 10.6642 11.25 10.25 11.25H4.25C3.83579 11.25 3.5 10.9142 3.5 10.5ZM4.25 12.75C3.83579 12.75 3.5 13.0858 3.5 13.5C3.5 13.9142 3.83579 14.25 4.25 14.25H7.25C7.66421 14.25 8 13.9142 8 13.5C8 13.0858 7.66421 12.75 7.25 12.75H4.25Z" fill="#3674B3"/>
                                </svg>
                                <div class="flex flex-col gap-2">
                                    <span class="text-base text-[#1D2939] font-medium">Payment Method</span>
                                    <span class="text-base text-[#667085] font-light">{{ $packageSummaryDetail->packageSummary->payment_method_label ?? 'n/a' }}</span>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="flex flex-col bg-white rounded-xl p-4 gap-4">
                        @foreach($packageSummaryDetail->packageSummary->packageSummaryDetails as $itemPackageSummaryDetail)
                            <div class="flex flex-row justify-between items-center">
                                <span class="text-base font-light linguise_package_name">{{ $itemPackageSummaryDetail->package_title }}</span>
                                <span
                                    class="text-base font-light linguise_package_price">Rp{{str_replace(',', '.', number_format((int)$itemPackageSummaryDetail['package_price']))}}</span>
                            </div>
                        @endforeach
                        <div class="flex flex-row justify-between items-center">
                            <span class="text-base font-light">Total Discount</span>
                            <span
                                class="text-base font-light linguise_package_discount">-Rp{{str_replace(',', '.', number_format($packageSummaryDetail->packageSummary->discount))}}</span>
                        </div>
                        <hr>
                        <div class="flex flex-row justify-between items-center">
                            <span class="text-[18px] font-semibold text-[#0D4D8B]">Total</span>
                            <span
                                class="text-[18px] font-semibold text-[#0D4D8B] linguise_package_price">Rp{{str_replace(',', '.', number_format((int)$packageSummaryDetail->packageSummary['amount_pay']))}}</span>
                        </div>
                    </div>
                </div>

            </div>

            <div class="md:w-2/4 p-6 bg-gradient-to-br bg-blue-50 bg-opacity-40 h-fit rounded-3xl">
                @include('landing-page.profile.transaction.component-medical-package.status.single',['packageSummaryDetail' => $packageSummaryDetail,'type'=>3])
            </div>
        </div>
    </div>

@endsection
@push('script')
    <script>
        function copyToClipboard() {
            var code = document.getElementById('code').innerText;
            var copyText = document.createElement('textarea');
            copyText.value = code;
            document.body.appendChild(copyText);
            copyText.select();
            document.execCommand('copy');
            document.body.removeChild(copyText);

            // Show tooltip
            var tooltip = document.getElementById('copy-tooltip');
            tooltip.classList.remove('hidden');
            var currentTooltip = document.getElementById('copy-button');
            currentTooltip.classList.add('hidden');
            setTimeout(function () {
                tooltip.classList.add('hidden');
                currentTooltip.classList.remove('hidden')
            }, 2000); // Hide tooltip after 2 seconds
        }

        function onClickUrl(url) {
            // Check if the URL starts with 'http://' or 'https://'
            if (!url.startsWith('http://') && !url.startsWith('https://')) {
                // If not, prepend 'http://'
                url = 'http://' + url;
            }

            // Navigate to the specified URL
            window.location.href = url;
        }

        function onHandleStatus(uuid, status) {
            if (status) {
                document.getElementById('more-' + uuid).style.display = 'block';
                document.getElementById('hide-button-' + uuid).style.display = 'block';
                document.getElementById('more-button-' + uuid).style.display = 'none';
            } else {
                document.getElementById('more-' + uuid).style.display = 'none';
                document.getElementById('hide-button-' + uuid).style.display = 'none';
                document.getElementById('more-button-' + uuid).style.display = 'block';
            }
        }
    </script>
@endpush
