<div class="bg-blue-50 bg-opacity-50 p-6 rounded-3xl flex flex-col gap-4">
    <div class="flex gap-2 items-center">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22"
             fill="none">
            <path d="M7.1665 16.1667C7.16651 22 17.0832 22.5833 17.0832 16.1667V11.5"
                  stroke="#0D4D8B" stroke-width="1.1"/>
            <path d="M17.0832 9.73829H17.0949V9.74996H17.0832V9.73829Z" stroke="#0D4D8B"
                  stroke-width="1.1" stroke-linejoin="round"/>
            <path
                d="M18.8332 9.75C18.8332 10.7165 18.0497 11.5 17.0832 11.5C16.1167 11.5 15.3332 10.7165 15.3332 9.75C15.3332 8.7835 16.1167 8 17.0832 8C18.0497 8 18.8332 8.7835 18.8332 9.75Z"
                stroke="#0D4D8B" stroke-width="1.1"/>
            <path
                d="M4.24986 2.16667V2.16667C2.63903 2.16667 1.33319 3.4725 1.33319 5.08333V10.3333C1.33319 13.555 3.94486 16.1667 7.16652 16.1667V16.1667C10.3882 16.1667 12.9999 13.555 12.9999 10.3333V4.5C12.9999 3.21134 11.9552 2.16667 10.6665 2.16667V2.16667M4.24986 2.16667C4.24986 2.811 4.77219 3.33333 5.41652 3.33333C6.06086 3.33333 6.58319 2.811 6.58319 2.16667C6.58319 1.52233 6.06086 1 5.41652 1C4.77219 1 4.24986 1.52233 4.24986 2.16667ZM10.6665 2.16667C10.6665 2.811 10.1442 3.33333 9.49986 3.33333C8.85553 3.33333 8.33319 2.811 8.33319 2.16667C8.33319 1.52233 8.85553 1 9.49986 1C10.1442 1 10.6665 1.52233 10.6665 2.16667Z"
                stroke="#0D4D8B" stroke-width="1.1"/>
        </svg>
        <span class="text-2xl font-medium">Health Packages</span>
    </div>
    <div class="md:p-6 flex flex-col gap-4 rounded-3xl">
        <span class="text-[20px] font-medium">Package Details</span>
        @foreach($packageSummary->packageSummaryDetails as $packageSummaryDetail)
            <div class="flex flex-col p-6 rounded-3xl bg-white gap-2">
                <div class="flex gap-2 items-center">
                    @if(@$packageSummaryDetail->packageType->icon)
                        <img src="{{ asset_gcs($packageSummaryDetail->packageType->icon) }}"
                             alt="icon bithealth" class="h-[20px] w-[20px] object-cover">
                    @else
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="22"
                             viewBox="0 0 20 22" fill="none">
                            <path
                                d="M7.1665 16.1667C7.16651 22 17.0832 22.5833 17.0832 16.1667V11.5"
                                stroke="#0D4D8B" stroke-width="1.1"/>
                            <path d="M17.0832 9.73829H17.0949V9.74996H17.0832V9.73829Z"
                                  stroke="#0D4D8B" stroke-width="1.1" stroke-linejoin="round"/>
                            <path
                                d="M18.8332 9.75C18.8332 10.7165 18.0497 11.5 17.0832 11.5C16.1167 11.5 15.3332 10.7165 15.3332 9.75C15.3332 8.7835 16.1167 8 17.0832 8C18.0497 8 18.8332 8.7835 18.8332 9.75Z"
                                stroke="#0D4D8B" stroke-width="1.1"/>
                            <path
                                d="M4.24986 2.16667V2.16667C2.63903 2.16667 1.33319 3.4725 1.33319 5.08333V10.3333C1.33319 13.555 3.94486 16.1667 7.16652 16.1667V16.1667C10.3882 16.1667 12.9999 13.555 12.9999 10.3333V4.5C12.9999 3.21134 11.9552 2.16667 10.6665 2.16667V2.16667M4.24986 2.16667C4.24986 2.811 4.77219 3.33333 5.41652 3.33333C6.06086 3.33333 6.58319 2.811 6.58319 2.16667C6.58319 1.52233 6.06086 1 5.41652 1C4.77219 1 4.24986 1.52233 4.24986 2.16667ZM10.6665 2.16667C10.6665 2.811 10.1442 3.33333 9.49986 3.33333C8.85553 3.33333 8.33319 2.811 8.33319 2.16667C8.33319 1.52233 8.85553 1 9.49986 1C10.1442 1 10.6665 1.52233 10.6665 2.16667Z"
                                stroke="#0D4D8B" stroke-width="1.1"/>
                        </svg>
                    @endif
                    <span
                        class="text-sm text-[#09335D] linguise_package_type">{{$packageSummaryDetail->package_type_title}}</span>
                </div>
                <div class="flex justify-between items-center">
                                        <span
                                            class="text-[20px] font-medium linguise_package_name">{{$packageSummaryDetail->package_title}}</span>
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                         viewBox="0 0 24 24" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M9.86361 6.2636C10.2151 5.91213 10.7849 5.91213 11.1364 6.2636L16.2364 11.3636C16.5879 11.7151 16.5879 12.2849 16.2364 12.6364L11.1364 17.7364C10.7849 18.0879 10.2151 18.0879 9.86361 17.7364C9.51214 17.3849 9.51214 16.8151 9.86361 16.4636L14.3272 12L9.86361 7.5364C9.51214 7.18492 9.51214 6.61508 9.86361 6.2636Z"
                              fill="#667085"/>
                    </svg>
                </div>
                <span
                    class="text-base font-light linguise_package_price">Rp{{str_replace(',', '.', number_format((int)$packageSummaryDetail['package_price']))}}</span>
                <div>
                    <div class="flex flex-row gap-1 items-center hover:cursor-pointer"
                         onclick="onHandleStatus('{{$packageSummaryDetail->uuid}}',true)"
                         id="more-button-{{$packageSummaryDetail->uuid}}">
                        <span class="text-sm font-medium text-[#0D4D8B]">View Patient</span>
                    </div>
                    <div class="flex flex-row gap-1 items-center hover:cursor-pointer hidden"
                         onclick="onHandleStatus('{{$packageSummaryDetail->uuid}}',false)"
                         id="hide-button-{{$packageSummaryDetail->uuid}}">
                        <span class="text-sm font-medium text-[#0D4D8B]">Hide Patients</span>
                    </div>
                </div>
                <div class="hidden" id="more-{{$packageSummaryDetail->uuid}}">
                    <hr class="-mx-6">
                    <div class="flex flex-row px-4 py-2">
                                            <span class="text-[#667085] text-sm">Registration No <span
                                                    class="text-sm font-medium text-[#344054]">MPX001</span></span>
                    </div>
                    {{-- patient information section--}}
                    <div class="flex flex-row gap-2 py-2 px-4">
                        <div class="w-[26px] h-[26px]">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                 viewBox="0 0 24 24" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M11.9998 3C10.343 3 8.99984 4.34315 8.99984 6C8.99984 7.65685 10.343 9 11.9998 9C13.6567 9 14.9998 7.65685 14.9998 6C14.9998 4.34315 13.6567 3 11.9998 3ZM7.49984 6C7.49984 3.51472 9.51456 1.5 11.9998 1.5C14.4851 1.5 16.4998 3.51472 16.4998 6C16.4998 8.48528 14.4851 10.5 11.9998 10.5C9.51456 10.5 7.49984 8.48528 7.49984 6ZM5.27693 19.6409C7.34216 20.5158 9.61373 21 12.0002 21C14.3864 21 16.6577 20.5159 18.7228 19.6412C18.4151 16.1987 15.5226 13.5 11.9998 13.5C8.47721 13.5 5.58472 16.1985 5.27693 19.6409ZM3.75109 20.1053C3.82843 15.6156 7.49183 12 11.9998 12C16.508 12 20.1714 15.6157 20.2486 20.1056C20.2537 20.4034 20.0822 20.676 19.8115 20.8002C17.4326 21.8918 14.7864 22.5 12.0002 22.5C9.2137 22.5 6.56728 21.8917 4.18816 20.7999C3.91749 20.6757 3.74596 20.4031 3.75109 20.1053Z"
                                      fill="#3674B3"/>
                            </svg>
                        </div>
                        <div class="flex flex-col gap-2">
                                                <span
                                                    class="text-base font-medium text-[#1D2939]">Patient Information</span>
                            <div class="flex flex-col gap-1">
                                <div class="flex gap-1 items-center">
                                    @if($packageSummaryDetail->patient->verified_at)
                                        <svg xmlns="http://www.w3.org/2000/svg" width="20"
                                             height="20" viewBox="0 0 20 20" fill="none">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                  d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z"
                                                  fill="#32D583"/>
                                        </svg>
                                    @endif
                                    <span
                                        class="text-base text-[#344054] font-medium linguise_patient_name">{{ $packageSummaryDetail->patient->fullname }}</span>
                                </div>
                                @if($packageSummaryDetail->patient->dob)
                                    <span
                                        class="text-base text-[#475467]">{{ \Carbon\Carbon::parse($packageSummaryDetail->patient->dob)->format('d F Y') }}</span>
                                @endif
                            </div>
                        </div>
                    </div>

                    {{-- visit date section--}}
                    <div class="flex flex-row gap-2 py-2 px-4">
                        <div class="w-[26px] h-[26px]">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                 viewBox="0 0 24 24" fill="none">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M6.75 2.25C7.16421 2.25 7.5 2.58579 7.5 3V4.5H16.5V3C16.5 2.58579 16.8358 2.25 17.25 2.25C17.6642 2.25 18 2.58579 18 3V4.5H18.75C20.4069 4.5 21.75 5.84315 21.75 7.5V18.75C21.75 20.4069 20.4069 21.75 18.75 21.75H5.25C3.59315 21.75 2.25 20.4069 2.25 18.75V7.5C2.25 5.84315 3.59315 4.5 5.25 4.5H6V3C6 2.58579 6.33579 2.25 6.75 2.25ZM5.25 6C4.42157 6 3.75 6.67157 3.75 7.5V8.65135C4.19126 8.39609 4.70357 8.25 5.25 8.25H18.75C19.2964 8.25 19.8087 8.39609 20.25 8.65135V7.5C20.25 6.67157 19.5784 6 18.75 6H5.25ZM20.25 11.25C20.25 10.4216 19.5784 9.75 18.75 9.75H5.25C4.42157 9.75 3.75 10.4216 3.75 11.25V18.75C3.75 19.5784 4.42157 20.25 5.25 20.25H18.75C19.5784 20.25 20.25 19.5784 20.25 18.75V11.25ZM11.25 12.75C11.25 12.3358 11.5858 12 12 12H12.0075C12.4217 12 12.7575 12.3358 12.7575 12.75V12.7575C12.7575 13.1717 12.4217 13.5075 12.0075 13.5075H12C11.5858 13.5075 11.25 13.1717 11.25 12.7575V12.75ZM13.5 12.75C13.5 12.3358 13.8358 12 14.25 12H14.2575C14.6717 12 15.0075 12.3358 15.0075 12.75V12.7575C15.0075 13.1717 14.6717 13.5075 14.2575 13.5075H14.25C13.8358 13.5075 13.5 13.1717 13.5 12.7575V12.75ZM15.75 12.75C15.75 12.3358 16.0858 12 16.5 12H16.5075C16.9217 12 17.2575 12.3358 17.2575 12.75V12.7575C17.2575 13.1717 16.9217 13.5075 16.5075 13.5075H16.5C16.0858 13.5075 15.75 13.1717 15.75 12.7575V12.75ZM6.75 15C6.75 14.5858 7.08579 14.25 7.5 14.25H7.5075C7.92171 14.25 8.2575 14.5858 8.2575 15V15.0075C8.2575 15.4217 7.92171 15.7575 7.5075 15.7575H7.5C7.08579 15.7575 6.75 15.4217 6.75 15.0075V15ZM9 15C9 14.5858 9.33579 14.25 9.75 14.25H9.7575C10.1717 14.25 10.5075 14.5858 10.5075 15V15.0075C10.5075 15.4217 10.1717 15.7575 9.7575 15.7575H9.75C9.33579 15.7575 9 15.4217 9 15.0075V15ZM11.25 15C11.25 14.5858 11.5858 14.25 12 14.25H12.0075C12.4217 14.25 12.7575 14.5858 12.7575 15V15.0075C12.7575 15.4217 12.4217 15.7575 12.0075 15.7575H12C11.5858 15.7575 11.25 15.4217 11.25 15.0075V15ZM13.5 15C13.5 14.5858 13.8358 14.25 14.25 14.25H14.2575C14.6717 14.25 15.0075 14.5858 15.0075 15V15.0075C15.0075 15.4217 14.6717 15.7575 14.2575 15.7575H14.25C13.8358 15.7575 13.5 15.4217 13.5 15.0075V15ZM15.75 15C15.75 14.5858 16.0858 14.25 16.5 14.25H16.5075C16.9217 14.25 17.2575 14.5858 17.2575 15V15.0075C17.2575 15.4217 16.9217 15.7575 16.5075 15.7575H16.5C16.0858 15.7575 15.75 15.4217 15.75 15.0075V15ZM6.75 17.25C6.75 16.8358 7.08579 16.5 7.5 16.5H7.5075C7.92171 16.5 8.2575 16.8358 8.2575 17.25V17.2575C8.2575 17.6717 7.92171 18.0075 7.5075 18.0075H7.5C7.08579 18.0075 6.75 17.6717 6.75 17.2575V17.25ZM9 17.25C9 16.8358 9.33579 16.5 9.75 16.5H9.7575C10.1717 16.5 10.5075 16.8358 10.5075 17.25V17.2575C10.5075 17.6717 10.1717 18.0075 9.7575 18.0075H9.75C9.33579 18.0075 9 17.6717 9 17.2575V17.25ZM11.25 17.25C11.25 16.8358 11.5858 16.5 12 16.5H12.0075C12.4217 16.5 12.7575 16.8358 12.7575 17.25V17.2575C12.7575 17.6717 12.4217 18.0075 12.0075 18.0075H12C11.5858 18.0075 11.25 17.6717 11.25 17.2575V17.25ZM13.5 17.25C13.5 16.8358 13.8358 16.5 14.25 16.5H14.2575C14.6717 16.5 15.0075 16.8358 15.0075 17.25V17.2575C15.0075 17.6717 14.6717 18.0075 14.2575 18.0075H14.25C13.8358 18.0075 13.5 17.6717 13.5 17.2575V17.25Z"
                                      fill="#3674B3"/>
                            </svg>
                        </div>
                        <div class="flex flex-col gap-2">
                            <span class="text-base font-medium text-[#1D2939]">Visit Date</span>
                            <span
                                class="text-base text-[#344054] font-medium">{{ \Carbon\Carbon::parse($packageSummaryDetail->visit_date)->format('l, d M Y') }}</span>
                        </div>
                    </div>

                    <div class="flex flex-row gap-2 py-2 px-4">
                        <div class="w-[26px] h-[26px]">
                            <div class="w-6 h-6">
                                <svg width="18" height="21" viewBox="0 0 18 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.53975 20.351C8.56995 20.3685 8.59369 20.3821 8.6105 20.3915L8.63843 20.4071C8.8613 20.5294 9.13776 20.5285 9.36084 20.4075L9.3895 20.3915C9.40631 20.3821 9.43005 20.3685 9.46025 20.351C9.52066 20.316 9.60697 20.265 9.7155 20.1982C9.93246 20.0646 10.2388 19.8676 10.6046 19.6091C11.3351 19.0931 12.3097 18.3274 13.2865 17.3273C15.2307 15.3368 17.25 12.3462 17.25 8.5C17.25 3.94365 13.5563 0.25 9 0.25C4.44365 0.25 0.75 3.94365 0.75 8.5C0.75 12.3462 2.76932 15.3368 4.71346 17.3273C5.69025 18.3274 6.66491 19.0931 7.39539 19.6091C7.76125 19.8676 8.06754 20.0646 8.2845 20.1982C8.39303 20.265 8.47934 20.316 8.53975 20.351ZM9 11.5C10.6569 11.5 12 10.1569 12 8.5C12 6.84315 10.6569 5.5 9 5.5C7.34315 5.5 6 6.84315 6 8.5C6 10.1569 7.34315 11.5 9 11.5Z" fill="#3674B3"/>
                                </svg>
                            </div>
                        </div>
                        <div class="flex flex-col gap-2">
                            <span class="text-base font-medium text-[#1D2939]">Consulting Room</span>
                            <span
                                class="text-base text-[#344054] font-medium">Bali International Hospital</span>
                        </div>
                    </div>

                    {{-- attachment section--}}
                    @if($packageSummaryDetail->attachment_url)
                        <div class="flex flex-row gap-2 py-2 px-4">
                            <div class="w-[26px] h-[26px]">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                     viewBox="0 0 24 24" fill="none">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M5.625 3C5.41789 3 5.25 3.16789 5.25 3.375V20.625C5.25 20.8321 5.41789 21 5.625 21H18.375C18.5821 21 18.75 20.8321 18.75 20.625V11.625C18.75 10.1753 17.5747 9 16.125 9H14.625C13.5895 9 12.75 8.16053 12.75 7.125V5.625C12.75 4.17525 11.5747 3 10.125 3H5.625ZM5.625 1.5C4.58947 1.5 3.75 2.33947 3.75 3.375V20.625C3.75 21.6605 4.58947 22.5 5.625 22.5H18.375C19.4105 22.5 20.25 21.6605 20.25 20.625V11.25C20.25 5.86522 15.8848 1.5 10.5 1.5H5.625ZM13.757 3.66785C14.0715 4.25019 14.25 4.91675 14.25 5.625V7.125C14.25 7.33211 14.4179 7.5 14.625 7.5H16.125C16.8332 7.5 17.4998 7.6785 18.0822 7.99296C17.2488 6.05549 15.6945 4.50123 13.757 3.66785ZM7.5 15C7.5 14.5858 7.83579 14.25 8.25 14.25H15.75C16.1642 14.25 16.5 14.5858 16.5 15C16.5 15.4142 16.1642 15.75 15.75 15.75H8.25C7.83579 15.75 7.5 15.4142 7.5 15ZM7.5 18C7.5 17.5858 7.83579 17.25 8.25 17.25H12C12.4142 17.25 12.75 17.5858 12.75 18C12.75 18.4142 12.4142 18.75 12 18.75H8.25C7.83579 18.75 7.5 18.4142 7.5 18Z"
                                          fill="#3674B3"/>
                                </svg>
                            </div>
                            <div class="flex flex-col gap-2">
                                                    <span
                                                        class="text-base font-medium text-[#1D2939]">Attachment File <span
                                                            class="text-[#667085] font-light">(.jpg, .png, .pdf, .zip)</span></span>
                                <a href="{{ route('static-page-gcs', ['path' => $packageSummaryDetail->attachment_url]) }}"
                                   target="_blank"
                                   class="hover:cursor-pointer text-base font-medium text-[#0D4D8B]">
                                    {{ get_file_name_from_path($packageSummaryDetail->attachment_url) }}
                                </a>
                            </div>
                        </div>

                    @endif
                </div>
            </div>
        @endforeach
    </div>

</div>
