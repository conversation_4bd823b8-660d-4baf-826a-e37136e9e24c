<div class="flex gap-2 items-center mb-2">
    @if(@$packageSummaryDetail->packageType->icon)
        <img src="{{ asset_gcs($packageSummaryDetail->packageType->icon) }}" alt="icon bithealth" class="h-[20px] w-[20px] object-cover">
    @else
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none">
            <path d="M7.1665 16.1667C7.16651 22 17.0832 22.5833 17.0832 16.1667V11.5" stroke="#0D4D8B" stroke-width="1.1"/>
            <path d="M17.0832 9.73829H17.0949V9.74996H17.0832V9.73829Z" stroke="#0D4D8B" stroke-width="1.1" stroke-linejoin="round"/>
            <path d="M18.8332 9.75C18.8332 10.7165 18.0497 11.5 17.0832 11.5C16.1167 11.5 15.3332 10.7165 15.3332 9.75C15.3332 8.7835 16.1167 8 17.0832 8C18.0497 8 18.8332 8.7835 18.8332 9.75Z" stroke="#0D4D8B" stroke-width="1.1"/>
            <path d="M4.24986 2.16667V2.16667C2.63903 2.16667 1.33319 3.4725 1.33319 5.08333V10.3333C1.33319 13.555 3.94486 16.1667 7.16652 16.1667V16.1667C10.3882 16.1667 12.9999 13.555 12.9999 10.3333V4.5C12.9999 3.21134 11.9552 2.16667 10.6665 2.16667V2.16667M4.24986 2.16667C4.24986 2.811 4.77219 3.33333 5.41652 3.33333C6.06086 3.33333 6.58319 2.811 6.58319 2.16667C6.58319 1.52233 6.06086 1 5.41652 1C4.77219 1 4.24986 1.52233 4.24986 2.16667ZM10.6665 2.16667C10.6665 2.811 10.1442 3.33333 9.49986 3.33333C8.85553 3.33333 8.33319 2.811 8.33319 2.16667C8.33319 1.52233 8.85553 1 9.49986 1C10.1442 1 10.6665 1.52233 10.6665 2.16667Z" stroke="#0D4D8B" stroke-width="1.1"/>
        </svg>
    @endif
    <span class="text-2xl font-medium linguise_package_type">{{$packageSummaryDetail->packageType->name}}</span>
</div>

<div class="flex flex-col p-6 rounded-3xl bg-gradient-to-br from-blue-25 via-transparent  gap-4">
    <span class="text-lg font-bold text-brand">Patient’s Information</span>
    @if(!$packageSummaryDetail->patient->is_complete_data)
        <div class="flex flex-row">
            <div class="border border-[#B54708] rounded-xl py-3 px-4 bg-[#FFFAEB]">
                <div class="flex flex-row gap-4">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M14.4866 12.0005L9.15329 2.66714C9.037 2.46194 8.86836 2.29127 8.66457 2.17252C8.46078 2.05378 8.22915 1.99121 7.99329 1.99121C7.75743 1.99121 7.52579 2.05378 7.322 2.17252C7.11822 2.29127 6.94958 2.46194 6.83329 2.66714L1.49995 12.0005C1.38241 12.204 1.32077 12.4351 1.32129 12.6701C1.32181 12.9052 1.38447 13.136 1.50292 13.339C1.62136 13.5421 1.79138 13.7102 1.99575 13.8264C2.20011 13.9425 2.43156 14.0026 2.66662 14.0005H13.3333C13.5672 14.0002 13.797 13.9385 13.9995 13.8213C14.202 13.7042 14.3701 13.5359 14.487 13.3332C14.6038 13.1306 14.6653 12.9007 14.6653 12.6668C14.6652 12.4329 14.6036 12.2031 14.4866 12.0005Z" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 6V8.66667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 11.333H8.00667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="flex flex-col text-[#93370D] gap-1">
                        <span class="text-base font-semibold text-[#B54708]">Patient data is still incomplete</span>
                        <span class="">Please complete your profile before the appointment date. This will reduce your waiting time in the hospital.</span>
                    </div>
                </div>
            </div>
        </div>
    @endif
    @if($packageSummaryDetail->showComponentDetail(3))
        <div class="flex flex-row">
            <div class="border border-[#B54708] rounded-xl py-3 px-4 bg-[#FFFAEB]">
                <div class="flex flex-row gap-4">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M14.4866 12.0005L9.15329 2.66714C9.037 2.46194 8.86836 2.29127 8.66457 2.17252C8.46078 2.05378 8.22915 1.99121 7.99329 1.99121C7.75743 1.99121 7.52579 2.05378 7.322 2.17252C7.11822 2.29127 6.94958 2.46194 6.83329 2.66714L1.49995 12.0005C1.38241 12.204 1.32077 12.4351 1.32129 12.6701C1.32181 12.9052 1.38447 13.136 1.50292 13.339C1.62136 13.5421 1.79138 13.7102 1.99575 13.8264C2.20011 13.9425 2.43156 14.0026 2.66662 14.0005H13.3333C13.5672 14.0002 13.797 13.9385 13.9995 13.8213C14.202 13.7042 14.3701 13.5359 14.487 13.3332C14.6038 13.1306 14.6653 12.9007 14.6653 12.6668C14.6652 12.4329 14.6036 12.2031 14.4866 12.0005Z" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 6V8.66667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 11.333H8.00667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="flex flex-col text-[#93370D] gap-1">
                        <span class="text-base font-semibold text-[#B54708]">You will get a refund in 30 business days</span>
                        <span class="">If you have yet to receive your refund, contact support: 1500238.</span>
                    </div>
                </div>
            </div>
        </div>
    @endif
    @if($packageSummaryDetail->showComponentDetail(4))
        <div class="flex flex-row">
            <div class="border border-[#B54708] rounded-xl py-3 px-4 bg-[#FFFAEB]">
                <div class="flex flex-row gap-4">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                            <path d="M14.4866 12.0005L9.15329 2.66714C9.037 2.46194 8.86836 2.29127 8.66457 2.17252C8.46078 2.05378 8.22915 1.99121 7.99329 1.99121C7.75743 1.99121 7.52579 2.05378 7.322 2.17252C7.11822 2.29127 6.94958 2.46194 6.83329 2.66714L1.49995 12.0005C1.38241 12.204 1.32077 12.4351 1.32129 12.6701C1.32181 12.9052 1.38447 13.136 1.50292 13.339C1.62136 13.5421 1.79138 13.7102 1.99575 13.8264C2.20011 13.9425 2.43156 14.0026 2.66662 14.0005H13.3333C13.5672 14.0002 13.797 13.9385 13.9995 13.8213C14.202 13.7042 14.3701 13.5359 14.487 13.3332C14.6038 13.1306 14.6653 12.9007 14.6653 12.6668C14.6652 12.4329 14.6036 12.2031 14.4866 12.0005Z" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 6V8.66667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M8 11.333H8.00667" stroke="#B54708" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="flex flex-col text-[#93370D] gap-1">
                        <span class="text-base font-semibold text-[#B54708]">Did not remember cancelling?:</span>
                        <span class="">Contact support: 150-442. Our team will resolve the issue promptly.</span>
                    </div>
                </div>
            </div>
        </div>
    @endif
    <div class="flex flex-col bg-white p-4 rounded-2xl">
        <div class="flex flex-row gap-4 items-center">
            @if($packageSummaryDetail->patient->image)
                <img class="w-[56px] h-[56px] rounded-full object-cover"
                     src="{{asset_gcs($packageSummaryDetail->patient->image)}}" alt="Image">
            @else
                <svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
                    <rect x="1.25" y="1.25" width="57.5" height="57.5" stroke="white" stroke-width="1.5"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M45.5992 46.5599C50.0017 42.4113 52.75 36.5266 52.75 30C52.75 17.4355 42.5645 7.25 30 7.25C17.4355 7.25 7.25 17.4355 7.25 30C7.25 36.5266 9.99832 42.4113 14.4008 46.5599C18.4736 50.3979 23.9621 52.75 30 52.75C36.0379 52.75 41.5264 50.3979 45.5992 46.5599ZM16.3386 43.562C19.5458 39.5615 24.4737 37 30 37C35.5263 37 40.4542 39.5615 43.6614 43.562C40.174 47.0748 35.341 49.25 30 49.25C24.659 49.25 19.826 47.0748 16.3386 43.562ZM38.75 23C38.75 27.8325 34.8325 31.75 30 31.75C25.1675 31.75 21.25 27.8325 21.25 23C21.25 18.1675 25.1675 14.25 30 14.25C34.8325 14.25 38.75 18.1675 38.75 23Z" fill="#98A2B3"/>
                </svg>
            @endif
            <div class="flex flex-col gap-1">
                <div class="flex gap-1 items-center">
                    @if($packageSummaryDetail->patient->verified_at)
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M16.4032 12.6523C17.353 12.1487 18 11.1499 18 10C18 8.85007 17.353 7.85126 16.4032 7.34771C16.7188 6.32002 16.47 5.15625 15.6569 4.34312C14.8437 3.53 13.68 3.28122 12.6523 3.59679C12.1487 2.64698 11.1499 2 10 2C8.85007 2 7.85125 2.64699 7.3477 3.59681C6.32002 3.28126 5.15627 3.53004 4.34315 4.34316C3.53003 5.15628 3.28125 6.32003 3.5968 7.34771C2.64699 7.85126 2 8.85007 2 10C2 11.1499 2.64699 12.1487 3.59681 12.6523C3.28124 13.68 3.53001 14.8437 4.34314 15.6569C5.15627 16.47 6.32003 16.7188 7.34771 16.4032C7.85126 17.353 8.85007 18 10 18C11.1499 18 12.1488 17.353 12.6523 16.4032C13.68 16.7187 14.8437 16.47 15.6569 15.6568C16.47 14.8437 16.7188 13.68 16.4032 12.6523ZM13.8566 8.19113C14.1002 7.85614 14.0261 7.38708 13.6911 7.14345C13.3561 6.89982 12.8871 6.97388 12.6434 7.30887L9.15969 12.099L7.28033 10.2197C6.98744 9.92678 6.51256 9.92678 6.21967 10.2197C5.92678 10.5126 5.92678 10.9874 6.21967 11.2803L8.71967 13.7803C8.87477 13.9354 9.08999 14.0149 9.30867 13.9977C9.52734 13.9805 9.72754 13.8685 9.85655 13.6911L13.8566 8.19113Z" fill="#32D583"/>
                        </svg>
                    @endif
                    <span class="text-base font-light text-[#344054] linguise_patient_name">{{ $packageSummaryDetail->patient->fullname }}</span>
                    <span class="text-base font-light text-[#344054] {{ $packageSummaryDetail->patient->relation_patient ? '' : 'hidden' }}">({{\App\Enums\Table\Patient\RelationPatient::getLabel($packageSummaryDetail->patient->relation_patient)}})</span>
                </div>
                @if(@$packageSummaryDetail->patient->mr_no)
                    <div class="flex flex-row gap-1 text-[#667085] text-sm font-light">
                        <span>MR ID: </span>
                        <span>{{ @$packageSummaryDetail->patient->mr_no }}</span>
                    </div>
                @endif
                <span class="text-sm text-[#475467]">{{ \Carbon\Carbon::parse($packageSummaryDetail->patient->dob)->format('d F Y') }}</span>
                <span class="text-sm text-[#475467]">{{ $packageSummaryDetail->patient->contact_with_code }}</span>
            </div>
        </div>
    </div>
</div>

<div class="flex flex-col p-6 rounded-3xl bg-gradient-to-br from-blue-25 via-transparent gap-4">
    <span class="text-lg font-bold text-brand linguise_package_type">{{$packageSummaryDetail->packageType->name}} Details</span>
    <div class="flex flex-col gap-4 px-4 bg-white py-4 rounded-2xl">
        <div class="flex flex-row gap-4">
            @if(@$packageSummaryDetail->packageType->icon)
                <img src="{{ asset_gcs($packageSummaryDetail->packageType->icon) }}" alt="icon bithealth" class="h-[20px] w-[20px] object-cover">
            @else
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none">
                    <path d="M7.1665 16.1667C7.16651 22 17.0832 22.5833 17.0832 16.1667V11.5" stroke="#0D4D8B" stroke-width="1.1"/>
                    <path d="M17.0832 9.73829H17.0949V9.74996H17.0832V9.73829Z" stroke="#0D4D8B" stroke-width="1.1" stroke-linejoin="round"/>
                    <path d="M18.8332 9.75C18.8332 10.7165 18.0497 11.5 17.0832 11.5C16.1167 11.5 15.3332 10.7165 15.3332 9.75C15.3332 8.7835 16.1167 8 17.0832 8C18.0497 8 18.8332 8.7835 18.8332 9.75Z" stroke="#0D4D8B" stroke-width="1.1"/>
                    <path d="M4.24986 2.16667V2.16667C2.63903 2.16667 1.33319 3.4725 1.33319 5.08333V10.3333C1.33319 13.555 3.94486 16.1667 7.16652 16.1667V16.1667C10.3882 16.1667 12.9999 13.555 12.9999 10.3333V4.5C12.9999 3.21134 11.9552 2.16667 10.6665 2.16667V2.16667M4.24986 2.16667C4.24986 2.811 4.77219 3.33333 5.41652 3.33333C6.06086 3.33333 6.58319 2.811 6.58319 2.16667C6.58319 1.52233 6.06086 1 5.41652 1C4.77219 1 4.24986 1.52233 4.24986 2.16667ZM10.6665 2.16667C10.6665 2.811 10.1442 3.33333 9.49986 3.33333C8.85553 3.33333 8.33319 2.811 8.33319 2.16667C8.33319 1.52233 8.85553 1 9.49986 1C10.1442 1 10.6665 1.52233 10.6665 2.16667Z" stroke="#0D4D8B" stroke-width="1.1"/>
                </svg>
            @endif
            <div class="flex flex-col gap-2">
                <span class="text-base text-[#1D2939] font-medium linguise_package_name">{{$packageSummaryDetail->package->title}}</span>
                <span class="text-base text-[#667085] font-light linguise_package_category">{{ $packageSummaryDetail->packageCategory->name }}</span>
            </div>
        </div>

        <livewire:landing-page.profile.transaction.component.reschedule-medical-package :packageSummaryDetail="$packageSummaryDetail" wireKey="reschedule-{{\Illuminate\Support\Str::uuid()}}"/>

        <div class="flex flex-row gap-4">
            <div class="w-6 h-6">
                <svg width="18" height="21" viewBox="0 0 18 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8.53975 20.351C8.56995 20.3685 8.59369 20.3821 8.6105 20.3915L8.63843 20.4071C8.8613 20.5294 9.13776 20.5285 9.36084 20.4075L9.3895 20.3915C9.40631 20.3821 9.43005 20.3685 9.46025 20.351C9.52066 20.316 9.60697 20.265 9.7155 20.1982C9.93246 20.0646 10.2388 19.8676 10.6046 19.6091C11.3351 19.0931 12.3097 18.3274 13.2865 17.3273C15.2307 15.3368 17.25 12.3462 17.25 8.5C17.25 3.94365 13.5563 0.25 9 0.25C4.44365 0.25 0.75 3.94365 0.75 8.5C0.75 12.3462 2.76932 15.3368 4.71346 17.3273C5.69025 18.3274 6.66491 19.0931 7.39539 19.6091C7.76125 19.8676 8.06754 20.0646 8.2845 20.1982C8.39303 20.265 8.47934 20.316 8.53975 20.351ZM9 11.5C10.6569 11.5 12 10.1569 12 8.5C12 6.84315 10.6569 5.5 9 5.5C7.34315 5.5 6 6.84315 6 8.5C6 10.1569 7.34315 11.5 9 11.5Z" fill="#3674B3"/>
                </svg>
            </div>
            <div class="flex flex-col gap-2">
                <span class="text-base text-[#1D2939] font-medium">Consulting Room</span>
                <span class="text-base text-[#667085] font-medium">Bali International Hospital</span>
            </div>
        </div>

        @if($packageSummaryDetail->attachment_url)
            <div class="flex flex-row gap-4">
                <div class="w-6 h-6">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M5.625 1.5C4.58947 1.5 3.75 2.33947 3.75 3.375V20.625C3.75 21.6605 4.58947 22.5 5.625 22.5H18.375C19.4105 22.5 20.25 21.6605 20.25 20.625V12.75C20.25 10.6789 18.5711 9 16.5 9H14.625C13.5895 9 12.75 8.16053 12.75 7.125V5.25C12.75 3.17893 11.0711 1.5 9 1.5H5.625ZM7.5 15C7.5 14.5858 7.83579 14.25 8.25 14.25H15.75C16.1642 14.25 16.5 14.5858 16.5 15C16.5 15.4142 16.1642 15.75 15.75 15.75H8.25C7.83579 15.75 7.5 15.4142 7.5 15ZM8.25 17.25C7.83579 17.25 7.5 17.5858 7.5 18C7.5 18.4142 7.83579 18.75 8.25 18.75H12C12.4142 18.75 12.75 18.4142 12.75 18C12.75 17.5858 12.4142 17.25 12 17.25H8.25Z" fill="#3674B3"/>
                        <path d="M12.9712 1.8159C13.768 2.73648 14.25 3.93695 14.25 5.25V7.125C14.25 7.33211 14.4179 7.5 14.625 7.5H16.5C17.8131 7.5 19.0135 7.98204 19.9341 8.77881C19.0462 5.37988 16.3701 2.70377 12.9712 1.8159Z" fill="#3674B3"/>
                    </svg>
                </div>
                <div class="flex flex-col gap-2">
                    <span class="text-base text-[#1D2939] font-medium">Attachment File (.jpg, .png, .pdf, .zip)</span>
                    <a href="{{ route('static-page-gcs', ['path' => $packageSummaryDetail->attachment_url]) }}"
                       target="_blank" class="hover:cursor-pointer text-base font-medium text-[#0D4D8B]">{{ get_file_name_from_path($packageSummaryDetail->attachment_url) }}</a>
                </div>
            </div>
        @endif

    </div>
</div>
