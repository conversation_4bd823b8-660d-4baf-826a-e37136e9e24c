<?php

use App\Http\Controllers\LandingPage\Career\CareerController;
use Illuminate\Support\Facades\Route;


Route::resource('registration', \App\Http\Controllers\LandingPage\Registration\RegistrationController::class)->except([
    'edit',
    'destroy',
    'update',
    'show',
    'create'
]);

Route::get('/about-us', [\App\Http\Controllers\LandingPage\AboutUs\AboutUsController::class, 'index'])->name('aboutUs.index');
Route::get('/contact-us', [\App\Http\Controllers\LandingPage\ContactUs\ContactUsController::class, 'index'])->name('contactUs.index');
Route::get('/gallery', [\App\Http\Controllers\LandingPage\Homepage\HomepageController::class, 'gallery'])->name('gallery.index');
Route::get('/our-technologies', [\App\Http\Controllers\LandingPage\Homepage\HomepageController::class, 'ourTechnology'])->name('our-technology.index');
Route::get('terms-and-conditions', [\App\Http\Controllers\LandingPage\Homepage\HomepageController::class, 'termAndCondition'])->name('tnc.index');
Route::get('privacy-policy', [\App\Http\Controllers\LandingPage\Homepage\HomepageController::class, 'privacyPolicy'])->name('privacy-policy.index');
Route::get('cookie-policy', [\App\Http\Controllers\LandingPage\Homepage\HomepageController::class, 'cookiePolicy'])->name('cookie-policy.index');
Route::get('faq', [\App\Http\Controllers\LandingPage\Homepage\HomepageController::class, 'faq'])->name('faq.index');
// Route::get('nearest-hotels', [\App\Http\Controllers\LandingPage\Homepage\HomepageController::class, 'nearestHotel'])->name('nearest-hotel.index');
Route::get('shops-services', [\App\Http\Controllers\LandingPage\Homepage\HomepageController::class, 'fnb'])->name('fnb.index');
Route::get('/ihc-hospitals', \App\Http\Controllers\LandingPage\IHC\IHCController::class)->name('ihc.index');
Route::get('/ihc-profile', [\App\Http\Controllers\LandingPage\IHCProfile\IHCProfileController::class, 'index'])->name('ihcProfile.index');
Route::get('/outpatient-general-consent', [\App\Http\Controllers\LandingPage\Homepage\HomepageController::class, 'outpatientGeneralConsent'])->name('outpatient-general-consent.index');
Route::get('/career', CareerController::class)->name('careers.index');

Route::get('/insurance', [\App\Http\Controllers\LandingPage\Partnership\PartnershipController::class, 'index'])->name('partnership.index');

// Route::group(['prefix' => 'patient-centered-care'], function () {
Route::get('/center-of-excellence', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'centerOfExcellence'])
    ->name('patientCenteredCare.center-of-excellence');

Route::group(['prefix' => 'center-of-excellence'], function () {
    Route::get('/cardiovascular-centre', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'centerOfExcellenceCardiovascularCenter'])
        ->name('patientCenteredCare.coe-detail.cardiovascular-center');
    Route::get('/oncology', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'centerOfExcellenceOncology'])
        ->name('patientCenteredCare.coe-detail.oncology');
    Route::get('/neurology', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'centerOfExcellenceNeurology'])
        ->name('patientCenteredCare.coe-detail.neurology');
    Route::get('/gastroenterology', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'centerOfExcellenceGastroenterology'])
        ->name('patientCenteredCare.coe-detail.gastroenterology');
    Route::get('/orthopedics', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'centerOfExcellenceOrthopedics'])
        ->name('patientCenteredCare.coe-detail.orthopedics');
    Route::get('/pediatric-woman', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'centerOfExcellencePediatricWoman'])
        ->name('patientCenteredCare.coe-detail.pediatric-woman');
    Route::get('/diagnostic-center', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'centerOfExcellenceDiagnosticCenter'])
        ->name('patientCenteredCare.coe-detail.diagnostic-center');
});
// });



Route::get('/segara-integrated-clinic', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'centerOfExcellenceIntegrativeClinics'])
    ->name('patientCenteredCare.coe-detail.integrative-clinics');
// Route::get('/aurora-international-patient-service', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'auroraInternationalPatientService'])
//     ->name('patientCenteredCare.aurora-international-patient-service');

Route::get('/patient-privilege', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'patientPrivilege'])
    ->name('patientCenteredCare.patient-privilege');

Route::get('/healthcare-at-home', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'healthcareAtHome'])
    ->name('patientCenteredCare.healthcare-at-home');

Route::get('/inpatient-rooms', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'inpatientRooms'])
    ->name('patientCenteredCare.inpatient-rooms');

// Route::get('/insurance', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'insurance'])
//     ->name('patientCenteredCare.insurance');

Route::get('/emergency-medical-services-ems', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'emergencyDepartment'])
    ->name('patientCenteredCare.emergency-medical-services-ems');

Route::get('/icon-cancer-centre', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'iconCancerCentre'])
    ->name('patientCenteredCare.icon-cancer-centre');

Route::get('/innoquest', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'innoquest'])
    ->name('patientCenteredCare.innoquest');

//Route::get('/sapporo-cardiovascular-clinic-scvc', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'SapporoCardiovascularClinicSCVC'])->name('patientCenteredCare.sapporo-cardiovascular-clinic-scvc');

Route::get('/medicine-inquiries', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'medicineInquiries'])
    ->name('patientCenteredCare.medicine-inquiries');

Route::get('/air-ground-ambulance-services', [\App\Http\Controllers\LandingPage\PatientCenteredCare\PatientCenteredCareController::class, 'ambulanceServices'])
    ->name('patientCenteredCare.air-ground-ambulance-services');

Route::group(['prefix' => 'medical-travel'], function () {
    Route::get('/medical-tourism', [\App\Http\Controllers\LandingPage\MedicalTravel\MedicalTravelController::class, 'medicalTourism'])
        ->name('medicalTravel.medical-tourism');

    Route::get('/medical-travel-guide', [\App\Http\Controllers\LandingPage\MedicalTravel\MedicalTravelController::class, 'medicalTravelGuide'])
        ->name('medicalTravel.medical-travel-guide');

    Route::get('/medical-coordination-office', [\App\Http\Controllers\LandingPage\MedicalTravel\MedicalTravelController::class, 'medicalCoordinationOffice'])
        ->name('medicalTravel.medical-coordination-office');

    Route::get('/medical-services-cost-financing-information', [\App\Http\Controllers\LandingPage\MedicalTravel\MedicalTravelController::class, 'medicalServicesCostFinancingInformation'])
        ->name('medicalTravel.medical-services-cost-financing-information');

    Route::get('/fast-track-pay-services', [\App\Http\Controllers\LandingPage\MedicalTravel\MedicalTravelController::class, 'fastTrackPayServices'])
        ->name('medicalTravel.fast-track-pay-services');

    Route::get('/associated-health-insurance-companies', [\App\Http\Controllers\LandingPage\MedicalTravel\MedicalTravelController::class, 'associatedHealthInsuranceCompanies'])
        ->name('medicalTravel.associated-health-insurance-companies');
});

Route::group(['prefix' => 'promos'], function () {
    Route::get('/', [\App\Http\Controllers\Promo\PromoController::class, 'index'])->name('promos.index');
    Route::get('/{slug}', [\App\Http\Controllers\Promo\PromoController::class, 'show'])->name('promos.show');
});

Route::group(['prefix' => 'articles'], function () {
    Route::get('/', [\App\Http\Controllers\LandingPage\Article\ArticleController::class, 'index'])->name('articles.index');
    Route::get('/{slug}', [\App\Http\Controllers\LandingPage\Article\ArticleController::class, 'show'])->name('articles.show')->middleware('count.view');
});

Route::group(['prefix' => 'training'], function () {
    Route::get('/', \App\Http\Controllers\LandingPage\Training\TrainingController::class)->name('training.index');
    Route::get('/{slug}', [\App\Http\Controllers\LandingPage\Training\TrainingController::class, 'show'])->name('training.show')->middleware('count.view');
});


Route::get('doctors', \App\Livewire\LandingPage\Appointment\Index::class)->name('doctors.index');
Route::group(['middleware' => 'auth.public_user'], function () {
    Route::group(['prefix' => 'appointments'], function () {
        Route::get('book/{uuid}/{doctor_uuid}/{type}', \App\Http\Controllers\LandingPage\Appointment\BookController::class)->name('appointments.book');
        //        Route::get('book/{uuid}/{doctor_uuid}/{type}', \App\Livewire\LandingPage\Appointment\Book::class)->name('appointments.book');
    });
});
//Route::get('/doctors/{uuid}', \App\Livewire\LandingPage\Doctor\ShowDoctor::class)->name('doctors.show');
Route::get('/doctors/{uuid}/{name}', \App\Http\Controllers\LandingPage\Doctor\ShowDoctorController::class)->name('doctors.show')->middleware('count.view');

Route::group(['middleware' => ['web', 'auth.public'], 'prefix' => 'profile'], function () {
    Route::get('/', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'index'])->name('profile.index');
    Route::get('/detail/{uuid}', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'show'])->name('profile.show');
    Route::post('/add/basic', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'storeBasic'])->name('profile.store.basic');
    Route::post('/add/advanced', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'storeAdvanced'])->name('profile.store.advanced');
    Route::post('/add/address', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'storeAddress'])->name('profile.store.address');
    Route::get('/destroy/{id}', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::get('/detail/{uuid}/ajax', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'getDetail'])->name('profile.detail');
    Route::post('/upload/photo', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'uploadPhotoProfile'])->name('profile.upload.photo');
    Route::post('/delete/photo', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'deletePhotoProfile'])->name('profile.delete.photo');
    Route::post('/upload/document', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'uploadIdentityDocument'])->name('profile.upload.document');
    Route::post('/delete/document', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'deleteIdentityDocument'])->name('profile.delete.document');

    Route::get('/my-bookings', [\App\Http\Controllers\LandingPage\Profile\MyBookingController::class, 'index'])->name('profile.mybook.index');
    Route::get('/my-bookings/{uuid}/{type}', [\App\Http\Controllers\LandingPage\Profile\MyBookingController::class, 'show'])->name('profile.mybook.show');
    Route::get('/my-bookings/cancel/{uuid}/{type}', [\App\Http\Controllers\LandingPage\Profile\MyBookingController::class, 'cancel'])->name('profile.mybook.cancel');

    Route::get('geo-location/cities/{province_code}', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'getCityByProvinceCode'])
        ->name('profile.geoLocation.cities');
    Route::get('geo-location/city-areas/{city_code}', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'getCityAreaByCityCode'])
        ->name('profile.geoLocation.city_areas');
    Route::get('geo-location/postal-codes/{province_code}/{city_code}/{city_area_code}', [\App\Http\Controllers\LandingPage\Profile\ProfileController::class, 'getPostalCodeByRelationCodes'])
        ->name('profile.geoLocation.postal_codes');
    Route::get('check-cart', \App\Http\Controllers\LandingPage\CheckCartController::class)->name('check-cart');
});

Route::group(['prefix' => 'medical-packages'], function () {
    Route::get('/type/{slug_type}', [\App\Http\Controllers\LandingPage\MedicalPackage\MedicalPackageController::class, 'type'])->name('medical_packages.type');
    Route::get('/category/{slug_category}', [\App\Http\Controllers\LandingPage\MedicalPackage\MedicalPackageController::class, 'category'])->name('medical_packages.category');
    Route::get('/detail/{slug_category}/{slug_package}', [\App\Http\Controllers\LandingPage\MedicalPackage\MedicalPackageController::class, 'show'])->name('medical_packages.show')->middleware('count.view');
    Route::get('success/{uuid}', [\App\Http\Controllers\LandingPage\MedicalPackage\MedicalPackageController::class, 'success'])->name('medical_packages.success');
    Route::get('send-inquiry/{uuid}', [\App\Http\Controllers\LandingPage\InquiryRequest\InquiryRequestController::class, 'sendInquiry'])->name('medical_packages.send_inquiry');
    Route::group(['middleware' => ['auth.public_user']], function () {
        Route::get('/checkout-package/{package_uuid}', [\App\Http\Controllers\LandingPage\MedicalPackage\MedicalPackageController::class, 'checkoutPackage'])->name('medical_packages.checkout_package');
        Route::get('/checkout/{uuid}', [\App\Http\Controllers\LandingPage\MedicalPackage\MedicalPackageController::class, 'checkout'])->name('medical_packages.checkout');
        Route::get('/carts/store/{slug_package}', [\App\Http\Controllers\LandingPage\MedicalPackage\MedicalPackageController::class, 'addToCart'])->name('medical_packages.carts.store');
        Route::get('/carts/delete/{slug_package}', [\App\Http\Controllers\LandingPage\MedicalPackage\MedicalPackageController::class, 'deleteFromCart'])->name('medical_packages.carts.destroy');
    });
});

Route::group(['prefix' => 'ajax'], function () {
    Route::get('/package-types', \App\Http\Controllers\LandingPage\Ajax\GetPackageTypeAjaxController::class)->name('ajax.package_types');
    Route::get('/package-types/{uuid}', \App\Http\Controllers\LandingPage\Ajax\MedicalPackage\GetDetailPackageTypeAjaxController::class)->name('ajax.package_types.show');
});

Route::get('/pre-visit', \App\Http\Controllers\PreVisitController::class);
Route::get('/static-page-gcs', \App\Http\Controllers\LandingPage\StaticPagePathGCSController::class)->name('static-page-gcs');
Route::get('/playground/cipher', \App\Http\Controllers\Playground\CipherController::class)->name('playground.cipher');
Route::get('/playground/email-view', \App\Http\Controllers\Playground\EmailViewController::class);
Route::get('/playground/email-view-reminder-appointment', [\App\Http\Controllers\Playground\EmailViewController::class, 'reminderAppointment']);
Route::get('/playground/email-view-reminder-medical-package', [\App\Http\Controllers\Playground\EmailViewController::class, 'reminderMedicalPackage']);
Route::get('/playground/email-sync-data', [\App\Http\Controllers\Playground\EmailViewController::class, 'emailSycnData']);
Route::get('/playground/email-view-purchase-confirmed', [\App\Http\Controllers\Playground\EmailViewController::class, 'purchaseConfirmed']);
Route::get('/playground/email-view-try-send', [\App\Http\Controllers\Playground\EmailViewController::class, 'trysend']);

// Redirect old url to new url
Route::get('/patient-centered-care/center-of-excellence', function () {
    return redirect()->route('patientCenteredCare.center-of-excellence');
});
Route::get('/our-partner', function () {
    return redirect()->route('landing-page.homepage');
});