<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::group(['middleware' => 'jwt.verify'], function () {
    Route::get('/playground/user', [\App\Http\Controllers\Playground\LoginApiController::class, 'user']);
    Route::post('/api-test-invoice', [\App\Http\Controllers\ApiTestInvoice::class, 'testAPI']);
    
    Route::group([
        'prefix' => 'public'
    ], function () {
        Route::get('/invoice-history', [\App\Http\Controllers\InvoiceHistoryController::class, 'InvoiceHistory']);
        Route::get('/list_patient', [\App\Http\Controllers\Api\Profile\ProfileController::class, 'getListPatient']);
        Route::post('/add_patient_data_basic', [\App\Http\Controllers\Api\Profile\ProfileController::class, 'addPatientBasicInfo']);
        Route::get('/get_patient_option_menu', [\App\Http\Controllers\Api\Profile\ProfileController::class, 'getPatientOptionMenu']);
        Route::post('/add_patient_data_advance', [\App\Http\Controllers\Api\Profile\ProfileController::class, 'addPatientAdvancedInfo']);
        Route::post('/add_patient_data_address', [\App\Http\Controllers\Api\Profile\ProfileController::class, 'addPatientAddressInfo']);
        Route::get('/get_patient_detail/{id}', [\App\Http\Controllers\Api\Profile\ProfileController::class, 'getPatientDetail']);
        Route::put('/profiles/update/fcm', \App\Http\Controllers\Api\Profile\UpdateFCMTokenController::class);
        Route::delete('/profiles/patients/{patient_id}', [\App\Http\Controllers\Api\Profile\ProfileController::class,'deletePatient']);
    });

    Route::group([
        'prefix' => 'public/appointment'
    ], function () {
        Route::post('store', [\App\Http\Controllers\Api\Appointment\AppointmentController::class, 'store']);
        Route::get('insurance', [\App\Http\Controllers\Api\Appointment\AppointmentController::class, 'get_insurances']);
        Route::get('companies', [\App\Http\Controllers\Api\Appointment\AppointmentController::class, 'get_companies']);
        Route::get('find/{id}', [\App\Http\Controllers\Api\Appointment\AppointmentController::class, 'find']);
        Route::get('list', [\App\Http\Controllers\Api\Appointment\AppointmentController::class, 'list']);
        Route::get('visit_journey_histories', \App\Http\Controllers\Api\Appointment\GetVisitJourneyController::class);
        // Route::post('cancel', [\App\Http\Controllers\Api\Appointment\AppointmentController::class, 'cancel']);
        // Route::post('reschedule', [\App\Http\Controllers\Api\Appointment\AppointmentController::class, 'reschedule']);
        Route::post('date-available', [\App\Http\Controllers\Api\Appointment\AppointmentController::class, 'date_available']);
//        Route::post('time-available', [\App\Http\Controllers\Api\Appointment\AppointmentController::class, 'time_available']);
        Route::get('time-available/{equipment_id}/{date}', [\App\Http\Controllers\Api\Appointment\AppointmentController::class, 'time_available_mcu']);
        Route::post('reschedule', [\App\Http\Controllers\Api\Appointment\AppointmentController::class, 'reschedule']);
    });

    Route::group([
        'prefix' => 'public/my_booking'
    ], function () {
        Route::post('cancel', [\App\Http\Controllers\Api\MyBooking\BookingController::class, 'cancel']);
        Route::post('data', [\App\Http\Controllers\Api\MyBooking\BookingController::class, 'data']);
        Route::post('detail', [\App\Http\Controllers\Api\MyBooking\BookingController::class, 'detail']);
        Route::post('getTeleUrl', [\App\Http\Controllers\Api\MyBooking\BookingController::class, 'getUrlLink']);
        Route::get('parameters', [\App\Http\Controllers\Api\MyBooking\BookingController::class, 'parameters']);
        Route::get('reason', [\App\Http\Controllers\Api\MyBooking\BookingController::class, 'cancelReason']);
    });

    Route::group([
        'prefix' => 'public/medical-resume'
    ], function () {
        Route::post('request-otp', \App\Http\Controllers\Api\Otp\ResumeRequestOtpController::class);
        Route::post('verify-otp', \App\Http\Controllers\Api\Otp\ResumeVerifyOtpController::class);
        Route::get('/patients/{uuid}', [\App\Http\Controllers\Api\MedicalResume\DetailPatientController::class, 'show']);
        Route::get('/episodes/{id}', [\App\Http\Controllers\Api\MedicalResume\DetailPatientController::class, 'detailEpisode']);
    });
});

Route::group([
    'prefix' => 'public'
], function () {
    Route::post('pre-registration', \App\Http\Controllers\Api\Ajax\PreRegisterApiController::class)->name('pre-registration');
    Route::post('registration', \App\Http\Controllers\Api\Authentication\RegistrationController::class);
    Route::post('otps/request', \App\Http\Controllers\Api\Otp\RequestOtpController::class);
    Route::post('otps/verify', \App\Http\Controllers\Api\Otp\VerifyOtpController::class);
    Route::post('login', \App\Http\Controllers\Api\Authentication\LoginController::class);
    Route::get('/resource', \App\Http\Controllers\Api\ResourceController::class);
    Route::get('/home', [\App\Http\Controllers\Api\HomeController::class, 'index']);
    Route::get('/home/<USER>/{user_id}', [\App\Http\Controllers\Api\HomeController::class, 'cart']); //->middleware('jwt.verify');
    Route::get('/account/{user_id}', [\App\Http\Controllers\Api\HomeController::class, 'account']);
    Route::get('logout', \App\Http\Controllers\Api\Authentication\LogoutController::class);

    Route::get('/list_country', [\App\Http\Controllers\Api\Location\LocationController::class, 'getCountries']);
    Route::get('/list_province', [\App\Http\Controllers\Api\Location\LocationController::class, 'getProvince']);
    Route::get('/list_city/{province_code}', [\App\Http\Controllers\Api\Location\LocationController::class, 'getCities']);
    Route::get('/list_city_area/{city_code}', [\App\Http\Controllers\Api\Location\LocationController::class, 'getDistricts']);
    Route::post('/list_postal_code', [\App\Http\Controllers\Api\Location\LocationController::class, 'getPostalCodes']);
    Route::get('list_gender', [\App\Http\Controllers\Api\Gender\GenderController::class, 'getGenders']);
    Route::get('get_visit_journey/{appointment_id}', [\App\Http\Controllers\GetVisitJourneyController::class, 'get_visit_journey']);

    Route::group([
        'prefix' => 'gcs'
    ], function () {
        Route::get('list', [\App\Http\Controllers\Api\GCS\GoogleCloudController::class, 'list']);
        Route::post('store', [\App\Http\Controllers\Api\GCS\GoogleCloudController::class, 'store']);
        Route::get('find/{id}', [\App\Http\Controllers\Api\GCS\GoogleCloudController::class, 'find']);
        Route::delete('delete/{id}', [\App\Http\Controllers\Api\GCS\GoogleCloudController::class, 'delete']);
    });


    Route::group([
        'prefix' => 'doctor'
    ], function () {
        Route::post('search/clinical_condition',  [\App\Http\Controllers\Api\Doctor\DoctorController::class, 'search_clinical_condition']);
        Route::post('search',  [\App\Http\Controllers\Api\Doctor\DoctorController::class, 'search']);
        Route::get('search/param',  [\App\Http\Controllers\Api\Doctor\DoctorController::class, 'search_param']);
        Route::get('find/{id}',  [\App\Http\Controllers\Api\Doctor\DoctorController::class, 'find']);
        Route::post('search/schedule',  [\App\Http\Controllers\Api\Doctor\DoctorController::class, 'search_schedule']);
        Route::get('search/schedule_daily/{id}/{date}',  [\App\Http\Controllers\Api\Doctor\DoctorController::class, 'search_daily_schedule']);
    });
});

Route::post('/webhook/assessment', [\App\Http\Controllers\Playground\PlaygroundController::class, 'assessment']);
Route::get('/form-assessment/result-id', [\App\Http\Controllers\FormAssessmentController::class, 'assessmentResultId']);

Route::post('/xendit/webhook/invoice', \App\Http\Controllers\Api\Webhook\Xendit\HandleWebhookInvoiceController::class);
Route::post('playground/simulation', \App\Http\Controllers\Playground\SimulationController::class);
Route::post('playground/package-conclusion', [\App\Http\Controllers\Playground\PlaygroundController::class,'packageConclusion']);
Route::post('playground/simrs-sync-patient', [\App\Http\Controllers\Playground\PlaygroundController::class,'syncPatient']);
Route::post('playground/fcm-notification', \App\Http\Controllers\Playground\FCMNotificationController::class);
Route::get('/playground/sync', function () {
    \Illuminate\Support\Facades\Artisan::call('app:sync-doctor');
    \Illuminate\Support\Facades\Artisan::call('app:sync-specialty');
    \Illuminate\Support\Facades\Artisan::call('app:sync-package-type-category');
    return 'Queue worker started!';
});
