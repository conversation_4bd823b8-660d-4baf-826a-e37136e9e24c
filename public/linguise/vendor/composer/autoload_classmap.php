<?php

// autoload_classmap.php @generated by Composer

$vendorDir = dirname(__DIR__);
$baseDir = dirname($vendorDir);

return array(
    'Composer\\InstalledVersions' => $vendorDir . '/composer/InstalledVersions.php',
    'Linguise\\Script\\Core\\AfterUpdate' => $baseDir . '/src/AfterUpdate.php',
    'Linguise\\Script\\Core\\Boundary' => $baseDir . '/src/Boundary.php',
    'Linguise\\Script\\Core\\Cache' => $baseDir . '/src/Cache.php',
    'Linguise\\Script\\Core\\Certificates' => $baseDir . '/src/Certificates.php',
    'Linguise\\Script\\Core\\CmsDetect' => $baseDir . '/src/CmsDetect.php',
    'Linguise\\Script\\Core\\Configuration' => $baseDir . '/src/Configuration.php',
    'Linguise\\Script\\Core\\CurlMulti' => $baseDir . '/src/CurlMulti.php',
    'Linguise\\Script\\Core\\CurlRequest' => $baseDir . '/src/CurlRequest.php',
    'Linguise\\Script\\Core\\Database' => $baseDir . '/src/Database.php',
    'Linguise\\Script\\Core\\Databases\\Mysql' => $baseDir . '/src/Databases/Mysql.php',
    'Linguise\\Script\\Core\\Databases\\Sqlite' => $baseDir . '/src/Databases/Sqlite.php',
    'Linguise\\Script\\Core\\Debug' => $baseDir . '/src/Debug.php',
    'Linguise\\Script\\Core\\Defer' => $baseDir . '/src/Defer.php',
    'Linguise\\Script\\Core\\Helper' => $baseDir . '/src/Helper.php',
    'Linguise\\Script\\Core\\Hook' => $baseDir . '/src/Hook.php',
    'Linguise\\Script\\Core\\JsonWalker' => $baseDir . '/src/JsonWalker.php',
    'Linguise\\Script\\Core\\Management' => $baseDir . '/src/Management.php',
    'Linguise\\Script\\Core\\Platforms\\OpenCart' => $baseDir . '/src/Platforms/OpenCart.php',
    'Linguise\\Script\\Core\\Platforms\\PrestaShop' => $baseDir . '/src/Platforms/PrestaShop.php',
    'Linguise\\Script\\Core\\Platforms\\Zencart' => $baseDir . '/src/Platforms/Zencart.php',
    'Linguise\\Script\\Core\\Processor' => $baseDir . '/src/Processor.php',
    'Linguise\\Script\\Core\\Request' => $baseDir . '/src/Request.php',
    'Linguise\\Script\\Core\\Response' => $baseDir . '/src/Response.php',
    'Linguise\\Script\\Core\\Session' => $baseDir . '/src/Session.php',
    'Linguise\\Script\\Core\\SetCookie' => $baseDir . '/src/SetCookie.php',
    'Linguise\\Script\\Core\\Translation' => $baseDir . '/src/Translation.php',
    'Linguise\\Script\\Core\\Updater' => $baseDir . '/src/Updater.php',
    'Linguise\\Script\\Core\\Url' => $baseDir . '/src/Url.php',
);
