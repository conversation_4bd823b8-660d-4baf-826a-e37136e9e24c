<?php

use Linguise\Script\Core\CmsDetect;
use Linguise\Script\Core\Configuration;
use Linguise\Script\Core\Hook;
use Linguise\Script\Core\Management;

define('LINGUISE_SCRIPT_TRANSLATION', true);
define('LINGUISE_SCRIPT_TRANSLATION_VERSION', '1.3.22');

ini_set('display_errors', true);

require_once('./vendor/autoload.php');

if (file_exists(__DIR__ . DIRECTORY_SEPARATOR . 'Configuration.php')) {
    require(__DIR__ . DIRECTORY_SEPARATOR . 'Configuration.php');
    foreach (get_class_vars(\Linguise\Script\Configuration::class) as $attribute_name => $attribute_value) {
        Configuration::getInstance()->set($attribute_name, $attribute_value);
    }
    foreach (get_class_methods(\Linguise\Script\Configuration::class) as $hook) {
        if (strpos($hook, 'on') !== 0) {
            continue;
        }
        Hook::add($hook, \Linguise\Script\Configuration::class);
    }
}

Configuration::getInstance()->set('base_dir', realpath(__DIR__ . DIRECTORY_SEPARATOR . '..') . DIRECTORY_SEPARATOR);
$detected_cms = CmsDetect::detect();
Configuration::getInstance()->set('cms', $detected_cms);

if (!defined('LINGUISE_BASE_DIR')) {
    define('LINGUISE_BASE_DIR', realpath(__DIR__) . DIRECTORY_SEPARATOR);
}

$management = Management::getInstance();

if (file_exists(__DIR__ . DIRECTORY_SEPARATOR . 'ui-config.php')) {
    require_once (__DIR__ . DIRECTORY_SEPARATOR . 'ui-config.php');
    $management->mergeConfig();
} else {
    // Doesn't exist, let's create it

    $login_secret = bin2hex(random_bytes(32));
    $metadata = <<<EOT
<?php
// This file is generated by Linguise. Do not edit it manually.
// You can edit the configuration by visiting your domain followed by zz-zz
// Example: https://example.com/zz-zz

// Ensure this file is not directly accessed
defined('LINGUISE_SCRIPT_TRANSLATION') or die();

define('LINGUISE_OOBE_DONE', false);
define('LINGUISE_LOGIN_SECRET', '$login_secret');
EOT;

    // Check if token is set
    $cur_token = Configuration::getInstance()->get('token');
    if (!empty($cur_token) && $cur_token !== 'REPLACE_BY_YOUR_TOKEN') {
        $metadata .= "\ndefine('LINGUISE_OOBE_TOKEN_EXIST', true);\n";
    } else {
        $metadata .= "\ndefine('LINGUISE_OOBE_TOKEN_EXIST', false);\n";
    }

    file_put_contents(__DIR__ . DIRECTORY_SEPARATOR . 'ui-config.php', $metadata);
    require_once (__DIR__ . DIRECTORY_SEPARATOR . 'ui-config.php');
}

if (in_array($_SERVER['REQUEST_METHOD'], array('GET')) && isset($_GET['linguise_language']) && $_GET['linguise_language'] === 'zz-zz') {
    // Run the UI management
    if (defined('LINGUISE_OOBE_DONE') && LINGUISE_OOBE_DONE) {
        $management->run();
    } else {
        $management->oobeRun();
    }
} elseif (isset($_GET['linguise_language']) && $_GET['linguise_language'] === 'zz-zz' &&  isset($_GET['linguise_action']) && $_GET['linguise_action'] === 'update') {
    $processor = new \Linguise\Script\Core\Processor();
    $processor->update();
} elseif (in_array($_SERVER['REQUEST_METHOD'], array('POST', 'HEAD')) && isset($_GET['linguise_language']) && $_GET['linguise_language'] === 'zz-zz' && isset($_GET['linguise_action'])) {
    switch ($_GET['linguise_action']) {
        case 'clear-cache':
            $processor = new \Linguise\Script\Core\Processor();
            $processor->clearCache();
            break;
        case 'clear-cache-all':
            $management->clearCache();
            break;
        case 'clear-debug':
            $management->clearDebug();
            break;
        case 'update-certificates':
            $processor = new \Linguise\Script\Core\Processor();
            $processor->updateCertificates();
            break;
        case 'update-config':
            $management->updateConfig();
            break;
        case 'update-config-iframe':
            $management->updateConfigIframe();
            break;
        case 'activate-linguise':
            $management->activateLinguise();
            break;
        case 'remote-update':
            $management->remoteUpdate();
            break;
        case 'test-connection':
            $management->storeDatabaseConnection(true);
            break;
        case 'logout':
            $management->logout();
            break;
    }
} elseif (in_array($_SERVER['REQUEST_METHOD'], array('POST', 'HEAD')) && isset($_GET['linguise_language']) && $_GET['linguise_language'] === 'zz-zz' && isset($_POST['linguise_action'])) {
    // some post method
    switch ($_POST['linguise_action']) {
        case 'update-config':
            $management->updateConfig();
            break;
        case 'update-config-iframe':
            $management->updateConfigIframe();
            break;
        case 'login':
            $management->login();
            break;
    }
} elseif ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_GET['linguise_language']) && $_GET['linguise_language'] === 'zz-zz') {
    $management->editorRun();
} else {
    $processor = new \Linguise\Script\Core\Processor();
    $processor->run();
}
