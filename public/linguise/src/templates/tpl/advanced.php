<?php

use Linguise\Script\Core\Database;
use Linguise\Script\Core\Templates\Helper as AdminHelper;

defined('LINGUISE_MANAGEMENT') or die('No access to this page.');
defined('LINGUISE_AUTHORIZED') or die('Access denied.');

$options = Database::getInstance()->retrieveOtherParam('linguise_options');
$has_api_key = !empty($options['token']);

$translation_strings = [
    'performance' => __('Caching & Performance', 'linguise'),
    'cache' => [
        'title' => __('Use cache', 'linguise'),
        'help' => __('Store URLs and some translated content in a local cache to render the pages faster', 'linguise'),
        'usage_disk' => __('Maximum cache disk space usage', 'linguise'),
        'usage_size' => __('in MB', 'linguise'),
        'clear' => __('Clear cache', 'linguise'),
    ],

    'translation_extra' => __('Translation Settings', 'linguise'),
    'redirect' => [ // XXX: unused right now
        'title' => __('Browser Language Redirect', 'linguise'),
        'help' => __('Automatically redirect users based on the browser language. The user will still be able to change the language manually but this is NOT recommended as users may use various browser languages or speak several languages', 'linguise'),
    ],
    'searches' => [
        'title' => __('Translate searches', 'linguise'),
        'help' => __('Enable search translation. Visitors will be able to search in their language. (This option can increase a lot your translation quota)', 'linguise'),
    ],
    'dynamic' => [
        'title' => __('Dynamic translations', 'linguise'),
        'help' => __('Translate dynamic content generated by JavaScript or AJAX request (This option can increase a lot your translation quota)', 'linguise'),
        'warning_1' => __('Note that this feature also need to be enabled in %s since it\'s not synchronized!', 'linguise'),
        'warning_2' => __('Please update the domain configuration from the dashboard and save the plugin settings again, thanks! :)', 'linguise'),
        'dashboard' => __('Linguise dashboard', 'linguise'),
    ],

    'popup_text' => __('Popup Text Customization', 'linguise'),
    'pre_text' => [
        'title' => __('Pre-text in language popup', 'linguise'),
        'help' => __('Add some text before the language switcher content in the popup view. HTML is also OK', 'linguise'),
    ],
    'post_text' => [
        'title' => __('Post-text in language popup', 'linguise'),
        'help' => __('Add some text after the language switcher content in the popup view. HTML is also OK', 'linguise'),
    ],
    'placeholder_popup' => __('Type your text (you can use HTML)', 'linguise'),

    'advanced' => __('Advanced Settings', 'linguise'),
    'custom_css' => [
        'title' => __('Custom CSS', 'linguise'),
        'help' => __('Add custom CSS to apply on the Linguise language switcher', 'linguise'),
    ],
    'debug' => [
        'title' => __('Enable debug', 'linguise'),
        'help' => __('Use for debugging purpose only. It will create a file with a log of content. Only enable it if you need it and only for a limited time', 'linguise'),
        'download' => __('Download debug file', 'linguise'),
        'clear' => __('Clear debug file', 'linguise'),
    ],
    'alternate_link' => [ // XXX: unused right now
        'title' => __('Insert alternate link tag', 'linguise'),
        'help' => __('Add an alternate link to your site to improve SEO. This will help search engines understand the relationship between different languages and improve your site\'s visibility in search results.', 'linguise'),
        'note' => __('It\'s highly recommended keeping this setting activated for SEO purpose', 'linguise'),
    ],
];

$merged_dynamic_warning = sprintf(
    $translation_strings['dynamic']['warning_1'],
    '<a href="https://dashboard.linguise.com/" class="linguise-link" target="_blank" rel="noopener noreferrer">'. esc_html($translation_strings['dynamic']['dashboard']) .'</a>'
);
$merged_dynamic_warning .= '<br />' . $translation_strings['dynamic']['warning_2'];

$debug_file = LINGUISE_BASE_DIR . 'debug.php';
$errors_file = LINGUISE_BASE_DIR . 'errors.php';

$last_errors = null;
if (file_exists($errors_file)) {
    $last_errors = file_get_contents($errors_file);
    $log_lines = explode("\n", $last_errors);
    if (count($log_lines) >= 1) {
        array_shift($log_lines);
    }
    $last_errors = implode("\n", $log_lines);
}

?>

<div class="tab-linguise-options">
    <!-- [BLOCK] Caching & Performance -->
    <div class="linguise-options full-width<?php echo $has_api_key ? '' : ' is-disabled'; ?>">
        <div class="disabled-warning-inset"></div>
        <div class="disabled-warning">
            <h2 class="disabled-warning-text">
                <?php echo esc_html($translation_strings_root['settings-hidden']['banner']); ?>
            </h2>
        </div>
        <h2 class="m-0 text-2xl font-bold text-black">
            <?php echo esc_html($translation_strings['performance']); ?>
        </h2>
        <div class="flex flex-col mt-4 linguise-inner-options">
            <div>
                <label class="linguise-slider-checkbox">
                    <input type="checkbox" class="slider-input" name="linguise_options[cache_enabled]" value="1" <?php echo isset($options['cache_enabled']) ? (AdminHelper::checked($options['cache_enabled'], 1)) : (''); ?> />
                    <span class="slider"></span>
                    <span class="slider-label font-semibold">
                        <?php echo esc_html($translation_strings['cache']['title']); ?>
                        <span class="material-icons help-tooltip" data-tippy="<?php echo esc_attr($translation_strings['cache']['help']); ?>">
                            help_outline
                        </span>
                    </span>
                </label>
            </div>
            <div data-id="cache-wrapper">
                <div class="flex flex-col mt-2">
                    <label for="opt-cache-max-size" class="m-0 text-base text-neutral">
                        <?php echo esc_html($translation_strings['cache']['usage_disk']); ?>
                        <small class="text-muted">(<?php echo esc_html($translation_strings['cache']['usage_size']); ?>)</small>
                    </label>
                    <input id="opt-cache-max-size" type="number" class="linguise-input rounder mt-1" name="linguise_options[cache_max_size]" value="<?php echo esc_attr((int)$options['cache_max_size']); ?>" min="0" max="1000" step="1" style="width: 7rem;" data-linguise-int="cache_max_size" />
                </div>
                <div class="mt-4">
                    <button type="button" class="linguise-btn rounder" data-linguise-action="clear-cache" data-action-link="<?php echo esc_url(make_nonce_url('clear-cache-all', 'linguise_clear_cache')); ?>">
                        <?php echo esc_html($translation_strings['cache']['clear']); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- [BLOCK] Translation settings -->
    <div class="linguise-options full-width<?php echo $has_api_key ? '' : ' is-disabled'; ?>">
        <div class="disabled-warning-inset"></div>
        <div class="disabled-warning">
            <h2 class="disabled-warning-text">
                <?php echo esc_html($translation_strings_root['settings-hidden']['banner']); ?>
            </h2>
        </div>
        <h2 class="m-0 text-2xl font-bold text-black">
            <?php echo esc_html($translation_strings['translation_extra']); ?>
        </h2>
        <div class="flex flex-col mt-4 gap-2 linguise-inner-options">
            <label class="linguise-slider-checkbox">
                <input type="checkbox" class="slider-input" name="linguise_options[search_translation]" value="1" <?php echo isset($options['search_translation']) ? (AdminHelper::checked($options['search_translation'], 1)) : (''); ?> />
                <span class="slider"></span>
                <span class="slider-label font-semibold">
                    <?php echo esc_html($translation_strings['searches']['title']); ?>
                    <span class="material-icons help-tooltip" data-tippy="<?php echo esc_attr($translation_strings['searches']['help']); ?>">
                        help_outline
                    </span>
                </span>
            </label>
            <label class="linguise-slider-checkbox">
                <input type="checkbox" class="slider-input" name="linguise_options[dynamic_translations]" value="1" <?php echo isset($options['dynamic_translations']['enabled']) ? (AdminHelper::checked($options['dynamic_translations']['enabled'], 1)) : (''); ?> />
                <span class="slider"></span>
                <span class="slider-label font-semibold">
                    <?php echo esc_html($translation_strings['dynamic']['title']); ?>
                    <span class="material-icons help-tooltip" data-tippy="<?php echo esc_attr($translation_strings['dynamic']['help']); ?>">
                        help_outline
                    </span>
                </span>
            </label>
            <?php echo AdminHelper::renderAdmonition($merged_dynamic_warning, 'warning', [
                'id' => 'linguise-dynamic-warning',
                'hide' => true,
            ]); ?>
        </div>
    </div>
    <!-- [BLOCK] Popup text -->
    <div class="linguise-options full-width<?php echo $has_api_key ? '' : ' is-disabled'; ?>">
        <div class="disabled-warning-inset"></div>
        <div class="disabled-warning">
            <h2 class="disabled-warning-text">
                <?php echo esc_html($translation_strings_root['settings-hidden']['banner']); ?>
            </h2>
        </div>
        <h2 class="m-0 text-2xl font-bold text-black">
            <?php echo esc_html($translation_strings['popup_text']); ?>
        </h2>
        <div class="flex flex-col mt-4 gap-3 linguise-inner-options">
            <div>
                <label for="popup-pre-text" class="text-base text-neutral">
                    <?php echo esc_html($translation_strings['pre_text']['title']); ?>
                    <span class="material-icons help-tooltip" data-tippy="<?php echo esc_attr($translation_strings['pre_text']['help']); ?>">
                        help_outline
                    </span>
                </label>
                <textarea id="popup-pre-text" name="linguise_options[pre_text]" placeholder="<?php echo esc_attr($translation_strings['placeholder_popup']); ?>" class="linguise-input min-h-text rounder mt-2 w-full"><?php echo esc_html($options['pre_text']); ?></textarea>
            </div>
            <div>
                <label for="popup-post-text" class="text-base text-neutral">
                    <?php echo esc_html($translation_strings['post_text']['title']); ?>
                    <span class="material-icons help-tooltip" data-tippy="<?php echo esc_attr($translation_strings['post_text']['help']); ?>">
                        help_outline
                    </span>
                </label>
                <textarea id="popup-post-text" name="linguise_options[post_text]" placeholder="<?php echo esc_attr($translation_strings['placeholder_popup']); ?>" class="linguise-input min-h-text rounder mt-2 w-full"><?php echo esc_html($options['post_text']); ?></textarea>
            </div>
        </div>
    </div>
    <!-- [BLOCK] Advanced settings -->
    <div class="linguise-options full-width<?php echo $has_api_key ? '' : ' is-disabled'; ?>">
        <div class="disabled-warning-inset"></div>
        <div class="disabled-warning">
            <h2 class="disabled-warning-text">
                <?php echo esc_html($translation_strings_root['settings-hidden']['banner']); ?>
            </h2>
        </div>
        <h2 class="m-0 text-2xl font-bold text-black">
            <?php echo esc_html($translation_strings['advanced']); ?>
        </h2>
        <div class="flex flex-col mt-4 gap-3 linguise-inner-options">
            <div class="flex flex-col w-full gap-2">
                <label class="text-base text-neutral">
                    <?php echo esc_html($translation_strings['custom_css']['title']); ?>
                    <span class="material-icons help-tooltip" data-tippy="<?php echo esc_attr($translation_strings['custom_css']['help']); ?>">
                        help_outline
                    </span>
                </label>
                <textarea name="linguise_options[custom_css]" class="linguise-input min-h-text rounder mt-2 w-full" novalidate><?php echo esc_html($options['custom_css']); ?></textarea>
            </div>
            <div class="flex flex-row items-center">
                <label class="linguise-slider-checkbox">
                    <input type="checkbox" class="slider-input" name="linguise_options[debug]" value="1" <?php echo isset($options['debug']) ? (AdminHelper::checked($options['debug'], 1)) : (''); ?> />
                    <span class="slider"></span>
                    <span class="slider-label font-semibold">
                        <?php echo esc_html($translation_strings['debug']['title']); ?>
                        <span class="material-icons help-tooltip" data-tippy="<?php echo esc_attr($translation_strings['debug']['help']); ?>">
                            help_outline
                        </span>
                    </span>
                </label>
                <?php if (file_exists($debug_file)) { ?>
                    <a href="<?php echo esc_url(make_action_url('download-debug')); ?>" class="linguise-link ml-2" target="_blank">
                        <?php echo esc_html($translation_strings['debug']['download']); ?>
                    </a>
                    <?php $truncate_url = esc_url(make_nonce_url('clear-debug', 'linguise_clear_debug')); ?>
                    <a href="<?php echo esc_url($truncate_url); ?>" class="linguise-link ml-2" data-linguise-action="clear-debug">
                        <?php echo esc_html($translation_strings['debug']['clear']); ?>
                    </a>
                <?php } ?>
            </div>
            <?php if (!empty($last_errors)) { ?>
                <div>
                    <textarea class="linguise-input min-h-text last-log" rows="5" readonly><?php echo esc_html($last_errors); ?></textarea>
                </div>
            <?php } ?>
        </div>
    </div>
    <script type="text/javascript">
        "use strict";

        (() => {
            const hideUnhide = () => {
                const checkboxDynTranslation = document.querySelector('input[name="linguise_options[dynamic_translations]"]');
                const dynamicWarning = jQuery('#linguise-dynamic-warning');

                checkboxDynTranslation.addEventListener('change', () => {
                    dynamicWarning.fadeIn(1000);
                });
            }

            if (document.readyState !== 'loading') {
                hideUnhide();
            } else {
                document.addEventListener('DOMContentLoaded', hideUnhide);
            }
        })();
    </script>
</div>