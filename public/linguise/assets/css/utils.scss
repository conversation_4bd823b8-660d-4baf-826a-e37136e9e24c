.p-0 {
    padding: 0;
}
.p-1 {
    padding: 0.25rem;
}
.p-2 {
    padding: 0.5rem;
}
.p-3 {
    padding: 0.75rem;
}
.p-4 {
    padding: 1rem;
}

.px-0 {
    padding-left: 0;
    padding-right: 0;
}
.px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
}
.px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}
.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}
.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.py-0 {
    padding-top: 0;
    padding-bottom: 0;
}
.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}
.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}
.py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
}
.py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.pt-0 {
    padding-top: 0;
}
.pt-1 {
    padding-top: 0.25rem;
}
.pt-2 {
    padding-top: 0.5rem;
}
.pt-3 {
    padding-top: 0.75rem;
}
.pt-4 {
    padding-top: 1rem;
}

.pb-0 {
    padding-bottom: 0;
}
.pb-1 {
    padding-bottom: 0.25rem;
}
.pb-2 {
    padding-bottom: 0.5rem;
}
.pb-3 {
    padding-bottom: 0.75rem;
}
.pb-4 {
    padding-bottom: 1rem;
}

.pl-0 {
    padding-left: 0;
}
.pl-1 {
    padding-left: 0.25rem;
}
.pl-2 {
    padding-left: 0.5rem;
}
.pl-3 {
    padding-left: 0.75rem;
}
.pl-4 {
    padding-left: 1rem;
}

.pr-0 {
    padding-right: 0;
}
.pr-1 {
    padding-right: 0.25rem;
}
.pr-2 {
    padding-right: 0.5rem;
}
.pr-3 {
    padding-right: 0.75rem;
}
.pr-4 {
    padding-right: 1rem;
}

.w-full {
    width: 100%;
}
.mw-full {
    max-width: 100%;
}
.mw-fit-content {
    max-width: fit-content;
}

.align-middle {
    vertical-align: middle;
}
.align-bottom {
    vertical-align: bottom;
}

.m-0 {
    margin: 0;
}
.\!m-0 {
    margin: 0 !important;
}

.my-0 {
    margin-top: 0;
    margin-bottom: 0;
}
.my-1 {
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;
}
.my-2 {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}
.my-3 {
    margin-top: 0.75rem;
    margin-bottom: 0.75rem;
}
.my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.mt-0 {
    margin-top: 0;
}
.mt-1 {
    margin-top: 0.25rem;
}
.mt-2 {
    margin-top: 0.5rem;
}
.mt-3 {
    margin-top: 0.75rem;
}
.mt-4 {
    margin-top: 1rem;
}

.ml-0 {
    margin-left: 0;
}
.ml-1 {
    margin-left: 0.25rem;
}
.ml-2 {
    margin-left: 0.5rem;
}
.ml-3 {
    margin-left: 0.75rem;
}
.ml-4 {
    margin-left: 1rem;
}

.mb-0 {
    margin-bottom: 0;
}
.mb-1 {
    margin-bottom: 0.25rem;
}
.mb-2 {
    margin-bottom: 0.5rem;
}
.mb-3 {
    margin-bottom: 0.75rem;
}
.mb-4 {
    margin-bottom: 1rem;
}

.flex {
    display: flex;
}
.flex-col {
    flex-direction: column;
}
.flex-row {
    flex-direction: row;
}
.flex-wrap {
    flex-wrap: wrap;
}

.gap-0 {
    gap: 0;
}
.gap-1 {
    gap: 0.25rem;
}
.gap-2 {
    gap: 0.5rem;
}
.gap-3 {
    gap: 0.75rem;
}
.gap-4 {
    gap: 1rem;
}

.font-medium {
    font-weight: 500;
}
.font-semibold {
    font-weight: 600;
}
.font-bold {
    font-weight: 700;
}

.font-italic {
    font-style: italic;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}
.text-base {
    font-size: 1rem;
    line-height: 1.5rem;
}
.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}
.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}
.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}

.items-center {
    align-items: center;
}
.justify-between {
    justify-content: space-between;
}
.justify-center {
    justify-content: center;
}
.justify-end {
    justify-content: flex-end;
}

.no-select {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
}

.box-border {
    box-sizing: border-box;
}
.box-content {
    box-sizing: content-box;
}

.overflow-auto {
    overflow: auto;
}

.text-center {
    text-align: center;
}

.hidden {
    display: none;
}

.break-all-words {
    word-break: break-all;
    white-space: pre-wrap;
    overflow-wrap: break-word;
}