
.linguise-btn {
    background-color: $primary-500;
    color: $white;

    border: none;
    border-radius: 4px;
    padding: 0.75rem 1rem;
    cursor: pointer;

    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    text-decoration: none;

    /* text-transform: uppercase; */
    /* letter-spacing: 0.05em; */
    transition: background-color 0.3s ease, box-shadow 0.3s ease;

    &.btn-sm {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }

    &:hover,
    &:active {
        color: $white;
        background-color: $primary-700;
    }

    &:focus {
        // add ring
        outline: none;
        box-shadow: 0 0 0 4px $primary-100;
    }

    &.disabled,
    &:disabled,
    &[aria-disabled="true"] {
        cursor: not-allowed;
        color: $white;
        background-color: $neutral-150;
    }

    &.disabled,
    &[aria-disabled="true"] {
        pointer-events: none;
    }

    &.pill {
        border-radius: 9999px;
    }

    &.rounder {
        border-radius: 8px;

        &.btn-sm {
            border-radius: 6px;
        }
    }

    &.sharp {
        border-radius: 0px;
    }
    
    &.danger {
        background-color: $red-500;
        color: $white;

        &:hover,
        &:active {
            color: $white;
            background-color: $red-700;
        }

        &:focus {
            // add ring
            outline: none;
            box-shadow: 0 0 0 4px $red-100;
        }

        &:disabled {
            cursor: not-allowed;
            background-color: $neutral-150;
        }
    }

    &.success {
        background-color: $green-500;
        color: $white;

        &:hover,
        &:active {
            color: $white;
            background-color: $green-700;
        }

        &:focus {
            // add ring
            outline: none;
            box-shadow: 0 0 0 4px $green-100;
        }

        &:disabled {
            cursor: not-allowed;
            background-color: $neutral-150;
        }
    }

    &.outlined {
        background-color: transparent;
        color: $primary-500;
        border: 2px solid $primary-500;

        &:hover,
        &:active {
            background-color: $primary-10;
            color: $primary-500;
        }

        &:focus {
            // add ring
            outline: none;
            box-shadow: 0 0 0 4px $primary-100;
        }

        &:disabled {
            cursor: not-allowed;
            background-color: $neutral-20;
            color: $white;
            border-color: $neutral-300;
        }

        &.danger {
            border-color: $red-500;
            color: $red-500;

            &:hover {
                background-color: $red-10;
                color: $red-500;
            }

            &:focus {
                // add ring
                outline: none;
                box-shadow: 0 0 0 4px $red-100;
            }

            &:disabled {
                cursor: not-allowed;
                background-color: $neutral-20;
                color: $white;
                border-color: $neutral-300;
            }
        }

        &.success {
            border-color: $green-500;
            color: $green-500;

            &:hover,
            &:active {
                background-color: $green-10;
                color: $green-500;
            }

            &:focus {
                // add ring
                outline: none;
                box-shadow: 0 0 0 4px $green-100;
            }

            &:disabled {
                cursor: not-allowed;
                background-color: $neutral-20;
                color: $white;
                border-color: $neutral-300;
            }
        }
    }
}

.linguise-input {
    background-color: $white !important;
    color: $neutral-900 !important;

    border: 1px solid $neutral-100 !important;
    border-radius: 4px;
    padding: 0 1rem;
    box-sizing: border-box;
    width: 100%;

    font-size: 1rem !important;
    font-weight: 400 !important;
    text-align: left !important;

    height: 2.5rem;
    line-height: 1.5rem !important;

    transition: border-color 0.3s ease, box-shadow 0.3s ease;

    &.checkbox-mode {
        height: 1.25rem;
        width: 1.25rem;
        margin-top: auto !important;
        margin-bottom: auto !important;
    }

    &:is(textarea) {
        padding: 0.75rem 1rem;
    }

    &.rounder {
        border-radius: 8px;
    }

    &.sharp {
        border-radius: 0px;
    }

    &:focus {
        outline: none;
        border-color: $primary-500 !important;
        box-shadow: 0 0 0 4px $primary-100 !important;
    }

    &.is-invalid {
        outline: none;
        border-color: $red-500 !important;
        box-shadow: 0 0 0 4px $red-100 !important;
    }

    &.disabled,
    &:disabled,
    &[aria-disabled="true"] {
        cursor: not-allowed;
        background-color: $neutral-20 !important;
        color: $neutral-500 !important;
        border-color: $neutral-100 !important;
    }

    &.disabled,
    &[aria-disabled="true"] {
        pointer-events: none;
    }

    &::placeholder {
        color: $neutral-500 !important;
    }

    &.shrink-fit {
        width: fit-content;
        max-width: 100%;
    }

    &.min-h-text {
        height: unset;
        min-height: 6rem !important;
    }
}

.linguise-radio {
    position: relative;
    display: flex;
    align-items: center;
    cursor: pointer;

    padding: 0.75rem 0.75rem;
    background-color: white;

    border-radius: 4px;
    border: 1px solid $neutral-50;

    transition: background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease, color 0.2s ease;

    &.rounder {
        border-radius: 8px;
    }

    input[type="radio"] {
        // Hide the default radio button
        appearance: none;
        position: absolute;
        opacity: 0;
        height: 0;
        width: 0;
        
        // When radio is checked, style the material-icons span
        &:checked + .material-icons {
            background-color: $green-700;
            border-color: $green-700;
            color: $white;
            opacity: 1;
            transform: scale(1);
        }

        // Focus styles for accessibility
        &:focus + .material-icons {
            box-shadow: 0 0 0 4px $green-100;
        }
    }

    // Custom radio design with material-icons
    .material-icons {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid $neutral-400;
        background-color: $white;
        margin-right: 12px;
        transition: all 0.2s ease;
        font-size: 14px;
        color: $white;
        opacity: 0.9;
        transform: scale(0.9);
        flex-shrink: 0;
        padding: 2px;
    }

    // Text label
    .text-label {
        font-size: 14px;
        color: $neutral-700;
        cursor: pointer;
    }

    // Hover state
    &:hover {
        .material-icons {
            border-color: $green-500;
        }
    }

    // Active/selected state
    &:has(input:checked) {
        .text-label {
            color: $green-700;
        }

        background-color: $green-20;
        border-color: $green-700;
    }
}

.linguise-slider-checkbox {
    position: relative;
    display: inline-flex;
    min-height: 2.125rem;
    align-items: center;

    .slider-input {
        appearance: none;
        opacity: 0;
        width: 0;
        height: 0;

        /* unset some default styles */
        margin: 0 !important;
        min-width: unset !important;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: $neutral-150;
        transition: .4s;
        border-radius: 34px;
        width: 3.75rem;

        &:before {
            position: absolute;
            content: "";
            height: 1.625rem;
            width: 1.625rem;
            left: 0.25rem;
            bottom: 0.25rem;
            background-color: $white;
            transition: .4s;
            border-radius: 50%;
        }
    }

    .slider-input:checked + .slider {
        background-color: $primary-500;
        border-color: $primary-500;
    }

    .slider-input:focus + .slider {
        box-shadow: 0 0 1px $primary-500;
    }

    .slider-input:checked + .slider:before {
        transform: translateX(1.625rem);
    }

    .slider-label {
        margin-left: calc(1.625rem * 2 + 1rem);
        font-size: 1rem;
        color: $neutral-700;
        cursor: pointer;
    }
}

.linguise-link {
    color: $primary-500;
    text-decoration: underline;
    font-size: inherit;
    font-weight: inherit;
    cursor: pointer;

    transition: color 0.3s ease;

    &:hover,
    &:active,
    &:focus {
        color: $primary-800;
        text-decoration: dashed underline;
    }
}
