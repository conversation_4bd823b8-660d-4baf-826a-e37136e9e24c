/*! For license information please see login.bundle.js.LICENSE.txt */
(()=>{"use strict";var e={n:t=>{var n=t&&t.__esModule?()=>t.default:()=>t;return e.d(n,{a:n}),n},d:(t,n)=>{for(var r in n)e.o(n,r)&&!e.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:n[r]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=jQuery;var n=e.n(t);function r(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function o(e){return e instanceof r(e).Element||e instanceof Element}function i(e){return e instanceof r(e).HTMLElement||e instanceof HTMLElement}function a(e){return"undefined"!=typeof ShadowRoot&&(e instanceof r(e).ShadowRoot||e instanceof ShadowRoot)}var s=Math.max,c=Math.min,u=Math.round;function f(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function p(){return!/^((?!chrome|android).)*safari/i.test(f())}function l(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var a=e.getBoundingClientRect(),s=1,c=1;t&&i(e)&&(s=e.offsetWidth>0&&u(a.width)/e.offsetWidth||1,c=e.offsetHeight>0&&u(a.height)/e.offsetHeight||1);var f=(o(e)?r(e):window).visualViewport,l=!p()&&n,d=(a.left+(l&&f?f.offsetLeft:0))/s,h=(a.top+(l&&f?f.offsetTop:0))/c,v=a.width/s,m=a.height/c;return{width:v,height:m,top:h,right:d+v,bottom:h+m,left:d,x:d,y:h}}function d(e){var t=r(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function h(e){return e?(e.nodeName||"").toLowerCase():null}function v(e){return((o(e)?e.ownerDocument:e.document)||window.document).documentElement}function m(e){return l(v(e)).left+d(e).scrollLeft}function y(e){return r(e).getComputedStyle(e)}function g(e){var t=y(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function b(e,t,n){void 0===n&&(n=!1);var o,a,s=i(t),c=i(t)&&function(e){var t=e.getBoundingClientRect(),n=u(t.width)/e.offsetWidth||1,r=u(t.height)/e.offsetHeight||1;return 1!==n||1!==r}(t),f=v(t),p=l(e,c,n),y={scrollLeft:0,scrollTop:0},b={x:0,y:0};return(s||!s&&!n)&&(("body"!==h(t)||g(f))&&(y=(o=t)!==r(o)&&i(o)?{scrollLeft:(a=o).scrollLeft,scrollTop:a.scrollTop}:d(o)),i(t)?((b=l(t,!0)).x+=t.clientLeft,b.y+=t.clientTop):f&&(b.x=m(f))),{x:p.left+y.scrollLeft-b.x,y:p.top+y.scrollTop-b.y,width:p.width,height:p.height}}function w(e){var t=l(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function x(e){return"html"===h(e)?e:e.assignedSlot||e.parentNode||(a(e)?e.host:null)||v(e)}function O(e){return["html","body","#document"].indexOf(h(e))>=0?e.ownerDocument.body:i(e)&&g(e)?e:O(x(e))}function E(e,t){var n;void 0===t&&(t=[]);var o=O(e),i=o===(null==(n=e.ownerDocument)?void 0:n.body),a=r(o),s=i?[a].concat(a.visualViewport||[],g(o)?o:[]):o,c=t.concat(s);return i?c:c.concat(E(x(s)))}function A(e){return["table","td","th"].indexOf(h(e))>=0}function L(e){return i(e)&&"fixed"!==y(e).position?e.offsetParent:null}function T(e){for(var t=r(e),n=L(e);n&&A(n)&&"static"===y(n).position;)n=L(n);return n&&("html"===h(n)||"body"===h(n)&&"static"===y(n).position)?t:n||function(e){var t=/firefox/i.test(f());if(/Trident/i.test(f())&&i(e)&&"fixed"===y(e).position)return null;var n=x(e);for(a(n)&&(n=n.host);i(n)&&["html","body"].indexOf(h(n))<0;){var r=y(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}var j="top",D="bottom",S="right",k="left",P="auto",C=[j,D,S,k],q="start",M="end",_="viewport",N="popper",V=C.reduce((function(e,t){return e.concat([t+"-"+q,t+"-"+M])}),[]),W=[].concat(C,[P]).reduce((function(e,t){return e.concat([t,t+"-"+q,t+"-"+M])}),[]),H=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function R(e){var t=new Map,n=new Set,r=[];function o(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var r=t.get(e);r&&o(r)}})),r.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||o(e)})),r}var B={placement:"bottom",modifiers:[],strategy:"absolute"};function I(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function F(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,i=t.defaultOptions,a=void 0===i?B:i;return function(e,t,n){void 0===n&&(n=a);var i,s,c={placement:"bottom",orderedModifiers:[],options:Object.assign({},B,a),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},u=[],f=!1,p={state:c,setOptions:function(n){var i="function"==typeof n?n(c.options):n;l(),c.options=Object.assign({},a,c.options,i),c.scrollParents={reference:o(e)?E(e):e.contextElement?E(e.contextElement):[],popper:E(t)};var s,f,d=function(e){var t=R(e);return H.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((s=[].concat(r,c.options.modifiers),f=s.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(f).map((function(e){return f[e]}))));return c.orderedModifiers=d.filter((function(e){return e.enabled})),c.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,r=void 0===n?{}:n,o=e.effect;if("function"==typeof o){var i=o({state:c,name:t,instance:p,options:r}),a=function(){};u.push(i||a)}})),p.update()},forceUpdate:function(){if(!f){var e=c.elements,t=e.reference,n=e.popper;if(I(t,n)){c.rects={reference:b(t,T(n),"fixed"===c.options.strategy),popper:w(n)},c.reset=!1,c.placement=c.options.placement,c.orderedModifiers.forEach((function(e){return c.modifiersData[e.name]=Object.assign({},e.data)}));for(var r=0;r<c.orderedModifiers.length;r++)if(!0!==c.reset){var o=c.orderedModifiers[r],i=o.fn,a=o.options,s=void 0===a?{}:a,u=o.name;"function"==typeof i&&(c=i({state:c,options:s,name:u,instance:p})||c)}else c.reset=!1,r=-1}}},update:(i=function(){return new Promise((function(e){p.forceUpdate(),e(c)}))},function(){return s||(s=new Promise((function(e){Promise.resolve().then((function(){s=void 0,e(i())}))}))),s}),destroy:function(){l(),f=!0}};if(!I(e,t))return p;function l(){u.forEach((function(e){return e()})),u=[]}return p.setOptions(n).then((function(e){!f&&n.onFirstUpdate&&n.onFirstUpdate(e)})),p}}var U={passive:!0};function G(e){return e.split("-")[0]}function z(e){return e.split("-")[1]}function $(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Y(e){var t,n=e.reference,r=e.element,o=e.placement,i=o?G(o):null,a=o?z(o):null,s=n.x+n.width/2-r.width/2,c=n.y+n.height/2-r.height/2;switch(i){case j:t={x:s,y:n.y-r.height};break;case D:t={x:s,y:n.y+n.height};break;case S:t={x:n.x+n.width,y:c};break;case k:t={x:n.x-r.width,y:c};break;default:t={x:n.x,y:n.y}}var u=i?$(i):null;if(null!=u){var f="y"===u?"height":"width";switch(a){case q:t[u]=t[u]-(n[f]/2-r[f]/2);break;case M:t[u]=t[u]+(n[f]/2-r[f]/2)}}return t}var J={top:"auto",right:"auto",bottom:"auto",left:"auto"};function X(e){var t,n=e.popper,o=e.popperRect,i=e.placement,a=e.variation,s=e.offsets,c=e.position,f=e.gpuAcceleration,p=e.adaptive,l=e.roundOffsets,d=e.isFixed,h=s.x,m=void 0===h?0:h,g=s.y,b=void 0===g?0:g,w="function"==typeof l?l({x:m,y:b}):{x:m,y:b};m=w.x,b=w.y;var x=s.hasOwnProperty("x"),O=s.hasOwnProperty("y"),E=k,A=j,L=window;if(p){var P=T(n),C="clientHeight",q="clientWidth";if(P===r(n)&&"static"!==y(P=v(n)).position&&"absolute"===c&&(C="scrollHeight",q="scrollWidth"),i===j||(i===k||i===S)&&a===M)A=D,b-=(d&&P===L&&L.visualViewport?L.visualViewport.height:P[C])-o.height,b*=f?1:-1;if(i===k||(i===j||i===D)&&a===M)E=S,m-=(d&&P===L&&L.visualViewport?L.visualViewport.width:P[q])-o.width,m*=f?1:-1}var _,N=Object.assign({position:c},p&&J),V=!0===l?function(e,t){var n=e.x,r=e.y,o=t.devicePixelRatio||1;return{x:u(n*o)/o||0,y:u(r*o)/o||0}}({x:m,y:b},r(n)):{x:m,y:b};return m=V.x,b=V.y,f?Object.assign({},N,((_={})[A]=O?"0":"",_[E]=x?"0":"",_.transform=(L.devicePixelRatio||1)<=1?"translate("+m+"px, "+b+"px)":"translate3d("+m+"px, "+b+"px, 0)",_)):Object.assign({},N,((t={})[A]=O?b+"px":"",t[E]=x?m+"px":"",t.transform="",t))}const Q={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];i(o)&&h(o)&&(Object.assign(o.style,n),Object.keys(r).forEach((function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var r=t.elements[e],o=t.attributes[e]||{},a=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});i(r)&&h(r)&&(Object.assign(r.style,a),Object.keys(o).forEach((function(e){r.removeAttribute(e)})))}))}},requires:["computeStyles"]};const K={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,i=void 0===o?[0,0]:o,a=W.reduce((function(e,n){return e[n]=function(e,t,n){var r=G(e),o=[k,j].indexOf(r)>=0?-1:1,i="function"==typeof n?n(Object.assign({},t,{placement:e})):n,a=i[0],s=i[1];return a=a||0,s=(s||0)*o,[k,S].indexOf(r)>=0?{x:s,y:a}:{x:a,y:s}}(n,t.rects,i),e}),{}),s=a[t.placement],c=s.x,u=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=c,t.modifiersData.popperOffsets.y+=u),t.modifiersData[r]=a}};var Z={left:"right",right:"left",bottom:"top",top:"bottom"};function ee(e){return e.replace(/left|right|bottom|top/g,(function(e){return Z[e]}))}var te={start:"end",end:"start"};function ne(e){return e.replace(/start|end/g,(function(e){return te[e]}))}function re(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&a(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function oe(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ie(e,t,n){return t===_?oe(function(e,t){var n=r(e),o=v(e),i=n.visualViewport,a=o.clientWidth,s=o.clientHeight,c=0,u=0;if(i){a=i.width,s=i.height;var f=p();(f||!f&&"fixed"===t)&&(c=i.offsetLeft,u=i.offsetTop)}return{width:a,height:s,x:c+m(e),y:u}}(e,n)):o(t)?function(e,t){var n=l(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):oe(function(e){var t,n=v(e),r=d(e),o=null==(t=e.ownerDocument)?void 0:t.body,i=s(n.scrollWidth,n.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),a=s(n.scrollHeight,n.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),c=-r.scrollLeft+m(e),u=-r.scrollTop;return"rtl"===y(o||n).direction&&(c+=s(n.clientWidth,o?o.clientWidth:0)-i),{width:i,height:a,x:c,y:u}}(v(e)))}function ae(e,t,n,r){var a="clippingParents"===t?function(e){var t=E(x(e)),n=["absolute","fixed"].indexOf(y(e).position)>=0&&i(e)?T(e):e;return o(n)?t.filter((function(e){return o(e)&&re(e,n)&&"body"!==h(e)})):[]}(e):[].concat(t),u=[].concat(a,[n]),f=u[0],p=u.reduce((function(t,n){var o=ie(e,n,r);return t.top=s(o.top,t.top),t.right=c(o.right,t.right),t.bottom=c(o.bottom,t.bottom),t.left=s(o.left,t.left),t}),ie(e,f,r));return p.width=p.right-p.left,p.height=p.bottom-p.top,p.x=p.left,p.y=p.top,p}function se(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function ce(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}function ue(e,t){void 0===t&&(t={});var n=t,r=n.placement,i=void 0===r?e.placement:r,a=n.strategy,s=void 0===a?e.strategy:a,c=n.boundary,u=void 0===c?"clippingParents":c,f=n.rootBoundary,p=void 0===f?_:f,d=n.elementContext,h=void 0===d?N:d,m=n.altBoundary,y=void 0!==m&&m,g=n.padding,b=void 0===g?0:g,w=se("number"!=typeof b?b:ce(b,C)),x=h===N?"reference":N,O=e.rects.popper,E=e.elements[y?x:h],A=ae(o(E)?E:E.contextElement||v(e.elements.popper),u,p,s),L=l(e.elements.reference),T=Y({reference:L,element:O,strategy:"absolute",placement:i}),k=oe(Object.assign({},O,T)),P=h===N?k:L,q={top:A.top-P.top+w.top,bottom:P.bottom-A.bottom+w.bottom,left:A.left-P.left+w.left,right:P.right-A.right+w.right},M=e.modifiersData.offset;if(h===N&&M){var V=M[i];Object.keys(q).forEach((function(e){var t=[S,D].indexOf(e)>=0?1:-1,n=[j,D].indexOf(e)>=0?"y":"x";q[e]+=V[n]*t}))}return q}function fe(e,t,n){return s(e,c(t,n))}const pe={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,i=void 0===o||o,a=n.altAxis,u=void 0!==a&&a,f=n.boundary,p=n.rootBoundary,l=n.altBoundary,d=n.padding,h=n.tether,v=void 0===h||h,m=n.tetherOffset,y=void 0===m?0:m,g=ue(t,{boundary:f,rootBoundary:p,padding:d,altBoundary:l}),b=G(t.placement),x=z(t.placement),O=!x,E=$(b),A="x"===E?"y":"x",L=t.modifiersData.popperOffsets,P=t.rects.reference,C=t.rects.popper,M="function"==typeof y?y(Object.assign({},t.rects,{placement:t.placement})):y,_="number"==typeof M?{mainAxis:M,altAxis:M}:Object.assign({mainAxis:0,altAxis:0},M),N=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,V={x:0,y:0};if(L){if(i){var W,H="y"===E?j:k,R="y"===E?D:S,B="y"===E?"height":"width",I=L[E],F=I+g[H],U=I-g[R],Y=v?-C[B]/2:0,J=x===q?P[B]:C[B],X=x===q?-C[B]:-P[B],Q=t.elements.arrow,K=v&&Q?w(Q):{width:0,height:0},Z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},ee=Z[H],te=Z[R],ne=fe(0,P[B],K[B]),re=O?P[B]/2-Y-ne-ee-_.mainAxis:J-ne-ee-_.mainAxis,oe=O?-P[B]/2+Y+ne+te+_.mainAxis:X+ne+te+_.mainAxis,ie=t.elements.arrow&&T(t.elements.arrow),ae=ie?"y"===E?ie.clientTop||0:ie.clientLeft||0:0,se=null!=(W=null==N?void 0:N[E])?W:0,ce=I+oe-se,pe=fe(v?c(F,I+re-se-ae):F,I,v?s(U,ce):U);L[E]=pe,V[E]=pe-I}if(u){var le,de="x"===E?j:k,he="x"===E?D:S,ve=L[A],me="y"===A?"height":"width",ye=ve+g[de],ge=ve-g[he],be=-1!==[j,k].indexOf(b),we=null!=(le=null==N?void 0:N[A])?le:0,xe=be?ye:ve-P[me]-C[me]-we+_.altAxis,Oe=be?ve+P[me]+C[me]-we-_.altAxis:ge,Ee=v&&be?function(e,t,n){var r=fe(e,t,n);return r>n?n:r}(xe,ve,Oe):fe(v?xe:ye,ve,v?Oe:ge);L[A]=Ee,V[A]=Ee-ve}t.modifiersData[r]=V}},requiresIfExists:["offset"]};const le={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,s=G(n.placement),c=$(s),u=[k,S].indexOf(s)>=0?"height":"width";if(i&&a){var f=function(e,t){return se("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:ce(e,C))}(o.padding,n),p=w(i),l="y"===c?j:k,d="y"===c?D:S,h=n.rects.reference[u]+n.rects.reference[c]-a[c]-n.rects.popper[u],v=a[c]-n.rects.reference[c],m=T(i),y=m?"y"===c?m.clientHeight||0:m.clientWidth||0:0,g=h/2-v/2,b=f[l],x=y-p[u]-f[d],O=y/2-p[u]/2+g,E=fe(b,O,x),A=c;n.modifiersData[r]=((t={})[A]=E,t.centerOffset=E-O,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;null!=r&&("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&re(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function de(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function he(e){return[j,S,D,k].some((function(t){return e[t]>=0}))}var ve=F({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,o=e.options,i=o.scroll,a=void 0===i||i,s=o.resize,c=void 0===s||s,u=r(t.elements.popper),f=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&f.forEach((function(e){e.addEventListener("scroll",n.update,U)})),c&&u.addEventListener("resize",n.update,U),function(){a&&f.forEach((function(e){e.removeEventListener("scroll",n.update,U)})),c&&u.removeEventListener("resize",n.update,U)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Y({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=void 0===r||r,i=n.adaptive,a=void 0===i||i,s=n.roundOffsets,c=void 0===s||s,u={placement:G(t.placement),variation:z(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,X(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:c})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,X(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},Q,K,{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,i=void 0===o||o,a=n.altAxis,s=void 0===a||a,c=n.fallbackPlacements,u=n.padding,f=n.boundary,p=n.rootBoundary,l=n.altBoundary,d=n.flipVariations,h=void 0===d||d,v=n.allowedAutoPlacements,m=t.options.placement,y=G(m),g=c||(y===m||!h?[ee(m)]:function(e){if(G(e)===P)return[];var t=ee(e);return[ne(e),t,ne(t)]}(m)),b=[m].concat(g).reduce((function(e,n){return e.concat(G(n)===P?function(e,t){void 0===t&&(t={});var n=t,r=n.placement,o=n.boundary,i=n.rootBoundary,a=n.padding,s=n.flipVariations,c=n.allowedAutoPlacements,u=void 0===c?W:c,f=z(r),p=f?s?V:V.filter((function(e){return z(e)===f})):C,l=p.filter((function(e){return u.indexOf(e)>=0}));0===l.length&&(l=p);var d=l.reduce((function(t,n){return t[n]=ue(e,{placement:n,boundary:o,rootBoundary:i,padding:a})[G(n)],t}),{});return Object.keys(d).sort((function(e,t){return d[e]-d[t]}))}(t,{placement:n,boundary:f,rootBoundary:p,padding:u,flipVariations:h,allowedAutoPlacements:v}):n)}),[]),w=t.rects.reference,x=t.rects.popper,O=new Map,E=!0,A=b[0],L=0;L<b.length;L++){var T=b[L],M=G(T),_=z(T)===q,N=[j,D].indexOf(M)>=0,H=N?"width":"height",R=ue(t,{placement:T,boundary:f,rootBoundary:p,altBoundary:l,padding:u}),B=N?_?S:k:_?D:j;w[H]>x[H]&&(B=ee(B));var I=ee(B),F=[];if(i&&F.push(R[M]<=0),s&&F.push(R[B]<=0,R[I]<=0),F.every((function(e){return e}))){A=T,E=!1;break}O.set(T,F)}if(E)for(var U=function(e){var t=b.find((function(t){var n=O.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return A=t,"break"},$=h?3:1;$>0;$--){if("break"===U($))break}t.placement!==A&&(t.modifiersData[r]._skip=!0,t.placement=A,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},pe,le,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=ue(t,{elementContext:"reference"}),s=ue(t,{altBoundary:!0}),c=de(a,r),u=de(s,o,i),f=he(c),p=he(u);t.modifiersData[n]={referenceClippingOffsets:c,popperEscapeOffsets:u,isReferenceHidden:f,hasPopperEscaped:p},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":f,"data-popper-escaped":p})}}]}),me="tippy-content",ye="tippy-backdrop",ge="tippy-arrow",be="tippy-svg-arrow",we={passive:!0,capture:!0},xe=function(){return document.body};function Oe(e,t,n){if(Array.isArray(e)){var r=e[t];return null==r?Array.isArray(n)?n[t]:n:r}return e}function Ee(e,t){var n={}.toString.call(e);return 0===n.indexOf("[object")&&n.indexOf(t+"]")>-1}function Ae(e,t){return"function"==typeof e?e.apply(void 0,t):e}function Le(e,t){return 0===t?e:function(r){clearTimeout(n),n=setTimeout((function(){e(r)}),t)};var n}function Te(e){return[].concat(e)}function je(e,t){-1===e.indexOf(t)&&e.push(t)}function De(e){return e.split("-")[0]}function Se(e){return[].slice.call(e)}function ke(e){return Object.keys(e).reduce((function(t,n){return void 0!==e[n]&&(t[n]=e[n]),t}),{})}function Pe(){return document.createElement("div")}function Ce(e){return["Element","Fragment"].some((function(t){return Ee(e,t)}))}function qe(e){return Ee(e,"MouseEvent")}function Me(e){return!(!e||!e._tippy||e._tippy.reference!==e)}function _e(e){return Ce(e)?[e]:function(e){return Ee(e,"NodeList")}(e)?Se(e):Array.isArray(e)?e:Se(document.querySelectorAll(e))}function Ne(e,t){e.forEach((function(e){e&&(e.style.transitionDuration=t+"ms")}))}function Ve(e,t){e.forEach((function(e){e&&e.setAttribute("data-state",t)}))}function We(e){var t,n=Te(e)[0];return null!=n&&null!=(t=n.ownerDocument)&&t.body?n.ownerDocument:document}function He(e,t,n){var r=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(t){e[r](t,n)}))}function Re(e,t){for(var n=t;n;){var r;if(e.contains(n))return!0;n=null==n.getRootNode||null==(r=n.getRootNode())?void 0:r.host}return!1}var Be={isTouch:!1},Ie=0;function Fe(){Be.isTouch||(Be.isTouch=!0,window.performance&&document.addEventListener("mousemove",Ue))}function Ue(){var e=performance.now();e-Ie<20&&(Be.isTouch=!1,document.removeEventListener("mousemove",Ue)),Ie=e}function Ge(){var e=document.activeElement;if(Me(e)){var t=e._tippy;e.blur&&!t.state.isVisible&&e.blur()}}var ze=!!("undefined"!=typeof window&&"undefined"!=typeof document)&&!!window.msCrypto;var $e={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Ye=Object.assign({appendTo:xe,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},$e,{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),Je=Object.keys(Ye);function Xe(e){var t=(e.plugins||[]).reduce((function(t,n){var r,o=n.name,i=n.defaultValue;o&&(t[o]=void 0!==e[o]?e[o]:null!=(r=Ye[o])?r:i);return t}),{});return Object.assign({},e,t)}function Qe(e,t){var n=Object.assign({},t,{content:Ae(t.content,[e])},t.ignoreAttributes?{}:function(e,t){return(t?Object.keys(Xe(Object.assign({},Ye,{plugins:t}))):Je).reduce((function(t,n){var r=(e.getAttribute("data-tippy-"+n)||"").trim();if(!r)return t;if("content"===n)t[n]=r;else try{t[n]=JSON.parse(r)}catch(e){t[n]=r}return t}),{})}(e,t.plugins));return n.aria=Object.assign({},Ye.aria,n.aria),n.aria={expanded:"auto"===n.aria.expanded?t.interactive:n.aria.expanded,content:"auto"===n.aria.content?t.interactive?null:"describedby":n.aria.content},n}function Ke(e,t){e.innerHTML=t}function Ze(e){var t=Pe();return!0===e?t.className=ge:(t.className=be,Ce(e)?t.appendChild(e):Ke(t,e)),t}function et(e,t){Ce(t.content)?(Ke(e,""),e.appendChild(t.content)):"function"!=typeof t.content&&(t.allowHTML?Ke(e,t.content):e.textContent=t.content)}function tt(e){var t=e.firstElementChild,n=Se(t.children);return{box:t,content:n.find((function(e){return e.classList.contains(me)})),arrow:n.find((function(e){return e.classList.contains(ge)||e.classList.contains(be)})),backdrop:n.find((function(e){return e.classList.contains(ye)}))}}function nt(e){var t=Pe(),n=Pe();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var r=Pe();function o(n,r){var o=tt(t),i=o.box,a=o.content,s=o.arrow;r.theme?i.setAttribute("data-theme",r.theme):i.removeAttribute("data-theme"),"string"==typeof r.animation?i.setAttribute("data-animation",r.animation):i.removeAttribute("data-animation"),r.inertia?i.setAttribute("data-inertia",""):i.removeAttribute("data-inertia"),i.style.maxWidth="number"==typeof r.maxWidth?r.maxWidth+"px":r.maxWidth,r.role?i.setAttribute("role",r.role):i.removeAttribute("role"),n.content===r.content&&n.allowHTML===r.allowHTML||et(a,e.props),r.arrow?s?n.arrow!==r.arrow&&(i.removeChild(s),i.appendChild(Ze(r.arrow))):i.appendChild(Ze(r.arrow)):s&&i.removeChild(s)}return r.className=me,r.setAttribute("data-state","hidden"),et(r,e.props),t.appendChild(n),n.appendChild(r),o(e.props,e.props),{popper:t,onUpdate:o}}nt.$$tippy=!0;var rt=1,ot=[],it=[];function at(e,t){var n,r,o,i,a,s,c,u,f=Qe(e,Object.assign({},Ye,Xe(ke(t)))),p=!1,l=!1,d=!1,h=!1,v=[],m=Le($,f.interactiveDebounce),y=rt++,g=(u=f.plugins).filter((function(e,t){return u.indexOf(e)===t})),b={id:y,reference:e,popper:Pe(),popperInstance:null,props:f,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:g,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(r),cancelAnimationFrame(o)},setProps:function(t){0;if(b.state.isDestroyed)return;q("onBeforeUpdate",[b,t]),G();var n=b.props,r=Qe(e,Object.assign({},n,ke(t),{ignoreAttributes:!0}));b.props=r,U(),n.interactiveDebounce!==r.interactiveDebounce&&(N(),m=Le($,r.interactiveDebounce));n.triggerTarget&&!r.triggerTarget?Te(n.triggerTarget).forEach((function(e){e.removeAttribute("aria-expanded")})):r.triggerTarget&&e.removeAttribute("aria-expanded");_(),C(),O&&O(n,r);b.popperInstance&&(Q(),Z().forEach((function(e){requestAnimationFrame(e._tippy.popperInstance.forceUpdate)})));q("onAfterUpdate",[b,t])},setContent:function(e){b.setProps({content:e})},show:function(){0;var e=b.state.isVisible,t=b.state.isDestroyed,n=!b.state.isEnabled,r=Be.isTouch&&!b.props.touch,o=Oe(b.props.duration,0,Ye.duration);if(e||t||n||r)return;if(D().hasAttribute("disabled"))return;if(q("onShow",[b],!1),!1===b.props.onShow(b))return;b.state.isVisible=!0,j()&&(x.style.visibility="visible");C(),R(),b.state.isMounted||(x.style.transition="none");if(j()){var i=k();Ne([i.box,i.content],0)}s=function(){var e;if(b.state.isVisible&&!h){if(h=!0,x.offsetHeight,x.style.transition=b.props.moveTransition,j()&&b.props.animation){var t=k(),n=t.box,r=t.content;Ne([n,r],o),Ve([n,r],"visible")}M(),_(),je(it,b),null==(e=b.popperInstance)||e.forceUpdate(),q("onMount",[b]),b.props.animation&&j()&&function(e,t){I(e,t)}(o,(function(){b.state.isShown=!0,q("onShown",[b])}))}},function(){var e,t=b.props.appendTo,n=D();e=b.props.interactive&&t===xe||"parent"===t?n.parentNode:Ae(t,[n]);e.contains(x)||e.appendChild(x);b.state.isMounted=!0,Q(),!1}()},hide:function(){0;var e=!b.state.isVisible,t=b.state.isDestroyed,n=!b.state.isEnabled,r=Oe(b.props.duration,1,Ye.duration);if(e||t||n)return;if(q("onHide",[b],!1),!1===b.props.onHide(b))return;b.state.isVisible=!1,b.state.isShown=!1,h=!1,p=!1,j()&&(x.style.visibility="hidden");if(N(),B(),C(!0),j()){var o=k(),i=o.box,a=o.content;b.props.animation&&(Ne([i,a],r),Ve([i,a],"hidden"))}M(),_(),b.props.animation?j()&&function(e,t){I(e,(function(){!b.state.isVisible&&x.parentNode&&x.parentNode.contains(x)&&t()}))}(r,b.unmount):b.unmount()},hideWithInteractivity:function(e){0;S().addEventListener("mousemove",m),je(ot,m),m(e)},enable:function(){b.state.isEnabled=!0},disable:function(){b.hide(),b.state.isEnabled=!1},unmount:function(){0;b.state.isVisible&&b.hide();if(!b.state.isMounted)return;K(),Z().forEach((function(e){e._tippy.unmount()})),x.parentNode&&x.parentNode.removeChild(x);it=it.filter((function(e){return e!==b})),b.state.isMounted=!1,q("onHidden",[b])},destroy:function(){0;if(b.state.isDestroyed)return;b.clearDelayTimeouts(),b.unmount(),G(),delete e._tippy,b.state.isDestroyed=!0,q("onDestroy",[b])}};if(!f.render)return b;var w=f.render(b),x=w.popper,O=w.onUpdate;x.setAttribute("data-tippy-root",""),x.id="tippy-"+b.id,b.popper=x,e._tippy=b,x._tippy=b;var E=g.map((function(e){return e.fn(b)})),A=e.hasAttribute("aria-expanded");return U(),_(),C(),q("onCreate",[b]),f.showOnCreate&&ee(),x.addEventListener("mouseenter",(function(){b.props.interactive&&b.state.isVisible&&b.clearDelayTimeouts()})),x.addEventListener("mouseleave",(function(){b.props.interactive&&b.props.trigger.indexOf("mouseenter")>=0&&S().addEventListener("mousemove",m)})),b;function L(){var e=b.props.touch;return Array.isArray(e)?e:[e,0]}function T(){return"hold"===L()[0]}function j(){var e;return!(null==(e=b.props.render)||!e.$$tippy)}function D(){return c||e}function S(){var e=D().parentNode;return e?We(e):document}function k(){return tt(x)}function P(e){return b.state.isMounted&&!b.state.isVisible||Be.isTouch||i&&"focus"===i.type?0:Oe(b.props.delay,e?0:1,Ye.delay)}function C(e){void 0===e&&(e=!1),x.style.pointerEvents=b.props.interactive&&!e?"":"none",x.style.zIndex=""+b.props.zIndex}function q(e,t,n){var r;(void 0===n&&(n=!0),E.forEach((function(n){n[e]&&n[e].apply(n,t)})),n)&&(r=b.props)[e].apply(r,t)}function M(){var t=b.props.aria;if(t.content){var n="aria-"+t.content,r=x.id;Te(b.props.triggerTarget||e).forEach((function(e){var t=e.getAttribute(n);if(b.state.isVisible)e.setAttribute(n,t?t+" "+r:r);else{var o=t&&t.replace(r,"").trim();o?e.setAttribute(n,o):e.removeAttribute(n)}}))}}function _(){!A&&b.props.aria.expanded&&Te(b.props.triggerTarget||e).forEach((function(e){b.props.interactive?e.setAttribute("aria-expanded",b.state.isVisible&&e===D()?"true":"false"):e.removeAttribute("aria-expanded")}))}function N(){S().removeEventListener("mousemove",m),ot=ot.filter((function(e){return e!==m}))}function V(t){if(!Be.isTouch||!d&&"mousedown"!==t.type){var n=t.composedPath&&t.composedPath()[0]||t.target;if(!b.props.interactive||!Re(x,n)){if(Te(b.props.triggerTarget||e).some((function(e){return Re(e,n)}))){if(Be.isTouch)return;if(b.state.isVisible&&b.props.trigger.indexOf("click")>=0)return}else q("onClickOutside",[b,t]);!0===b.props.hideOnClick&&(b.clearDelayTimeouts(),b.hide(),l=!0,setTimeout((function(){l=!1})),b.state.isMounted||B())}}}function W(){d=!0}function H(){d=!1}function R(){var e=S();e.addEventListener("mousedown",V,!0),e.addEventListener("touchend",V,we),e.addEventListener("touchstart",H,we),e.addEventListener("touchmove",W,we)}function B(){var e=S();e.removeEventListener("mousedown",V,!0),e.removeEventListener("touchend",V,we),e.removeEventListener("touchstart",H,we),e.removeEventListener("touchmove",W,we)}function I(e,t){var n=k().box;function r(e){e.target===n&&(He(n,"remove",r),t())}if(0===e)return t();He(n,"remove",a),He(n,"add",r),a=r}function F(t,n,r){void 0===r&&(r=!1),Te(b.props.triggerTarget||e).forEach((function(e){e.addEventListener(t,n,r),v.push({node:e,eventType:t,handler:n,options:r})}))}function U(){var e;T()&&(F("touchstart",z,{passive:!0}),F("touchend",Y,{passive:!0})),(e=b.props.trigger,e.split(/\s+/).filter(Boolean)).forEach((function(e){if("manual"!==e)switch(F(e,z),e){case"mouseenter":F("mouseleave",Y);break;case"focus":F(ze?"focusout":"blur",J);break;case"focusin":F("focusout",J)}}))}function G(){v.forEach((function(e){var t=e.node,n=e.eventType,r=e.handler,o=e.options;t.removeEventListener(n,r,o)})),v=[]}function z(e){var t,n=!1;if(b.state.isEnabled&&!X(e)&&!l){var r="focus"===(null==(t=i)?void 0:t.type);i=e,c=e.currentTarget,_(),!b.state.isVisible&&qe(e)&&ot.forEach((function(t){return t(e)})),"click"===e.type&&(b.props.trigger.indexOf("mouseenter")<0||p)&&!1!==b.props.hideOnClick&&b.state.isVisible?n=!0:ee(e),"click"===e.type&&(p=!n),n&&!r&&te(e)}}function $(e){var t=e.target,n=D().contains(t)||x.contains(t);if("mousemove"!==e.type||!n){var r=Z().concat(x).map((function(e){var t,n=null==(t=e._tippy.popperInstance)?void 0:t.state;return n?{popperRect:e.getBoundingClientRect(),popperState:n,props:f}:null})).filter(Boolean);(function(e,t){var n=t.clientX,r=t.clientY;return e.every((function(e){var t=e.popperRect,o=e.popperState,i=e.props.interactiveBorder,a=De(o.placement),s=o.modifiersData.offset;if(!s)return!0;var c="bottom"===a?s.top.y:0,u="top"===a?s.bottom.y:0,f="right"===a?s.left.x:0,p="left"===a?s.right.x:0,l=t.top-r+c>i,d=r-t.bottom-u>i,h=t.left-n+f>i,v=n-t.right-p>i;return l||d||h||v}))})(r,e)&&(N(),te(e))}}function Y(e){X(e)||b.props.trigger.indexOf("click")>=0&&p||(b.props.interactive?b.hideWithInteractivity(e):te(e))}function J(e){b.props.trigger.indexOf("focusin")<0&&e.target!==D()||b.props.interactive&&e.relatedTarget&&x.contains(e.relatedTarget)||te(e)}function X(e){return!!Be.isTouch&&T()!==e.type.indexOf("touch")>=0}function Q(){K();var t=b.props,n=t.popperOptions,r=t.placement,o=t.offset,i=t.getReferenceClientRect,a=t.moveTransition,c=j()?tt(x).arrow:null,u=i?{getBoundingClientRect:i,contextElement:i.contextElement||D()}:e,f={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(e){var t=e.state;if(j()){var n=k().box;["placement","reference-hidden","escaped"].forEach((function(e){"placement"===e?n.setAttribute("data-placement",t.placement):t.attributes.popper["data-popper-"+e]?n.setAttribute("data-"+e,""):n.removeAttribute("data-"+e)})),t.attributes.popper={}}}},p=[{name:"offset",options:{offset:o}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!a}},f];j()&&c&&p.push({name:"arrow",options:{element:c,padding:3}}),p.push.apply(p,(null==n?void 0:n.modifiers)||[]),b.popperInstance=ve(u,x,Object.assign({},n,{placement:r,onFirstUpdate:s,modifiers:p}))}function K(){b.popperInstance&&(b.popperInstance.destroy(),b.popperInstance=null)}function Z(){return Se(x.querySelectorAll("[data-tippy-root]"))}function ee(e){b.clearDelayTimeouts(),e&&q("onTrigger",[b,e]),R();var t=P(!0),r=L(),o=r[0],i=r[1];Be.isTouch&&"hold"===o&&i&&(t=i),t?n=setTimeout((function(){b.show()}),t):b.show()}function te(e){if(b.clearDelayTimeouts(),q("onUntrigger",[b,e]),b.state.isVisible){if(!(b.props.trigger.indexOf("mouseenter")>=0&&b.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(e.type)>=0&&p)){var t=P(!1);t?r=setTimeout((function(){b.state.isVisible&&b.hide()}),t):o=requestAnimationFrame((function(){b.hide()}))}}else B()}}function st(e,t){void 0===t&&(t={});var n=Ye.plugins.concat(t.plugins||[]);document.addEventListener("touchstart",Fe,we),window.addEventListener("blur",Ge);var r=Object.assign({},t,{plugins:n}),o=_e(e).reduce((function(e,t){var n=t&&at(t,r);return n&&e.push(n),e}),[]);return Ce(e)?o[0]:o}st.defaultProps=Ye,st.setDefaultProps=function(e){Object.keys(e).forEach((function(t){Ye[t]=e[t]}))},st.currentInput=Be;Object.assign({},Q,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}});st.setDefaultProps({render:nt});const ct=st;function ut(e){return ut="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ut(e)}function ft(){ft=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",s=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function u(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{u({},"")}catch(e){u=function(e,t,n){return e[t]=n}}function f(e,t,n,r){var i=t&&t.prototype instanceof y?t:y,a=Object.create(i.prototype),s=new k(r||[]);return o(a,"_invoke",{value:T(e,n,s)}),a}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=f;var l="suspendedStart",d="suspendedYield",h="executing",v="completed",m={};function y(){}function g(){}function b(){}var w={};u(w,a,(function(){return this}));var x=Object.getPrototypeOf,O=x&&x(x(P([])));O&&O!==n&&r.call(O,a)&&(w=O);var E=b.prototype=y.prototype=Object.create(w);function A(e){["next","throw","return"].forEach((function(t){u(e,t,(function(e){return this._invoke(t,e)}))}))}function L(e,t){function n(o,i,a,s){var c=p(e[o],e,i);if("throw"!==c.type){var u=c.arg,f=u.value;return f&&"object"==ut(f)&&r.call(f,"__await")?t.resolve(f.__await).then((function(e){n("next",e,a,s)}),(function(e){n("throw",e,a,s)})):t.resolve(f).then((function(e){u.value=e,a(u)}),(function(e){return n("throw",e,a,s)}))}s(c.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function T(t,n,r){var o=l;return function(i,a){if(o===h)throw Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var s=r.delegate;if(s){var c=j(s,r);if(c){if(c===m)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===l)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var u=p(t,n,r);if("normal"===u.type){if(o=r.done?v:d,u.arg===m)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(o=v,r.method="throw",r.arg=u.arg)}}}function j(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,j(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var i=p(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,m;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,m):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,m)}function D(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function S(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(D,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(ut(t)+" is not iterable")}return g.prototype=b,o(E,"constructor",{value:b,configurable:!0}),o(b,"constructor",{value:g,configurable:!0}),g.displayName=u(b,c,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===g||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,u(e,c,"GeneratorFunction")),e.prototype=Object.create(E),e},t.awrap=function(e){return{__await:e}},A(L.prototype),u(L.prototype,s,(function(){return this})),t.AsyncIterator=L,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new L(f(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},A(E),u(E,c,"Generator"),u(E,a,(function(){return this})),u(E,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,k.prototype={constructor:k,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(S),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return s.type="throw",s.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],s=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=r.call(a,"catchLoc"),u=r.call(a,"finallyLoc");if(c&&u){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!u)throw Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,m):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),S(n),m}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;S(n)}return o}}throw Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),m}},t}function pt(e,t,n,r,o,i,a){try{var s=e[i](a),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,o)}function lt(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){pt(i,r,o,a,s,"next",e)}function s(e){pt(i,r,o,a,s,"throw",e)}a(void 0)}))}}function dt(e){if(e)setTimeout((function(){e.fadeOut(2e3),setTimeout((function(){e.remove()}),2100)}),3e3);else{var t=n()(".linguise-notification-popup");t.length&&setTimeout((function(){t.fadeOut(2e3),setTimeout((function(){t.remove()}),2100)}),3e3)}}function ht(e){var t=n()('<div class="linguise-notification-popup"><span class="material-icons fail">close</span>'.concat(e,"</div>"));n()("body").append(t),dt(t)}function vt(){return(vt=lt(ft().mark((function e(t,n){return ft().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:t.addEventListener("submit",(function(e){var n=t.querySelector('input[type="submit"]'),r=t.querySelector("#login-box");n&&n.setAttribute("disabled","disabled"),r&&r.setAttribute("aria-disabled","true")}));case 1:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function mt(){return mt=lt(ft().mark((function e(t,r){var o,i,a,s,c,u,f;return ft().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:u=function(e){i.querySelectorAll(".mysql-marker").forEach((function(t){t.style.display=e?"":"none"}))},o=document.querySelector("#database-box"),i=document.querySelector("#db-container"),a=t.querySelector('input[type="submit"]'),s=document.querySelector('[data-action="test-connection"]'),c=["db_host","db_user","db_name","db_prefix"],o.addEventListener("change",(function(e){1!==o.querySelectorAll("option").length&&("sqlite"===o.value?(u(!1),c.forEach((function(e){var n=t.querySelector('[name="'.concat(e,'"]'));n&&n.removeAttribute("required")}))):(u(!0),c.forEach((function(e){var n=t.querySelector('[name="'.concat(e,'"]'));n&&n.setAttribute("required","required")}))))})),o.dispatchEvent(new Event("change")),f=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];t.querySelectorAll("input, select, textarea").forEach((function(t){var n;"register"!==(null===(n=t.dataset)||void 0===n?void 0:n.action)&&(t===o&&o.dataset.alwaysDisable||(e?t.setAttribute("disabled","disabled"):t.removeAttribute("disabled")))})),e?s.setAttribute("disabled","disabled"):s.removeAttribute("disabled")},t.addEventListener("submit",(function(e){e.preventDefault();var n=new FormData(t),i=new URL(t.action);n.has(o.name)||n.append(o.name,o.value),i.searchParams.set("linguise_action","activate-linguise"),f(!0),r.ajax({type:"POST",url:i.href,data:n,dataType:"json",processData:!1,contentType:!1,success:function(e){e.error?(ht(e.message),f(!1)):setTimeout((function(){window.location.reload()}),100)},error:function(e,t,n){console.error("Error:",n);var r=e.responseJSON;r&&r.message?ht(r.message):ht("An error occurred while processing your request."),f(!1)}})})),s.addEventListener("click",(function(e){e.preventDefault();var i=new FormData(t),s=new URL(t.action);i.has(o.name)||i.append(o.name,o.value),s.searchParams.set("linguise_action","test-connection"),f(!0),r.ajax({type:"POST",url:s.href,data:i,dataType:"json",processData:!1,contentType:!1,success:function(e){var t,r;f(!1),e.error?(ht(e.message),a.setAttribute("disabled","disabled")):(t=e.message,r=n()('<div class="linguise-notification-popup"><span class="material-icons">done</span>'.concat(t,"</div>")),n()("body").append(r),dt(r),a.removeAttribute("disabled"))},error:function(e,t,n){console.error("Error:",n);var r=e.responseJSON;r&&r.message?ht(r.message):ht("An error occurred while processing your request."),f(!1),a.setAttribute("disabled","disabled")}})}));case 11:case"end":return e.stop()}}),e)}))),mt.apply(this,arguments)}jQuery(document).ready((function(e){dt(),window.history.replaceState(null,null,window.location.href),document.querySelectorAll("[data-tippy]").forEach((function(e){var t=e.dataset.tippyDirection||"top";ct(e,{theme:"reviews",animation:"scale",animateFill:!1,maxWidth:300,duration:0,arrow:!0,placement:t,onShow:function(e){e.setContent(e.reference.dataset.tippy),e.popper.hidden=!e.reference.dataset.tippy}})}));var t=document.querySelector("#login-page");t&&function(e,t){vt.apply(this,arguments)}(t,e);var n=document.querySelector("#register-page");n&&function(e,t){mt.apply(this,arguments)}(n,e);var r=e(".linguise-notification-popup");r.length&&setTimeout((function(){r.fadeOut(2e3),setTimeout((function(){r.remove()}),2100)}),3e3)}))})();