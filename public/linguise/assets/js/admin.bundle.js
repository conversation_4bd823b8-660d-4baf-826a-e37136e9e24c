/*! For license information please see admin.bundle.js.LICENSE.txt */
(()=>{var e={56:(e,t,n)=>{"use strict";n.r(t)},124:function(){(function(){var e,t,n,i,r={}.hasOwnProperty;(i=function(){function e(){this.options_index=0,this.parsed=[]}return e.prototype.add_node=function(e){return"OPTGROUP"===e.nodeName.toUpperCase()?this.add_group(e):this.add_option(e)},e.prototype.add_group=function(e){var t,n,i,r,o,s;for(t=this.parsed.length,this.parsed.push({array_index:t,group:!0,label:e.label,title:e.title?e.title:void 0,children:0,disabled:e.disabled,classes:e.className}),s=[],n=0,i=(o=e.childNodes).length;n<i;n++)r=o[n],s.push(this.add_option(r,t,e.disabled));return s},e.prototype.add_option=function(e,t,n){if("OPTION"===e.nodeName.toUpperCase())return""!==e.text?(null!=t&&(this.parsed[t].children+=1),this.parsed.push({array_index:this.parsed.length,options_index:this.options_index,value:e.value,text:e.text,html:e.innerHTML,title:e.title?e.title:void 0,selected:e.selected,disabled:!0===n?n:e.disabled,group_array_index:t,group_label:null!=t?this.parsed[t].label:null,classes:e.className,style:e.style.cssText})):this.parsed.push({array_index:this.parsed.length,options_index:this.options_index,empty:!0}),this.options_index+=1},e}()).select_to_array=function(e){var t,n,r,o,s;for(o=new i,n=0,r=(s=e.childNodes).length;n<r;n++)t=s[n],o.add_node(t);return o.parsed},t=function(){function e(t,n){var i,r;this.form_field=t,this.options=null!=n?n:{},this.label_click_handler=(i=this.label_click_handler,r=this,function(){return i.apply(r,arguments)}),e.browser_is_supported()&&(this.is_multiple=this.form_field.multiple,this.set_default_text(),this.set_default_values(),this.setup(),this.set_up_html(),this.register_observers(),this.on_ready())}return e.prototype.set_default_values=function(){var e;return this.click_test_action=(e=this,function(t){return e.test_active_click(t)}),this.activate_action=function(e){return function(t){return e.activate_field(t)}}(this),this.active_field=!1,this.mouse_on_container=!1,this.results_showing=!1,this.result_highlighted=null,this.is_rtl=this.options.rtl||/\bchosen-rtl\b/.test(this.form_field.className),this.allow_single_deselect=null!=this.options.allow_single_deselect&&null!=this.form_field.options[0]&&""===this.form_field.options[0].text&&this.options.allow_single_deselect,this.disable_search_threshold=this.options.disable_search_threshold||0,this.disable_search=this.options.disable_search||!1,this.enable_split_word_search=null==this.options.enable_split_word_search||this.options.enable_split_word_search,this.group_search=null==this.options.group_search||this.options.group_search,this.search_contains=this.options.search_contains||!1,this.single_backstroke_delete=null==this.options.single_backstroke_delete||this.options.single_backstroke_delete,this.max_selected_options=this.options.max_selected_options||1/0,this.inherit_select_classes=this.options.inherit_select_classes||!1,this.display_selected_options=null==this.options.display_selected_options||this.options.display_selected_options,this.display_disabled_options=null==this.options.display_disabled_options||this.options.display_disabled_options,this.include_group_label_in_selected=this.options.include_group_label_in_selected||!1,this.max_shown_results=this.options.max_shown_results||Number.POSITIVE_INFINITY,this.case_sensitive_search=this.options.case_sensitive_search||!1,this.hide_results_on_select=null==this.options.hide_results_on_select||this.options.hide_results_on_select},e.prototype.set_default_text=function(){return this.form_field.getAttribute("data-placeholder")?this.default_text=this.form_field.getAttribute("data-placeholder"):this.is_multiple?this.default_text=this.options.placeholder_text_multiple||this.options.placeholder_text||e.default_multiple_text:this.default_text=this.options.placeholder_text_single||this.options.placeholder_text||e.default_single_text,this.default_text=this.escape_html(this.default_text),this.results_none_found=this.form_field.getAttribute("data-no_results_text")||this.options.no_results_text||e.default_no_result_text},e.prototype.choice_label=function(e){return this.include_group_label_in_selected&&null!=e.group_label?"<b class='group-name'>"+this.escape_html(e.group_label)+"</b>"+e.html:e.html},e.prototype.mouse_enter=function(){return this.mouse_on_container=!0},e.prototype.mouse_leave=function(){return this.mouse_on_container=!1},e.prototype.input_focus=function(e){if(this.is_multiple){if(!this.active_field)return setTimeout((t=this,function(){return t.container_mousedown()}),50)}else if(!this.active_field)return this.activate_field();var t},e.prototype.input_blur=function(e){if(!this.mouse_on_container)return this.active_field=!1,setTimeout((t=this,function(){return t.blur_test()}),100);var t},e.prototype.label_click_handler=function(e){return this.is_multiple?this.container_mousedown(e):this.activate_field()},e.prototype.results_option_build=function(e){var t,n,i,r,o,s,a;for(t="",a=0,r=0,o=(s=this.results_data).length;r<o&&(i="",""!==(i=(n=s[r]).group?this.result_add_group(n):this.result_add_option(n))&&(a++,t+=i),(null!=e?e.first:void 0)&&(n.selected&&this.is_multiple?this.choice_build(n):n.selected&&!this.is_multiple&&this.single_set_selected_text(this.choice_label(n))),!(a>=this.max_shown_results));r++);return t},e.prototype.result_add_option=function(e){var t,n;return e.search_match&&this.include_option_in_results(e)?(t=[],e.disabled||e.selected&&this.is_multiple||t.push("active-result"),!e.disabled||e.selected&&this.is_multiple||t.push("disabled-result"),e.selected&&t.push("result-selected"),null!=e.group_array_index&&t.push("group-option"),""!==e.classes&&t.push(e.classes),(n=document.createElement("li")).className=t.join(" "),e.style&&(n.style.cssText=e.style),n.setAttribute("data-option-array-index",e.array_index),n.innerHTML=e.highlighted_html||e.html,e.title&&(n.title=e.title),this.outerHTML(n)):""},e.prototype.result_add_group=function(e){var t,n;return(e.search_match||e.group_match)&&e.active_options>0?((t=[]).push("group-result"),e.classes&&t.push(e.classes),(n=document.createElement("li")).className=t.join(" "),n.innerHTML=e.highlighted_html||this.escape_html(e.label),e.title&&(n.title=e.title),this.outerHTML(n)):""},e.prototype.results_update_field=function(){if(this.set_default_text(),this.is_multiple||this.results_reset_cleanup(),this.result_clear_highlight(),this.results_build(),this.results_showing)return this.winnow_results()},e.prototype.reset_single_select_options=function(){var e,t,n,i,r;for(r=[],e=0,t=(n=this.results_data).length;e<t;e++)(i=n[e]).selected?r.push(i.selected=!1):r.push(void 0);return r},e.prototype.results_toggle=function(){return this.results_showing?this.results_hide():this.results_show()},e.prototype.results_search=function(e){return this.results_showing?this.winnow_results():this.results_show()},e.prototype.winnow_results=function(e){var t,n,i,r,o,s,a,l,c,u,h,d,f,p,g;for(this.no_results_clear(),u=0,t=(a=this.get_search_text()).replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"),c=this.get_search_regex(t),i=0,r=(l=this.results_data).length;i<r;i++)(o=l[i]).search_match=!1,h=null,d=null,o.highlighted_html="",this.include_option_in_results(o)&&(o.group&&(o.group_match=!1,o.active_options=0),null!=o.group_array_index&&this.results_data[o.group_array_index]&&(0===(h=this.results_data[o.group_array_index]).active_options&&h.search_match&&(u+=1),h.active_options+=1),g=o.group?o.label:o.text,o.group&&!this.group_search||(d=this.search_string_match(g,c),o.search_match=null!=d,o.search_match&&!o.group&&(u+=1),o.search_match?(a.length&&(f=d.index,s=g.slice(0,f),n=g.slice(f,f+a.length),p=g.slice(f+a.length),o.highlighted_html=this.escape_html(s)+"<em>"+this.escape_html(n)+"</em>"+this.escape_html(p)),null!=h&&(h.group_match=!0)):null!=o.group_array_index&&this.results_data[o.group_array_index].search_match&&(o.search_match=!0)));return this.result_clear_highlight(),u<1&&a.length?(this.update_results_content(""),this.no_results(a)):(this.update_results_content(this.results_option_build()),(null!=e?e.skip_highlight:void 0)?void 0:this.winnow_results_set_highlight())},e.prototype.get_search_regex=function(e){var t,n;return n=this.search_contains?e:"(^|\\s|\\b)"+e+"[^\\s]*",this.enable_split_word_search||this.search_contains||(n="^"+n),t=this.case_sensitive_search?"":"i",new RegExp(n,t)},e.prototype.search_string_match=function(e,t){var n;return n=t.exec(e),!this.search_contains&&(null!=n?n[1]:void 0)&&(n.index+=1),n},e.prototype.choices_count=function(){var e,t,n;if(null!=this.selected_option_count)return this.selected_option_count;for(this.selected_option_count=0,e=0,t=(n=this.form_field.options).length;e<t;e++)n[e].selected&&(this.selected_option_count+=1);return this.selected_option_count},e.prototype.choices_click=function(e){if(e.preventDefault(),this.activate_field(),!this.results_showing&&!this.is_disabled)return this.results_show()},e.prototype.keydown_checker=function(e){var t,n;switch(n=null!=(t=e.which)?t:e.keyCode,this.search_field_scale(),8!==n&&this.pending_backstroke&&this.clear_backstroke(),n){case 8:this.backstroke_length=this.get_search_field_value().length;break;case 9:this.results_showing&&!this.is_multiple&&this.result_select(e),this.mouse_on_container=!1;break;case 13:case 27:this.results_showing&&e.preventDefault();break;case 32:this.disable_search&&e.preventDefault();break;case 38:e.preventDefault(),this.keyup_arrow();break;case 40:e.preventDefault(),this.keydown_arrow()}},e.prototype.keyup_checker=function(e){var t,n;switch(n=null!=(t=e.which)?t:e.keyCode,this.search_field_scale(),n){case 8:this.is_multiple&&this.backstroke_length<1&&this.choices_count()>0?this.keydown_backstroke():this.pending_backstroke||(this.result_clear_highlight(),this.results_search());break;case 13:e.preventDefault(),this.results_showing&&this.result_select(e);break;case 27:this.results_showing&&this.results_hide();break;case 9:case 16:case 17:case 18:case 38:case 40:case 91:break;default:this.results_search()}},e.prototype.clipboard_event_checker=function(e){var t;if(!this.is_disabled)return setTimeout((t=this,function(){return t.results_search()}),50)},e.prototype.container_width=function(){return null!=this.options.width?this.options.width:this.form_field.offsetWidth+"px"},e.prototype.include_option_in_results=function(e){return!(this.is_multiple&&!this.display_selected_options&&e.selected)&&(!(!this.display_disabled_options&&e.disabled)&&!e.empty)},e.prototype.search_results_touchstart=function(e){return this.touch_started=!0,this.search_results_mouseover(e)},e.prototype.search_results_touchmove=function(e){return this.touch_started=!1,this.search_results_mouseout(e)},e.prototype.search_results_touchend=function(e){if(this.touch_started)return this.search_results_mouseup(e)},e.prototype.outerHTML=function(e){var t;return e.outerHTML?e.outerHTML:((t=document.createElement("div")).appendChild(e),t.innerHTML)},e.prototype.get_single_html=function(){return'<a class="chosen-single chosen-default">\n  <span>'+this.default_text+'</span>\n  <div><b></b></div>\n</a>\n<div class="chosen-drop">\n  <div class="chosen-search">\n    <input class="chosen-search-input" type="text" autocomplete="off" />\n  </div>\n  <ul class="chosen-results"></ul>\n</div>'},e.prototype.get_multi_html=function(){return'<ul class="chosen-choices">\n  <li class="search-field">\n    <input class="chosen-search-input" type="text" autocomplete="off" value="'+this.default_text+'" />\n  </li>\n</ul>\n<div class="chosen-drop">\n  <ul class="chosen-results"></ul>\n</div>'},e.prototype.get_no_results_html=function(e){return'<li class="no-results">\n  '+this.results_none_found+" <span>"+this.escape_html(e)+"</span>\n</li>"},e.browser_is_supported=function(){return"Microsoft Internet Explorer"===window.navigator.appName?document.documentMode>=8:!(/IEMobile/i.test(window.navigator.userAgent)||/Windows Phone/i.test(window.navigator.userAgent)||/BlackBerry/i.test(window.navigator.userAgent)||/BB10/i.test(window.navigator.userAgent))},e.default_multiple_text="Select Some Options",e.default_single_text="Select an Option",e.default_no_result_text="No results match",e}(),(e=jQuery).fn.extend({chosen:function(i){return t.browser_is_supported()?this.each((function(t){var r,o;o=(r=e(this)).data("chosen"),"destroy"!==i?o instanceof n||r.data("chosen",new n(this,i)):o instanceof n&&o.destroy()})):this}}),n=function(t){function n(){return n.__super__.constructor.apply(this,arguments)}return function(e,t){for(var n in t)r.call(t,n)&&(e[n]=t[n]);function i(){this.constructor=e}i.prototype=t.prototype,e.prototype=new i,e.__super__=t.prototype}(n,t),n.prototype.setup=function(){return this.form_field_jq=e(this.form_field),this.current_selectedIndex=this.form_field.selectedIndex},n.prototype.set_up_html=function(){var t,n;return(t=["chosen-container"]).push("chosen-container-"+(this.is_multiple?"multi":"single")),this.inherit_select_classes&&this.form_field.className&&t.push(this.form_field.className),this.is_rtl&&t.push("chosen-rtl"),n={class:t.join(" "),title:this.form_field.title},this.form_field.id.length&&(n.id=this.form_field.id.replace(/[^\w]/g,"_")+"_chosen"),this.container=e("<div />",n),this.container.width(this.container_width()),this.is_multiple?this.container.html(this.get_multi_html()):this.container.html(this.get_single_html()),this.form_field_jq.hide().after(this.container),this.dropdown=this.container.find("div.chosen-drop").first(),this.search_field=this.container.find("input").first(),this.search_results=this.container.find("ul.chosen-results").first(),this.search_field_scale(),this.search_no_results=this.container.find("li.no-results").first(),this.is_multiple?(this.search_choices=this.container.find("ul.chosen-choices").first(),this.search_container=this.container.find("li.search-field").first()):(this.search_container=this.container.find("div.chosen-search").first(),this.selected_item=this.container.find(".chosen-single").first()),this.results_build(),this.set_tab_index(),this.set_label_behavior()},n.prototype.on_ready=function(){return this.form_field_jq.trigger("chosen:ready",{chosen:this})},n.prototype.register_observers=function(){var e;return this.container.on("touchstart.chosen",(e=this,function(t){e.container_mousedown(t)})),this.container.on("touchend.chosen",function(e){return function(t){e.container_mouseup(t)}}(this)),this.container.on("mousedown.chosen",function(e){return function(t){e.container_mousedown(t)}}(this)),this.container.on("mouseup.chosen",function(e){return function(t){e.container_mouseup(t)}}(this)),this.container.on("mouseenter.chosen",function(e){return function(t){e.mouse_enter(t)}}(this)),this.container.on("mouseleave.chosen",function(e){return function(t){e.mouse_leave(t)}}(this)),this.search_results.on("mouseup.chosen",function(e){return function(t){e.search_results_mouseup(t)}}(this)),this.search_results.on("mouseover.chosen",function(e){return function(t){e.search_results_mouseover(t)}}(this)),this.search_results.on("mouseout.chosen",function(e){return function(t){e.search_results_mouseout(t)}}(this)),this.search_results.on("mousewheel.chosen DOMMouseScroll.chosen",function(e){return function(t){e.search_results_mousewheel(t)}}(this)),this.search_results.on("touchstart.chosen",function(e){return function(t){e.search_results_touchstart(t)}}(this)),this.search_results.on("touchmove.chosen",function(e){return function(t){e.search_results_touchmove(t)}}(this)),this.search_results.on("touchend.chosen",function(e){return function(t){e.search_results_touchend(t)}}(this)),this.form_field_jq.on("chosen:updated.chosen",function(e){return function(t){e.results_update_field(t)}}(this)),this.form_field_jq.on("chosen:activate.chosen",function(e){return function(t){e.activate_field(t)}}(this)),this.form_field_jq.on("chosen:open.chosen",function(e){return function(t){e.container_mousedown(t)}}(this)),this.form_field_jq.on("chosen:close.chosen",function(e){return function(t){e.close_field(t)}}(this)),this.search_field.on("blur.chosen",function(e){return function(t){e.input_blur(t)}}(this)),this.search_field.on("keyup.chosen",function(e){return function(t){e.keyup_checker(t)}}(this)),this.search_field.on("keydown.chosen",function(e){return function(t){e.keydown_checker(t)}}(this)),this.search_field.on("focus.chosen",function(e){return function(t){e.input_focus(t)}}(this)),this.search_field.on("cut.chosen",function(e){return function(t){e.clipboard_event_checker(t)}}(this)),this.search_field.on("paste.chosen",function(e){return function(t){e.clipboard_event_checker(t)}}(this)),this.is_multiple?this.search_choices.on("click.chosen",function(e){return function(t){e.choices_click(t)}}(this)):this.container.on("click.chosen",(function(e){e.preventDefault()}))},n.prototype.destroy=function(){return e(this.container[0].ownerDocument).off("click.chosen",this.click_test_action),this.form_field_label.length>0&&this.form_field_label.off("click.chosen"),this.search_field[0].tabIndex&&(this.form_field_jq[0].tabIndex=this.search_field[0].tabIndex),this.container.remove(),this.form_field_jq.removeData("chosen"),this.form_field_jq.show()},n.prototype.search_field_disabled=function(){return this.is_disabled=this.form_field.disabled||this.form_field_jq.parents("fieldset").is(":disabled"),this.container.toggleClass("chosen-disabled",this.is_disabled),this.search_field[0].disabled=this.is_disabled,this.is_multiple||this.selected_item.off("focus.chosen",this.activate_field),this.is_disabled?this.close_field():this.is_multiple?void 0:this.selected_item.on("focus.chosen",this.activate_field)},n.prototype.container_mousedown=function(t){var n;if(!this.is_disabled)return!t||"mousedown"!==(n=t.type)&&"touchstart"!==n||this.results_showing||t.preventDefault(),null!=t&&e(t.target).hasClass("search-choice-close")?void 0:(this.active_field?this.is_multiple||!t||e(t.target)[0]!==this.selected_item[0]&&!e(t.target).parents("a.chosen-single").length||(t.preventDefault(),this.results_toggle()):(this.is_multiple&&this.search_field.val(""),e(this.container[0].ownerDocument).on("click.chosen",this.click_test_action),this.results_show()),this.activate_field())},n.prototype.container_mouseup=function(e){if("ABBR"===e.target.nodeName&&!this.is_disabled)return this.results_reset(e)},n.prototype.search_results_mousewheel=function(e){var t;if(e.originalEvent&&(t=e.originalEvent.deltaY||-e.originalEvent.wheelDelta||e.originalEvent.detail),null!=t)return e.preventDefault(),"DOMMouseScroll"===e.type&&(t*=40),this.search_results.scrollTop(t+this.search_results.scrollTop())},n.prototype.blur_test=function(e){if(!this.active_field&&this.container.hasClass("chosen-container-active"))return this.close_field()},n.prototype.close_field=function(){return e(this.container[0].ownerDocument).off("click.chosen",this.click_test_action),this.active_field=!1,this.results_hide(),this.container.removeClass("chosen-container-active"),this.clear_backstroke(),this.show_search_field_default(),this.search_field_scale(),this.search_field.blur()},n.prototype.activate_field=function(){if(!this.is_disabled)return this.container.addClass("chosen-container-active"),this.active_field=!0,this.search_field.val(this.search_field.val()),this.search_field.focus()},n.prototype.test_active_click=function(t){var n;return(n=e(t.target).closest(".chosen-container")).length&&this.container[0]===n[0]?this.active_field=!0:this.close_field()},n.prototype.results_build=function(){return this.parsing=!0,this.selected_option_count=null,this.results_data=i.select_to_array(this.form_field),this.is_multiple?this.search_choices.find("li.search-choice").remove():(this.single_set_selected_text(),this.disable_search||this.form_field.options.length<=this.disable_search_threshold?(this.search_field[0].readOnly=!0,this.container.addClass("chosen-container-single-nosearch")):(this.search_field[0].readOnly=!1,this.container.removeClass("chosen-container-single-nosearch"))),this.update_results_content(this.results_option_build({first:!0})),this.search_field_disabled(),this.show_search_field_default(),this.search_field_scale(),this.parsing=!1},n.prototype.result_do_highlight=function(e){var t,n,i,r,o;if(e.length){if(this.result_clear_highlight(),this.result_highlight=e,this.result_highlight.addClass("highlighted"),r=(i=parseInt(this.search_results.css("maxHeight"),10))+(o=this.search_results.scrollTop()),(t=(n=this.result_highlight.position().top+this.search_results.scrollTop())+this.result_highlight.outerHeight())>=r)return this.search_results.scrollTop(t-i>0?t-i:0);if(n<o)return this.search_results.scrollTop(n)}},n.prototype.result_clear_highlight=function(){return this.result_highlight&&this.result_highlight.removeClass("highlighted"),this.result_highlight=null},n.prototype.results_show=function(){return this.is_multiple&&this.max_selected_options<=this.choices_count()?(this.form_field_jq.trigger("chosen:maxselected",{chosen:this}),!1):(this.container.addClass("chosen-with-drop"),this.results_showing=!0,this.search_field.focus(),this.search_field.val(this.get_search_field_value()),this.winnow_results(),this.form_field_jq.trigger("chosen:showing_dropdown",{chosen:this}))},n.prototype.update_results_content=function(e){return this.search_results.html(e)},n.prototype.results_hide=function(){return this.results_showing&&(this.result_clear_highlight(),this.container.removeClass("chosen-with-drop"),this.form_field_jq.trigger("chosen:hiding_dropdown",{chosen:this})),this.results_showing=!1},n.prototype.set_tab_index=function(e){var t;if(this.form_field.tabIndex)return t=this.form_field.tabIndex,this.form_field.tabIndex=-1,this.search_field[0].tabIndex=t},n.prototype.set_label_behavior=function(){if(this.form_field_label=this.form_field_jq.parents("label"),!this.form_field_label.length&&this.form_field.id.length&&(this.form_field_label=e("label[for='"+this.form_field.id+"']")),this.form_field_label.length>0)return this.form_field_label.on("click.chosen",this.label_click_handler)},n.prototype.show_search_field_default=function(){return this.is_multiple&&this.choices_count()<1&&!this.active_field?(this.search_field.val(this.default_text),this.search_field.addClass("default")):(this.search_field.val(""),this.search_field.removeClass("default"))},n.prototype.search_results_mouseup=function(t){var n;if((n=e(t.target).hasClass("active-result")?e(t.target):e(t.target).parents(".active-result").first()).length)return this.result_highlight=n,this.result_select(t),this.search_field.focus()},n.prototype.search_results_mouseover=function(t){var n;if(n=e(t.target).hasClass("active-result")?e(t.target):e(t.target).parents(".active-result").first())return this.result_do_highlight(n)},n.prototype.search_results_mouseout=function(t){if(e(t.target).hasClass("active-result")||e(t.target).parents(".active-result").first())return this.result_clear_highlight()},n.prototype.choice_build=function(t){var n,i,r;return n=e("<li />",{class:"search-choice"}).html("<span>"+this.choice_label(t)+"</span>"),t.disabled?n.addClass("search-choice-disabled"):((i=e("<a />",{class:"search-choice-close","data-option-array-index":t.array_index})).on("click.chosen",(r=this,function(e){return r.choice_destroy_link_click(e)})),n.append(i)),this.search_container.before(n)},n.prototype.choice_destroy_link_click=function(t){if(t.preventDefault(),t.stopPropagation(),!this.is_disabled)return this.choice_destroy(e(t.target))},n.prototype.choice_destroy=function(e){if(this.result_deselect(e[0].getAttribute("data-option-array-index")))return this.active_field?this.search_field.focus():this.show_search_field_default(),this.is_multiple&&this.choices_count()>0&&this.get_search_field_value().length<1&&this.results_hide(),e.parents("li").first().remove(),this.search_field_scale()},n.prototype.results_reset=function(){if(this.reset_single_select_options(),this.form_field.options[0].selected=!0,this.single_set_selected_text(),this.show_search_field_default(),this.results_reset_cleanup(),this.trigger_form_field_change(),this.active_field)return this.results_hide()},n.prototype.results_reset_cleanup=function(){return this.current_selectedIndex=this.form_field.selectedIndex,this.selected_item.find("abbr").remove()},n.prototype.result_select=function(e){var t,n;if(this.result_highlight)return t=this.result_highlight,this.result_clear_highlight(),this.is_multiple&&this.max_selected_options<=this.choices_count()?(this.form_field_jq.trigger("chosen:maxselected",{chosen:this}),!1):(this.is_multiple?t.removeClass("active-result"):this.reset_single_select_options(),t.addClass("result-selected"),(n=this.results_data[t[0].getAttribute("data-option-array-index")]).selected=!0,this.form_field.options[n.options_index].selected=!0,this.selected_option_count=null,this.is_multiple?this.choice_build(n):this.single_set_selected_text(this.choice_label(n)),this.is_multiple&&(!this.hide_results_on_select||e.metaKey||e.ctrlKey)?e.metaKey||e.ctrlKey?this.winnow_results({skip_highlight:!0}):(this.search_field.val(""),this.winnow_results()):(this.results_hide(),this.show_search_field_default()),(this.is_multiple||this.form_field.selectedIndex!==this.current_selectedIndex)&&this.trigger_form_field_change({selected:this.form_field.options[n.options_index].value}),this.current_selectedIndex=this.form_field.selectedIndex,e.preventDefault(),this.search_field_scale())},n.prototype.single_set_selected_text=function(e){return null==e&&(e=this.default_text),e===this.default_text?this.selected_item.addClass("chosen-default"):(this.single_deselect_control_build(),this.selected_item.removeClass("chosen-default")),this.selected_item.find("span").html(e)},n.prototype.result_deselect=function(e){var t;return t=this.results_data[e],!this.form_field.options[t.options_index].disabled&&(t.selected=!1,this.form_field.options[t.options_index].selected=!1,this.selected_option_count=null,this.result_clear_highlight(),this.results_showing&&this.winnow_results(),this.trigger_form_field_change({deselected:this.form_field.options[t.options_index].value}),this.search_field_scale(),!0)},n.prototype.single_deselect_control_build=function(){if(this.allow_single_deselect)return this.selected_item.find("abbr").length||this.selected_item.find("span").first().after('<abbr class="search-choice-close"></abbr>'),this.selected_item.addClass("chosen-single-with-deselect")},n.prototype.get_search_field_value=function(){return this.search_field.val()},n.prototype.get_search_text=function(){return e.trim(this.get_search_field_value())},n.prototype.escape_html=function(t){return e("<div/>").text(t).html()},n.prototype.winnow_results_set_highlight=function(){var e,t;if(null!=(e=(t=this.is_multiple?[]:this.search_results.find(".result-selected.active-result")).length?t.first():this.search_results.find(".active-result").first()))return this.result_do_highlight(e)},n.prototype.no_results=function(e){var t;return t=this.get_no_results_html(e),this.search_results.append(t),this.form_field_jq.trigger("chosen:no_results",{chosen:this})},n.prototype.no_results_clear=function(){return this.search_results.find(".no-results").remove()},n.prototype.keydown_arrow=function(){var e;return this.results_showing&&this.result_highlight?(e=this.result_highlight.nextAll("li.active-result").first())?this.result_do_highlight(e):void 0:this.results_show()},n.prototype.keyup_arrow=function(){var e;return this.results_showing||this.is_multiple?this.result_highlight?(e=this.result_highlight.prevAll("li.active-result")).length?this.result_do_highlight(e.first()):(this.choices_count()>0&&this.results_hide(),this.result_clear_highlight()):void 0:this.results_show()},n.prototype.keydown_backstroke=function(){var e;return this.pending_backstroke?(this.choice_destroy(this.pending_backstroke.find("a").first()),this.clear_backstroke()):(e=this.search_container.siblings("li.search-choice").last()).length&&!e.hasClass("search-choice-disabled")?(this.pending_backstroke=e,this.single_backstroke_delete?this.keydown_backstroke():this.pending_backstroke.addClass("search-choice-focus")):void 0},n.prototype.clear_backstroke=function(){return this.pending_backstroke&&this.pending_backstroke.removeClass("search-choice-focus"),this.pending_backstroke=null},n.prototype.search_field_scale=function(){var t,n,i,r,o,s,a;if(this.is_multiple){for(o={position:"absolute",left:"-1000px",top:"-1000px",display:"none",whiteSpace:"pre"},n=0,i=(s=["fontSize","fontStyle","fontWeight","fontFamily","lineHeight","textTransform","letterSpacing"]).length;n<i;n++)o[r=s[n]]=this.search_field.css(r);return(t=e("<div />").css(o)).text(this.get_search_field_value()),e("body").append(t),a=t.width()+25,t.remove(),this.container.is(":visible")&&(a=Math.min(this.container.outerWidth()-10,a)),this.search_field.width(a)}},n.prototype.trigger_form_field_change=function(e){return this.form_field_jq.trigger("input",e),this.form_field_jq.trigger("change",e)},n}(t)}).call(this)},237:function(e){e.exports=function(){"use strict";var e=navigator.userAgent,t=navigator.platform,n=/gecko\/\d/i.test(e),i=/MSIE \d/.test(e),r=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(e),o=/Edge\/(\d+)/.exec(e),s=i||r||o,a=s&&(i?document.documentMode||6:+(o||r)[1]),l=!o&&/WebKit\//.test(e),c=l&&/Qt\/\d+\.\d+/.test(e),u=!o&&/Chrome\/(\d+)/.exec(e),h=u&&+u[1],d=/Opera\//.test(e),f=/Apple Computer/.test(navigator.vendor),p=/Mac OS X 1\d\D([8-9]|\d\d)\D/.test(e),g=/PhantomJS/.test(e),m=f&&(/Mobile\/\w+/.test(e)||navigator.maxTouchPoints>2),v=/Android/.test(e),y=m||v||/webOS|BlackBerry|Opera Mini|Opera Mobi|IEMobile/i.test(e),b=m||/Mac/.test(t),_=/\bCrOS\b/.test(e),w=/win/i.test(t),x=d&&e.match(/Version\/(\d*\.\d*)/);x&&(x=Number(x[1])),x&&x>=15&&(d=!1,l=!0);var k=b&&(c||d&&(null==x||x<12.11)),C=n||s&&a>=9;function S(e){return new RegExp("(^|\\s)"+e+"(?:$|\\s)\\s*")}var L,T=function(e,t){var n=e.className,i=S(t).exec(n);if(i){var r=n.slice(i.index+i[0].length);e.className=n.slice(0,i.index)+(r?i[1]+r:"")}};function A(e){for(var t=e.childNodes.length;t>0;--t)e.removeChild(e.firstChild);return e}function M(e,t){return A(e).appendChild(t)}function O(e,t,n,i){var r=document.createElement(e);if(n&&(r.className=n),i&&(r.style.cssText=i),"string"==typeof t)r.appendChild(document.createTextNode(t));else if(t)for(var o=0;o<t.length;++o)r.appendChild(t[o]);return r}function E(e,t,n,i){var r=O(e,t,n,i);return r.setAttribute("role","presentation"),r}function N(e,t){if(3==t.nodeType&&(t=t.parentNode),e.contains)return e.contains(t);do{if(11==t.nodeType&&(t=t.host),t==e)return!0}while(t=t.parentNode)}function D(e){var t,n=e.ownerDocument||e;try{t=e.activeElement}catch(e){t=n.body||null}for(;t&&t.shadowRoot&&t.shadowRoot.activeElement;)t=t.shadowRoot.activeElement;return t}function P(e,t){var n=e.className;S(t).test(n)||(e.className+=(n?" ":"")+t)}function H(e,t){for(var n=e.split(" "),i=0;i<n.length;i++)n[i]&&!S(n[i]).test(t)&&(t+=" "+n[i]);return t}L=document.createRange?function(e,t,n,i){var r=document.createRange();return r.setEnd(i||e,n),r.setStart(e,t),r}:function(e,t,n){var i=document.body.createTextRange();try{i.moveToElementText(e.parentNode)}catch(e){return i}return i.collapse(!0),i.moveEnd("character",n),i.moveStart("character",t),i};var W=function(e){e.select()};function I(e){return e.display.wrapper.ownerDocument}function F(e){return B(e.display.wrapper)}function B(e){return e.getRootNode?e.getRootNode():e.ownerDocument}function R(e){return I(e).defaultView}function z(e){var t=Array.prototype.slice.call(arguments,1);return function(){return e.apply(null,t)}}function j(e,t,n){for(var i in t||(t={}),e)!e.hasOwnProperty(i)||!1===n&&t.hasOwnProperty(i)||(t[i]=e[i]);return t}function q(e,t,n,i,r){null==t&&-1==(t=e.search(/[^\s\u00a0]/))&&(t=e.length);for(var o=i||0,s=r||0;;){var a=e.indexOf("\t",o);if(a<0||a>=t)return s+(t-o);s+=a-o,s+=n-s%n,o=a+1}}m?W=function(e){e.selectionStart=0,e.selectionEnd=e.value.length}:s&&(W=function(e){try{e.select()}catch(e){}});var U=function(){this.id=null,this.f=null,this.time=0,this.handler=z(this.onTimeout,this)};function $(e,t){for(var n=0;n<e.length;++n)if(e[n]==t)return n;return-1}U.prototype.onTimeout=function(e){e.id=0,e.time<=+new Date?e.f():setTimeout(e.handler,e.time-+new Date)},U.prototype.set=function(e,t){this.f=t;var n=+new Date+e;(!this.id||n<this.time)&&(clearTimeout(this.id),this.id=setTimeout(this.handler,e),this.time=n)};var V=50,K={toString:function(){return"CodeMirror.Pass"}},G={scroll:!1},X={origin:"*mouse"},Y={origin:"+move"};function J(e,t,n){for(var i=0,r=0;;){var o=e.indexOf("\t",i);-1==o&&(o=e.length);var s=o-i;if(o==e.length||r+s>=t)return i+Math.min(s,t-r);if(r+=o-i,i=o+1,(r+=n-r%n)>=t)return i}}var Z=[""];function Q(e){for(;Z.length<=e;)Z.push(ee(Z)+" ");return Z[e]}function ee(e){return e[e.length-1]}function te(e,t){for(var n=[],i=0;i<e.length;i++)n[i]=t(e[i],i);return n}function ne(e,t,n){for(var i=0,r=n(t);i<e.length&&n(e[i])<=r;)i++;e.splice(i,0,t)}function ie(){}function re(e,t){var n;return Object.create?n=Object.create(e):(ie.prototype=e,n=new ie),t&&j(t,n),n}var oe=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;function se(e){return/\w/.test(e)||e>""&&(e.toUpperCase()!=e.toLowerCase()||oe.test(e))}function ae(e,t){return t?!!(t.source.indexOf("\\w")>-1&&se(e))||t.test(e):se(e)}function le(e){for(var t in e)if(e.hasOwnProperty(t)&&e[t])return!1;return!0}var ce=/[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065e\u0670\u06d6-\u06dc\u06de-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0900-\u0902\u093c\u0941-\u0948\u094d\u0951-\u0955\u0962\u0963\u0981\u09bc\u09be\u09c1-\u09c4\u09cd\u09d7\u09e2\u09e3\u0a01\u0a02\u0a3c\u0a41\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81\u0a82\u0abc\u0ac1-\u0ac5\u0ac7\u0ac8\u0acd\u0ae2\u0ae3\u0b01\u0b3c\u0b3e\u0b3f\u0b41-\u0b44\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe\u0bc0\u0bcd\u0bd7\u0c3e-\u0c40\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0cbc\u0cbf\u0cc2\u0cc6\u0ccc\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d3e\u0d41-\u0d44\u0d4d\u0d57\u0d62\u0d63\u0dca\u0dcf\u0dd2-\u0dd4\u0dd6\u0ddf\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f71-\u0f7e\u0f80-\u0f84\u0f86\u0f87\u0f90-\u0f97\u0f99-\u0fbc\u0fc6\u102d-\u1030\u1032-\u1037\u1039\u103a\u103d\u103e\u1058\u1059\u105e-\u1060\u1071-\u1074\u1082\u1085\u1086\u108d\u109d\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b7-\u17bd\u17c6\u17c9-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u1922\u1927\u1928\u1932\u1939-\u193b\u1a17\u1a18\u1a56\u1a58-\u1a5e\u1a60\u1a62\u1a65-\u1a6c\u1a73-\u1a7c\u1a7f\u1b00-\u1b03\u1b34\u1b36-\u1b3a\u1b3c\u1b42\u1b6b-\u1b73\u1b80\u1b81\u1ba2-\u1ba5\u1ba8\u1ba9\u1c2c-\u1c33\u1c36\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce0\u1ce2-\u1ce8\u1ced\u1dc0-\u1de6\u1dfd-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua825\ua826\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua951\ua980-\ua982\ua9b3\ua9b6-\ua9b9\ua9bc\uaa29-\uaa2e\uaa31\uaa32\uaa35\uaa36\uaa43\uaa4c\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe5\uabe8\uabed\udc00-\udfff\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]/;function ue(e){return e.charCodeAt(0)>=768&&ce.test(e)}function he(e,t,n){for(;(n<0?t>0:t<e.length)&&ue(e.charAt(t));)t+=n;return t}function de(e,t,n){for(var i=t>n?-1:1;;){if(t==n)return t;var r=(t+n)/2,o=i<0?Math.ceil(r):Math.floor(r);if(o==t)return e(o)?t:n;e(o)?n=o:t=o+i}}function fe(e,t,n,i){if(!e)return i(t,n,"ltr",0);for(var r=!1,o=0;o<e.length;++o){var s=e[o];(s.from<n&&s.to>t||t==n&&s.to==t)&&(i(Math.max(s.from,t),Math.min(s.to,n),1==s.level?"rtl":"ltr",o),r=!0)}r||i(t,n,"ltr")}var pe=null;function ge(e,t,n){var i;pe=null;for(var r=0;r<e.length;++r){var o=e[r];if(o.from<t&&o.to>t)return r;o.to==t&&(o.from!=o.to&&"before"==n?i=r:pe=r),o.from==t&&(o.from!=o.to&&"before"!=n?i=r:pe=r)}return null!=i?i:pe}var me=function(){var e="bbbbbbbbbtstwsbbbbbbbbbbbbbbssstwNN%%%NNNNNN,N,N1111111111NNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNNNLLLLLLLLLLLLLLLLLLLLLLLLLLNNNNbbbbbbsbbbbbbbbbbbbbbbbbbbbbbbbbb,N%%%%NNNNLNNNNN%%11NLNNN1LNNNNNLLLLLLLLLLLLLLLLLLLLLLLNLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLLN",t="nnnnnnNNr%%r,rNNmmmmmmmmmmmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmmmmmmmmmmmmmmmnnnnnnnnnn%nnrrrmrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrrmmmmmmmnNmmmmmmrrmmNmmmmrr1111111111";function n(n){return n<=247?e.charAt(n):1424<=n&&n<=1524?"R":1536<=n&&n<=1785?t.charAt(n-1536):1774<=n&&n<=2220?"r":8192<=n&&n<=8203?"w":8204==n?"b":"L"}var i=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/,r=/[stwN]/,o=/[LRr]/,s=/[Lb1n]/,a=/[1n]/;function l(e,t,n){this.level=e,this.from=t,this.to=n}return function(e,t){var c="ltr"==t?"L":"R";if(0==e.length||"ltr"==t&&!i.test(e))return!1;for(var u=e.length,h=[],d=0;d<u;++d)h.push(n(e.charCodeAt(d)));for(var f=0,p=c;f<u;++f){var g=h[f];"m"==g?h[f]=p:p=g}for(var m=0,v=c;m<u;++m){var y=h[m];"1"==y&&"r"==v?h[m]="n":o.test(y)&&(v=y,"r"==y&&(h[m]="R"))}for(var b=1,_=h[0];b<u-1;++b){var w=h[b];"+"==w&&"1"==_&&"1"==h[b+1]?h[b]="1":","!=w||_!=h[b+1]||"1"!=_&&"n"!=_||(h[b]=_),_=w}for(var x=0;x<u;++x){var k=h[x];if(","==k)h[x]="N";else if("%"==k){var C=void 0;for(C=x+1;C<u&&"%"==h[C];++C);for(var S=x&&"!"==h[x-1]||C<u&&"1"==h[C]?"1":"N",L=x;L<C;++L)h[L]=S;x=C-1}}for(var T=0,A=c;T<u;++T){var M=h[T];"L"==A&&"1"==M?h[T]="L":o.test(M)&&(A=M)}for(var O=0;O<u;++O)if(r.test(h[O])){var E=void 0;for(E=O+1;E<u&&r.test(h[E]);++E);for(var N="L"==(O?h[O-1]:c),D=N==("L"==(E<u?h[E]:c))?N?"L":"R":c,P=O;P<E;++P)h[P]=D;O=E-1}for(var H,W=[],I=0;I<u;)if(s.test(h[I])){var F=I;for(++I;I<u&&s.test(h[I]);++I);W.push(new l(0,F,I))}else{var B=I,R=W.length,z="rtl"==t?1:0;for(++I;I<u&&"L"!=h[I];++I);for(var j=B;j<I;)if(a.test(h[j])){B<j&&(W.splice(R,0,new l(1,B,j)),R+=z);var q=j;for(++j;j<I&&a.test(h[j]);++j);W.splice(R,0,new l(2,q,j)),R+=z,B=j}else++j;B<I&&W.splice(R,0,new l(1,B,I))}return"ltr"==t&&(1==W[0].level&&(H=e.match(/^\s+/))&&(W[0].from=H[0].length,W.unshift(new l(0,0,H[0].length))),1==ee(W).level&&(H=e.match(/\s+$/))&&(ee(W).to-=H[0].length,W.push(new l(0,u-H[0].length,u)))),"rtl"==t?W.reverse():W}}();function ve(e,t){var n=e.order;return null==n&&(n=e.order=me(e.text,t)),n}var ye=[],be=function(e,t,n){if(e.addEventListener)e.addEventListener(t,n,!1);else if(e.attachEvent)e.attachEvent("on"+t,n);else{var i=e._handlers||(e._handlers={});i[t]=(i[t]||ye).concat(n)}};function _e(e,t){return e._handlers&&e._handlers[t]||ye}function we(e,t,n){if(e.removeEventListener)e.removeEventListener(t,n,!1);else if(e.detachEvent)e.detachEvent("on"+t,n);else{var i=e._handlers,r=i&&i[t];if(r){var o=$(r,n);o>-1&&(i[t]=r.slice(0,o).concat(r.slice(o+1)))}}}function xe(e,t){var n=_e(e,t);if(n.length)for(var i=Array.prototype.slice.call(arguments,2),r=0;r<n.length;++r)n[r].apply(null,i)}function ke(e,t,n){return"string"==typeof t&&(t={type:t,preventDefault:function(){this.defaultPrevented=!0}}),xe(e,n||t.type,e,t),Me(t)||t.codemirrorIgnore}function Ce(e){var t=e._handlers&&e._handlers.cursorActivity;if(t)for(var n=e.curOp.cursorActivityHandlers||(e.curOp.cursorActivityHandlers=[]),i=0;i<t.length;++i)-1==$(n,t[i])&&n.push(t[i])}function Se(e,t){return _e(e,t).length>0}function Le(e){e.prototype.on=function(e,t){be(this,e,t)},e.prototype.off=function(e,t){we(this,e,t)}}function Te(e){e.preventDefault?e.preventDefault():e.returnValue=!1}function Ae(e){e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}function Me(e){return null!=e.defaultPrevented?e.defaultPrevented:0==e.returnValue}function Oe(e){Te(e),Ae(e)}function Ee(e){return e.target||e.srcElement}function Ne(e){var t=e.which;return null==t&&(1&e.button?t=1:2&e.button?t=3:4&e.button&&(t=2)),b&&e.ctrlKey&&1==t&&(t=3),t}var De,Pe,He=function(){if(s&&a<9)return!1;var e=O("div");return"draggable"in e||"dragDrop"in e}();function We(e){if(null==De){var t=O("span","​");M(e,O("span",[t,document.createTextNode("x")])),0!=e.firstChild.offsetHeight&&(De=t.offsetWidth<=1&&t.offsetHeight>2&&!(s&&a<8))}var n=De?O("span","​"):O("span"," ",null,"display: inline-block; width: 1px; margin-right: -1px");return n.setAttribute("cm-text",""),n}function Ie(e){if(null!=Pe)return Pe;var t=M(e,document.createTextNode("AخA")),n=L(t,0,1).getBoundingClientRect(),i=L(t,1,2).getBoundingClientRect();return A(e),!(!n||n.left==n.right)&&(Pe=i.right-n.right<3)}var Fe,Be=3!="\n\nb".split(/\n/).length?function(e){for(var t=0,n=[],i=e.length;t<=i;){var r=e.indexOf("\n",t);-1==r&&(r=e.length);var o=e.slice(t,"\r"==e.charAt(r-1)?r-1:r),s=o.indexOf("\r");-1!=s?(n.push(o.slice(0,s)),t+=s+1):(n.push(o),t=r+1)}return n}:function(e){return e.split(/\r\n?|\n/)},Re=window.getSelection?function(e){try{return e.selectionStart!=e.selectionEnd}catch(e){return!1}}:function(e){var t;try{t=e.ownerDocument.selection.createRange()}catch(e){}return!(!t||t.parentElement()!=e)&&0!=t.compareEndPoints("StartToEnd",t)},ze="oncopy"in(Fe=O("div"))||(Fe.setAttribute("oncopy","return;"),"function"==typeof Fe.oncopy),je=null;function qe(e){if(null!=je)return je;var t=M(e,O("span","x")),n=t.getBoundingClientRect(),i=L(t,0,1).getBoundingClientRect();return je=Math.abs(n.left-i.left)>1}var Ue={},$e={};function Ve(e,t){arguments.length>2&&(t.dependencies=Array.prototype.slice.call(arguments,2)),Ue[e]=t}function Ke(e,t){$e[e]=t}function Ge(e){if("string"==typeof e&&$e.hasOwnProperty(e))e=$e[e];else if(e&&"string"==typeof e.name&&$e.hasOwnProperty(e.name)){var t=$e[e.name];"string"==typeof t&&(t={name:t}),(e=re(t,e)).name=t.name}else{if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+xml$/.test(e))return Ge("application/xml");if("string"==typeof e&&/^[\w\-]+\/[\w\-]+\+json$/.test(e))return Ge("application/json")}return"string"==typeof e?{name:e}:e||{name:"null"}}function Xe(e,t){t=Ge(t);var n=Ue[t.name];if(!n)return Xe(e,"text/plain");var i=n(e,t);if(Ye.hasOwnProperty(t.name)){var r=Ye[t.name];for(var o in r)r.hasOwnProperty(o)&&(i.hasOwnProperty(o)&&(i["_"+o]=i[o]),i[o]=r[o])}if(i.name=t.name,t.helperType&&(i.helperType=t.helperType),t.modeProps)for(var s in t.modeProps)i[s]=t.modeProps[s];return i}var Ye={};function Je(e,t){j(t,Ye.hasOwnProperty(e)?Ye[e]:Ye[e]={})}function Ze(e,t){if(!0===t)return t;if(e.copyState)return e.copyState(t);var n={};for(var i in t){var r=t[i];r instanceof Array&&(r=r.concat([])),n[i]=r}return n}function Qe(e,t){for(var n;e.innerMode&&(n=e.innerMode(t))&&n.mode!=e;)t=n.state,e=n.mode;return n||{mode:e,state:t}}function et(e,t,n){return!e.startState||e.startState(t,n)}var tt=function(e,t,n){this.pos=this.start=0,this.string=e,this.tabSize=t||8,this.lastColumnPos=this.lastColumnValue=0,this.lineStart=0,this.lineOracle=n};function nt(e,t){if((t-=e.first)<0||t>=e.size)throw new Error("There is no line "+(t+e.first)+" in the document.");for(var n=e;!n.lines;)for(var i=0;;++i){var r=n.children[i],o=r.chunkSize();if(t<o){n=r;break}t-=o}return n.lines[t]}function it(e,t,n){var i=[],r=t.line;return e.iter(t.line,n.line+1,(function(e){var o=e.text;r==n.line&&(o=o.slice(0,n.ch)),r==t.line&&(o=o.slice(t.ch)),i.push(o),++r})),i}function rt(e,t,n){var i=[];return e.iter(t,n,(function(e){i.push(e.text)})),i}function ot(e,t){var n=t-e.height;if(n)for(var i=e;i;i=i.parent)i.height+=n}function st(e){if(null==e.parent)return null;for(var t=e.parent,n=$(t.lines,e),i=t.parent;i;t=i,i=i.parent)for(var r=0;i.children[r]!=t;++r)n+=i.children[r].chunkSize();return n+t.first}function at(e,t){var n=e.first;e:do{for(var i=0;i<e.children.length;++i){var r=e.children[i],o=r.height;if(t<o){e=r;continue e}t-=o,n+=r.chunkSize()}return n}while(!e.lines);for(var s=0;s<e.lines.length;++s){var a=e.lines[s].height;if(t<a)break;t-=a}return n+s}function lt(e,t){return t>=e.first&&t<e.first+e.size}function ct(e,t){return String(e.lineNumberFormatter(t+e.firstLineNumber))}function ut(e,t,n){if(void 0===n&&(n=null),!(this instanceof ut))return new ut(e,t,n);this.line=e,this.ch=t,this.sticky=n}function ht(e,t){return e.line-t.line||e.ch-t.ch}function dt(e,t){return e.sticky==t.sticky&&0==ht(e,t)}function ft(e){return ut(e.line,e.ch)}function pt(e,t){return ht(e,t)<0?t:e}function gt(e,t){return ht(e,t)<0?e:t}function mt(e,t){return Math.max(e.first,Math.min(t,e.first+e.size-1))}function vt(e,t){if(t.line<e.first)return ut(e.first,0);var n=e.first+e.size-1;return t.line>n?ut(n,nt(e,n).text.length):yt(t,nt(e,t.line).text.length)}function yt(e,t){var n=e.ch;return null==n||n>t?ut(e.line,t):n<0?ut(e.line,0):e}function bt(e,t){for(var n=[],i=0;i<t.length;i++)n[i]=vt(e,t[i]);return n}tt.prototype.eol=function(){return this.pos>=this.string.length},tt.prototype.sol=function(){return this.pos==this.lineStart},tt.prototype.peek=function(){return this.string.charAt(this.pos)||void 0},tt.prototype.next=function(){if(this.pos<this.string.length)return this.string.charAt(this.pos++)},tt.prototype.eat=function(e){var t=this.string.charAt(this.pos);if("string"==typeof e?t==e:t&&(e.test?e.test(t):e(t)))return++this.pos,t},tt.prototype.eatWhile=function(e){for(var t=this.pos;this.eat(e););return this.pos>t},tt.prototype.eatSpace=function(){for(var e=this.pos;/[\s\u00a0]/.test(this.string.charAt(this.pos));)++this.pos;return this.pos>e},tt.prototype.skipToEnd=function(){this.pos=this.string.length},tt.prototype.skipTo=function(e){var t=this.string.indexOf(e,this.pos);if(t>-1)return this.pos=t,!0},tt.prototype.backUp=function(e){this.pos-=e},tt.prototype.column=function(){return this.lastColumnPos<this.start&&(this.lastColumnValue=q(this.string,this.start,this.tabSize,this.lastColumnPos,this.lastColumnValue),this.lastColumnPos=this.start),this.lastColumnValue-(this.lineStart?q(this.string,this.lineStart,this.tabSize):0)},tt.prototype.indentation=function(){return q(this.string,null,this.tabSize)-(this.lineStart?q(this.string,this.lineStart,this.tabSize):0)},tt.prototype.match=function(e,t,n){if("string"!=typeof e){var i=this.string.slice(this.pos).match(e);return i&&i.index>0?null:(i&&!1!==t&&(this.pos+=i[0].length),i)}var r=function(e){return n?e.toLowerCase():e};if(r(this.string.substr(this.pos,e.length))==r(e))return!1!==t&&(this.pos+=e.length),!0},tt.prototype.current=function(){return this.string.slice(this.start,this.pos)},tt.prototype.hideFirstChars=function(e,t){this.lineStart+=e;try{return t()}finally{this.lineStart-=e}},tt.prototype.lookAhead=function(e){var t=this.lineOracle;return t&&t.lookAhead(e)},tt.prototype.baseToken=function(){var e=this.lineOracle;return e&&e.baseToken(this.pos)};var _t=function(e,t){this.state=e,this.lookAhead=t},wt=function(e,t,n,i){this.state=t,this.doc=e,this.line=n,this.maxLookAhead=i||0,this.baseTokens=null,this.baseTokenPos=1};function xt(e,t,n,i){var r=[e.state.modeGen],o={};Et(e,t.text,e.doc.mode,n,(function(e,t){return r.push(e,t)}),o,i);for(var s=n.state,a=function(i){n.baseTokens=r;var a=e.state.overlays[i],l=1,c=0;n.state=!0,Et(e,t.text,a.mode,n,(function(e,t){for(var n=l;c<e;){var i=r[l];i>e&&r.splice(l,1,e,r[l+1],i),l+=2,c=Math.min(e,i)}if(t)if(a.opaque)r.splice(n,l-n,e,"overlay "+t),l=n+2;else for(;n<l;n+=2){var o=r[n+1];r[n+1]=(o?o+" ":"")+"overlay "+t}}),o),n.state=s,n.baseTokens=null,n.baseTokenPos=1},l=0;l<e.state.overlays.length;++l)a(l);return{styles:r,classes:o.bgClass||o.textClass?o:null}}function kt(e,t,n){if(!t.styles||t.styles[0]!=e.state.modeGen){var i=Ct(e,st(t)),r=t.text.length>e.options.maxHighlightLength&&Ze(e.doc.mode,i.state),o=xt(e,t,i);r&&(i.state=r),t.stateAfter=i.save(!r),t.styles=o.styles,o.classes?t.styleClasses=o.classes:t.styleClasses&&(t.styleClasses=null),n===e.doc.highlightFrontier&&(e.doc.modeFrontier=Math.max(e.doc.modeFrontier,++e.doc.highlightFrontier))}return t.styles}function Ct(e,t,n){var i=e.doc,r=e.display;if(!i.mode.startState)return new wt(i,!0,t);var o=Nt(e,t,n),s=o>i.first&&nt(i,o-1).stateAfter,a=s?wt.fromSaved(i,s,o):new wt(i,et(i.mode),o);return i.iter(o,t,(function(n){St(e,n.text,a);var i=a.line;n.stateAfter=i==t-1||i%5==0||i>=r.viewFrom&&i<r.viewTo?a.save():null,a.nextLine()})),n&&(i.modeFrontier=a.line),a}function St(e,t,n,i){var r=e.doc.mode,o=new tt(t,e.options.tabSize,n);for(o.start=o.pos=i||0,""==t&&Lt(r,n.state);!o.eol();)Tt(r,o,n.state),o.start=o.pos}function Lt(e,t){if(e.blankLine)return e.blankLine(t);if(e.innerMode){var n=Qe(e,t);return n.mode.blankLine?n.mode.blankLine(n.state):void 0}}function Tt(e,t,n,i){for(var r=0;r<10;r++){i&&(i[0]=Qe(e,n).mode);var o=e.token(t,n);if(t.pos>t.start)return o}throw new Error("Mode "+e.name+" failed to advance stream.")}wt.prototype.lookAhead=function(e){var t=this.doc.getLine(this.line+e);return null!=t&&e>this.maxLookAhead&&(this.maxLookAhead=e),t},wt.prototype.baseToken=function(e){if(!this.baseTokens)return null;for(;this.baseTokens[this.baseTokenPos]<=e;)this.baseTokenPos+=2;var t=this.baseTokens[this.baseTokenPos+1];return{type:t&&t.replace(/( |^)overlay .*/,""),size:this.baseTokens[this.baseTokenPos]-e}},wt.prototype.nextLine=function(){this.line++,this.maxLookAhead>0&&this.maxLookAhead--},wt.fromSaved=function(e,t,n){return t instanceof _t?new wt(e,Ze(e.mode,t.state),n,t.lookAhead):new wt(e,Ze(e.mode,t),n)},wt.prototype.save=function(e){var t=!1!==e?Ze(this.doc.mode,this.state):this.state;return this.maxLookAhead>0?new _t(t,this.maxLookAhead):t};var At=function(e,t,n){this.start=e.start,this.end=e.pos,this.string=e.current(),this.type=t||null,this.state=n};function Mt(e,t,n,i){var r,o,s=e.doc,a=s.mode,l=nt(s,(t=vt(s,t)).line),c=Ct(e,t.line,n),u=new tt(l.text,e.options.tabSize,c);for(i&&(o=[]);(i||u.pos<t.ch)&&!u.eol();)u.start=u.pos,r=Tt(a,u,c.state),i&&o.push(new At(u,r,Ze(s.mode,c.state)));return i?o:new At(u,r,c.state)}function Ot(e,t){if(e)for(;;){var n=e.match(/(?:^|\s+)line-(background-)?(\S+)/);if(!n)break;e=e.slice(0,n.index)+e.slice(n.index+n[0].length);var i=n[1]?"bgClass":"textClass";null==t[i]?t[i]=n[2]:new RegExp("(?:^|\\s)"+n[2]+"(?:$|\\s)").test(t[i])||(t[i]+=" "+n[2])}return e}function Et(e,t,n,i,r,o,s){var a=n.flattenSpans;null==a&&(a=e.options.flattenSpans);var l,c=0,u=null,h=new tt(t,e.options.tabSize,i),d=e.options.addModeClass&&[null];for(""==t&&Ot(Lt(n,i.state),o);!h.eol();){if(h.pos>e.options.maxHighlightLength?(a=!1,s&&St(e,t,i,h.pos),h.pos=t.length,l=null):l=Ot(Tt(n,h,i.state,d),o),d){var f=d[0].name;f&&(l="m-"+(l?f+" "+l:f))}if(!a||u!=l){for(;c<h.start;)r(c=Math.min(h.start,c+5e3),u);u=l}h.start=h.pos}for(;c<h.pos;){var p=Math.min(h.pos,c+5e3);r(p,u),c=p}}function Nt(e,t,n){for(var i,r,o=e.doc,s=n?-1:t-(e.doc.mode.innerMode?1e3:100),a=t;a>s;--a){if(a<=o.first)return o.first;var l=nt(o,a-1),c=l.stateAfter;if(c&&(!n||a+(c instanceof _t?c.lookAhead:0)<=o.modeFrontier))return a;var u=q(l.text,null,e.options.tabSize);(null==r||i>u)&&(r=a-1,i=u)}return r}function Dt(e,t){if(e.modeFrontier=Math.min(e.modeFrontier,t),!(e.highlightFrontier<t-10)){for(var n=e.first,i=t-1;i>n;i--){var r=nt(e,i).stateAfter;if(r&&(!(r instanceof _t)||i+r.lookAhead<t)){n=i+1;break}}e.highlightFrontier=Math.min(e.highlightFrontier,n)}}var Pt=!1,Ht=!1;function Wt(){Pt=!0}function It(){Ht=!0}function Ft(e,t,n){this.marker=e,this.from=t,this.to=n}function Bt(e,t){if(e)for(var n=0;n<e.length;++n){var i=e[n];if(i.marker==t)return i}}function Rt(e,t){for(var n,i=0;i<e.length;++i)e[i]!=t&&(n||(n=[])).push(e[i]);return n}function zt(e,t,n){var i=n&&window.WeakSet&&(n.markedSpans||(n.markedSpans=new WeakSet));i&&e.markedSpans&&i.has(e.markedSpans)?e.markedSpans.push(t):(e.markedSpans=e.markedSpans?e.markedSpans.concat([t]):[t],i&&i.add(e.markedSpans)),t.marker.attachLine(e)}function jt(e,t,n){var i;if(e)for(var r=0;r<e.length;++r){var o=e[r],s=o.marker;if(null==o.from||(s.inclusiveLeft?o.from<=t:o.from<t)||o.from==t&&"bookmark"==s.type&&(!n||!o.marker.insertLeft)){var a=null==o.to||(s.inclusiveRight?o.to>=t:o.to>t);(i||(i=[])).push(new Ft(s,o.from,a?null:o.to))}}return i}function qt(e,t,n){var i;if(e)for(var r=0;r<e.length;++r){var o=e[r],s=o.marker;if(null==o.to||(s.inclusiveRight?o.to>=t:o.to>t)||o.from==t&&"bookmark"==s.type&&(!n||o.marker.insertLeft)){var a=null==o.from||(s.inclusiveLeft?o.from<=t:o.from<t);(i||(i=[])).push(new Ft(s,a?null:o.from-t,null==o.to?null:o.to-t))}}return i}function Ut(e,t){if(t.full)return null;var n=lt(e,t.from.line)&&nt(e,t.from.line).markedSpans,i=lt(e,t.to.line)&&nt(e,t.to.line).markedSpans;if(!n&&!i)return null;var r=t.from.ch,o=t.to.ch,s=0==ht(t.from,t.to),a=jt(n,r,s),l=qt(i,o,s),c=1==t.text.length,u=ee(t.text).length+(c?r:0);if(a)for(var h=0;h<a.length;++h){var d=a[h];if(null==d.to){var f=Bt(l,d.marker);f?c&&(d.to=null==f.to?null:f.to+u):d.to=r}}if(l)for(var p=0;p<l.length;++p){var g=l[p];null!=g.to&&(g.to+=u),null==g.from?Bt(a,g.marker)||(g.from=u,c&&(a||(a=[])).push(g)):(g.from+=u,c&&(a||(a=[])).push(g))}a&&(a=$t(a)),l&&l!=a&&(l=$t(l));var m=[a];if(!c){var v,y=t.text.length-2;if(y>0&&a)for(var b=0;b<a.length;++b)null==a[b].to&&(v||(v=[])).push(new Ft(a[b].marker,null,null));for(var _=0;_<y;++_)m.push(v);m.push(l)}return m}function $t(e){for(var t=0;t<e.length;++t){var n=e[t];null!=n.from&&n.from==n.to&&!1!==n.marker.clearWhenEmpty&&e.splice(t--,1)}return e.length?e:null}function Vt(e,t,n){var i=null;if(e.iter(t.line,n.line+1,(function(e){if(e.markedSpans)for(var t=0;t<e.markedSpans.length;++t){var n=e.markedSpans[t].marker;!n.readOnly||i&&-1!=$(i,n)||(i||(i=[])).push(n)}})),!i)return null;for(var r=[{from:t,to:n}],o=0;o<i.length;++o)for(var s=i[o],a=s.find(0),l=0;l<r.length;++l){var c=r[l];if(!(ht(c.to,a.from)<0||ht(c.from,a.to)>0)){var u=[l,1],h=ht(c.from,a.from),d=ht(c.to,a.to);(h<0||!s.inclusiveLeft&&!h)&&u.push({from:c.from,to:a.from}),(d>0||!s.inclusiveRight&&!d)&&u.push({from:a.to,to:c.to}),r.splice.apply(r,u),l+=u.length-3}}return r}function Kt(e){var t=e.markedSpans;if(t){for(var n=0;n<t.length;++n)t[n].marker.detachLine(e);e.markedSpans=null}}function Gt(e,t){if(t){for(var n=0;n<t.length;++n)t[n].marker.attachLine(e);e.markedSpans=t}}function Xt(e){return e.inclusiveLeft?-1:0}function Yt(e){return e.inclusiveRight?1:0}function Jt(e,t){var n=e.lines.length-t.lines.length;if(0!=n)return n;var i=e.find(),r=t.find(),o=ht(i.from,r.from)||Xt(e)-Xt(t);if(o)return-o;var s=ht(i.to,r.to)||Yt(e)-Yt(t);return s||t.id-e.id}function Zt(e,t){var n,i=Ht&&e.markedSpans;if(i)for(var r=void 0,o=0;o<i.length;++o)(r=i[o]).marker.collapsed&&null==(t?r.from:r.to)&&(!n||Jt(n,r.marker)<0)&&(n=r.marker);return n}function Qt(e){return Zt(e,!0)}function en(e){return Zt(e,!1)}function tn(e,t){var n,i=Ht&&e.markedSpans;if(i)for(var r=0;r<i.length;++r){var o=i[r];o.marker.collapsed&&(null==o.from||o.from<t)&&(null==o.to||o.to>t)&&(!n||Jt(n,o.marker)<0)&&(n=o.marker)}return n}function nn(e,t,n,i,r){var o=nt(e,t),s=Ht&&o.markedSpans;if(s)for(var a=0;a<s.length;++a){var l=s[a];if(l.marker.collapsed){var c=l.marker.find(0),u=ht(c.from,n)||Xt(l.marker)-Xt(r),h=ht(c.to,i)||Yt(l.marker)-Yt(r);if(!(u>=0&&h<=0||u<=0&&h>=0)&&(u<=0&&(l.marker.inclusiveRight&&r.inclusiveLeft?ht(c.to,n)>=0:ht(c.to,n)>0)||u>=0&&(l.marker.inclusiveRight&&r.inclusiveLeft?ht(c.from,i)<=0:ht(c.from,i)<0)))return!0}}}function rn(e){for(var t;t=Qt(e);)e=t.find(-1,!0).line;return e}function on(e){for(var t;t=en(e);)e=t.find(1,!0).line;return e}function sn(e){for(var t,n;t=en(e);)e=t.find(1,!0).line,(n||(n=[])).push(e);return n}function an(e,t){var n=nt(e,t),i=rn(n);return n==i?t:st(i)}function ln(e,t){if(t>e.lastLine())return t;var n,i=nt(e,t);if(!cn(e,i))return t;for(;n=en(i);)i=n.find(1,!0).line;return st(i)+1}function cn(e,t){var n=Ht&&t.markedSpans;if(n)for(var i=void 0,r=0;r<n.length;++r)if((i=n[r]).marker.collapsed){if(null==i.from)return!0;if(!i.marker.widgetNode&&0==i.from&&i.marker.inclusiveLeft&&un(e,t,i))return!0}}function un(e,t,n){if(null==n.to){var i=n.marker.find(1,!0);return un(e,i.line,Bt(i.line.markedSpans,n.marker))}if(n.marker.inclusiveRight&&n.to==t.text.length)return!0;for(var r=void 0,o=0;o<t.markedSpans.length;++o)if((r=t.markedSpans[o]).marker.collapsed&&!r.marker.widgetNode&&r.from==n.to&&(null==r.to||r.to!=n.from)&&(r.marker.inclusiveLeft||n.marker.inclusiveRight)&&un(e,t,r))return!0}function hn(e){for(var t=0,n=(e=rn(e)).parent,i=0;i<n.lines.length;++i){var r=n.lines[i];if(r==e)break;t+=r.height}for(var o=n.parent;o;o=(n=o).parent)for(var s=0;s<o.children.length;++s){var a=o.children[s];if(a==n)break;t+=a.height}return t}function dn(e){if(0==e.height)return 0;for(var t,n=e.text.length,i=e;t=Qt(i);){var r=t.find(0,!0);i=r.from.line,n+=r.from.ch-r.to.ch}for(i=e;t=en(i);){var o=t.find(0,!0);n-=i.text.length-o.from.ch,n+=(i=o.to.line).text.length-o.to.ch}return n}function fn(e){var t=e.display,n=e.doc;t.maxLine=nt(n,n.first),t.maxLineLength=dn(t.maxLine),t.maxLineChanged=!0,n.iter((function(e){var n=dn(e);n>t.maxLineLength&&(t.maxLineLength=n,t.maxLine=e)}))}var pn=function(e,t,n){this.text=e,Gt(this,t),this.height=n?n(this):1};function gn(e,t,n,i){e.text=t,e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null),null!=e.order&&(e.order=null),Kt(e),Gt(e,n);var r=i?i(e):1;r!=e.height&&ot(e,r)}function mn(e){e.parent=null,Kt(e)}pn.prototype.lineNo=function(){return st(this)},Le(pn);var vn={},yn={};function bn(e,t){if(!e||/^\s*$/.test(e))return null;var n=t.addModeClass?yn:vn;return n[e]||(n[e]=e.replace(/\S+/g,"cm-$&"))}function _n(e,t){var n=E("span",null,null,l?"padding-right: .1px":null),i={pre:E("pre",[n],"CodeMirror-line"),content:n,col:0,pos:0,cm:e,trailingSpace:!1,splitSpaces:e.getOption("lineWrapping")};t.measure={};for(var r=0;r<=(t.rest?t.rest.length:0);r++){var o=r?t.rest[r-1]:t.line,s=void 0;i.pos=0,i.addToken=xn,Ie(e.display.measure)&&(s=ve(o,e.doc.direction))&&(i.addToken=Cn(i.addToken,s)),i.map=[],Ln(o,i,kt(e,o,t!=e.display.externalMeasured&&st(o))),o.styleClasses&&(o.styleClasses.bgClass&&(i.bgClass=H(o.styleClasses.bgClass,i.bgClass||"")),o.styleClasses.textClass&&(i.textClass=H(o.styleClasses.textClass,i.textClass||""))),0==i.map.length&&i.map.push(0,0,i.content.appendChild(We(e.display.measure))),0==r?(t.measure.map=i.map,t.measure.cache={}):((t.measure.maps||(t.measure.maps=[])).push(i.map),(t.measure.caches||(t.measure.caches=[])).push({}))}if(l){var a=i.content.lastChild;(/\bcm-tab\b/.test(a.className)||a.querySelector&&a.querySelector(".cm-tab"))&&(i.content.className="cm-tab-wrap-hack")}return xe(e,"renderLine",e,t.line,i.pre),i.pre.className&&(i.textClass=H(i.pre.className,i.textClass||"")),i}function wn(e){var t=O("span","•","cm-invalidchar");return t.title="\\u"+e.charCodeAt(0).toString(16),t.setAttribute("aria-label",t.title),t}function xn(e,t,n,i,r,o,l){if(t){var c,u=e.splitSpaces?kn(t,e.trailingSpace):t,h=e.cm.state.specialChars,d=!1;if(h.test(t)){c=document.createDocumentFragment();for(var f=0;;){h.lastIndex=f;var p=h.exec(t),g=p?p.index-f:t.length-f;if(g){var m=document.createTextNode(u.slice(f,f+g));s&&a<9?c.appendChild(O("span",[m])):c.appendChild(m),e.map.push(e.pos,e.pos+g,m),e.col+=g,e.pos+=g}if(!p)break;f+=g+1;var v=void 0;if("\t"==p[0]){var y=e.cm.options.tabSize,b=y-e.col%y;(v=c.appendChild(O("span",Q(b),"cm-tab"))).setAttribute("role","presentation"),v.setAttribute("cm-text","\t"),e.col+=b}else"\r"==p[0]||"\n"==p[0]?((v=c.appendChild(O("span","\r"==p[0]?"␍":"␤","cm-invalidchar"))).setAttribute("cm-text",p[0]),e.col+=1):((v=e.cm.options.specialCharPlaceholder(p[0])).setAttribute("cm-text",p[0]),s&&a<9?c.appendChild(O("span",[v])):c.appendChild(v),e.col+=1);e.map.push(e.pos,e.pos+1,v),e.pos++}}else e.col+=t.length,c=document.createTextNode(u),e.map.push(e.pos,e.pos+t.length,c),s&&a<9&&(d=!0),e.pos+=t.length;if(e.trailingSpace=32==u.charCodeAt(t.length-1),n||i||r||d||o||l){var _=n||"";i&&(_+=i),r&&(_+=r);var w=O("span",[c],_,o);if(l)for(var x in l)l.hasOwnProperty(x)&&"style"!=x&&"class"!=x&&w.setAttribute(x,l[x]);return e.content.appendChild(w)}e.content.appendChild(c)}}function kn(e,t){if(e.length>1&&!/  /.test(e))return e;for(var n=t,i="",r=0;r<e.length;r++){var o=e.charAt(r);" "!=o||!n||r!=e.length-1&&32!=e.charCodeAt(r+1)||(o=" "),i+=o,n=" "==o}return i}function Cn(e,t){return function(n,i,r,o,s,a,l){r=r?r+" cm-force-border":"cm-force-border";for(var c=n.pos,u=c+i.length;;){for(var h=void 0,d=0;d<t.length&&!((h=t[d]).to>c&&h.from<=c);d++);if(h.to>=u)return e(n,i,r,o,s,a,l);e(n,i.slice(0,h.to-c),r,o,null,a,l),o=null,i=i.slice(h.to-c),c=h.to}}}function Sn(e,t,n,i){var r=!i&&n.widgetNode;r&&e.map.push(e.pos,e.pos+t,r),!i&&e.cm.display.input.needsContentAttribute&&(r||(r=e.content.appendChild(document.createElement("span"))),r.setAttribute("cm-marker",n.id)),r&&(e.cm.display.input.setUneditable(r),e.content.appendChild(r)),e.pos+=t,e.trailingSpace=!1}function Ln(e,t,n){var i=e.markedSpans,r=e.text,o=0;if(i)for(var s,a,l,c,u,h,d,f=r.length,p=0,g=1,m="",v=0;;){if(v==p){l=c=u=a="",d=null,h=null,v=1/0;for(var y=[],b=void 0,_=0;_<i.length;++_){var w=i[_],x=w.marker;if("bookmark"==x.type&&w.from==p&&x.widgetNode)y.push(x);else if(w.from<=p&&(null==w.to||w.to>p||x.collapsed&&w.to==p&&w.from==p)){if(null!=w.to&&w.to!=p&&v>w.to&&(v=w.to,c=""),x.className&&(l+=" "+x.className),x.css&&(a=(a?a+";":"")+x.css),x.startStyle&&w.from==p&&(u+=" "+x.startStyle),x.endStyle&&w.to==v&&(b||(b=[])).push(x.endStyle,w.to),x.title&&((d||(d={})).title=x.title),x.attributes)for(var k in x.attributes)(d||(d={}))[k]=x.attributes[k];x.collapsed&&(!h||Jt(h.marker,x)<0)&&(h=w)}else w.from>p&&v>w.from&&(v=w.from)}if(b)for(var C=0;C<b.length;C+=2)b[C+1]==v&&(c+=" "+b[C]);if(!h||h.from==p)for(var S=0;S<y.length;++S)Sn(t,0,y[S]);if(h&&(h.from||0)==p){if(Sn(t,(null==h.to?f+1:h.to)-p,h.marker,null==h.from),null==h.to)return;h.to==p&&(h=!1)}}if(p>=f)break;for(var L=Math.min(f,v);;){if(m){var T=p+m.length;if(!h){var A=T>L?m.slice(0,L-p):m;t.addToken(t,A,s?s+l:l,u,p+A.length==v?c:"",a,d)}if(T>=L){m=m.slice(L-p),p=L;break}p=T,u=""}m=r.slice(o,o=n[g++]),s=bn(n[g++],t.cm.options)}}else for(var M=1;M<n.length;M+=2)t.addToken(t,r.slice(o,o=n[M]),bn(n[M+1],t.cm.options))}function Tn(e,t,n){this.line=t,this.rest=sn(t),this.size=this.rest?st(ee(this.rest))-n+1:1,this.node=this.text=null,this.hidden=cn(e,t)}function An(e,t,n){for(var i,r=[],o=t;o<n;o=i){var s=new Tn(e.doc,nt(e.doc,o),o);i=o+s.size,r.push(s)}return r}var Mn=null;function On(e){Mn?Mn.ops.push(e):e.ownsGroup=Mn={ops:[e],delayedCallbacks:[]}}function En(e){var t=e.delayedCallbacks,n=0;do{for(;n<t.length;n++)t[n].call(null);for(var i=0;i<e.ops.length;i++){var r=e.ops[i];if(r.cursorActivityHandlers)for(;r.cursorActivityCalled<r.cursorActivityHandlers.length;)r.cursorActivityHandlers[r.cursorActivityCalled++].call(null,r.cm)}}while(n<t.length)}function Nn(e,t){var n=e.ownsGroup;if(n)try{En(n)}finally{Mn=null,t(n)}}var Dn=null;function Pn(e,t){var n=_e(e,t);if(n.length){var i,r=Array.prototype.slice.call(arguments,2);Mn?i=Mn.delayedCallbacks:Dn?i=Dn:(i=Dn=[],setTimeout(Hn,0));for(var o=function(e){i.push((function(){return n[e].apply(null,r)}))},s=0;s<n.length;++s)o(s)}}function Hn(){var e=Dn;Dn=null;for(var t=0;t<e.length;++t)e[t]()}function Wn(e,t,n,i){for(var r=0;r<t.changes.length;r++){var o=t.changes[r];"text"==o?Rn(e,t):"gutter"==o?jn(e,t,n,i):"class"==o?zn(e,t):"widget"==o&&qn(e,t,i)}t.changes=null}function In(e){return e.node==e.text&&(e.node=O("div",null,null,"position: relative"),e.text.parentNode&&e.text.parentNode.replaceChild(e.node,e.text),e.node.appendChild(e.text),s&&a<8&&(e.node.style.zIndex=2)),e.node}function Fn(e,t){var n=t.bgClass?t.bgClass+" "+(t.line.bgClass||""):t.line.bgClass;if(n&&(n+=" CodeMirror-linebackground"),t.background)n?t.background.className=n:(t.background.parentNode.removeChild(t.background),t.background=null);else if(n){var i=In(t);t.background=i.insertBefore(O("div",null,n),i.firstChild),e.display.input.setUneditable(t.background)}}function Bn(e,t){var n=e.display.externalMeasured;return n&&n.line==t.line?(e.display.externalMeasured=null,t.measure=n.measure,n.built):_n(e,t)}function Rn(e,t){var n=t.text.className,i=Bn(e,t);t.text==t.node&&(t.node=i.pre),t.text.parentNode.replaceChild(i.pre,t.text),t.text=i.pre,i.bgClass!=t.bgClass||i.textClass!=t.textClass?(t.bgClass=i.bgClass,t.textClass=i.textClass,zn(e,t)):n&&(t.text.className=n)}function zn(e,t){Fn(e,t),t.line.wrapClass?In(t).className=t.line.wrapClass:t.node!=t.text&&(t.node.className="");var n=t.textClass?t.textClass+" "+(t.line.textClass||""):t.line.textClass;t.text.className=n||""}function jn(e,t,n,i){if(t.gutter&&(t.node.removeChild(t.gutter),t.gutter=null),t.gutterBackground&&(t.node.removeChild(t.gutterBackground),t.gutterBackground=null),t.line.gutterClass){var r=In(t);t.gutterBackground=O("div",null,"CodeMirror-gutter-background "+t.line.gutterClass,"left: "+(e.options.fixedGutter?i.fixedPos:-i.gutterTotalWidth)+"px; width: "+i.gutterTotalWidth+"px"),e.display.input.setUneditable(t.gutterBackground),r.insertBefore(t.gutterBackground,t.text)}var o=t.line.gutterMarkers;if(e.options.lineNumbers||o){var s=In(t),a=t.gutter=O("div",null,"CodeMirror-gutter-wrapper","left: "+(e.options.fixedGutter?i.fixedPos:-i.gutterTotalWidth)+"px");if(a.setAttribute("aria-hidden","true"),e.display.input.setUneditable(a),s.insertBefore(a,t.text),t.line.gutterClass&&(a.className+=" "+t.line.gutterClass),!e.options.lineNumbers||o&&o["CodeMirror-linenumbers"]||(t.lineNumber=a.appendChild(O("div",ct(e.options,n),"CodeMirror-linenumber CodeMirror-gutter-elt","left: "+i.gutterLeft["CodeMirror-linenumbers"]+"px; width: "+e.display.lineNumInnerWidth+"px"))),o)for(var l=0;l<e.display.gutterSpecs.length;++l){var c=e.display.gutterSpecs[l].className,u=o.hasOwnProperty(c)&&o[c];u&&a.appendChild(O("div",[u],"CodeMirror-gutter-elt","left: "+i.gutterLeft[c]+"px; width: "+i.gutterWidth[c]+"px"))}}}function qn(e,t,n){t.alignable&&(t.alignable=null);for(var i=S("CodeMirror-linewidget"),r=t.node.firstChild,o=void 0;r;r=o)o=r.nextSibling,i.test(r.className)&&t.node.removeChild(r);$n(e,t,n)}function Un(e,t,n,i){var r=Bn(e,t);return t.text=t.node=r.pre,r.bgClass&&(t.bgClass=r.bgClass),r.textClass&&(t.textClass=r.textClass),zn(e,t),jn(e,t,n,i),$n(e,t,i),t.node}function $n(e,t,n){if(Vn(e,t.line,t,n,!0),t.rest)for(var i=0;i<t.rest.length;i++)Vn(e,t.rest[i],t,n,!1)}function Vn(e,t,n,i,r){if(t.widgets)for(var o=In(n),s=0,a=t.widgets;s<a.length;++s){var l=a[s],c=O("div",[l.node],"CodeMirror-linewidget"+(l.className?" "+l.className:""));l.handleMouseEvents||c.setAttribute("cm-ignore-events","true"),Kn(l,c,n,i),e.display.input.setUneditable(c),r&&l.above?o.insertBefore(c,n.gutter||n.text):o.appendChild(c),Pn(l,"redraw")}}function Kn(e,t,n,i){if(e.noHScroll){(n.alignable||(n.alignable=[])).push(t);var r=i.wrapperWidth;t.style.left=i.fixedPos+"px",e.coverGutter||(r-=i.gutterTotalWidth,t.style.paddingLeft=i.gutterTotalWidth+"px"),t.style.width=r+"px"}e.coverGutter&&(t.style.zIndex=5,t.style.position="relative",e.noHScroll||(t.style.marginLeft=-i.gutterTotalWidth+"px"))}function Gn(e){if(null!=e.height)return e.height;var t=e.doc.cm;if(!t)return 0;if(!N(document.body,e.node)){var n="position: relative;";e.coverGutter&&(n+="margin-left: -"+t.display.gutters.offsetWidth+"px;"),e.noHScroll&&(n+="width: "+t.display.wrapper.clientWidth+"px;"),M(t.display.measure,O("div",[e.node],null,n))}return e.height=e.node.parentNode.offsetHeight}function Xn(e,t){for(var n=Ee(t);n!=e.wrapper;n=n.parentNode)if(!n||1==n.nodeType&&"true"==n.getAttribute("cm-ignore-events")||n.parentNode==e.sizer&&n!=e.mover)return!0}function Yn(e){return e.lineSpace.offsetTop}function Jn(e){return e.mover.offsetHeight-e.lineSpace.offsetHeight}function Zn(e){if(e.cachedPaddingH)return e.cachedPaddingH;var t=M(e.measure,O("pre","x","CodeMirror-line-like")),n=window.getComputedStyle?window.getComputedStyle(t):t.currentStyle,i={left:parseInt(n.paddingLeft),right:parseInt(n.paddingRight)};return isNaN(i.left)||isNaN(i.right)||(e.cachedPaddingH=i),i}function Qn(e){return V-e.display.nativeBarWidth}function ei(e){return e.display.scroller.clientWidth-Qn(e)-e.display.barWidth}function ti(e){return e.display.scroller.clientHeight-Qn(e)-e.display.barHeight}function ni(e,t,n){var i=e.options.lineWrapping,r=i&&ei(e);if(!t.measure.heights||i&&t.measure.width!=r){var o=t.measure.heights=[];if(i){t.measure.width=r;for(var s=t.text.firstChild.getClientRects(),a=0;a<s.length-1;a++){var l=s[a],c=s[a+1];Math.abs(l.bottom-c.bottom)>2&&o.push((l.bottom+c.top)/2-n.top)}}o.push(n.bottom-n.top)}}function ii(e,t,n){if(e.line==t)return{map:e.measure.map,cache:e.measure.cache};if(e.rest){for(var i=0;i<e.rest.length;i++)if(e.rest[i]==t)return{map:e.measure.maps[i],cache:e.measure.caches[i]};for(var r=0;r<e.rest.length;r++)if(st(e.rest[r])>n)return{map:e.measure.maps[r],cache:e.measure.caches[r],before:!0}}}function ri(e,t){var n=st(t=rn(t)),i=e.display.externalMeasured=new Tn(e.doc,t,n);i.lineN=n;var r=i.built=_n(e,i);return i.text=r.pre,M(e.display.lineMeasure,r.pre),i}function oi(e,t,n,i){return li(e,ai(e,t),n,i)}function si(e,t){if(t>=e.display.viewFrom&&t<e.display.viewTo)return e.display.view[zi(e,t)];var n=e.display.externalMeasured;return n&&t>=n.lineN&&t<n.lineN+n.size?n:void 0}function ai(e,t){var n=st(t),i=si(e,n);i&&!i.text?i=null:i&&i.changes&&(Wn(e,i,n,Wi(e)),e.curOp.forceUpdate=!0),i||(i=ri(e,t));var r=ii(i,t,n);return{line:t,view:i,rect:null,map:r.map,cache:r.cache,before:r.before,hasHeights:!1}}function li(e,t,n,i,r){t.before&&(n=-1);var o,s=n+(i||"");return t.cache.hasOwnProperty(s)?o=t.cache[s]:(t.rect||(t.rect=t.view.text.getBoundingClientRect()),t.hasHeights||(ni(e,t.view,t.rect),t.hasHeights=!0),(o=fi(e,t,n,i)).bogus||(t.cache[s]=o)),{left:o.left,right:o.right,top:r?o.rtop:o.top,bottom:r?o.rbottom:o.bottom}}var ci,ui={left:0,right:0,top:0,bottom:0};function hi(e,t,n){for(var i,r,o,s,a,l,c=0;c<e.length;c+=3)if(a=e[c],l=e[c+1],t<a?(r=0,o=1,s="left"):t<l?o=1+(r=t-a):(c==e.length-3||t==l&&e[c+3]>t)&&(r=(o=l-a)-1,t>=l&&(s="right")),null!=r){if(i=e[c+2],a==l&&n==(i.insertLeft?"left":"right")&&(s=n),"left"==n&&0==r)for(;c&&e[c-2]==e[c-3]&&e[c-1].insertLeft;)i=e[2+(c-=3)],s="left";if("right"==n&&r==l-a)for(;c<e.length-3&&e[c+3]==e[c+4]&&!e[c+5].insertLeft;)i=e[(c+=3)+2],s="right";break}return{node:i,start:r,end:o,collapse:s,coverStart:a,coverEnd:l}}function di(e,t){var n=ui;if("left"==t)for(var i=0;i<e.length&&(n=e[i]).left==n.right;i++);else for(var r=e.length-1;r>=0&&(n=e[r]).left==n.right;r--);return n}function fi(e,t,n,i){var r,o=hi(t.map,n,i),l=o.node,c=o.start,u=o.end,h=o.collapse;if(3==l.nodeType){for(var d=0;d<4;d++){for(;c&&ue(t.line.text.charAt(o.coverStart+c));)--c;for(;o.coverStart+u<o.coverEnd&&ue(t.line.text.charAt(o.coverStart+u));)++u;if((r=s&&a<9&&0==c&&u==o.coverEnd-o.coverStart?l.parentNode.getBoundingClientRect():di(L(l,c,u).getClientRects(),i)).left||r.right||0==c)break;u=c,c-=1,h="right"}s&&a<11&&(r=pi(e.display.measure,r))}else{var f;c>0&&(h=i="right"),r=e.options.lineWrapping&&(f=l.getClientRects()).length>1?f["right"==i?f.length-1:0]:l.getBoundingClientRect()}if(s&&a<9&&!c&&(!r||!r.left&&!r.right)){var p=l.parentNode.getClientRects()[0];r=p?{left:p.left,right:p.left+Hi(e.display),top:p.top,bottom:p.bottom}:ui}for(var g=r.top-t.rect.top,m=r.bottom-t.rect.top,v=(g+m)/2,y=t.view.measure.heights,b=0;b<y.length-1&&!(v<y[b]);b++);var _=b?y[b-1]:0,w=y[b],x={left:("right"==h?r.right:r.left)-t.rect.left,right:("left"==h?r.left:r.right)-t.rect.left,top:_,bottom:w};return r.left||r.right||(x.bogus=!0),e.options.singleCursorHeightPerLine||(x.rtop=g,x.rbottom=m),x}function pi(e,t){if(!window.screen||null==screen.logicalXDPI||screen.logicalXDPI==screen.deviceXDPI||!qe(e))return t;var n=screen.logicalXDPI/screen.deviceXDPI,i=screen.logicalYDPI/screen.deviceYDPI;return{left:t.left*n,right:t.right*n,top:t.top*i,bottom:t.bottom*i}}function gi(e){if(e.measure&&(e.measure.cache={},e.measure.heights=null,e.rest))for(var t=0;t<e.rest.length;t++)e.measure.caches[t]={}}function mi(e){e.display.externalMeasure=null,A(e.display.lineMeasure);for(var t=0;t<e.display.view.length;t++)gi(e.display.view[t])}function vi(e){mi(e),e.display.cachedCharWidth=e.display.cachedTextHeight=e.display.cachedPaddingH=null,e.options.lineWrapping||(e.display.maxLineChanged=!0),e.display.lineNumChars=null}function yi(e){return u&&v?-(e.body.getBoundingClientRect().left-parseInt(getComputedStyle(e.body).marginLeft)):e.defaultView.pageXOffset||(e.documentElement||e.body).scrollLeft}function bi(e){return u&&v?-(e.body.getBoundingClientRect().top-parseInt(getComputedStyle(e.body).marginTop)):e.defaultView.pageYOffset||(e.documentElement||e.body).scrollTop}function _i(e){var t=rn(e).widgets,n=0;if(t)for(var i=0;i<t.length;++i)t[i].above&&(n+=Gn(t[i]));return n}function wi(e,t,n,i,r){if(!r){var o=_i(t);n.top+=o,n.bottom+=o}if("line"==i)return n;i||(i="local");var s=hn(t);if("local"==i?s+=Yn(e.display):s-=e.display.viewOffset,"page"==i||"window"==i){var a=e.display.lineSpace.getBoundingClientRect();s+=a.top+("window"==i?0:bi(I(e)));var l=a.left+("window"==i?0:yi(I(e)));n.left+=l,n.right+=l}return n.top+=s,n.bottom+=s,n}function xi(e,t,n){if("div"==n)return t;var i=t.left,r=t.top;if("page"==n)i-=yi(I(e)),r-=bi(I(e));else if("local"==n||!n){var o=e.display.sizer.getBoundingClientRect();i+=o.left,r+=o.top}var s=e.display.lineSpace.getBoundingClientRect();return{left:i-s.left,top:r-s.top}}function ki(e,t,n,i,r){return i||(i=nt(e.doc,t.line)),wi(e,i,oi(e,i,t.ch,r),n)}function Ci(e,t,n,i,r,o){function s(t,s){var a=li(e,r,t,s?"right":"left",o);return s?a.left=a.right:a.right=a.left,wi(e,i,a,n)}i=i||nt(e.doc,t.line),r||(r=ai(e,i));var a=ve(i,e.doc.direction),l=t.ch,c=t.sticky;if(l>=i.text.length?(l=i.text.length,c="before"):l<=0&&(l=0,c="after"),!a)return s("before"==c?l-1:l,"before"==c);function u(e,t,n){return s(n?e-1:e,1==a[t].level!=n)}var h=ge(a,l,c),d=pe,f=u(l,h,"before"==c);return null!=d&&(f.other=u(l,d,"before"!=c)),f}function Si(e,t){var n=0;t=vt(e.doc,t),e.options.lineWrapping||(n=Hi(e.display)*t.ch);var i=nt(e.doc,t.line),r=hn(i)+Yn(e.display);return{left:n,right:n,top:r,bottom:r+i.height}}function Li(e,t,n,i,r){var o=ut(e,t,n);return o.xRel=r,i&&(o.outside=i),o}function Ti(e,t,n){var i=e.doc;if((n+=e.display.viewOffset)<0)return Li(i.first,0,null,-1,-1);var r=at(i,n),o=i.first+i.size-1;if(r>o)return Li(i.first+i.size-1,nt(i,o).text.length,null,1,1);t<0&&(t=0);for(var s=nt(i,r);;){var a=Ei(e,s,r,t,n),l=tn(s,a.ch+(a.xRel>0||a.outside>0?1:0));if(!l)return a;var c=l.find(1);if(c.line==r)return c;s=nt(i,r=c.line)}}function Ai(e,t,n,i){i-=_i(t);var r=t.text.length,o=de((function(t){return li(e,n,t-1).bottom<=i}),r,0);return{begin:o,end:r=de((function(t){return li(e,n,t).top>i}),o,r)}}function Mi(e,t,n,i){return n||(n=ai(e,t)),Ai(e,t,n,wi(e,t,li(e,n,i),"line").top)}function Oi(e,t,n,i){return!(e.bottom<=n)&&(e.top>n||(i?e.left:e.right)>t)}function Ei(e,t,n,i,r){r-=hn(t);var o=ai(e,t),s=_i(t),a=0,l=t.text.length,c=!0,u=ve(t,e.doc.direction);if(u){var h=(e.options.lineWrapping?Di:Ni)(e,t,n,o,u,i,r);a=(c=1!=h.level)?h.from:h.to-1,l=c?h.to:h.from-1}var d,f,p=null,g=null,m=de((function(t){var n=li(e,o,t);return n.top+=s,n.bottom+=s,!!Oi(n,i,r,!1)&&(n.top<=r&&n.left<=i&&(p=t,g=n),!0)}),a,l),v=!1;if(g){var y=i-g.left<g.right-i,b=y==c;m=p+(b?0:1),f=b?"after":"before",d=y?g.left:g.right}else{c||m!=l&&m!=a||m++,f=0==m?"after":m==t.text.length?"before":li(e,o,m-(c?1:0)).bottom+s<=r==c?"after":"before";var _=Ci(e,ut(n,m,f),"line",t,o);d=_.left,v=r<_.top?-1:r>=_.bottom?1:0}return Li(n,m=he(t.text,m,1),f,v,i-d)}function Ni(e,t,n,i,r,o,s){var a=de((function(a){var l=r[a],c=1!=l.level;return Oi(Ci(e,ut(n,c?l.to:l.from,c?"before":"after"),"line",t,i),o,s,!0)}),0,r.length-1),l=r[a];if(a>0){var c=1!=l.level,u=Ci(e,ut(n,c?l.from:l.to,c?"after":"before"),"line",t,i);Oi(u,o,s,!0)&&u.top>s&&(l=r[a-1])}return l}function Di(e,t,n,i,r,o,s){var a=Ai(e,t,i,s),l=a.begin,c=a.end;/\s/.test(t.text.charAt(c-1))&&c--;for(var u=null,h=null,d=0;d<r.length;d++){var f=r[d];if(!(f.from>=c||f.to<=l)){var p=li(e,i,1!=f.level?Math.min(c,f.to)-1:Math.max(l,f.from)).right,g=p<o?o-p+1e9:p-o;(!u||h>g)&&(u=f,h=g)}}return u||(u=r[r.length-1]),u.from<l&&(u={from:l,to:u.to,level:u.level}),u.to>c&&(u={from:u.from,to:c,level:u.level}),u}function Pi(e){if(null!=e.cachedTextHeight)return e.cachedTextHeight;if(null==ci){ci=O("pre",null,"CodeMirror-line-like");for(var t=0;t<49;++t)ci.appendChild(document.createTextNode("x")),ci.appendChild(O("br"));ci.appendChild(document.createTextNode("x"))}M(e.measure,ci);var n=ci.offsetHeight/50;return n>3&&(e.cachedTextHeight=n),A(e.measure),n||1}function Hi(e){if(null!=e.cachedCharWidth)return e.cachedCharWidth;var t=O("span","xxxxxxxxxx"),n=O("pre",[t],"CodeMirror-line-like");M(e.measure,n);var i=t.getBoundingClientRect(),r=(i.right-i.left)/10;return r>2&&(e.cachedCharWidth=r),r||10}function Wi(e){for(var t=e.display,n={},i={},r=t.gutters.clientLeft,o=t.gutters.firstChild,s=0;o;o=o.nextSibling,++s){var a=e.display.gutterSpecs[s].className;n[a]=o.offsetLeft+o.clientLeft+r,i[a]=o.clientWidth}return{fixedPos:Ii(t),gutterTotalWidth:t.gutters.offsetWidth,gutterLeft:n,gutterWidth:i,wrapperWidth:t.wrapper.clientWidth}}function Ii(e){return e.scroller.getBoundingClientRect().left-e.sizer.getBoundingClientRect().left}function Fi(e){var t=Pi(e.display),n=e.options.lineWrapping,i=n&&Math.max(5,e.display.scroller.clientWidth/Hi(e.display)-3);return function(r){if(cn(e.doc,r))return 0;var o=0;if(r.widgets)for(var s=0;s<r.widgets.length;s++)r.widgets[s].height&&(o+=r.widgets[s].height);return n?o+(Math.ceil(r.text.length/i)||1)*t:o+t}}function Bi(e){var t=e.doc,n=Fi(e);t.iter((function(e){var t=n(e);t!=e.height&&ot(e,t)}))}function Ri(e,t,n,i){var r=e.display;if(!n&&"true"==Ee(t).getAttribute("cm-not-content"))return null;var o,s,a=r.lineSpace.getBoundingClientRect();try{o=t.clientX-a.left,s=t.clientY-a.top}catch(e){return null}var l,c=Ti(e,o,s);if(i&&c.xRel>0&&(l=nt(e.doc,c.line).text).length==c.ch){var u=q(l,l.length,e.options.tabSize)-l.length;c=ut(c.line,Math.max(0,Math.round((o-Zn(e.display).left)/Hi(e.display))-u))}return c}function zi(e,t){if(t>=e.display.viewTo)return null;if((t-=e.display.viewFrom)<0)return null;for(var n=e.display.view,i=0;i<n.length;i++)if((t-=n[i].size)<0)return i}function ji(e,t,n,i){null==t&&(t=e.doc.first),null==n&&(n=e.doc.first+e.doc.size),i||(i=0);var r=e.display;if(i&&n<r.viewTo&&(null==r.updateLineNumbers||r.updateLineNumbers>t)&&(r.updateLineNumbers=t),e.curOp.viewChanged=!0,t>=r.viewTo)Ht&&an(e.doc,t)<r.viewTo&&Ui(e);else if(n<=r.viewFrom)Ht&&ln(e.doc,n+i)>r.viewFrom?Ui(e):(r.viewFrom+=i,r.viewTo+=i);else if(t<=r.viewFrom&&n>=r.viewTo)Ui(e);else if(t<=r.viewFrom){var o=$i(e,n,n+i,1);o?(r.view=r.view.slice(o.index),r.viewFrom=o.lineN,r.viewTo+=i):Ui(e)}else if(n>=r.viewTo){var s=$i(e,t,t,-1);s?(r.view=r.view.slice(0,s.index),r.viewTo=s.lineN):Ui(e)}else{var a=$i(e,t,t,-1),l=$i(e,n,n+i,1);a&&l?(r.view=r.view.slice(0,a.index).concat(An(e,a.lineN,l.lineN)).concat(r.view.slice(l.index)),r.viewTo+=i):Ui(e)}var c=r.externalMeasured;c&&(n<c.lineN?c.lineN+=i:t<c.lineN+c.size&&(r.externalMeasured=null))}function qi(e,t,n){e.curOp.viewChanged=!0;var i=e.display,r=e.display.externalMeasured;if(r&&t>=r.lineN&&t<r.lineN+r.size&&(i.externalMeasured=null),!(t<i.viewFrom||t>=i.viewTo)){var o=i.view[zi(e,t)];if(null!=o.node){var s=o.changes||(o.changes=[]);-1==$(s,n)&&s.push(n)}}}function Ui(e){e.display.viewFrom=e.display.viewTo=e.doc.first,e.display.view=[],e.display.viewOffset=0}function $i(e,t,n,i){var r,o=zi(e,t),s=e.display.view;if(!Ht||n==e.doc.first+e.doc.size)return{index:o,lineN:n};for(var a=e.display.viewFrom,l=0;l<o;l++)a+=s[l].size;if(a!=t){if(i>0){if(o==s.length-1)return null;r=a+s[o].size-t,o++}else r=a-t;t+=r,n+=r}for(;an(e.doc,n)!=n;){if(o==(i<0?0:s.length-1))return null;n+=i*s[o-(i<0?1:0)].size,o+=i}return{index:o,lineN:n}}function Vi(e,t,n){var i=e.display;0==i.view.length||t>=i.viewTo||n<=i.viewFrom?(i.view=An(e,t,n),i.viewFrom=t):(i.viewFrom>t?i.view=An(e,t,i.viewFrom).concat(i.view):i.viewFrom<t&&(i.view=i.view.slice(zi(e,t))),i.viewFrom=t,i.viewTo<n?i.view=i.view.concat(An(e,i.viewTo,n)):i.viewTo>n&&(i.view=i.view.slice(0,zi(e,n)))),i.viewTo=n}function Ki(e){for(var t=e.display.view,n=0,i=0;i<t.length;i++){var r=t[i];r.hidden||r.node&&!r.changes||++n}return n}function Gi(e){e.display.input.showSelection(e.display.input.prepareSelection())}function Xi(e,t){void 0===t&&(t=!0);var n=e.doc,i={},r=i.cursors=document.createDocumentFragment(),o=i.selection=document.createDocumentFragment(),s=e.options.$customCursor;s&&(t=!0);for(var a=0;a<n.sel.ranges.length;a++)if(t||a!=n.sel.primIndex){var l=n.sel.ranges[a];if(!(l.from().line>=e.display.viewTo||l.to().line<e.display.viewFrom)){var c=l.empty();if(s){var u=s(e,l);u&&Yi(e,u,r)}else(c||e.options.showCursorWhenSelecting)&&Yi(e,l.head,r);c||Zi(e,l,o)}}return i}function Yi(e,t,n){var i=Ci(e,t,"div",null,null,!e.options.singleCursorHeightPerLine),r=n.appendChild(O("div"," ","CodeMirror-cursor"));if(r.style.left=i.left+"px",r.style.top=i.top+"px",r.style.height=Math.max(0,i.bottom-i.top)*e.options.cursorHeight+"px",/\bcm-fat-cursor\b/.test(e.getWrapperElement().className)){var o=ki(e,t,"div",null,null),s=o.right-o.left;r.style.width=(s>0?s:e.defaultCharWidth())+"px"}if(i.other){var a=n.appendChild(O("div"," ","CodeMirror-cursor CodeMirror-secondarycursor"));a.style.display="",a.style.left=i.other.left+"px",a.style.top=i.other.top+"px",a.style.height=.85*(i.other.bottom-i.other.top)+"px"}}function Ji(e,t){return e.top-t.top||e.left-t.left}function Zi(e,t,n){var i=e.display,r=e.doc,o=document.createDocumentFragment(),s=Zn(e.display),a=s.left,l=Math.max(i.sizerWidth,ei(e)-i.sizer.offsetLeft)-s.right,c="ltr"==r.direction;function u(e,t,n,i){t<0&&(t=0),t=Math.round(t),i=Math.round(i),o.appendChild(O("div",null,"CodeMirror-selected","position: absolute; left: "+e+"px;\n                             top: "+t+"px; width: "+(null==n?l-e:n)+"px;\n                             height: "+(i-t)+"px"))}function h(t,n,i){var o,s,h=nt(r,t),d=h.text.length;function f(n,i){return ki(e,ut(t,n),"div",h,i)}function p(t,n,i){var r=Mi(e,h,null,t),o="ltr"==n==("after"==i)?"left":"right";return f("after"==i?r.begin:r.end-(/\s/.test(h.text.charAt(r.end-1))?2:1),o)[o]}var g=ve(h,r.direction);return fe(g,n||0,null==i?d:i,(function(e,t,r,h){var m="ltr"==r,v=f(e,m?"left":"right"),y=f(t-1,m?"right":"left"),b=null==n&&0==e,_=null==i&&t==d,w=0==h,x=!g||h==g.length-1;if(y.top-v.top<=3){var k=(c?_:b)&&x,C=(c?b:_)&&w?a:(m?v:y).left,S=k?l:(m?y:v).right;u(C,v.top,S-C,v.bottom)}else{var L,T,A,M;m?(L=c&&b&&w?a:v.left,T=c?l:p(e,r,"before"),A=c?a:p(t,r,"after"),M=c&&_&&x?l:y.right):(L=c?p(e,r,"before"):a,T=!c&&b&&w?l:v.right,A=!c&&_&&x?a:y.left,M=c?p(t,r,"after"):l),u(L,v.top,T-L,v.bottom),v.bottom<y.top&&u(a,v.bottom,null,y.top),u(A,y.top,M-A,y.bottom)}(!o||Ji(v,o)<0)&&(o=v),Ji(y,o)<0&&(o=y),(!s||Ji(v,s)<0)&&(s=v),Ji(y,s)<0&&(s=y)})),{start:o,end:s}}var d=t.from(),f=t.to();if(d.line==f.line)h(d.line,d.ch,f.ch);else{var p=nt(r,d.line),g=nt(r,f.line),m=rn(p)==rn(g),v=h(d.line,d.ch,m?p.text.length+1:null).end,y=h(f.line,m?0:null,f.ch).start;m&&(v.top<y.top-2?(u(v.right,v.top,null,v.bottom),u(a,y.top,y.left,y.bottom)):u(v.right,v.top,y.left-v.right,v.bottom)),v.bottom<y.top&&u(a,v.bottom,null,y.top)}n.appendChild(o)}function Qi(e){if(e.state.focused){var t=e.display;clearInterval(t.blinker);var n=!0;t.cursorDiv.style.visibility="",e.options.cursorBlinkRate>0?t.blinker=setInterval((function(){e.hasFocus()||ir(e),t.cursorDiv.style.visibility=(n=!n)?"":"hidden"}),e.options.cursorBlinkRate):e.options.cursorBlinkRate<0&&(t.cursorDiv.style.visibility="hidden")}}function er(e){e.hasFocus()||(e.display.input.focus(),e.state.focused||nr(e))}function tr(e){e.state.delayingBlurEvent=!0,setTimeout((function(){e.state.delayingBlurEvent&&(e.state.delayingBlurEvent=!1,e.state.focused&&ir(e))}),100)}function nr(e,t){e.state.delayingBlurEvent&&!e.state.draggingText&&(e.state.delayingBlurEvent=!1),"nocursor"!=e.options.readOnly&&(e.state.focused||(xe(e,"focus",e,t),e.state.focused=!0,P(e.display.wrapper,"CodeMirror-focused"),e.curOp||e.display.selForContextMenu==e.doc.sel||(e.display.input.reset(),l&&setTimeout((function(){return e.display.input.reset(!0)}),20)),e.display.input.receivedFocus()),Qi(e))}function ir(e,t){e.state.delayingBlurEvent||(e.state.focused&&(xe(e,"blur",e,t),e.state.focused=!1,T(e.display.wrapper,"CodeMirror-focused")),clearInterval(e.display.blinker),setTimeout((function(){e.state.focused||(e.display.shift=!1)}),150))}function rr(e){for(var t=e.display,n=t.lineDiv.offsetTop,i=Math.max(0,t.scroller.getBoundingClientRect().top),r=t.lineDiv.getBoundingClientRect().top,o=0,l=0;l<t.view.length;l++){var c=t.view[l],u=e.options.lineWrapping,h=void 0,d=0;if(!c.hidden){if(r+=c.line.height,s&&a<8){var f=c.node.offsetTop+c.node.offsetHeight;h=f-n,n=f}else{var p=c.node.getBoundingClientRect();h=p.bottom-p.top,!u&&c.text.firstChild&&(d=c.text.firstChild.getBoundingClientRect().right-p.left-1)}var g=c.line.height-h;if((g>.005||g<-.005)&&(r<i&&(o-=g),ot(c.line,h),or(c.line),c.rest))for(var m=0;m<c.rest.length;m++)or(c.rest[m]);if(d>e.display.sizerWidth){var v=Math.ceil(d/Hi(e.display));v>e.display.maxLineLength&&(e.display.maxLineLength=v,e.display.maxLine=c.line,e.display.maxLineChanged=!0)}}}Math.abs(o)>2&&(t.scroller.scrollTop+=o)}function or(e){if(e.widgets)for(var t=0;t<e.widgets.length;++t){var n=e.widgets[t],i=n.node.parentNode;i&&(n.height=i.offsetHeight)}}function sr(e,t,n){var i=n&&null!=n.top?Math.max(0,n.top):e.scroller.scrollTop;i=Math.floor(i-Yn(e));var r=n&&null!=n.bottom?n.bottom:i+e.wrapper.clientHeight,o=at(t,i),s=at(t,r);if(n&&n.ensure){var a=n.ensure.from.line,l=n.ensure.to.line;a<o?(o=a,s=at(t,hn(nt(t,a))+e.wrapper.clientHeight)):Math.min(l,t.lastLine())>=s&&(o=at(t,hn(nt(t,l))-e.wrapper.clientHeight),s=l)}return{from:o,to:Math.max(s,o+1)}}function ar(e,t){if(!ke(e,"scrollCursorIntoView")){var n=e.display,i=n.sizer.getBoundingClientRect(),r=null,o=n.wrapper.ownerDocument;if(t.top+i.top<0?r=!0:t.bottom+i.top>(o.defaultView.innerHeight||o.documentElement.clientHeight)&&(r=!1),null!=r&&!g){var s=O("div","​",null,"position: absolute;\n                         top: "+(t.top-n.viewOffset-Yn(e.display))+"px;\n                         height: "+(t.bottom-t.top+Qn(e)+n.barHeight)+"px;\n                         left: "+t.left+"px; width: "+Math.max(2,t.right-t.left)+"px;");e.display.lineSpace.appendChild(s),s.scrollIntoView(r),e.display.lineSpace.removeChild(s)}}}function lr(e,t,n,i){var r;null==i&&(i=0),e.options.lineWrapping||t!=n||(n="before"==t.sticky?ut(t.line,t.ch+1,"before"):t,t=t.ch?ut(t.line,"before"==t.sticky?t.ch-1:t.ch,"after"):t);for(var o=0;o<5;o++){var s=!1,a=Ci(e,t),l=n&&n!=t?Ci(e,n):a,c=ur(e,r={left:Math.min(a.left,l.left),top:Math.min(a.top,l.top)-i,right:Math.max(a.left,l.left),bottom:Math.max(a.bottom,l.bottom)+i}),u=e.doc.scrollTop,h=e.doc.scrollLeft;if(null!=c.scrollTop&&(vr(e,c.scrollTop),Math.abs(e.doc.scrollTop-u)>1&&(s=!0)),null!=c.scrollLeft&&(br(e,c.scrollLeft),Math.abs(e.doc.scrollLeft-h)>1&&(s=!0)),!s)break}return r}function cr(e,t){var n=ur(e,t);null!=n.scrollTop&&vr(e,n.scrollTop),null!=n.scrollLeft&&br(e,n.scrollLeft)}function ur(e,t){var n=e.display,i=Pi(e.display);t.top<0&&(t.top=0);var r=e.curOp&&null!=e.curOp.scrollTop?e.curOp.scrollTop:n.scroller.scrollTop,o=ti(e),s={};t.bottom-t.top>o&&(t.bottom=t.top+o);var a=e.doc.height+Jn(n),l=t.top<i,c=t.bottom>a-i;if(t.top<r)s.scrollTop=l?0:t.top;else if(t.bottom>r+o){var u=Math.min(t.top,(c?a:t.bottom)-o);u!=r&&(s.scrollTop=u)}var h=e.options.fixedGutter?0:n.gutters.offsetWidth,d=e.curOp&&null!=e.curOp.scrollLeft?e.curOp.scrollLeft:n.scroller.scrollLeft-h,f=ei(e)-n.gutters.offsetWidth,p=t.right-t.left>f;return p&&(t.right=t.left+f),t.left<10?s.scrollLeft=0:t.left<d?s.scrollLeft=Math.max(0,t.left+h-(p?0:10)):t.right>f+d-3&&(s.scrollLeft=t.right+(p?0:10)-f),s}function hr(e,t){null!=t&&(gr(e),e.curOp.scrollTop=(null==e.curOp.scrollTop?e.doc.scrollTop:e.curOp.scrollTop)+t)}function dr(e){gr(e);var t=e.getCursor();e.curOp.scrollToPos={from:t,to:t,margin:e.options.cursorScrollMargin}}function fr(e,t,n){null==t&&null==n||gr(e),null!=t&&(e.curOp.scrollLeft=t),null!=n&&(e.curOp.scrollTop=n)}function pr(e,t){gr(e),e.curOp.scrollToPos=t}function gr(e){var t=e.curOp.scrollToPos;t&&(e.curOp.scrollToPos=null,mr(e,Si(e,t.from),Si(e,t.to),t.margin))}function mr(e,t,n,i){var r=ur(e,{left:Math.min(t.left,n.left),top:Math.min(t.top,n.top)-i,right:Math.max(t.right,n.right),bottom:Math.max(t.bottom,n.bottom)+i});fr(e,r.scrollLeft,r.scrollTop)}function vr(e,t){Math.abs(e.doc.scrollTop-t)<2||(n||Gr(e,{top:t}),yr(e,t,!0),n&&Gr(e),Rr(e,100))}function yr(e,t,n){t=Math.max(0,Math.min(e.display.scroller.scrollHeight-e.display.scroller.clientHeight,t)),(e.display.scroller.scrollTop!=t||n)&&(e.doc.scrollTop=t,e.display.scrollbars.setScrollTop(t),e.display.scroller.scrollTop!=t&&(e.display.scroller.scrollTop=t))}function br(e,t,n,i){t=Math.max(0,Math.min(t,e.display.scroller.scrollWidth-e.display.scroller.clientWidth)),(n?t==e.doc.scrollLeft:Math.abs(e.doc.scrollLeft-t)<2)&&!i||(e.doc.scrollLeft=t,Zr(e),e.display.scroller.scrollLeft!=t&&(e.display.scroller.scrollLeft=t),e.display.scrollbars.setScrollLeft(t))}function _r(e){var t=e.display,n=t.gutters.offsetWidth,i=Math.round(e.doc.height+Jn(e.display));return{clientHeight:t.scroller.clientHeight,viewHeight:t.wrapper.clientHeight,scrollWidth:t.scroller.scrollWidth,clientWidth:t.scroller.clientWidth,viewWidth:t.wrapper.clientWidth,barLeft:e.options.fixedGutter?n:0,docHeight:i,scrollHeight:i+Qn(e)+t.barHeight,nativeBarWidth:t.nativeBarWidth,gutterWidth:n}}var wr=function(e,t,n){this.cm=n;var i=this.vert=O("div",[O("div",null,null,"min-width: 1px")],"CodeMirror-vscrollbar"),r=this.horiz=O("div",[O("div",null,null,"height: 100%; min-height: 1px")],"CodeMirror-hscrollbar");i.tabIndex=r.tabIndex=-1,e(i),e(r),be(i,"scroll",(function(){i.clientHeight&&t(i.scrollTop,"vertical")})),be(r,"scroll",(function(){r.clientWidth&&t(r.scrollLeft,"horizontal")})),this.checkedZeroWidth=!1,s&&a<8&&(this.horiz.style.minHeight=this.vert.style.minWidth="18px")};wr.prototype.update=function(e){var t=e.scrollWidth>e.clientWidth+1,n=e.scrollHeight>e.clientHeight+1,i=e.nativeBarWidth;if(n){this.vert.style.display="block",this.vert.style.bottom=t?i+"px":"0";var r=e.viewHeight-(t?i:0);this.vert.firstChild.style.height=Math.max(0,e.scrollHeight-e.clientHeight+r)+"px"}else this.vert.scrollTop=0,this.vert.style.display="",this.vert.firstChild.style.height="0";if(t){this.horiz.style.display="block",this.horiz.style.right=n?i+"px":"0",this.horiz.style.left=e.barLeft+"px";var o=e.viewWidth-e.barLeft-(n?i:0);this.horiz.firstChild.style.width=Math.max(0,e.scrollWidth-e.clientWidth+o)+"px"}else this.horiz.style.display="",this.horiz.firstChild.style.width="0";return!this.checkedZeroWidth&&e.clientHeight>0&&(0==i&&this.zeroWidthHack(),this.checkedZeroWidth=!0),{right:n?i:0,bottom:t?i:0}},wr.prototype.setScrollLeft=function(e){this.horiz.scrollLeft!=e&&(this.horiz.scrollLeft=e),this.disableHoriz&&this.enableZeroWidthBar(this.horiz,this.disableHoriz,"horiz")},wr.prototype.setScrollTop=function(e){this.vert.scrollTop!=e&&(this.vert.scrollTop=e),this.disableVert&&this.enableZeroWidthBar(this.vert,this.disableVert,"vert")},wr.prototype.zeroWidthHack=function(){var e=b&&!p?"12px":"18px";this.horiz.style.height=this.vert.style.width=e,this.horiz.style.visibility=this.vert.style.visibility="hidden",this.disableHoriz=new U,this.disableVert=new U},wr.prototype.enableZeroWidthBar=function(e,t,n){function i(){var r=e.getBoundingClientRect();("vert"==n?document.elementFromPoint(r.right-1,(r.top+r.bottom)/2):document.elementFromPoint((r.right+r.left)/2,r.bottom-1))!=e?e.style.visibility="hidden":t.set(1e3,i)}e.style.visibility="",t.set(1e3,i)},wr.prototype.clear=function(){var e=this.horiz.parentNode;e.removeChild(this.horiz),e.removeChild(this.vert)};var xr=function(){};function kr(e,t){t||(t=_r(e));var n=e.display.barWidth,i=e.display.barHeight;Cr(e,t);for(var r=0;r<4&&n!=e.display.barWidth||i!=e.display.barHeight;r++)n!=e.display.barWidth&&e.options.lineWrapping&&rr(e),Cr(e,_r(e)),n=e.display.barWidth,i=e.display.barHeight}function Cr(e,t){var n=e.display,i=n.scrollbars.update(t);n.sizer.style.paddingRight=(n.barWidth=i.right)+"px",n.sizer.style.paddingBottom=(n.barHeight=i.bottom)+"px",n.heightForcer.style.borderBottom=i.bottom+"px solid transparent",i.right&&i.bottom?(n.scrollbarFiller.style.display="block",n.scrollbarFiller.style.height=i.bottom+"px",n.scrollbarFiller.style.width=i.right+"px"):n.scrollbarFiller.style.display="",i.bottom&&e.options.coverGutterNextToScrollbar&&e.options.fixedGutter?(n.gutterFiller.style.display="block",n.gutterFiller.style.height=i.bottom+"px",n.gutterFiller.style.width=t.gutterWidth+"px"):n.gutterFiller.style.display=""}xr.prototype.update=function(){return{bottom:0,right:0}},xr.prototype.setScrollLeft=function(){},xr.prototype.setScrollTop=function(){},xr.prototype.clear=function(){};var Sr={native:wr,null:xr};function Lr(e){e.display.scrollbars&&(e.display.scrollbars.clear(),e.display.scrollbars.addClass&&T(e.display.wrapper,e.display.scrollbars.addClass)),e.display.scrollbars=new Sr[e.options.scrollbarStyle]((function(t){e.display.wrapper.insertBefore(t,e.display.scrollbarFiller),be(t,"mousedown",(function(){e.state.focused&&setTimeout((function(){return e.display.input.focus()}),0)})),t.setAttribute("cm-not-content","true")}),(function(t,n){"horizontal"==n?br(e,t):vr(e,t)}),e),e.display.scrollbars.addClass&&P(e.display.wrapper,e.display.scrollbars.addClass)}var Tr=0;function Ar(e){e.curOp={cm:e,viewChanged:!1,startHeight:e.doc.height,forceUpdate:!1,updateInput:0,typing:!1,changeObjs:null,cursorActivityHandlers:null,cursorActivityCalled:0,selectionChanged:!1,updateMaxLine:!1,scrollLeft:null,scrollTop:null,scrollToPos:null,focus:!1,id:++Tr,markArrays:null},On(e.curOp)}function Mr(e){var t=e.curOp;t&&Nn(t,(function(e){for(var t=0;t<e.ops.length;t++)e.ops[t].cm.curOp=null;Or(e)}))}function Or(e){for(var t=e.ops,n=0;n<t.length;n++)Er(t[n]);for(var i=0;i<t.length;i++)Nr(t[i]);for(var r=0;r<t.length;r++)Dr(t[r]);for(var o=0;o<t.length;o++)Pr(t[o]);for(var s=0;s<t.length;s++)Hr(t[s])}function Er(e){var t=e.cm,n=t.display;qr(t),e.updateMaxLine&&fn(t),e.mustUpdate=e.viewChanged||e.forceUpdate||null!=e.scrollTop||e.scrollToPos&&(e.scrollToPos.from.line<n.viewFrom||e.scrollToPos.to.line>=n.viewTo)||n.maxLineChanged&&t.options.lineWrapping,e.update=e.mustUpdate&&new jr(t,e.mustUpdate&&{top:e.scrollTop,ensure:e.scrollToPos},e.forceUpdate)}function Nr(e){e.updatedDisplay=e.mustUpdate&&Vr(e.cm,e.update)}function Dr(e){var t=e.cm,n=t.display;e.updatedDisplay&&rr(t),e.barMeasure=_r(t),n.maxLineChanged&&!t.options.lineWrapping&&(e.adjustWidthTo=oi(t,n.maxLine,n.maxLine.text.length).left+3,t.display.sizerWidth=e.adjustWidthTo,e.barMeasure.scrollWidth=Math.max(n.scroller.clientWidth,n.sizer.offsetLeft+e.adjustWidthTo+Qn(t)+t.display.barWidth),e.maxScrollLeft=Math.max(0,n.sizer.offsetLeft+e.adjustWidthTo-ei(t))),(e.updatedDisplay||e.selectionChanged)&&(e.preparedSelection=n.input.prepareSelection())}function Pr(e){var t=e.cm;null!=e.adjustWidthTo&&(t.display.sizer.style.minWidth=e.adjustWidthTo+"px",e.maxScrollLeft<t.doc.scrollLeft&&br(t,Math.min(t.display.scroller.scrollLeft,e.maxScrollLeft),!0),t.display.maxLineChanged=!1);var n=e.focus&&e.focus==D(F(t));e.preparedSelection&&t.display.input.showSelection(e.preparedSelection,n),(e.updatedDisplay||e.startHeight!=t.doc.height)&&kr(t,e.barMeasure),e.updatedDisplay&&Jr(t,e.barMeasure),e.selectionChanged&&Qi(t),t.state.focused&&e.updateInput&&t.display.input.reset(e.typing),n&&er(e.cm)}function Hr(e){var t=e.cm,n=t.display,i=t.doc;e.updatedDisplay&&Kr(t,e.update),null==n.wheelStartX||null==e.scrollTop&&null==e.scrollLeft&&!e.scrollToPos||(n.wheelStartX=n.wheelStartY=null),null!=e.scrollTop&&yr(t,e.scrollTop,e.forceScroll),null!=e.scrollLeft&&br(t,e.scrollLeft,!0,!0),e.scrollToPos&&ar(t,lr(t,vt(i,e.scrollToPos.from),vt(i,e.scrollToPos.to),e.scrollToPos.margin));var r=e.maybeHiddenMarkers,o=e.maybeUnhiddenMarkers;if(r)for(var s=0;s<r.length;++s)r[s].lines.length||xe(r[s],"hide");if(o)for(var a=0;a<o.length;++a)o[a].lines.length&&xe(o[a],"unhide");n.wrapper.offsetHeight&&(i.scrollTop=t.display.scroller.scrollTop),e.changeObjs&&xe(t,"changes",t,e.changeObjs),e.update&&e.update.finish()}function Wr(e,t){if(e.curOp)return t();Ar(e);try{return t()}finally{Mr(e)}}function Ir(e,t){return function(){if(e.curOp)return t.apply(e,arguments);Ar(e);try{return t.apply(e,arguments)}finally{Mr(e)}}}function Fr(e){return function(){if(this.curOp)return e.apply(this,arguments);Ar(this);try{return e.apply(this,arguments)}finally{Mr(this)}}}function Br(e){return function(){var t=this.cm;if(!t||t.curOp)return e.apply(this,arguments);Ar(t);try{return e.apply(this,arguments)}finally{Mr(t)}}}function Rr(e,t){e.doc.highlightFrontier<e.display.viewTo&&e.state.highlight.set(t,z(zr,e))}function zr(e){var t=e.doc;if(!(t.highlightFrontier>=e.display.viewTo)){var n=+new Date+e.options.workTime,i=Ct(e,t.highlightFrontier),r=[];t.iter(i.line,Math.min(t.first+t.size,e.display.viewTo+500),(function(o){if(i.line>=e.display.viewFrom){var s=o.styles,a=o.text.length>e.options.maxHighlightLength?Ze(t.mode,i.state):null,l=xt(e,o,i,!0);a&&(i.state=a),o.styles=l.styles;var c=o.styleClasses,u=l.classes;u?o.styleClasses=u:c&&(o.styleClasses=null);for(var h=!s||s.length!=o.styles.length||c!=u&&(!c||!u||c.bgClass!=u.bgClass||c.textClass!=u.textClass),d=0;!h&&d<s.length;++d)h=s[d]!=o.styles[d];h&&r.push(i.line),o.stateAfter=i.save(),i.nextLine()}else o.text.length<=e.options.maxHighlightLength&&St(e,o.text,i),o.stateAfter=i.line%5==0?i.save():null,i.nextLine();if(+new Date>n)return Rr(e,e.options.workDelay),!0})),t.highlightFrontier=i.line,t.modeFrontier=Math.max(t.modeFrontier,i.line),r.length&&Wr(e,(function(){for(var t=0;t<r.length;t++)qi(e,r[t],"text")}))}}var jr=function(e,t,n){var i=e.display;this.viewport=t,this.visible=sr(i,e.doc,t),this.editorIsHidden=!i.wrapper.offsetWidth,this.wrapperHeight=i.wrapper.clientHeight,this.wrapperWidth=i.wrapper.clientWidth,this.oldDisplayWidth=ei(e),this.force=n,this.dims=Wi(e),this.events=[]};function qr(e){var t=e.display;!t.scrollbarsClipped&&t.scroller.offsetWidth&&(t.nativeBarWidth=t.scroller.offsetWidth-t.scroller.clientWidth,t.heightForcer.style.height=Qn(e)+"px",t.sizer.style.marginBottom=-t.nativeBarWidth+"px",t.sizer.style.borderRightWidth=Qn(e)+"px",t.scrollbarsClipped=!0)}function Ur(e){if(e.hasFocus())return null;var t=D(F(e));if(!t||!N(e.display.lineDiv,t))return null;var n={activeElt:t};if(window.getSelection){var i=R(e).getSelection();i.anchorNode&&i.extend&&N(e.display.lineDiv,i.anchorNode)&&(n.anchorNode=i.anchorNode,n.anchorOffset=i.anchorOffset,n.focusNode=i.focusNode,n.focusOffset=i.focusOffset)}return n}function $r(e){if(e&&e.activeElt&&e.activeElt!=D(B(e.activeElt))&&(e.activeElt.focus(),!/^(INPUT|TEXTAREA)$/.test(e.activeElt.nodeName)&&e.anchorNode&&N(document.body,e.anchorNode)&&N(document.body,e.focusNode))){var t=e.activeElt.ownerDocument,n=t.defaultView.getSelection(),i=t.createRange();i.setEnd(e.anchorNode,e.anchorOffset),i.collapse(!1),n.removeAllRanges(),n.addRange(i),n.extend(e.focusNode,e.focusOffset)}}function Vr(e,t){var n=e.display,i=e.doc;if(t.editorIsHidden)return Ui(e),!1;if(!t.force&&t.visible.from>=n.viewFrom&&t.visible.to<=n.viewTo&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo)&&n.renderedView==n.view&&0==Ki(e))return!1;Qr(e)&&(Ui(e),t.dims=Wi(e));var r=i.first+i.size,o=Math.max(t.visible.from-e.options.viewportMargin,i.first),s=Math.min(r,t.visible.to+e.options.viewportMargin);n.viewFrom<o&&o-n.viewFrom<20&&(o=Math.max(i.first,n.viewFrom)),n.viewTo>s&&n.viewTo-s<20&&(s=Math.min(r,n.viewTo)),Ht&&(o=an(e.doc,o),s=ln(e.doc,s));var a=o!=n.viewFrom||s!=n.viewTo||n.lastWrapHeight!=t.wrapperHeight||n.lastWrapWidth!=t.wrapperWidth;Vi(e,o,s),n.viewOffset=hn(nt(e.doc,n.viewFrom)),e.display.mover.style.top=n.viewOffset+"px";var l=Ki(e);if(!a&&0==l&&!t.force&&n.renderedView==n.view&&(null==n.updateLineNumbers||n.updateLineNumbers>=n.viewTo))return!1;var c=Ur(e);return l>4&&(n.lineDiv.style.display="none"),Xr(e,n.updateLineNumbers,t.dims),l>4&&(n.lineDiv.style.display=""),n.renderedView=n.view,$r(c),A(n.cursorDiv),A(n.selectionDiv),n.gutters.style.height=n.sizer.style.minHeight=0,a&&(n.lastWrapHeight=t.wrapperHeight,n.lastWrapWidth=t.wrapperWidth,Rr(e,400)),n.updateLineNumbers=null,!0}function Kr(e,t){for(var n=t.viewport,i=!0;;i=!1){if(i&&e.options.lineWrapping&&t.oldDisplayWidth!=ei(e))i&&(t.visible=sr(e.display,e.doc,n));else if(n&&null!=n.top&&(n={top:Math.min(e.doc.height+Jn(e.display)-ti(e),n.top)}),t.visible=sr(e.display,e.doc,n),t.visible.from>=e.display.viewFrom&&t.visible.to<=e.display.viewTo)break;if(!Vr(e,t))break;rr(e);var r=_r(e);Gi(e),kr(e,r),Jr(e,r),t.force=!1}t.signal(e,"update",e),e.display.viewFrom==e.display.reportedViewFrom&&e.display.viewTo==e.display.reportedViewTo||(t.signal(e,"viewportChange",e,e.display.viewFrom,e.display.viewTo),e.display.reportedViewFrom=e.display.viewFrom,e.display.reportedViewTo=e.display.viewTo)}function Gr(e,t){var n=new jr(e,t);if(Vr(e,n)){rr(e),Kr(e,n);var i=_r(e);Gi(e),kr(e,i),Jr(e,i),n.finish()}}function Xr(e,t,n){var i=e.display,r=e.options.lineNumbers,o=i.lineDiv,s=o.firstChild;function a(t){var n=t.nextSibling;return l&&b&&e.display.currentWheelTarget==t?t.style.display="none":t.parentNode.removeChild(t),n}for(var c=i.view,u=i.viewFrom,h=0;h<c.length;h++){var d=c[h];if(d.hidden);else if(d.node&&d.node.parentNode==o){for(;s!=d.node;)s=a(s);var f=r&&null!=t&&t<=u&&d.lineNumber;d.changes&&($(d.changes,"gutter")>-1&&(f=!1),Wn(e,d,u,n)),f&&(A(d.lineNumber),d.lineNumber.appendChild(document.createTextNode(ct(e.options,u)))),s=d.node.nextSibling}else{var p=Un(e,d,u,n);o.insertBefore(p,s)}u+=d.size}for(;s;)s=a(s)}function Yr(e){var t=e.gutters.offsetWidth;e.sizer.style.marginLeft=t+"px",Pn(e,"gutterChanged",e)}function Jr(e,t){e.display.sizer.style.minHeight=t.docHeight+"px",e.display.heightForcer.style.top=t.docHeight+"px",e.display.gutters.style.height=t.docHeight+e.display.barHeight+Qn(e)+"px"}function Zr(e){var t=e.display,n=t.view;if(t.alignWidgets||t.gutters.firstChild&&e.options.fixedGutter){for(var i=Ii(t)-t.scroller.scrollLeft+e.doc.scrollLeft,r=t.gutters.offsetWidth,o=i+"px",s=0;s<n.length;s++)if(!n[s].hidden){e.options.fixedGutter&&(n[s].gutter&&(n[s].gutter.style.left=o),n[s].gutterBackground&&(n[s].gutterBackground.style.left=o));var a=n[s].alignable;if(a)for(var l=0;l<a.length;l++)a[l].style.left=o}e.options.fixedGutter&&(t.gutters.style.left=i+r+"px")}}function Qr(e){if(!e.options.lineNumbers)return!1;var t=e.doc,n=ct(e.options,t.first+t.size-1),i=e.display;if(n.length!=i.lineNumChars){var r=i.measure.appendChild(O("div",[O("div",n)],"CodeMirror-linenumber CodeMirror-gutter-elt")),o=r.firstChild.offsetWidth,s=r.offsetWidth-o;return i.lineGutter.style.width="",i.lineNumInnerWidth=Math.max(o,i.lineGutter.offsetWidth-s)+1,i.lineNumWidth=i.lineNumInnerWidth+s,i.lineNumChars=i.lineNumInnerWidth?n.length:-1,i.lineGutter.style.width=i.lineNumWidth+"px",Yr(e.display),!0}return!1}function eo(e,t){for(var n=[],i=!1,r=0;r<e.length;r++){var o=e[r],s=null;if("string"!=typeof o&&(s=o.style,o=o.className),"CodeMirror-linenumbers"==o){if(!t)continue;i=!0}n.push({className:o,style:s})}return t&&!i&&n.push({className:"CodeMirror-linenumbers",style:null}),n}function to(e){var t=e.gutters,n=e.gutterSpecs;A(t),e.lineGutter=null;for(var i=0;i<n.length;++i){var r=n[i],o=r.className,s=r.style,a=t.appendChild(O("div",null,"CodeMirror-gutter "+o));s&&(a.style.cssText=s),"CodeMirror-linenumbers"==o&&(e.lineGutter=a,a.style.width=(e.lineNumWidth||1)+"px")}t.style.display=n.length?"":"none",Yr(e)}function no(e){to(e.display),ji(e),Zr(e)}function io(e,t,i,r){var o=this;this.input=i,o.scrollbarFiller=O("div",null,"CodeMirror-scrollbar-filler"),o.scrollbarFiller.setAttribute("cm-not-content","true"),o.gutterFiller=O("div",null,"CodeMirror-gutter-filler"),o.gutterFiller.setAttribute("cm-not-content","true"),o.lineDiv=E("div",null,"CodeMirror-code"),o.selectionDiv=O("div",null,null,"position: relative; z-index: 1"),o.cursorDiv=O("div",null,"CodeMirror-cursors"),o.measure=O("div",null,"CodeMirror-measure"),o.lineMeasure=O("div",null,"CodeMirror-measure"),o.lineSpace=E("div",[o.measure,o.lineMeasure,o.selectionDiv,o.cursorDiv,o.lineDiv],null,"position: relative; outline: none");var c=E("div",[o.lineSpace],"CodeMirror-lines");o.mover=O("div",[c],null,"position: relative"),o.sizer=O("div",[o.mover],"CodeMirror-sizer"),o.sizerWidth=null,o.heightForcer=O("div",null,null,"position: absolute; height: "+V+"px; width: 1px;"),o.gutters=O("div",null,"CodeMirror-gutters"),o.lineGutter=null,o.scroller=O("div",[o.sizer,o.heightForcer,o.gutters],"CodeMirror-scroll"),o.scroller.setAttribute("tabIndex","-1"),o.wrapper=O("div",[o.scrollbarFiller,o.gutterFiller,o.scroller],"CodeMirror"),u&&h>=105&&(o.wrapper.style.clipPath="inset(0px)"),o.wrapper.setAttribute("translate","no"),s&&a<8&&(o.gutters.style.zIndex=-1,o.scroller.style.paddingRight=0),l||n&&y||(o.scroller.draggable=!0),e&&(e.appendChild?e.appendChild(o.wrapper):e(o.wrapper)),o.viewFrom=o.viewTo=t.first,o.reportedViewFrom=o.reportedViewTo=t.first,o.view=[],o.renderedView=null,o.externalMeasured=null,o.viewOffset=0,o.lastWrapHeight=o.lastWrapWidth=0,o.updateLineNumbers=null,o.nativeBarWidth=o.barHeight=o.barWidth=0,o.scrollbarsClipped=!1,o.lineNumWidth=o.lineNumInnerWidth=o.lineNumChars=null,o.alignWidgets=!1,o.cachedCharWidth=o.cachedTextHeight=o.cachedPaddingH=null,o.maxLine=null,o.maxLineLength=0,o.maxLineChanged=!1,o.wheelDX=o.wheelDY=o.wheelStartX=o.wheelStartY=null,o.shift=!1,o.selForContextMenu=null,o.activeTouch=null,o.gutterSpecs=eo(r.gutters,r.lineNumbers),to(o),i.init(o)}jr.prototype.signal=function(e,t){Se(e,t)&&this.events.push(arguments)},jr.prototype.finish=function(){for(var e=0;e<this.events.length;e++)xe.apply(null,this.events[e])};var ro=0,oo=null;function so(e){var t=e.wheelDeltaX,n=e.wheelDeltaY;return null==t&&e.detail&&e.axis==e.HORIZONTAL_AXIS&&(t=e.detail),null==n&&e.detail&&e.axis==e.VERTICAL_AXIS?n=e.detail:null==n&&(n=e.wheelDelta),{x:t,y:n}}function ao(e){var t=so(e);return t.x*=oo,t.y*=oo,t}function lo(e,t){u&&102==h&&(null==e.display.chromeScrollHack?e.display.sizer.style.pointerEvents="none":clearTimeout(e.display.chromeScrollHack),e.display.chromeScrollHack=setTimeout((function(){e.display.chromeScrollHack=null,e.display.sizer.style.pointerEvents=""}),100));var i=so(t),r=i.x,o=i.y,s=oo;0===t.deltaMode&&(r=t.deltaX,o=t.deltaY,s=1);var a=e.display,c=a.scroller,f=c.scrollWidth>c.clientWidth,p=c.scrollHeight>c.clientHeight;if(r&&f||o&&p){if(o&&b&&l)e:for(var g=t.target,m=a.view;g!=c;g=g.parentNode)for(var v=0;v<m.length;v++)if(m[v].node==g){e.display.currentWheelTarget=g;break e}if(r&&!n&&!d&&null!=s)return o&&p&&vr(e,Math.max(0,c.scrollTop+o*s)),br(e,Math.max(0,c.scrollLeft+r*s)),(!o||o&&p)&&Te(t),void(a.wheelStartX=null);if(o&&null!=s){var y=o*s,_=e.doc.scrollTop,w=_+a.wrapper.clientHeight;y<0?_=Math.max(0,_+y-50):w=Math.min(e.doc.height,w+y+50),Gr(e,{top:_,bottom:w})}ro<20&&0!==t.deltaMode&&(null==a.wheelStartX?(a.wheelStartX=c.scrollLeft,a.wheelStartY=c.scrollTop,a.wheelDX=r,a.wheelDY=o,setTimeout((function(){if(null!=a.wheelStartX){var e=c.scrollLeft-a.wheelStartX,t=c.scrollTop-a.wheelStartY,n=t&&a.wheelDY&&t/a.wheelDY||e&&a.wheelDX&&e/a.wheelDX;a.wheelStartX=a.wheelStartY=null,n&&(oo=(oo*ro+n)/(ro+1),++ro)}}),200)):(a.wheelDX+=r,a.wheelDY+=o))}}s?oo=-.53:n?oo=15:u?oo=-.7:f&&(oo=-1/3);var co=function(e,t){this.ranges=e,this.primIndex=t};co.prototype.primary=function(){return this.ranges[this.primIndex]},co.prototype.equals=function(e){if(e==this)return!0;if(e.primIndex!=this.primIndex||e.ranges.length!=this.ranges.length)return!1;for(var t=0;t<this.ranges.length;t++){var n=this.ranges[t],i=e.ranges[t];if(!dt(n.anchor,i.anchor)||!dt(n.head,i.head))return!1}return!0},co.prototype.deepCopy=function(){for(var e=[],t=0;t<this.ranges.length;t++)e[t]=new uo(ft(this.ranges[t].anchor),ft(this.ranges[t].head));return new co(e,this.primIndex)},co.prototype.somethingSelected=function(){for(var e=0;e<this.ranges.length;e++)if(!this.ranges[e].empty())return!0;return!1},co.prototype.contains=function(e,t){t||(t=e);for(var n=0;n<this.ranges.length;n++){var i=this.ranges[n];if(ht(t,i.from())>=0&&ht(e,i.to())<=0)return n}return-1};var uo=function(e,t){this.anchor=e,this.head=t};function ho(e,t,n){var i=e&&e.options.selectionsMayTouch,r=t[n];t.sort((function(e,t){return ht(e.from(),t.from())})),n=$(t,r);for(var o=1;o<t.length;o++){var s=t[o],a=t[o-1],l=ht(a.to(),s.from());if(i&&!s.empty()?l>0:l>=0){var c=gt(a.from(),s.from()),u=pt(a.to(),s.to()),h=a.empty()?s.from()==s.head:a.from()==a.head;o<=n&&--n,t.splice(--o,2,new uo(h?u:c,h?c:u))}}return new co(t,n)}function fo(e,t){return new co([new uo(e,t||e)],0)}function po(e){return e.text?ut(e.from.line+e.text.length-1,ee(e.text).length+(1==e.text.length?e.from.ch:0)):e.to}function go(e,t){if(ht(e,t.from)<0)return e;if(ht(e,t.to)<=0)return po(t);var n=e.line+t.text.length-(t.to.line-t.from.line)-1,i=e.ch;return e.line==t.to.line&&(i+=po(t).ch-t.to.ch),ut(n,i)}function mo(e,t){for(var n=[],i=0;i<e.sel.ranges.length;i++){var r=e.sel.ranges[i];n.push(new uo(go(r.anchor,t),go(r.head,t)))}return ho(e.cm,n,e.sel.primIndex)}function vo(e,t,n){return e.line==t.line?ut(n.line,e.ch-t.ch+n.ch):ut(n.line+(e.line-t.line),e.ch)}function yo(e,t,n){for(var i=[],r=ut(e.first,0),o=r,s=0;s<t.length;s++){var a=t[s],l=vo(a.from,r,o),c=vo(po(a),r,o);if(r=a.to,o=c,"around"==n){var u=e.sel.ranges[s],h=ht(u.head,u.anchor)<0;i[s]=new uo(h?c:l,h?l:c)}else i[s]=new uo(l,l)}return new co(i,e.sel.primIndex)}function bo(e){e.doc.mode=Xe(e.options,e.doc.modeOption),_o(e)}function _o(e){e.doc.iter((function(e){e.stateAfter&&(e.stateAfter=null),e.styles&&(e.styles=null)})),e.doc.modeFrontier=e.doc.highlightFrontier=e.doc.first,Rr(e,100),e.state.modeGen++,e.curOp&&ji(e)}function wo(e,t){return 0==t.from.ch&&0==t.to.ch&&""==ee(t.text)&&(!e.cm||e.cm.options.wholeLineUpdateBefore)}function xo(e,t,n,i){function r(e){return n?n[e]:null}function o(e,n,r){gn(e,n,r,i),Pn(e,"change",e,t)}function s(e,t){for(var n=[],o=e;o<t;++o)n.push(new pn(c[o],r(o),i));return n}var a=t.from,l=t.to,c=t.text,u=nt(e,a.line),h=nt(e,l.line),d=ee(c),f=r(c.length-1),p=l.line-a.line;if(t.full)e.insert(0,s(0,c.length)),e.remove(c.length,e.size-c.length);else if(wo(e,t)){var g=s(0,c.length-1);o(h,h.text,f),p&&e.remove(a.line,p),g.length&&e.insert(a.line,g)}else if(u==h)if(1==c.length)o(u,u.text.slice(0,a.ch)+d+u.text.slice(l.ch),f);else{var m=s(1,c.length-1);m.push(new pn(d+u.text.slice(l.ch),f,i)),o(u,u.text.slice(0,a.ch)+c[0],r(0)),e.insert(a.line+1,m)}else if(1==c.length)o(u,u.text.slice(0,a.ch)+c[0]+h.text.slice(l.ch),r(0)),e.remove(a.line+1,p);else{o(u,u.text.slice(0,a.ch)+c[0],r(0)),o(h,d+h.text.slice(l.ch),f);var v=s(1,c.length-1);p>1&&e.remove(a.line+1,p-1),e.insert(a.line+1,v)}Pn(e,"change",e,t)}function ko(e,t,n){function i(e,r,o){if(e.linked)for(var s=0;s<e.linked.length;++s){var a=e.linked[s];if(a.doc!=r){var l=o&&a.sharedHist;n&&!l||(t(a.doc,l),i(a.doc,e,l))}}}i(e,null,!0)}function Co(e,t){if(t.cm)throw new Error("This document is already in use.");e.doc=t,t.cm=e,Bi(e),bo(e),So(e),e.options.direction=t.direction,e.options.lineWrapping||fn(e),e.options.mode=t.modeOption,ji(e)}function So(e){("rtl"==e.doc.direction?P:T)(e.display.lineDiv,"CodeMirror-rtl")}function Lo(e){Wr(e,(function(){So(e),ji(e)}))}function To(e){this.done=[],this.undone=[],this.undoDepth=e?e.undoDepth:1/0,this.lastModTime=this.lastSelTime=0,this.lastOp=this.lastSelOp=null,this.lastOrigin=this.lastSelOrigin=null,this.generation=this.maxGeneration=e?e.maxGeneration:1}function Ao(e,t){var n={from:ft(t.from),to:po(t),text:it(e,t.from,t.to)};return Ho(e,n,t.from.line,t.to.line+1),ko(e,(function(e){return Ho(e,n,t.from.line,t.to.line+1)}),!0),n}function Mo(e){for(;e.length&&ee(e).ranges;)e.pop()}function Oo(e,t){return t?(Mo(e.done),ee(e.done)):e.done.length&&!ee(e.done).ranges?ee(e.done):e.done.length>1&&!e.done[e.done.length-2].ranges?(e.done.pop(),ee(e.done)):void 0}function Eo(e,t,n,i){var r=e.history;r.undone.length=0;var o,s,a=+new Date;if((r.lastOp==i||r.lastOrigin==t.origin&&t.origin&&("+"==t.origin.charAt(0)&&r.lastModTime>a-(e.cm?e.cm.options.historyEventDelay:500)||"*"==t.origin.charAt(0)))&&(o=Oo(r,r.lastOp==i)))s=ee(o.changes),0==ht(t.from,t.to)&&0==ht(t.from,s.to)?s.to=po(t):o.changes.push(Ao(e,t));else{var l=ee(r.done);for(l&&l.ranges||Po(e.sel,r.done),o={changes:[Ao(e,t)],generation:r.generation},r.done.push(o);r.done.length>r.undoDepth;)r.done.shift(),r.done[0].ranges||r.done.shift()}r.done.push(n),r.generation=++r.maxGeneration,r.lastModTime=r.lastSelTime=a,r.lastOp=r.lastSelOp=i,r.lastOrigin=r.lastSelOrigin=t.origin,s||xe(e,"historyAdded")}function No(e,t,n,i){var r=t.charAt(0);return"*"==r||"+"==r&&n.ranges.length==i.ranges.length&&n.somethingSelected()==i.somethingSelected()&&new Date-e.history.lastSelTime<=(e.cm?e.cm.options.historyEventDelay:500)}function Do(e,t,n,i){var r=e.history,o=i&&i.origin;n==r.lastSelOp||o&&r.lastSelOrigin==o&&(r.lastModTime==r.lastSelTime&&r.lastOrigin==o||No(e,o,ee(r.done),t))?r.done[r.done.length-1]=t:Po(t,r.done),r.lastSelTime=+new Date,r.lastSelOrigin=o,r.lastSelOp=n,i&&!1!==i.clearRedo&&Mo(r.undone)}function Po(e,t){var n=ee(t);n&&n.ranges&&n.equals(e)||t.push(e)}function Ho(e,t,n,i){var r=t["spans_"+e.id],o=0;e.iter(Math.max(e.first,n),Math.min(e.first+e.size,i),(function(n){n.markedSpans&&((r||(r=t["spans_"+e.id]={}))[o]=n.markedSpans),++o}))}function Wo(e){if(!e)return null;for(var t,n=0;n<e.length;++n)e[n].marker.explicitlyCleared?t||(t=e.slice(0,n)):t&&t.push(e[n]);return t?t.length?t:null:e}function Io(e,t){var n=t["spans_"+e.id];if(!n)return null;for(var i=[],r=0;r<t.text.length;++r)i.push(Wo(n[r]));return i}function Fo(e,t){var n=Io(e,t),i=Ut(e,t);if(!n)return i;if(!i)return n;for(var r=0;r<n.length;++r){var o=n[r],s=i[r];if(o&&s)e:for(var a=0;a<s.length;++a){for(var l=s[a],c=0;c<o.length;++c)if(o[c].marker==l.marker)continue e;o.push(l)}else s&&(n[r]=s)}return n}function Bo(e,t,n){for(var i=[],r=0;r<e.length;++r){var o=e[r];if(o.ranges)i.push(n?co.prototype.deepCopy.call(o):o);else{var s=o.changes,a=[];i.push({changes:a});for(var l=0;l<s.length;++l){var c=s[l],u=void 0;if(a.push({from:c.from,to:c.to,text:c.text}),t)for(var h in c)(u=h.match(/^spans_(\d+)$/))&&$(t,Number(u[1]))>-1&&(ee(a)[h]=c[h],delete c[h])}}}return i}function Ro(e,t,n,i){if(i){var r=e.anchor;if(n){var o=ht(t,r)<0;o!=ht(n,r)<0?(r=t,t=n):o!=ht(t,n)<0&&(t=n)}return new uo(r,t)}return new uo(n||t,t)}function zo(e,t,n,i,r){null==r&&(r=e.cm&&(e.cm.display.shift||e.extend)),Ko(e,new co([Ro(e.sel.primary(),t,n,r)],0),i)}function jo(e,t,n){for(var i=[],r=e.cm&&(e.cm.display.shift||e.extend),o=0;o<e.sel.ranges.length;o++)i[o]=Ro(e.sel.ranges[o],t[o],null,r);Ko(e,ho(e.cm,i,e.sel.primIndex),n)}function qo(e,t,n,i){var r=e.sel.ranges.slice(0);r[t]=n,Ko(e,ho(e.cm,r,e.sel.primIndex),i)}function Uo(e,t,n,i){Ko(e,fo(t,n),i)}function $o(e,t,n){var i={ranges:t.ranges,update:function(t){this.ranges=[];for(var n=0;n<t.length;n++)this.ranges[n]=new uo(vt(e,t[n].anchor),vt(e,t[n].head))},origin:n&&n.origin};return xe(e,"beforeSelectionChange",e,i),e.cm&&xe(e.cm,"beforeSelectionChange",e.cm,i),i.ranges!=t.ranges?ho(e.cm,i.ranges,i.ranges.length-1):t}function Vo(e,t,n){var i=e.history.done,r=ee(i);r&&r.ranges?(i[i.length-1]=t,Go(e,t,n)):Ko(e,t,n)}function Ko(e,t,n){Go(e,t,n),Do(e,e.sel,e.cm?e.cm.curOp.id:NaN,n)}function Go(e,t,n){(Se(e,"beforeSelectionChange")||e.cm&&Se(e.cm,"beforeSelectionChange"))&&(t=$o(e,t,n));var i=n&&n.bias||(ht(t.primary().head,e.sel.primary().head)<0?-1:1);Xo(e,Jo(e,t,i,!0)),n&&!1===n.scroll||!e.cm||"nocursor"==e.cm.getOption("readOnly")||dr(e.cm)}function Xo(e,t){t.equals(e.sel)||(e.sel=t,e.cm&&(e.cm.curOp.updateInput=1,e.cm.curOp.selectionChanged=!0,Ce(e.cm)),Pn(e,"cursorActivity",e))}function Yo(e){Xo(e,Jo(e,e.sel,null,!1))}function Jo(e,t,n,i){for(var r,o=0;o<t.ranges.length;o++){var s=t.ranges[o],a=t.ranges.length==e.sel.ranges.length&&e.sel.ranges[o],l=Qo(e,s.anchor,a&&a.anchor,n,i),c=s.head==s.anchor?l:Qo(e,s.head,a&&a.head,n,i);(r||l!=s.anchor||c!=s.head)&&(r||(r=t.ranges.slice(0,o)),r[o]=new uo(l,c))}return r?ho(e.cm,r,t.primIndex):t}function Zo(e,t,n,i,r){var o=nt(e,t.line);if(o.markedSpans)for(var s=0;s<o.markedSpans.length;++s){var a=o.markedSpans[s],l=a.marker,c="selectLeft"in l?!l.selectLeft:l.inclusiveLeft,u="selectRight"in l?!l.selectRight:l.inclusiveRight;if((null==a.from||(c?a.from<=t.ch:a.from<t.ch))&&(null==a.to||(u?a.to>=t.ch:a.to>t.ch))){if(r&&(xe(l,"beforeCursorEnter"),l.explicitlyCleared)){if(o.markedSpans){--s;continue}break}if(!l.atomic)continue;if(n){var h=l.find(i<0?1:-1),d=void 0;if((i<0?u:c)&&(h=es(e,h,-i,h&&h.line==t.line?o:null)),h&&h.line==t.line&&(d=ht(h,n))&&(i<0?d<0:d>0))return Zo(e,h,t,i,r)}var f=l.find(i<0?-1:1);return(i<0?c:u)&&(f=es(e,f,i,f.line==t.line?o:null)),f?Zo(e,f,t,i,r):null}}return t}function Qo(e,t,n,i,r){var o=i||1,s=Zo(e,t,n,o,r)||!r&&Zo(e,t,n,o,!0)||Zo(e,t,n,-o,r)||!r&&Zo(e,t,n,-o,!0);return s||(e.cantEdit=!0,ut(e.first,0))}function es(e,t,n,i){return n<0&&0==t.ch?t.line>e.first?vt(e,ut(t.line-1)):null:n>0&&t.ch==(i||nt(e,t.line)).text.length?t.line<e.first+e.size-1?ut(t.line+1,0):null:new ut(t.line,t.ch+n)}function ts(e){e.setSelection(ut(e.firstLine(),0),ut(e.lastLine()),G)}function ns(e,t,n){var i={canceled:!1,from:t.from,to:t.to,text:t.text,origin:t.origin,cancel:function(){return i.canceled=!0}};return n&&(i.update=function(t,n,r,o){t&&(i.from=vt(e,t)),n&&(i.to=vt(e,n)),r&&(i.text=r),void 0!==o&&(i.origin=o)}),xe(e,"beforeChange",e,i),e.cm&&xe(e.cm,"beforeChange",e.cm,i),i.canceled?(e.cm&&(e.cm.curOp.updateInput=2),null):{from:i.from,to:i.to,text:i.text,origin:i.origin}}function is(e,t,n){if(e.cm){if(!e.cm.curOp)return Ir(e.cm,is)(e,t,n);if(e.cm.state.suppressEdits)return}if(!(Se(e,"beforeChange")||e.cm&&Se(e.cm,"beforeChange"))||(t=ns(e,t,!0))){var i=Pt&&!n&&Vt(e,t.from,t.to);if(i)for(var r=i.length-1;r>=0;--r)rs(e,{from:i[r].from,to:i[r].to,text:r?[""]:t.text,origin:t.origin});else rs(e,t)}}function rs(e,t){if(1!=t.text.length||""!=t.text[0]||0!=ht(t.from,t.to)){var n=mo(e,t);Eo(e,t,n,e.cm?e.cm.curOp.id:NaN),as(e,t,n,Ut(e,t));var i=[];ko(e,(function(e,n){n||-1!=$(i,e.history)||(ds(e.history,t),i.push(e.history)),as(e,t,null,Ut(e,t))}))}}function os(e,t,n){var i=e.cm&&e.cm.state.suppressEdits;if(!i||n){for(var r,o=e.history,s=e.sel,a="undo"==t?o.done:o.undone,l="undo"==t?o.undone:o.done,c=0;c<a.length&&(r=a[c],n?!r.ranges||r.equals(e.sel):r.ranges);c++);if(c!=a.length){for(o.lastOrigin=o.lastSelOrigin=null;;){if(!(r=a.pop()).ranges){if(i)return void a.push(r);break}if(Po(r,l),n&&!r.equals(e.sel))return void Ko(e,r,{clearRedo:!1});s=r}var u=[];Po(s,l),l.push({changes:u,generation:o.generation}),o.generation=r.generation||++o.maxGeneration;for(var h=Se(e,"beforeChange")||e.cm&&Se(e.cm,"beforeChange"),d=function(n){var i=r.changes[n];if(i.origin=t,h&&!ns(e,i,!1))return a.length=0,{};u.push(Ao(e,i));var o=n?mo(e,i):ee(a);as(e,i,o,Fo(e,i)),!n&&e.cm&&e.cm.scrollIntoView({from:i.from,to:po(i)});var s=[];ko(e,(function(e,t){t||-1!=$(s,e.history)||(ds(e.history,i),s.push(e.history)),as(e,i,null,Fo(e,i))}))},f=r.changes.length-1;f>=0;--f){var p=d(f);if(p)return p.v}}}}function ss(e,t){if(0!=t&&(e.first+=t,e.sel=new co(te(e.sel.ranges,(function(e){return new uo(ut(e.anchor.line+t,e.anchor.ch),ut(e.head.line+t,e.head.ch))})),e.sel.primIndex),e.cm)){ji(e.cm,e.first,e.first-t,t);for(var n=e.cm.display,i=n.viewFrom;i<n.viewTo;i++)qi(e.cm,i,"gutter")}}function as(e,t,n,i){if(e.cm&&!e.cm.curOp)return Ir(e.cm,as)(e,t,n,i);if(t.to.line<e.first)ss(e,t.text.length-1-(t.to.line-t.from.line));else if(!(t.from.line>e.lastLine())){if(t.from.line<e.first){var r=t.text.length-1-(e.first-t.from.line);ss(e,r),t={from:ut(e.first,0),to:ut(t.to.line+r,t.to.ch),text:[ee(t.text)],origin:t.origin}}var o=e.lastLine();t.to.line>o&&(t={from:t.from,to:ut(o,nt(e,o).text.length),text:[t.text[0]],origin:t.origin}),t.removed=it(e,t.from,t.to),n||(n=mo(e,t)),e.cm?ls(e.cm,t,i):xo(e,t,i),Go(e,n,G),e.cantEdit&&Qo(e,ut(e.firstLine(),0))&&(e.cantEdit=!1)}}function ls(e,t,n){var i=e.doc,r=e.display,o=t.from,s=t.to,a=!1,l=o.line;e.options.lineWrapping||(l=st(rn(nt(i,o.line))),i.iter(l,s.line+1,(function(e){if(e==r.maxLine)return a=!0,!0}))),i.sel.contains(t.from,t.to)>-1&&Ce(e),xo(i,t,n,Fi(e)),e.options.lineWrapping||(i.iter(l,o.line+t.text.length,(function(e){var t=dn(e);t>r.maxLineLength&&(r.maxLine=e,r.maxLineLength=t,r.maxLineChanged=!0,a=!1)})),a&&(e.curOp.updateMaxLine=!0)),Dt(i,o.line),Rr(e,400);var c=t.text.length-(s.line-o.line)-1;t.full?ji(e):o.line!=s.line||1!=t.text.length||wo(e.doc,t)?ji(e,o.line,s.line+1,c):qi(e,o.line,"text");var u=Se(e,"changes"),h=Se(e,"change");if(h||u){var d={from:o,to:s,text:t.text,removed:t.removed,origin:t.origin};h&&Pn(e,"change",e,d),u&&(e.curOp.changeObjs||(e.curOp.changeObjs=[])).push(d)}e.display.selForContextMenu=null}function cs(e,t,n,i,r){var o;i||(i=n),ht(i,n)<0&&(n=(o=[i,n])[0],i=o[1]),"string"==typeof t&&(t=e.splitLines(t)),is(e,{from:n,to:i,text:t,origin:r})}function us(e,t,n,i){n<e.line?e.line+=i:t<e.line&&(e.line=t,e.ch=0)}function hs(e,t,n,i){for(var r=0;r<e.length;++r){var o=e[r],s=!0;if(o.ranges){o.copied||((o=e[r]=o.deepCopy()).copied=!0);for(var a=0;a<o.ranges.length;a++)us(o.ranges[a].anchor,t,n,i),us(o.ranges[a].head,t,n,i)}else{for(var l=0;l<o.changes.length;++l){var c=o.changes[l];if(n<c.from.line)c.from=ut(c.from.line+i,c.from.ch),c.to=ut(c.to.line+i,c.to.ch);else if(t<=c.to.line){s=!1;break}}s||(e.splice(0,r+1),r=0)}}}function ds(e,t){var n=t.from.line,i=t.to.line,r=t.text.length-(i-n)-1;hs(e.done,n,i,r),hs(e.undone,n,i,r)}function fs(e,t,n,i){var r=t,o=t;return"number"==typeof t?o=nt(e,mt(e,t)):r=st(t),null==r?null:(i(o,r)&&e.cm&&qi(e.cm,r,n),o)}function ps(e){this.lines=e,this.parent=null;for(var t=0,n=0;n<e.length;++n)e[n].parent=this,t+=e[n].height;this.height=t}function gs(e){this.children=e;for(var t=0,n=0,i=0;i<e.length;++i){var r=e[i];t+=r.chunkSize(),n+=r.height,r.parent=this}this.size=t,this.height=n,this.parent=null}uo.prototype.from=function(){return gt(this.anchor,this.head)},uo.prototype.to=function(){return pt(this.anchor,this.head)},uo.prototype.empty=function(){return this.head.line==this.anchor.line&&this.head.ch==this.anchor.ch},ps.prototype={chunkSize:function(){return this.lines.length},removeInner:function(e,t){for(var n=e,i=e+t;n<i;++n){var r=this.lines[n];this.height-=r.height,mn(r),Pn(r,"delete")}this.lines.splice(e,t)},collapse:function(e){e.push.apply(e,this.lines)},insertInner:function(e,t,n){this.height+=n,this.lines=this.lines.slice(0,e).concat(t).concat(this.lines.slice(e));for(var i=0;i<t.length;++i)t[i].parent=this},iterN:function(e,t,n){for(var i=e+t;e<i;++e)if(n(this.lines[e]))return!0}},gs.prototype={chunkSize:function(){return this.size},removeInner:function(e,t){this.size-=t;for(var n=0;n<this.children.length;++n){var i=this.children[n],r=i.chunkSize();if(e<r){var o=Math.min(t,r-e),s=i.height;if(i.removeInner(e,o),this.height-=s-i.height,r==o&&(this.children.splice(n--,1),i.parent=null),0==(t-=o))break;e=0}else e-=r}if(this.size-t<25&&(this.children.length>1||!(this.children[0]instanceof ps))){var a=[];this.collapse(a),this.children=[new ps(a)],this.children[0].parent=this}},collapse:function(e){for(var t=0;t<this.children.length;++t)this.children[t].collapse(e)},insertInner:function(e,t,n){this.size+=t.length,this.height+=n;for(var i=0;i<this.children.length;++i){var r=this.children[i],o=r.chunkSize();if(e<=o){if(r.insertInner(e,t,n),r.lines&&r.lines.length>50){for(var s=r.lines.length%25+25,a=s;a<r.lines.length;){var l=new ps(r.lines.slice(a,a+=25));r.height-=l.height,this.children.splice(++i,0,l),l.parent=this}r.lines=r.lines.slice(0,s),this.maybeSpill()}break}e-=o}},maybeSpill:function(){if(!(this.children.length<=10)){var e=this;do{var t=new gs(e.children.splice(e.children.length-5,5));if(e.parent){e.size-=t.size,e.height-=t.height;var n=$(e.parent.children,e);e.parent.children.splice(n+1,0,t)}else{var i=new gs(e.children);i.parent=e,e.children=[i,t],e=i}t.parent=e.parent}while(e.children.length>10);e.parent.maybeSpill()}},iterN:function(e,t,n){for(var i=0;i<this.children.length;++i){var r=this.children[i],o=r.chunkSize();if(e<o){var s=Math.min(t,o-e);if(r.iterN(e,s,n))return!0;if(0==(t-=s))break;e=0}else e-=o}}};var ms=function(e,t,n){if(n)for(var i in n)n.hasOwnProperty(i)&&(this[i]=n[i]);this.doc=e,this.node=t};function vs(e,t,n){hn(t)<(e.curOp&&e.curOp.scrollTop||e.doc.scrollTop)&&hr(e,n)}function ys(e,t,n,i){var r=new ms(e,n,i),o=e.cm;return o&&r.noHScroll&&(o.display.alignWidgets=!0),fs(e,t,"widget",(function(t){var n=t.widgets||(t.widgets=[]);if(null==r.insertAt?n.push(r):n.splice(Math.min(n.length,Math.max(0,r.insertAt)),0,r),r.line=t,o&&!cn(e,t)){var i=hn(t)<e.scrollTop;ot(t,t.height+Gn(r)),i&&hr(o,r.height),o.curOp.forceUpdate=!0}return!0})),o&&Pn(o,"lineWidgetAdded",o,r,"number"==typeof t?t:st(t)),r}ms.prototype.clear=function(){var e=this.doc.cm,t=this.line.widgets,n=this.line,i=st(n);if(null!=i&&t){for(var r=0;r<t.length;++r)t[r]==this&&t.splice(r--,1);t.length||(n.widgets=null);var o=Gn(this);ot(n,Math.max(0,n.height-o)),e&&(Wr(e,(function(){vs(e,n,-o),qi(e,i,"widget")})),Pn(e,"lineWidgetCleared",e,this,i))}},ms.prototype.changed=function(){var e=this,t=this.height,n=this.doc.cm,i=this.line;this.height=null;var r=Gn(this)-t;r&&(cn(this.doc,i)||ot(i,i.height+r),n&&Wr(n,(function(){n.curOp.forceUpdate=!0,vs(n,i,r),Pn(n,"lineWidgetChanged",n,e,st(i))})))},Le(ms);var bs=0,_s=function(e,t){this.lines=[],this.type=t,this.doc=e,this.id=++bs};function ws(e,t,n,i,r){if(i&&i.shared)return ks(e,t,n,i,r);if(e.cm&&!e.cm.curOp)return Ir(e.cm,ws)(e,t,n,i,r);var o=new _s(e,r),s=ht(t,n);if(i&&j(i,o,!1),s>0||0==s&&!1!==o.clearWhenEmpty)return o;if(o.replacedWith&&(o.collapsed=!0,o.widgetNode=E("span",[o.replacedWith],"CodeMirror-widget"),i.handleMouseEvents||o.widgetNode.setAttribute("cm-ignore-events","true"),i.insertLeft&&(o.widgetNode.insertLeft=!0)),o.collapsed){if(nn(e,t.line,t,n,o)||t.line!=n.line&&nn(e,n.line,t,n,o))throw new Error("Inserting collapsed marker partially overlapping an existing one");It()}o.addToHistory&&Eo(e,{from:t,to:n,origin:"markText"},e.sel,NaN);var a,l=t.line,c=e.cm;if(e.iter(l,n.line+1,(function(i){c&&o.collapsed&&!c.options.lineWrapping&&rn(i)==c.display.maxLine&&(a=!0),o.collapsed&&l!=t.line&&ot(i,0),zt(i,new Ft(o,l==t.line?t.ch:null,l==n.line?n.ch:null),e.cm&&e.cm.curOp),++l})),o.collapsed&&e.iter(t.line,n.line+1,(function(t){cn(e,t)&&ot(t,0)})),o.clearOnEnter&&be(o,"beforeCursorEnter",(function(){return o.clear()})),o.readOnly&&(Wt(),(e.history.done.length||e.history.undone.length)&&e.clearHistory()),o.collapsed&&(o.id=++bs,o.atomic=!0),c){if(a&&(c.curOp.updateMaxLine=!0),o.collapsed)ji(c,t.line,n.line+1);else if(o.className||o.startStyle||o.endStyle||o.css||o.attributes||o.title)for(var u=t.line;u<=n.line;u++)qi(c,u,"text");o.atomic&&Yo(c.doc),Pn(c,"markerAdded",c,o)}return o}_s.prototype.clear=function(){if(!this.explicitlyCleared){var e=this.doc.cm,t=e&&!e.curOp;if(t&&Ar(e),Se(this,"clear")){var n=this.find();n&&Pn(this,"clear",n.from,n.to)}for(var i=null,r=null,o=0;o<this.lines.length;++o){var s=this.lines[o],a=Bt(s.markedSpans,this);e&&!this.collapsed?qi(e,st(s),"text"):e&&(null!=a.to&&(r=st(s)),null!=a.from&&(i=st(s))),s.markedSpans=Rt(s.markedSpans,a),null==a.from&&this.collapsed&&!cn(this.doc,s)&&e&&ot(s,Pi(e.display))}if(e&&this.collapsed&&!e.options.lineWrapping)for(var l=0;l<this.lines.length;++l){var c=rn(this.lines[l]),u=dn(c);u>e.display.maxLineLength&&(e.display.maxLine=c,e.display.maxLineLength=u,e.display.maxLineChanged=!0)}null!=i&&e&&this.collapsed&&ji(e,i,r+1),this.lines.length=0,this.explicitlyCleared=!0,this.atomic&&this.doc.cantEdit&&(this.doc.cantEdit=!1,e&&Yo(e.doc)),e&&Pn(e,"markerCleared",e,this,i,r),t&&Mr(e),this.parent&&this.parent.clear()}},_s.prototype.find=function(e,t){var n,i;null==e&&"bookmark"==this.type&&(e=1);for(var r=0;r<this.lines.length;++r){var o=this.lines[r],s=Bt(o.markedSpans,this);if(null!=s.from&&(n=ut(t?o:st(o),s.from),-1==e))return n;if(null!=s.to&&(i=ut(t?o:st(o),s.to),1==e))return i}return n&&{from:n,to:i}},_s.prototype.changed=function(){var e=this,t=this.find(-1,!0),n=this,i=this.doc.cm;t&&i&&Wr(i,(function(){var r=t.line,o=st(t.line),s=si(i,o);if(s&&(gi(s),i.curOp.selectionChanged=i.curOp.forceUpdate=!0),i.curOp.updateMaxLine=!0,!cn(n.doc,r)&&null!=n.height){var a=n.height;n.height=null;var l=Gn(n)-a;l&&ot(r,r.height+l)}Pn(i,"markerChanged",i,e)}))},_s.prototype.attachLine=function(e){if(!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;t.maybeHiddenMarkers&&-1!=$(t.maybeHiddenMarkers,this)||(t.maybeUnhiddenMarkers||(t.maybeUnhiddenMarkers=[])).push(this)}this.lines.push(e)},_s.prototype.detachLine=function(e){if(this.lines.splice($(this.lines,e),1),!this.lines.length&&this.doc.cm){var t=this.doc.cm.curOp;(t.maybeHiddenMarkers||(t.maybeHiddenMarkers=[])).push(this)}},Le(_s);var xs=function(e,t){this.markers=e,this.primary=t;for(var n=0;n<e.length;++n)e[n].parent=this};function ks(e,t,n,i,r){(i=j(i)).shared=!1;var o=[ws(e,t,n,i,r)],s=o[0],a=i.widgetNode;return ko(e,(function(e){a&&(i.widgetNode=a.cloneNode(!0)),o.push(ws(e,vt(e,t),vt(e,n),i,r));for(var l=0;l<e.linked.length;++l)if(e.linked[l].isParent)return;s=ee(o)})),new xs(o,s)}function Cs(e){return e.findMarks(ut(e.first,0),e.clipPos(ut(e.lastLine())),(function(e){return e.parent}))}function Ss(e,t){for(var n=0;n<t.length;n++){var i=t[n],r=i.find(),o=e.clipPos(r.from),s=e.clipPos(r.to);if(ht(o,s)){var a=ws(e,o,s,i.primary,i.primary.type);i.markers.push(a),a.parent=i}}}function Ls(e){for(var t=function(t){var n=e[t],i=[n.primary.doc];ko(n.primary.doc,(function(e){return i.push(e)}));for(var r=0;r<n.markers.length;r++){var o=n.markers[r];-1==$(i,o.doc)&&(o.parent=null,n.markers.splice(r--,1))}},n=0;n<e.length;n++)t(n)}xs.prototype.clear=function(){if(!this.explicitlyCleared){this.explicitlyCleared=!0;for(var e=0;e<this.markers.length;++e)this.markers[e].clear();Pn(this,"clear")}},xs.prototype.find=function(e,t){return this.primary.find(e,t)},Le(xs);var Ts=0,As=function(e,t,n,i,r){if(!(this instanceof As))return new As(e,t,n,i,r);null==n&&(n=0),gs.call(this,[new ps([new pn("",null)])]),this.first=n,this.scrollTop=this.scrollLeft=0,this.cantEdit=!1,this.cleanGeneration=1,this.modeFrontier=this.highlightFrontier=n;var o=ut(n,0);this.sel=fo(o),this.history=new To(null),this.id=++Ts,this.modeOption=t,this.lineSep=i,this.direction="rtl"==r?"rtl":"ltr",this.extend=!1,"string"==typeof e&&(e=this.splitLines(e)),xo(this,{from:o,to:o,text:e}),Ko(this,fo(o),G)};As.prototype=re(gs.prototype,{constructor:As,iter:function(e,t,n){n?this.iterN(e-this.first,t-e,n):this.iterN(this.first,this.first+this.size,e)},insert:function(e,t){for(var n=0,i=0;i<t.length;++i)n+=t[i].height;this.insertInner(e-this.first,t,n)},remove:function(e,t){this.removeInner(e-this.first,t)},getValue:function(e){var t=rt(this,this.first,this.first+this.size);return!1===e?t:t.join(e||this.lineSeparator())},setValue:Br((function(e){var t=ut(this.first,0),n=this.first+this.size-1;is(this,{from:t,to:ut(n,nt(this,n).text.length),text:this.splitLines(e),origin:"setValue",full:!0},!0),this.cm&&fr(this.cm,0,0),Ko(this,fo(t),G)})),replaceRange:function(e,t,n,i){cs(this,e,t=vt(this,t),n=n?vt(this,n):t,i)},getRange:function(e,t,n){var i=it(this,vt(this,e),vt(this,t));return!1===n?i:""===n?i.join(""):i.join(n||this.lineSeparator())},getLine:function(e){var t=this.getLineHandle(e);return t&&t.text},getLineHandle:function(e){if(lt(this,e))return nt(this,e)},getLineNumber:function(e){return st(e)},getLineHandleVisualStart:function(e){return"number"==typeof e&&(e=nt(this,e)),rn(e)},lineCount:function(){return this.size},firstLine:function(){return this.first},lastLine:function(){return this.first+this.size-1},clipPos:function(e){return vt(this,e)},getCursor:function(e){var t=this.sel.primary();return null==e||"head"==e?t.head:"anchor"==e?t.anchor:"end"==e||"to"==e||!1===e?t.to():t.from()},listSelections:function(){return this.sel.ranges},somethingSelected:function(){return this.sel.somethingSelected()},setCursor:Br((function(e,t,n){Uo(this,vt(this,"number"==typeof e?ut(e,t||0):e),null,n)})),setSelection:Br((function(e,t,n){Uo(this,vt(this,e),vt(this,t||e),n)})),extendSelection:Br((function(e,t,n){zo(this,vt(this,e),t&&vt(this,t),n)})),extendSelections:Br((function(e,t){jo(this,bt(this,e),t)})),extendSelectionsBy:Br((function(e,t){jo(this,bt(this,te(this.sel.ranges,e)),t)})),setSelections:Br((function(e,t,n){if(e.length){for(var i=[],r=0;r<e.length;r++)i[r]=new uo(vt(this,e[r].anchor),vt(this,e[r].head||e[r].anchor));null==t&&(t=Math.min(e.length-1,this.sel.primIndex)),Ko(this,ho(this.cm,i,t),n)}})),addSelection:Br((function(e,t,n){var i=this.sel.ranges.slice(0);i.push(new uo(vt(this,e),vt(this,t||e))),Ko(this,ho(this.cm,i,i.length-1),n)})),getSelection:function(e){for(var t,n=this.sel.ranges,i=0;i<n.length;i++){var r=it(this,n[i].from(),n[i].to());t=t?t.concat(r):r}return!1===e?t:t.join(e||this.lineSeparator())},getSelections:function(e){for(var t=[],n=this.sel.ranges,i=0;i<n.length;i++){var r=it(this,n[i].from(),n[i].to());!1!==e&&(r=r.join(e||this.lineSeparator())),t[i]=r}return t},replaceSelection:function(e,t,n){for(var i=[],r=0;r<this.sel.ranges.length;r++)i[r]=e;this.replaceSelections(i,t,n||"+input")},replaceSelections:Br((function(e,t,n){for(var i=[],r=this.sel,o=0;o<r.ranges.length;o++){var s=r.ranges[o];i[o]={from:s.from(),to:s.to(),text:this.splitLines(e[o]),origin:n}}for(var a=t&&"end"!=t&&yo(this,i,t),l=i.length-1;l>=0;l--)is(this,i[l]);a?Vo(this,a):this.cm&&dr(this.cm)})),undo:Br((function(){os(this,"undo")})),redo:Br((function(){os(this,"redo")})),undoSelection:Br((function(){os(this,"undo",!0)})),redoSelection:Br((function(){os(this,"redo",!0)})),setExtending:function(e){this.extend=e},getExtending:function(){return this.extend},historySize:function(){for(var e=this.history,t=0,n=0,i=0;i<e.done.length;i++)e.done[i].ranges||++t;for(var r=0;r<e.undone.length;r++)e.undone[r].ranges||++n;return{undo:t,redo:n}},clearHistory:function(){var e=this;this.history=new To(this.history),ko(this,(function(t){return t.history=e.history}),!0)},markClean:function(){this.cleanGeneration=this.changeGeneration(!0)},changeGeneration:function(e){return e&&(this.history.lastOp=this.history.lastSelOp=this.history.lastOrigin=null),this.history.generation},isClean:function(e){return this.history.generation==(e||this.cleanGeneration)},getHistory:function(){return{done:Bo(this.history.done),undone:Bo(this.history.undone)}},setHistory:function(e){var t=this.history=new To(this.history);t.done=Bo(e.done.slice(0),null,!0),t.undone=Bo(e.undone.slice(0),null,!0)},setGutterMarker:Br((function(e,t,n){return fs(this,e,"gutter",(function(e){var i=e.gutterMarkers||(e.gutterMarkers={});return i[t]=n,!n&&le(i)&&(e.gutterMarkers=null),!0}))})),clearGutter:Br((function(e){var t=this;this.iter((function(n){n.gutterMarkers&&n.gutterMarkers[e]&&fs(t,n,"gutter",(function(){return n.gutterMarkers[e]=null,le(n.gutterMarkers)&&(n.gutterMarkers=null),!0}))}))})),lineInfo:function(e){var t;if("number"==typeof e){if(!lt(this,e))return null;if(t=e,!(e=nt(this,e)))return null}else if(null==(t=st(e)))return null;return{line:t,handle:e,text:e.text,gutterMarkers:e.gutterMarkers,textClass:e.textClass,bgClass:e.bgClass,wrapClass:e.wrapClass,widgets:e.widgets}},addLineClass:Br((function(e,t,n){return fs(this,e,"gutter"==t?"gutter":"class",(function(e){var i="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass";if(e[i]){if(S(n).test(e[i]))return!1;e[i]+=" "+n}else e[i]=n;return!0}))})),removeLineClass:Br((function(e,t,n){return fs(this,e,"gutter"==t?"gutter":"class",(function(e){var i="text"==t?"textClass":"background"==t?"bgClass":"gutter"==t?"gutterClass":"wrapClass",r=e[i];if(!r)return!1;if(null==n)e[i]=null;else{var o=r.match(S(n));if(!o)return!1;var s=o.index+o[0].length;e[i]=r.slice(0,o.index)+(o.index&&s!=r.length?" ":"")+r.slice(s)||null}return!0}))})),addLineWidget:Br((function(e,t,n){return ys(this,e,t,n)})),removeLineWidget:function(e){e.clear()},markText:function(e,t,n){return ws(this,vt(this,e),vt(this,t),n,n&&n.type||"range")},setBookmark:function(e,t){var n={replacedWith:t&&(null==t.nodeType?t.widget:t),insertLeft:t&&t.insertLeft,clearWhenEmpty:!1,shared:t&&t.shared,handleMouseEvents:t&&t.handleMouseEvents};return ws(this,e=vt(this,e),e,n,"bookmark")},findMarksAt:function(e){var t=[],n=nt(this,(e=vt(this,e)).line).markedSpans;if(n)for(var i=0;i<n.length;++i){var r=n[i];(null==r.from||r.from<=e.ch)&&(null==r.to||r.to>=e.ch)&&t.push(r.marker.parent||r.marker)}return t},findMarks:function(e,t,n){e=vt(this,e),t=vt(this,t);var i=[],r=e.line;return this.iter(e.line,t.line+1,(function(o){var s=o.markedSpans;if(s)for(var a=0;a<s.length;a++){var l=s[a];null!=l.to&&r==e.line&&e.ch>=l.to||null==l.from&&r!=e.line||null!=l.from&&r==t.line&&l.from>=t.ch||n&&!n(l.marker)||i.push(l.marker.parent||l.marker)}++r})),i},getAllMarks:function(){var e=[];return this.iter((function(t){var n=t.markedSpans;if(n)for(var i=0;i<n.length;++i)null!=n[i].from&&e.push(n[i].marker)})),e},posFromIndex:function(e){var t,n=this.first,i=this.lineSeparator().length;return this.iter((function(r){var o=r.text.length+i;if(o>e)return t=e,!0;e-=o,++n})),vt(this,ut(n,t))},indexFromPos:function(e){var t=(e=vt(this,e)).ch;if(e.line<this.first||e.ch<0)return 0;var n=this.lineSeparator().length;return this.iter(this.first,e.line,(function(e){t+=e.text.length+n})),t},copy:function(e){var t=new As(rt(this,this.first,this.first+this.size),this.modeOption,this.first,this.lineSep,this.direction);return t.scrollTop=this.scrollTop,t.scrollLeft=this.scrollLeft,t.sel=this.sel,t.extend=!1,e&&(t.history.undoDepth=this.history.undoDepth,t.setHistory(this.getHistory())),t},linkedDoc:function(e){e||(e={});var t=this.first,n=this.first+this.size;null!=e.from&&e.from>t&&(t=e.from),null!=e.to&&e.to<n&&(n=e.to);var i=new As(rt(this,t,n),e.mode||this.modeOption,t,this.lineSep,this.direction);return e.sharedHist&&(i.history=this.history),(this.linked||(this.linked=[])).push({doc:i,sharedHist:e.sharedHist}),i.linked=[{doc:this,isParent:!0,sharedHist:e.sharedHist}],Ss(i,Cs(this)),i},unlinkDoc:function(e){if(e instanceof ja&&(e=e.doc),this.linked)for(var t=0;t<this.linked.length;++t)if(this.linked[t].doc==e){this.linked.splice(t,1),e.unlinkDoc(this),Ls(Cs(this));break}if(e.history==this.history){var n=[e.id];ko(e,(function(e){return n.push(e.id)}),!0),e.history=new To(null),e.history.done=Bo(this.history.done,n),e.history.undone=Bo(this.history.undone,n)}},iterLinkedDocs:function(e){ko(this,e)},getMode:function(){return this.mode},getEditor:function(){return this.cm},splitLines:function(e){return this.lineSep?e.split(this.lineSep):Be(e)},lineSeparator:function(){return this.lineSep||"\n"},setDirection:Br((function(e){"rtl"!=e&&(e="ltr"),e!=this.direction&&(this.direction=e,this.iter((function(e){return e.order=null})),this.cm&&Lo(this.cm))}))}),As.prototype.eachLine=As.prototype.iter;var Ms=0;function Os(e){var t=this;if(Ds(t),!ke(t,e)&&!Xn(t.display,e)){Te(e),s&&(Ms=+new Date);var n=Ri(t,e,!0),i=e.dataTransfer.files;if(n&&!t.isReadOnly())if(i&&i.length&&window.FileReader&&window.File)for(var r=i.length,o=Array(r),a=0,l=function(){++a==r&&Ir(t,(function(){var e={from:n=vt(t.doc,n),to:n,text:t.doc.splitLines(o.filter((function(e){return null!=e})).join(t.doc.lineSeparator())),origin:"paste"};is(t.doc,e),Vo(t.doc,fo(vt(t.doc,n),vt(t.doc,po(e))))}))()},c=function(e,n){if(t.options.allowDropFileTypes&&-1==$(t.options.allowDropFileTypes,e.type))l();else{var i=new FileReader;i.onerror=function(){return l()},i.onload=function(){var e=i.result;/[\x00-\x08\x0e-\x1f]{2}/.test(e)||(o[n]=e),l()},i.readAsText(e)}},u=0;u<i.length;u++)c(i[u],u);else{if(t.state.draggingText&&t.doc.sel.contains(n)>-1)return t.state.draggingText(e),void setTimeout((function(){return t.display.input.focus()}),20);try{var h=e.dataTransfer.getData("Text");if(h){var d;if(t.state.draggingText&&!t.state.draggingText.copy&&(d=t.listSelections()),Go(t.doc,fo(n,n)),d)for(var f=0;f<d.length;++f)cs(t.doc,"",d[f].anchor,d[f].head,"drag");t.replaceSelection(h,"around","paste"),t.display.input.focus()}}catch(e){}}}}function Es(e,t){if(s&&(!e.state.draggingText||+new Date-Ms<100))Oe(t);else if(!ke(e,t)&&!Xn(e.display,t)&&(t.dataTransfer.setData("Text",e.getSelection()),t.dataTransfer.effectAllowed="copyMove",t.dataTransfer.setDragImage&&!f)){var n=O("img",null,null,"position: fixed; left: 0; top: 0;");n.src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==",d&&(n.width=n.height=1,e.display.wrapper.appendChild(n),n._top=n.offsetTop),t.dataTransfer.setDragImage(n,0,0),d&&n.parentNode.removeChild(n)}}function Ns(e,t){var n=Ri(e,t);if(n){var i=document.createDocumentFragment();Yi(e,n,i),e.display.dragCursor||(e.display.dragCursor=O("div",null,"CodeMirror-cursors CodeMirror-dragcursors"),e.display.lineSpace.insertBefore(e.display.dragCursor,e.display.cursorDiv)),M(e.display.dragCursor,i)}}function Ds(e){e.display.dragCursor&&(e.display.lineSpace.removeChild(e.display.dragCursor),e.display.dragCursor=null)}function Ps(e){if(document.getElementsByClassName){for(var t=document.getElementsByClassName("CodeMirror"),n=[],i=0;i<t.length;i++){var r=t[i].CodeMirror;r&&n.push(r)}n.length&&n[0].operation((function(){for(var t=0;t<n.length;t++)e(n[t])}))}}var Hs=!1;function Ws(){Hs||(Is(),Hs=!0)}function Is(){var e;be(window,"resize",(function(){null==e&&(e=setTimeout((function(){e=null,Ps(Fs)}),100))})),be(window,"blur",(function(){return Ps(ir)}))}function Fs(e){var t=e.display;t.cachedCharWidth=t.cachedTextHeight=t.cachedPaddingH=null,t.scrollbarsClipped=!1,e.setSize()}for(var Bs={3:"Pause",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"PrintScrn",45:"Insert",46:"Delete",59:";",61:"=",91:"Mod",92:"Mod",93:"Mod",106:"*",107:"=",109:"-",110:".",111:"/",145:"ScrollLock",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",224:"Mod",63232:"Up",63233:"Down",63234:"Left",63235:"Right",63272:"Delete",63273:"Home",63275:"End",63276:"PageUp",63277:"PageDown",63302:"Insert"},Rs=0;Rs<10;Rs++)Bs[Rs+48]=Bs[Rs+96]=String(Rs);for(var zs=65;zs<=90;zs++)Bs[zs]=String.fromCharCode(zs);for(var js=1;js<=12;js++)Bs[js+111]=Bs[js+63235]="F"+js;var qs={};function Us(e){var t,n,i,r,o=e.split(/-(?!$)/);e=o[o.length-1];for(var s=0;s<o.length-1;s++){var a=o[s];if(/^(cmd|meta|m)$/i.test(a))r=!0;else if(/^a(lt)?$/i.test(a))t=!0;else if(/^(c|ctrl|control)$/i.test(a))n=!0;else{if(!/^s(hift)?$/i.test(a))throw new Error("Unrecognized modifier name: "+a);i=!0}}return t&&(e="Alt-"+e),n&&(e="Ctrl-"+e),r&&(e="Cmd-"+e),i&&(e="Shift-"+e),e}function $s(e){var t={};for(var n in e)if(e.hasOwnProperty(n)){var i=e[n];if(/^(name|fallthrough|(de|at)tach)$/.test(n))continue;if("..."==i){delete e[n];continue}for(var r=te(n.split(" "),Us),o=0;o<r.length;o++){var s=void 0,a=void 0;o==r.length-1?(a=r.join(" "),s=i):(a=r.slice(0,o+1).join(" "),s="...");var l=t[a];if(l){if(l!=s)throw new Error("Inconsistent bindings for "+a)}else t[a]=s}delete e[n]}for(var c in t)e[c]=t[c];return e}function Vs(e,t,n,i){var r=(t=Ys(t)).call?t.call(e,i):t[e];if(!1===r)return"nothing";if("..."===r)return"multi";if(null!=r&&n(r))return"handled";if(t.fallthrough){if("[object Array]"!=Object.prototype.toString.call(t.fallthrough))return Vs(e,t.fallthrough,n,i);for(var o=0;o<t.fallthrough.length;o++){var s=Vs(e,t.fallthrough[o],n,i);if(s)return s}}}function Ks(e){var t="string"==typeof e?e:Bs[e.keyCode];return"Ctrl"==t||"Alt"==t||"Shift"==t||"Mod"==t}function Gs(e,t,n){var i=e;return t.altKey&&"Alt"!=i&&(e="Alt-"+e),(k?t.metaKey:t.ctrlKey)&&"Ctrl"!=i&&(e="Ctrl-"+e),(k?t.ctrlKey:t.metaKey)&&"Mod"!=i&&(e="Cmd-"+e),!n&&t.shiftKey&&"Shift"!=i&&(e="Shift-"+e),e}function Xs(e,t){if(d&&34==e.keyCode&&e.char)return!1;var n=Bs[e.keyCode];return null!=n&&!e.altGraphKey&&(3==e.keyCode&&e.code&&(n=e.code),Gs(n,e,t))}function Ys(e){return"string"==typeof e?qs[e]:e}function Js(e,t){for(var n=e.doc.sel.ranges,i=[],r=0;r<n.length;r++){for(var o=t(n[r]);i.length&&ht(o.from,ee(i).to)<=0;){var s=i.pop();if(ht(s.from,o.from)<0){o.from=s.from;break}}i.push(o)}Wr(e,(function(){for(var t=i.length-1;t>=0;t--)cs(e.doc,"",i[t].from,i[t].to,"+delete");dr(e)}))}function Zs(e,t,n){var i=he(e.text,t+n,n);return i<0||i>e.text.length?null:i}function Qs(e,t,n){var i=Zs(e,t.ch,n);return null==i?null:new ut(t.line,i,n<0?"after":"before")}function ea(e,t,n,i,r){if(e){"rtl"==t.doc.direction&&(r=-r);var o=ve(n,t.doc.direction);if(o){var s,a=r<0?ee(o):o[0],l=r<0==(1==a.level)?"after":"before";if(a.level>0||"rtl"==t.doc.direction){var c=ai(t,n);s=r<0?n.text.length-1:0;var u=li(t,c,s).top;s=de((function(e){return li(t,c,e).top==u}),r<0==(1==a.level)?a.from:a.to-1,s),"before"==l&&(s=Zs(n,s,1))}else s=r<0?a.to:a.from;return new ut(i,s,l)}}return new ut(i,r<0?n.text.length:0,r<0?"before":"after")}function ta(e,t,n,i){var r=ve(t,e.doc.direction);if(!r)return Qs(t,n,i);n.ch>=t.text.length?(n.ch=t.text.length,n.sticky="before"):n.ch<=0&&(n.ch=0,n.sticky="after");var o=ge(r,n.ch,n.sticky),s=r[o];if("ltr"==e.doc.direction&&s.level%2==0&&(i>0?s.to>n.ch:s.from<n.ch))return Qs(t,n,i);var a,l=function(e,n){return Zs(t,e instanceof ut?e.ch:e,n)},c=function(n){return e.options.lineWrapping?(a=a||ai(e,t),Mi(e,t,a,n)):{begin:0,end:t.text.length}},u=c("before"==n.sticky?l(n,-1):n.ch);if("rtl"==e.doc.direction||1==s.level){var h=1==s.level==i<0,d=l(n,h?1:-1);if(null!=d&&(h?d<=s.to&&d<=u.end:d>=s.from&&d>=u.begin)){var f=h?"before":"after";return new ut(n.line,d,f)}}var p=function(e,t,i){for(var o=function(e,t){return t?new ut(n.line,l(e,1),"before"):new ut(n.line,e,"after")};e>=0&&e<r.length;e+=t){var s=r[e],a=t>0==(1!=s.level),c=a?i.begin:l(i.end,-1);if(s.from<=c&&c<s.to)return o(c,a);if(c=a?s.from:l(s.to,-1),i.begin<=c&&c<i.end)return o(c,a)}},g=p(o+i,i,u);if(g)return g;var m=i>0?u.end:l(u.begin,-1);return null==m||i>0&&m==t.text.length||!(g=p(i>0?0:r.length-1,i,c(m)))?null:g}qs.basic={Left:"goCharLeft",Right:"goCharRight",Up:"goLineUp",Down:"goLineDown",End:"goLineEnd",Home:"goLineStartSmart",PageUp:"goPageUp",PageDown:"goPageDown",Delete:"delCharAfter",Backspace:"delCharBefore","Shift-Backspace":"delCharBefore",Tab:"defaultTab","Shift-Tab":"indentAuto",Enter:"newlineAndIndent",Insert:"toggleOverwrite",Esc:"singleSelection"},qs.pcDefault={"Ctrl-A":"selectAll","Ctrl-D":"deleteLine","Ctrl-Z":"undo","Shift-Ctrl-Z":"redo","Ctrl-Y":"redo","Ctrl-Home":"goDocStart","Ctrl-End":"goDocEnd","Ctrl-Up":"goLineUp","Ctrl-Down":"goLineDown","Ctrl-Left":"goGroupLeft","Ctrl-Right":"goGroupRight","Alt-Left":"goLineStart","Alt-Right":"goLineEnd","Ctrl-Backspace":"delGroupBefore","Ctrl-Delete":"delGroupAfter","Ctrl-S":"save","Ctrl-F":"find","Ctrl-G":"findNext","Shift-Ctrl-G":"findPrev","Shift-Ctrl-F":"replace","Shift-Ctrl-R":"replaceAll","Ctrl-[":"indentLess","Ctrl-]":"indentMore","Ctrl-U":"undoSelection","Shift-Ctrl-U":"redoSelection","Alt-U":"redoSelection",fallthrough:"basic"},qs.emacsy={"Ctrl-F":"goCharRight","Ctrl-B":"goCharLeft","Ctrl-P":"goLineUp","Ctrl-N":"goLineDown","Ctrl-A":"goLineStart","Ctrl-E":"goLineEnd","Ctrl-V":"goPageDown","Shift-Ctrl-V":"goPageUp","Ctrl-D":"delCharAfter","Ctrl-H":"delCharBefore","Alt-Backspace":"delWordBefore","Ctrl-K":"killLine","Ctrl-T":"transposeChars","Ctrl-O":"openLine"},qs.macDefault={"Cmd-A":"selectAll","Cmd-D":"deleteLine","Cmd-Z":"undo","Shift-Cmd-Z":"redo","Cmd-Y":"redo","Cmd-Home":"goDocStart","Cmd-Up":"goDocStart","Cmd-End":"goDocEnd","Cmd-Down":"goDocEnd","Alt-Left":"goGroupLeft","Alt-Right":"goGroupRight","Cmd-Left":"goLineLeft","Cmd-Right":"goLineRight","Alt-Backspace":"delGroupBefore","Ctrl-Alt-Backspace":"delGroupAfter","Alt-Delete":"delGroupAfter","Cmd-S":"save","Cmd-F":"find","Cmd-G":"findNext","Shift-Cmd-G":"findPrev","Cmd-Alt-F":"replace","Shift-Cmd-Alt-F":"replaceAll","Cmd-[":"indentLess","Cmd-]":"indentMore","Cmd-Backspace":"delWrappedLineLeft","Cmd-Delete":"delWrappedLineRight","Cmd-U":"undoSelection","Shift-Cmd-U":"redoSelection","Ctrl-Up":"goDocStart","Ctrl-Down":"goDocEnd",fallthrough:["basic","emacsy"]},qs.default=b?qs.macDefault:qs.pcDefault;var na={selectAll:ts,singleSelection:function(e){return e.setSelection(e.getCursor("anchor"),e.getCursor("head"),G)},killLine:function(e){return Js(e,(function(t){if(t.empty()){var n=nt(e.doc,t.head.line).text.length;return t.head.ch==n&&t.head.line<e.lastLine()?{from:t.head,to:ut(t.head.line+1,0)}:{from:t.head,to:ut(t.head.line,n)}}return{from:t.from(),to:t.to()}}))},deleteLine:function(e){return Js(e,(function(t){return{from:ut(t.from().line,0),to:vt(e.doc,ut(t.to().line+1,0))}}))},delLineLeft:function(e){return Js(e,(function(e){return{from:ut(e.from().line,0),to:e.from()}}))},delWrappedLineLeft:function(e){return Js(e,(function(t){var n=e.charCoords(t.head,"div").top+5;return{from:e.coordsChar({left:0,top:n},"div"),to:t.from()}}))},delWrappedLineRight:function(e){return Js(e,(function(t){var n=e.charCoords(t.head,"div").top+5,i=e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div");return{from:t.from(),to:i}}))},undo:function(e){return e.undo()},redo:function(e){return e.redo()},undoSelection:function(e){return e.undoSelection()},redoSelection:function(e){return e.redoSelection()},goDocStart:function(e){return e.extendSelection(ut(e.firstLine(),0))},goDocEnd:function(e){return e.extendSelection(ut(e.lastLine()))},goLineStart:function(e){return e.extendSelectionsBy((function(t){return ia(e,t.head.line)}),{origin:"+move",bias:1})},goLineStartSmart:function(e){return e.extendSelectionsBy((function(t){return oa(e,t.head)}),{origin:"+move",bias:1})},goLineEnd:function(e){return e.extendSelectionsBy((function(t){return ra(e,t.head.line)}),{origin:"+move",bias:-1})},goLineRight:function(e){return e.extendSelectionsBy((function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:e.display.lineDiv.offsetWidth+100,top:n},"div")}),Y)},goLineLeft:function(e){return e.extendSelectionsBy((function(t){var n=e.cursorCoords(t.head,"div").top+5;return e.coordsChar({left:0,top:n},"div")}),Y)},goLineLeftSmart:function(e){return e.extendSelectionsBy((function(t){var n=e.cursorCoords(t.head,"div").top+5,i=e.coordsChar({left:0,top:n},"div");return i.ch<e.getLine(i.line).search(/\S/)?oa(e,t.head):i}),Y)},goLineUp:function(e){return e.moveV(-1,"line")},goLineDown:function(e){return e.moveV(1,"line")},goPageUp:function(e){return e.moveV(-1,"page")},goPageDown:function(e){return e.moveV(1,"page")},goCharLeft:function(e){return e.moveH(-1,"char")},goCharRight:function(e){return e.moveH(1,"char")},goColumnLeft:function(e){return e.moveH(-1,"column")},goColumnRight:function(e){return e.moveH(1,"column")},goWordLeft:function(e){return e.moveH(-1,"word")},goGroupRight:function(e){return e.moveH(1,"group")},goGroupLeft:function(e){return e.moveH(-1,"group")},goWordRight:function(e){return e.moveH(1,"word")},delCharBefore:function(e){return e.deleteH(-1,"codepoint")},delCharAfter:function(e){return e.deleteH(1,"char")},delWordBefore:function(e){return e.deleteH(-1,"word")},delWordAfter:function(e){return e.deleteH(1,"word")},delGroupBefore:function(e){return e.deleteH(-1,"group")},delGroupAfter:function(e){return e.deleteH(1,"group")},indentAuto:function(e){return e.indentSelection("smart")},indentMore:function(e){return e.indentSelection("add")},indentLess:function(e){return e.indentSelection("subtract")},insertTab:function(e){return e.replaceSelection("\t")},insertSoftTab:function(e){for(var t=[],n=e.listSelections(),i=e.options.tabSize,r=0;r<n.length;r++){var o=n[r].from(),s=q(e.getLine(o.line),o.ch,i);t.push(Q(i-s%i))}e.replaceSelections(t)},defaultTab:function(e){e.somethingSelected()?e.indentSelection("add"):e.execCommand("insertTab")},transposeChars:function(e){return Wr(e,(function(){for(var t=e.listSelections(),n=[],i=0;i<t.length;i++)if(t[i].empty()){var r=t[i].head,o=nt(e.doc,r.line).text;if(o)if(r.ch==o.length&&(r=new ut(r.line,r.ch-1)),r.ch>0)r=new ut(r.line,r.ch+1),e.replaceRange(o.charAt(r.ch-1)+o.charAt(r.ch-2),ut(r.line,r.ch-2),r,"+transpose");else if(r.line>e.doc.first){var s=nt(e.doc,r.line-1).text;s&&(r=new ut(r.line,1),e.replaceRange(o.charAt(0)+e.doc.lineSeparator()+s.charAt(s.length-1),ut(r.line-1,s.length-1),r,"+transpose"))}n.push(new uo(r,r))}e.setSelections(n)}))},newlineAndIndent:function(e){return Wr(e,(function(){for(var t=e.listSelections(),n=t.length-1;n>=0;n--)e.replaceRange(e.doc.lineSeparator(),t[n].anchor,t[n].head,"+input");t=e.listSelections();for(var i=0;i<t.length;i++)e.indentLine(t[i].from().line,null,!0);dr(e)}))},openLine:function(e){return e.replaceSelection("\n","start")},toggleOverwrite:function(e){return e.toggleOverwrite()}};function ia(e,t){var n=nt(e.doc,t),i=rn(n);return i!=n&&(t=st(i)),ea(!0,e,i,t,1)}function ra(e,t){var n=nt(e.doc,t),i=on(n);return i!=n&&(t=st(i)),ea(!0,e,n,t,-1)}function oa(e,t){var n=ia(e,t.line),i=nt(e.doc,n.line),r=ve(i,e.doc.direction);if(!r||0==r[0].level){var o=Math.max(n.ch,i.text.search(/\S/)),s=t.line==n.line&&t.ch<=o&&t.ch;return ut(n.line,s?0:o,n.sticky)}return n}function sa(e,t,n){if("string"==typeof t&&!(t=na[t]))return!1;e.display.input.ensurePolled();var i=e.display.shift,r=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),n&&(e.display.shift=!1),r=t(e)!=K}finally{e.display.shift=i,e.state.suppressEdits=!1}return r}function aa(e,t,n){for(var i=0;i<e.state.keyMaps.length;i++){var r=Vs(t,e.state.keyMaps[i],n,e);if(r)return r}return e.options.extraKeys&&Vs(t,e.options.extraKeys,n,e)||Vs(t,e.options.keyMap,n,e)}var la=new U;function ca(e,t,n,i){var r=e.state.keySeq;if(r){if(Ks(t))return"handled";if(/\'$/.test(t)?e.state.keySeq=null:la.set(50,(function(){e.state.keySeq==r&&(e.state.keySeq=null,e.display.input.reset())})),ua(e,r+" "+t,n,i))return!0}return ua(e,t,n,i)}function ua(e,t,n,i){var r=aa(e,t,i);return"multi"==r&&(e.state.keySeq=t),"handled"==r&&Pn(e,"keyHandled",e,t,n),"handled"!=r&&"multi"!=r||(Te(n),Qi(e)),!!r}function ha(e,t){var n=Xs(t,!0);return!!n&&(t.shiftKey&&!e.state.keySeq?ca(e,"Shift-"+n,t,(function(t){return sa(e,t,!0)}))||ca(e,n,t,(function(t){if("string"==typeof t?/^go[A-Z]/.test(t):t.motion)return sa(e,t)})):ca(e,n,t,(function(t){return sa(e,t)})))}function da(e,t,n){return ca(e,"'"+n+"'",t,(function(t){return sa(e,t,!0)}))}var fa=null;function pa(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||(t.curOp.focus=D(F(t)),ke(t,e)))){s&&a<11&&27==e.keyCode&&(e.returnValue=!1);var i=e.keyCode;t.display.shift=16==i||e.shiftKey;var r=ha(t,e);d&&(fa=r?i:null,r||88!=i||ze||!(b?e.metaKey:e.ctrlKey)||t.replaceSelection("",null,"cut")),n&&!b&&!r&&46==i&&e.shiftKey&&!e.ctrlKey&&document.execCommand&&document.execCommand("cut"),18!=i||/\bCodeMirror-crosshair\b/.test(t.display.lineDiv.className)||ga(t)}}function ga(e){var t=e.display.lineDiv;function n(e){18!=e.keyCode&&e.altKey||(T(t,"CodeMirror-crosshair"),we(document,"keyup",n),we(document,"mouseover",n))}P(t,"CodeMirror-crosshair"),be(document,"keyup",n),be(document,"mouseover",n)}function ma(e){16==e.keyCode&&(this.doc.sel.shift=!1),ke(this,e)}function va(e){var t=this;if(!(e.target&&e.target!=t.display.input.getField()||Xn(t.display,e)||ke(t,e)||e.ctrlKey&&!e.altKey||b&&e.metaKey)){var n=e.keyCode,i=e.charCode;if(d&&n==fa)return fa=null,void Te(e);if(!d||e.which&&!(e.which<10)||!ha(t,e)){var r=String.fromCharCode(null==i?n:i);"\b"!=r&&(da(t,e,r)||t.display.input.onKeyPress(e))}}}var ya,ba,_a=400,wa=function(e,t,n){this.time=e,this.pos=t,this.button=n};function xa(e,t){var n=+new Date;return ba&&ba.compare(n,e,t)?(ya=ba=null,"triple"):ya&&ya.compare(n,e,t)?(ba=new wa(n,e,t),ya=null,"double"):(ya=new wa(n,e,t),ba=null,"single")}function ka(e){var t=this,n=t.display;if(!(ke(t,e)||n.activeTouch&&n.input.supportsTouch()))if(n.input.ensurePolled(),n.shift=e.shiftKey,Xn(n,e))l||(n.scroller.draggable=!1,setTimeout((function(){return n.scroller.draggable=!0}),100));else if(!Na(t,e)){var i=Ri(t,e),r=Ne(e),o=i?xa(i,r):"single";R(t).focus(),1==r&&t.state.selectingText&&t.state.selectingText(e),i&&Ca(t,r,i,o,e)||(1==r?i?La(t,i,o,e):Ee(e)==n.scroller&&Te(e):2==r?(i&&zo(t.doc,i),setTimeout((function(){return n.input.focus()}),20)):3==r&&(C?t.display.input.onContextMenu(e):tr(t)))}}function Ca(e,t,n,i,r){var o="Click";return"double"==i?o="Double"+o:"triple"==i&&(o="Triple"+o),ca(e,Gs(o=(1==t?"Left":2==t?"Middle":"Right")+o,r),r,(function(t){if("string"==typeof t&&(t=na[t]),!t)return!1;var i=!1;try{e.isReadOnly()&&(e.state.suppressEdits=!0),i=t(e,n)!=K}finally{e.state.suppressEdits=!1}return i}))}function Sa(e,t,n){var i=e.getOption("configureMouse"),r=i?i(e,t,n):{};if(null==r.unit){var o=_?n.shiftKey&&n.metaKey:n.altKey;r.unit=o?"rectangle":"single"==t?"char":"double"==t?"word":"line"}return(null==r.extend||e.doc.extend)&&(r.extend=e.doc.extend||n.shiftKey),null==r.addNew&&(r.addNew=b?n.metaKey:n.ctrlKey),null==r.moveOnDrag&&(r.moveOnDrag=!(b?n.altKey:n.ctrlKey)),r}function La(e,t,n,i){s?setTimeout(z(er,e),0):e.curOp.focus=D(F(e));var r,o=Sa(e,n,i),a=e.doc.sel;e.options.dragDrop&&He&&!e.isReadOnly()&&"single"==n&&(r=a.contains(t))>-1&&(ht((r=a.ranges[r]).from(),t)<0||t.xRel>0)&&(ht(r.to(),t)>0||t.xRel<0)?Ta(e,i,t,o):Ma(e,i,t,o)}function Ta(e,t,n,i){var r=e.display,o=!1,c=Ir(e,(function(t){l&&(r.scroller.draggable=!1),e.state.draggingText=!1,e.state.delayingBlurEvent&&(e.hasFocus()?e.state.delayingBlurEvent=!1:tr(e)),we(r.wrapper.ownerDocument,"mouseup",c),we(r.wrapper.ownerDocument,"mousemove",u),we(r.scroller,"dragstart",h),we(r.scroller,"drop",c),o||(Te(t),i.addNew||zo(e.doc,n,null,null,i.extend),l&&!f||s&&9==a?setTimeout((function(){r.wrapper.ownerDocument.body.focus({preventScroll:!0}),r.input.focus()}),20):r.input.focus())})),u=function(e){o=o||Math.abs(t.clientX-e.clientX)+Math.abs(t.clientY-e.clientY)>=10},h=function(){return o=!0};l&&(r.scroller.draggable=!0),e.state.draggingText=c,c.copy=!i.moveOnDrag,be(r.wrapper.ownerDocument,"mouseup",c),be(r.wrapper.ownerDocument,"mousemove",u),be(r.scroller,"dragstart",h),be(r.scroller,"drop",c),e.state.delayingBlurEvent=!0,setTimeout((function(){return r.input.focus()}),20),r.scroller.dragDrop&&r.scroller.dragDrop()}function Aa(e,t,n){if("char"==n)return new uo(t,t);if("word"==n)return e.findWordAt(t);if("line"==n)return new uo(ut(t.line,0),vt(e.doc,ut(t.line+1,0)));var i=n(e,t);return new uo(i.from,i.to)}function Ma(e,t,n,i){s&&tr(e);var r=e.display,o=e.doc;Te(t);var a,l,c=o.sel,u=c.ranges;if(i.addNew&&!i.extend?(l=o.sel.contains(n),a=l>-1?u[l]:new uo(n,n)):(a=o.sel.primary(),l=o.sel.primIndex),"rectangle"==i.unit)i.addNew||(a=new uo(n,n)),n=Ri(e,t,!0,!0),l=-1;else{var h=Aa(e,n,i.unit);a=i.extend?Ro(a,h.anchor,h.head,i.extend):h}i.addNew?-1==l?(l=u.length,Ko(o,ho(e,u.concat([a]),l),{scroll:!1,origin:"*mouse"})):u.length>1&&u[l].empty()&&"char"==i.unit&&!i.extend?(Ko(o,ho(e,u.slice(0,l).concat(u.slice(l+1)),0),{scroll:!1,origin:"*mouse"}),c=o.sel):qo(o,l,a,X):(l=0,Ko(o,new co([a],0),X),c=o.sel);var d=n;function f(t){if(0!=ht(d,t))if(d=t,"rectangle"==i.unit){for(var r=[],s=e.options.tabSize,u=q(nt(o,n.line).text,n.ch,s),h=q(nt(o,t.line).text,t.ch,s),f=Math.min(u,h),p=Math.max(u,h),g=Math.min(n.line,t.line),m=Math.min(e.lastLine(),Math.max(n.line,t.line));g<=m;g++){var v=nt(o,g).text,y=J(v,f,s);f==p?r.push(new uo(ut(g,y),ut(g,y))):v.length>y&&r.push(new uo(ut(g,y),ut(g,J(v,p,s))))}r.length||r.push(new uo(n,n)),Ko(o,ho(e,c.ranges.slice(0,l).concat(r),l),{origin:"*mouse",scroll:!1}),e.scrollIntoView(t)}else{var b,_=a,w=Aa(e,t,i.unit),x=_.anchor;ht(w.anchor,x)>0?(b=w.head,x=gt(_.from(),w.anchor)):(b=w.anchor,x=pt(_.to(),w.head));var k=c.ranges.slice(0);k[l]=Oa(e,new uo(vt(o,x),b)),Ko(o,ho(e,k,l),X)}}var p=r.wrapper.getBoundingClientRect(),g=0;function m(t){var n=++g,s=Ri(e,t,!0,"rectangle"==i.unit);if(s)if(0!=ht(s,d)){e.curOp.focus=D(F(e)),f(s);var a=sr(r,o);(s.line>=a.to||s.line<a.from)&&setTimeout(Ir(e,(function(){g==n&&m(t)})),150)}else{var l=t.clientY<p.top?-20:t.clientY>p.bottom?20:0;l&&setTimeout(Ir(e,(function(){g==n&&(r.scroller.scrollTop+=l,m(t))})),50)}}function v(t){e.state.selectingText=!1,g=1/0,t&&(Te(t),r.input.focus()),we(r.wrapper.ownerDocument,"mousemove",y),we(r.wrapper.ownerDocument,"mouseup",b),o.history.lastSelOrigin=null}var y=Ir(e,(function(e){0!==e.buttons&&Ne(e)?m(e):v(e)})),b=Ir(e,v);e.state.selectingText=b,be(r.wrapper.ownerDocument,"mousemove",y),be(r.wrapper.ownerDocument,"mouseup",b)}function Oa(e,t){var n=t.anchor,i=t.head,r=nt(e.doc,n.line);if(0==ht(n,i)&&n.sticky==i.sticky)return t;var o=ve(r);if(!o)return t;var s=ge(o,n.ch,n.sticky),a=o[s];if(a.from!=n.ch&&a.to!=n.ch)return t;var l,c=s+(a.from==n.ch==(1!=a.level)?0:1);if(0==c||c==o.length)return t;if(i.line!=n.line)l=(i.line-n.line)*("ltr"==e.doc.direction?1:-1)>0;else{var u=ge(o,i.ch,i.sticky),h=u-s||(i.ch-n.ch)*(1==a.level?-1:1);l=u==c-1||u==c?h<0:h>0}var d=o[c+(l?-1:0)],f=l==(1==d.level),p=f?d.from:d.to,g=f?"after":"before";return n.ch==p&&n.sticky==g?t:new uo(new ut(n.line,p,g),i)}function Ea(e,t,n,i){var r,o;if(t.touches)r=t.touches[0].clientX,o=t.touches[0].clientY;else try{r=t.clientX,o=t.clientY}catch(e){return!1}if(r>=Math.floor(e.display.gutters.getBoundingClientRect().right))return!1;i&&Te(t);var s=e.display,a=s.lineDiv.getBoundingClientRect();if(o>a.bottom||!Se(e,n))return Me(t);o-=a.top-s.viewOffset;for(var l=0;l<e.display.gutterSpecs.length;++l){var c=s.gutters.childNodes[l];if(c&&c.getBoundingClientRect().right>=r)return xe(e,n,e,at(e.doc,o),e.display.gutterSpecs[l].className,t),Me(t)}}function Na(e,t){return Ea(e,t,"gutterClick",!0)}function Da(e,t){Xn(e.display,t)||Pa(e,t)||ke(e,t,"contextmenu")||C||e.display.input.onContextMenu(t)}function Pa(e,t){return!!Se(e,"gutterContextMenu")&&Ea(e,t,"gutterContextMenu",!1)}function Ha(e){e.display.wrapper.className=e.display.wrapper.className.replace(/\s*cm-s-\S+/g,"")+e.options.theme.replace(/(^|\s)\s*/g," cm-s-"),vi(e)}wa.prototype.compare=function(e,t,n){return this.time+_a>e&&0==ht(t,this.pos)&&n==this.button};var Wa={toString:function(){return"CodeMirror.Init"}},Ia={},Fa={};function Ba(e){var t=e.optionHandlers;function n(n,i,r,o){e.defaults[n]=i,r&&(t[n]=o?function(e,t,n){n!=Wa&&r(e,t,n)}:r)}e.defineOption=n,e.Init=Wa,n("value","",(function(e,t){return e.setValue(t)}),!0),n("mode",null,(function(e,t){e.doc.modeOption=t,bo(e)}),!0),n("indentUnit",2,bo,!0),n("indentWithTabs",!1),n("smartIndent",!0),n("tabSize",4,(function(e){_o(e),vi(e),ji(e)}),!0),n("lineSeparator",null,(function(e,t){if(e.doc.lineSep=t,t){var n=[],i=e.doc.first;e.doc.iter((function(e){for(var r=0;;){var o=e.text.indexOf(t,r);if(-1==o)break;r=o+t.length,n.push(ut(i,o))}i++}));for(var r=n.length-1;r>=0;r--)cs(e.doc,t,n[r],ut(n[r].line,n[r].ch+t.length))}})),n("specialChars",/[\u0000-\u001f\u007f-\u009f\u00ad\u061c\u200b\u200e\u200f\u2028\u2029\u202d\u202e\u2066\u2067\u2069\ufeff\ufff9-\ufffc]/g,(function(e,t,n){e.state.specialChars=new RegExp(t.source+(t.test("\t")?"":"|\t"),"g"),n!=Wa&&e.refresh()})),n("specialCharPlaceholder",wn,(function(e){return e.refresh()}),!0),n("electricChars",!0),n("inputStyle",y?"contenteditable":"textarea",(function(){throw new Error("inputStyle can not (yet) be changed in a running editor")}),!0),n("spellcheck",!1,(function(e,t){return e.getInputField().spellcheck=t}),!0),n("autocorrect",!1,(function(e,t){return e.getInputField().autocorrect=t}),!0),n("autocapitalize",!1,(function(e,t){return e.getInputField().autocapitalize=t}),!0),n("rtlMoveVisually",!w),n("wholeLineUpdateBefore",!0),n("theme","default",(function(e){Ha(e),no(e)}),!0),n("keyMap","default",(function(e,t,n){var i=Ys(t),r=n!=Wa&&Ys(n);r&&r.detach&&r.detach(e,i),i.attach&&i.attach(e,r||null)})),n("extraKeys",null),n("configureMouse",null),n("lineWrapping",!1,za,!0),n("gutters",[],(function(e,t){e.display.gutterSpecs=eo(t,e.options.lineNumbers),no(e)}),!0),n("fixedGutter",!0,(function(e,t){e.display.gutters.style.left=t?Ii(e.display)+"px":"0",e.refresh()}),!0),n("coverGutterNextToScrollbar",!1,(function(e){return kr(e)}),!0),n("scrollbarStyle","native",(function(e){Lr(e),kr(e),e.display.scrollbars.setScrollTop(e.doc.scrollTop),e.display.scrollbars.setScrollLeft(e.doc.scrollLeft)}),!0),n("lineNumbers",!1,(function(e,t){e.display.gutterSpecs=eo(e.options.gutters,t),no(e)}),!0),n("firstLineNumber",1,no,!0),n("lineNumberFormatter",(function(e){return e}),no,!0),n("showCursorWhenSelecting",!1,Gi,!0),n("resetSelectionOnContextMenu",!0),n("lineWiseCopyCut",!0),n("pasteLinesPerSelection",!0),n("selectionsMayTouch",!1),n("readOnly",!1,(function(e,t){"nocursor"==t&&(ir(e),e.display.input.blur()),e.display.input.readOnlyChanged(t)})),n("screenReaderLabel",null,(function(e,t){t=""===t?null:t,e.display.input.screenReaderLabelChanged(t)})),n("disableInput",!1,(function(e,t){t||e.display.input.reset()}),!0),n("dragDrop",!0,Ra),n("allowDropFileTypes",null),n("cursorBlinkRate",530),n("cursorScrollMargin",0),n("cursorHeight",1,Gi,!0),n("singleCursorHeightPerLine",!0,Gi,!0),n("workTime",100),n("workDelay",100),n("flattenSpans",!0,_o,!0),n("addModeClass",!1,_o,!0),n("pollInterval",100),n("undoDepth",200,(function(e,t){return e.doc.history.undoDepth=t})),n("historyEventDelay",1250),n("viewportMargin",10,(function(e){return e.refresh()}),!0),n("maxHighlightLength",1e4,_o,!0),n("moveInputWithCursor",!0,(function(e,t){t||e.display.input.resetPosition()})),n("tabindex",null,(function(e,t){return e.display.input.getField().tabIndex=t||""})),n("autofocus",null),n("direction","ltr",(function(e,t){return e.doc.setDirection(t)}),!0),n("phrases",null)}function Ra(e,t,n){if(!t!=!(n&&n!=Wa)){var i=e.display.dragFunctions,r=t?be:we;r(e.display.scroller,"dragstart",i.start),r(e.display.scroller,"dragenter",i.enter),r(e.display.scroller,"dragover",i.over),r(e.display.scroller,"dragleave",i.leave),r(e.display.scroller,"drop",i.drop)}}function za(e){e.options.lineWrapping?(P(e.display.wrapper,"CodeMirror-wrap"),e.display.sizer.style.minWidth="",e.display.sizerWidth=null):(T(e.display.wrapper,"CodeMirror-wrap"),fn(e)),Bi(e),ji(e),vi(e),setTimeout((function(){return kr(e)}),100)}function ja(e,t){var n=this;if(!(this instanceof ja))return new ja(e,t);this.options=t=t?j(t):{},j(Ia,t,!1);var i=t.value;"string"==typeof i?i=new As(i,t.mode,null,t.lineSeparator,t.direction):t.mode&&(i.modeOption=t.mode),this.doc=i;var r=new ja.inputStyles[t.inputStyle](this),o=this.display=new io(e,i,r,t);for(var c in o.wrapper.CodeMirror=this,Ha(this),t.lineWrapping&&(this.display.wrapper.className+=" CodeMirror-wrap"),Lr(this),this.state={keyMaps:[],overlays:[],modeGen:0,overwrite:!1,delayingBlurEvent:!1,focused:!1,suppressEdits:!1,pasteIncoming:-1,cutIncoming:-1,selectingText:!1,draggingText:!1,highlight:new U,keySeq:null,specialChars:null},t.autofocus&&!y&&o.input.focus(),s&&a<11&&setTimeout((function(){return n.display.input.reset(!0)}),20),qa(this),Ws(),Ar(this),this.curOp.forceUpdate=!0,Co(this,i),t.autofocus&&!y||this.hasFocus()?setTimeout((function(){n.hasFocus()&&!n.state.focused&&nr(n)}),20):ir(this),Fa)Fa.hasOwnProperty(c)&&Fa[c](this,t[c],Wa);Qr(this),t.finishInit&&t.finishInit(this);for(var u=0;u<Ua.length;++u)Ua[u](this);Mr(this),l&&t.lineWrapping&&"optimizelegibility"==getComputedStyle(o.lineDiv).textRendering&&(o.lineDiv.style.textRendering="auto")}function qa(e){var t=e.display;be(t.scroller,"mousedown",Ir(e,ka)),be(t.scroller,"dblclick",s&&a<11?Ir(e,(function(t){if(!ke(e,t)){var n=Ri(e,t);if(n&&!Na(e,t)&&!Xn(e.display,t)){Te(t);var i=e.findWordAt(n);zo(e.doc,i.anchor,i.head)}}})):function(t){return ke(e,t)||Te(t)}),be(t.scroller,"contextmenu",(function(t){return Da(e,t)})),be(t.input.getField(),"contextmenu",(function(n){t.scroller.contains(n.target)||Da(e,n)}));var n,i={end:0};function r(){t.activeTouch&&(n=setTimeout((function(){return t.activeTouch=null}),1e3),(i=t.activeTouch).end=+new Date)}function o(e){if(1!=e.touches.length)return!1;var t=e.touches[0];return t.radiusX<=1&&t.radiusY<=1}function l(e,t){if(null==t.left)return!0;var n=t.left-e.left,i=t.top-e.top;return n*n+i*i>400}be(t.scroller,"touchstart",(function(r){if(!ke(e,r)&&!o(r)&&!Na(e,r)){t.input.ensurePolled(),clearTimeout(n);var s=+new Date;t.activeTouch={start:s,moved:!1,prev:s-i.end<=300?i:null},1==r.touches.length&&(t.activeTouch.left=r.touches[0].pageX,t.activeTouch.top=r.touches[0].pageY)}})),be(t.scroller,"touchmove",(function(){t.activeTouch&&(t.activeTouch.moved=!0)})),be(t.scroller,"touchend",(function(n){var i=t.activeTouch;if(i&&!Xn(t,n)&&null!=i.left&&!i.moved&&new Date-i.start<300){var o,s=e.coordsChar(t.activeTouch,"page");o=!i.prev||l(i,i.prev)?new uo(s,s):!i.prev.prev||l(i,i.prev.prev)?e.findWordAt(s):new uo(ut(s.line,0),vt(e.doc,ut(s.line+1,0))),e.setSelection(o.anchor,o.head),e.focus(),Te(n)}r()})),be(t.scroller,"touchcancel",r),be(t.scroller,"scroll",(function(){t.scroller.clientHeight&&(vr(e,t.scroller.scrollTop),br(e,t.scroller.scrollLeft,!0),xe(e,"scroll",e))})),be(t.scroller,"mousewheel",(function(t){return lo(e,t)})),be(t.scroller,"DOMMouseScroll",(function(t){return lo(e,t)})),be(t.wrapper,"scroll",(function(){return t.wrapper.scrollTop=t.wrapper.scrollLeft=0})),t.dragFunctions={enter:function(t){ke(e,t)||Oe(t)},over:function(t){ke(e,t)||(Ns(e,t),Oe(t))},start:function(t){return Es(e,t)},drop:Ir(e,Os),leave:function(t){ke(e,t)||Ds(e)}};var c=t.input.getField();be(c,"keyup",(function(t){return ma.call(e,t)})),be(c,"keydown",Ir(e,pa)),be(c,"keypress",Ir(e,va)),be(c,"focus",(function(t){return nr(e,t)})),be(c,"blur",(function(t){return ir(e,t)}))}ja.defaults=Ia,ja.optionHandlers=Fa;var Ua=[];function $a(e,t,n,i){var r,o=e.doc;null==n&&(n="add"),"smart"==n&&(o.mode.indent?r=Ct(e,t).state:n="prev");var s=e.options.tabSize,a=nt(o,t),l=q(a.text,null,s);a.stateAfter&&(a.stateAfter=null);var c,u=a.text.match(/^\s*/)[0];if(i||/\S/.test(a.text)){if("smart"==n&&((c=o.mode.indent(r,a.text.slice(u.length),a.text))==K||c>150)){if(!i)return;n="prev"}}else c=0,n="not";"prev"==n?c=t>o.first?q(nt(o,t-1).text,null,s):0:"add"==n?c=l+e.options.indentUnit:"subtract"==n?c=l-e.options.indentUnit:"number"==typeof n&&(c=l+n),c=Math.max(0,c);var h="",d=0;if(e.options.indentWithTabs)for(var f=Math.floor(c/s);f;--f)d+=s,h+="\t";if(d<c&&(h+=Q(c-d)),h!=u)return cs(o,h,ut(t,0),ut(t,u.length),"+input"),a.stateAfter=null,!0;for(var p=0;p<o.sel.ranges.length;p++){var g=o.sel.ranges[p];if(g.head.line==t&&g.head.ch<u.length){var m=ut(t,u.length);qo(o,p,new uo(m,m));break}}}ja.defineInitHook=function(e){return Ua.push(e)};var Va=null;function Ka(e){Va=e}function Ga(e,t,n,i,r){var o=e.doc;e.display.shift=!1,i||(i=o.sel);var s=+new Date-200,a="paste"==r||e.state.pasteIncoming>s,l=Be(t),c=null;if(a&&i.ranges.length>1)if(Va&&Va.text.join("\n")==t){if(i.ranges.length%Va.text.length==0){c=[];for(var u=0;u<Va.text.length;u++)c.push(o.splitLines(Va.text[u]))}}else l.length==i.ranges.length&&e.options.pasteLinesPerSelection&&(c=te(l,(function(e){return[e]})));for(var h=e.curOp.updateInput,d=i.ranges.length-1;d>=0;d--){var f=i.ranges[d],p=f.from(),g=f.to();f.empty()&&(n&&n>0?p=ut(p.line,p.ch-n):e.state.overwrite&&!a?g=ut(g.line,Math.min(nt(o,g.line).text.length,g.ch+ee(l).length)):a&&Va&&Va.lineWise&&Va.text.join("\n")==l.join("\n")&&(p=g=ut(p.line,0)));var m={from:p,to:g,text:c?c[d%c.length]:l,origin:r||(a?"paste":e.state.cutIncoming>s?"cut":"+input")};is(e.doc,m),Pn(e,"inputRead",e,m)}t&&!a&&Ya(e,t),dr(e),e.curOp.updateInput<2&&(e.curOp.updateInput=h),e.curOp.typing=!0,e.state.pasteIncoming=e.state.cutIncoming=-1}function Xa(e,t){var n=e.clipboardData&&e.clipboardData.getData("Text");if(n)return e.preventDefault(),t.isReadOnly()||t.options.disableInput||!t.hasFocus()||Wr(t,(function(){return Ga(t,n,0,null,"paste")})),!0}function Ya(e,t){if(e.options.electricChars&&e.options.smartIndent)for(var n=e.doc.sel,i=n.ranges.length-1;i>=0;i--){var r=n.ranges[i];if(!(r.head.ch>100||i&&n.ranges[i-1].head.line==r.head.line)){var o=e.getModeAt(r.head),s=!1;if(o.electricChars){for(var a=0;a<o.electricChars.length;a++)if(t.indexOf(o.electricChars.charAt(a))>-1){s=$a(e,r.head.line,"smart");break}}else o.electricInput&&o.electricInput.test(nt(e.doc,r.head.line).text.slice(0,r.head.ch))&&(s=$a(e,r.head.line,"smart"));s&&Pn(e,"electricInput",e,r.head.line)}}}function Ja(e){for(var t=[],n=[],i=0;i<e.doc.sel.ranges.length;i++){var r=e.doc.sel.ranges[i].head.line,o={anchor:ut(r,0),head:ut(r+1,0)};n.push(o),t.push(e.getRange(o.anchor,o.head))}return{text:t,ranges:n}}function Za(e,t,n,i){e.setAttribute("autocorrect",n?"on":"off"),e.setAttribute("autocapitalize",i?"on":"off"),e.setAttribute("spellcheck",!!t)}function Qa(){var e=O("textarea",null,null,"position: absolute; bottom: -1em; padding: 0; width: 1px; height: 1em; min-height: 1em; outline: none"),t=O("div",[e],null,"overflow: hidden; position: relative; width: 3px; height: 0px;");return l?e.style.width="1000px":e.setAttribute("wrap","off"),m&&(e.style.border="1px solid black"),t}function el(e){var t=e.optionHandlers,n=e.helpers={};e.prototype={constructor:e,focus:function(){R(this).focus(),this.display.input.focus()},setOption:function(e,n){var i=this.options,r=i[e];i[e]==n&&"mode"!=e||(i[e]=n,t.hasOwnProperty(e)&&Ir(this,t[e])(this,n,r),xe(this,"optionChange",this,e))},getOption:function(e){return this.options[e]},getDoc:function(){return this.doc},addKeyMap:function(e,t){this.state.keyMaps[t?"push":"unshift"](Ys(e))},removeKeyMap:function(e){for(var t=this.state.keyMaps,n=0;n<t.length;++n)if(t[n]==e||t[n].name==e)return t.splice(n,1),!0},addOverlay:Fr((function(t,n){var i=t.token?t:e.getMode(this.options,t);if(i.startState)throw new Error("Overlays may not be stateful.");ne(this.state.overlays,{mode:i,modeSpec:t,opaque:n&&n.opaque,priority:n&&n.priority||0},(function(e){return e.priority})),this.state.modeGen++,ji(this)})),removeOverlay:Fr((function(e){for(var t=this.state.overlays,n=0;n<t.length;++n){var i=t[n].modeSpec;if(i==e||"string"==typeof e&&i.name==e)return t.splice(n,1),this.state.modeGen++,void ji(this)}})),indentLine:Fr((function(e,t,n){"string"!=typeof t&&"number"!=typeof t&&(t=null==t?this.options.smartIndent?"smart":"prev":t?"add":"subtract"),lt(this.doc,e)&&$a(this,e,t,n)})),indentSelection:Fr((function(e){for(var t=this.doc.sel.ranges,n=-1,i=0;i<t.length;i++){var r=t[i];if(r.empty())r.head.line>n&&($a(this,r.head.line,e,!0),n=r.head.line,i==this.doc.sel.primIndex&&dr(this));else{var o=r.from(),s=r.to(),a=Math.max(n,o.line);n=Math.min(this.lastLine(),s.line-(s.ch?0:1))+1;for(var l=a;l<n;++l)$a(this,l,e);var c=this.doc.sel.ranges;0==o.ch&&t.length==c.length&&c[i].from().ch>0&&qo(this.doc,i,new uo(o,c[i].to()),G)}}})),getTokenAt:function(e,t){return Mt(this,e,t)},getLineTokens:function(e,t){return Mt(this,ut(e),t,!0)},getTokenTypeAt:function(e){e=vt(this.doc,e);var t,n=kt(this,nt(this.doc,e.line)),i=0,r=(n.length-1)/2,o=e.ch;if(0==o)t=n[2];else for(;;){var s=i+r>>1;if((s?n[2*s-1]:0)>=o)r=s;else{if(!(n[2*s+1]<o)){t=n[2*s+2];break}i=s+1}}var a=t?t.indexOf("overlay "):-1;return a<0?t:0==a?null:t.slice(0,a-1)},getModeAt:function(t){var n=this.doc.mode;return n.innerMode?e.innerMode(n,this.getTokenAt(t).state).mode:n},getHelper:function(e,t){return this.getHelpers(e,t)[0]},getHelpers:function(e,t){var i=[];if(!n.hasOwnProperty(t))return i;var r=n[t],o=this.getModeAt(e);if("string"==typeof o[t])r[o[t]]&&i.push(r[o[t]]);else if(o[t])for(var s=0;s<o[t].length;s++){var a=r[o[t][s]];a&&i.push(a)}else o.helperType&&r[o.helperType]?i.push(r[o.helperType]):r[o.name]&&i.push(r[o.name]);for(var l=0;l<r._global.length;l++){var c=r._global[l];c.pred(o,this)&&-1==$(i,c.val)&&i.push(c.val)}return i},getStateAfter:function(e,t){var n=this.doc;return Ct(this,(e=mt(n,null==e?n.first+n.size-1:e))+1,t).state},cursorCoords:function(e,t){var n=this.doc.sel.primary();return Ci(this,null==e?n.head:"object"==typeof e?vt(this.doc,e):e?n.from():n.to(),t||"page")},charCoords:function(e,t){return ki(this,vt(this.doc,e),t||"page")},coordsChar:function(e,t){return Ti(this,(e=xi(this,e,t||"page")).left,e.top)},lineAtHeight:function(e,t){return e=xi(this,{top:e,left:0},t||"page").top,at(this.doc,e+this.display.viewOffset)},heightAtLine:function(e,t,n){var i,r=!1;if("number"==typeof e){var o=this.doc.first+this.doc.size-1;e<this.doc.first?e=this.doc.first:e>o&&(e=o,r=!0),i=nt(this.doc,e)}else i=e;return wi(this,i,{top:0,left:0},t||"page",n||r).top+(r?this.doc.height-hn(i):0)},defaultTextHeight:function(){return Pi(this.display)},defaultCharWidth:function(){return Hi(this.display)},getViewport:function(){return{from:this.display.viewFrom,to:this.display.viewTo}},addWidget:function(e,t,n,i,r){var o=this.display,s=(e=Ci(this,vt(this.doc,e))).bottom,a=e.left;if(t.style.position="absolute",t.setAttribute("cm-ignore-events","true"),this.display.input.setUneditable(t),o.sizer.appendChild(t),"over"==i)s=e.top;else if("above"==i||"near"==i){var l=Math.max(o.wrapper.clientHeight,this.doc.height),c=Math.max(o.sizer.clientWidth,o.lineSpace.clientWidth);("above"==i||e.bottom+t.offsetHeight>l)&&e.top>t.offsetHeight?s=e.top-t.offsetHeight:e.bottom+t.offsetHeight<=l&&(s=e.bottom),a+t.offsetWidth>c&&(a=c-t.offsetWidth)}t.style.top=s+"px",t.style.left=t.style.right="","right"==r?(a=o.sizer.clientWidth-t.offsetWidth,t.style.right="0px"):("left"==r?a=0:"middle"==r&&(a=(o.sizer.clientWidth-t.offsetWidth)/2),t.style.left=a+"px"),n&&cr(this,{left:a,top:s,right:a+t.offsetWidth,bottom:s+t.offsetHeight})},triggerOnKeyDown:Fr(pa),triggerOnKeyPress:Fr(va),triggerOnKeyUp:ma,triggerOnMouseDown:Fr(ka),execCommand:function(e){if(na.hasOwnProperty(e))return na[e].call(null,this)},triggerElectric:Fr((function(e){Ya(this,e)})),findPosH:function(e,t,n,i){var r=1;t<0&&(r=-1,t=-t);for(var o=vt(this.doc,e),s=0;s<t&&!(o=tl(this.doc,o,r,n,i)).hitSide;++s);return o},moveH:Fr((function(e,t){var n=this;this.extendSelectionsBy((function(i){return n.display.shift||n.doc.extend||i.empty()?tl(n.doc,i.head,e,t,n.options.rtlMoveVisually):e<0?i.from():i.to()}),Y)})),deleteH:Fr((function(e,t){var n=this.doc.sel,i=this.doc;n.somethingSelected()?i.replaceSelection("",null,"+delete"):Js(this,(function(n){var r=tl(i,n.head,e,t,!1);return e<0?{from:r,to:n.head}:{from:n.head,to:r}}))})),findPosV:function(e,t,n,i){var r=1,o=i;t<0&&(r=-1,t=-t);for(var s=vt(this.doc,e),a=0;a<t;++a){var l=Ci(this,s,"div");if(null==o?o=l.left:l.left=o,(s=nl(this,l,r,n)).hitSide)break}return s},moveV:Fr((function(e,t){var n=this,i=this.doc,r=[],o=!this.display.shift&&!i.extend&&i.sel.somethingSelected();if(i.extendSelectionsBy((function(s){if(o)return e<0?s.from():s.to();var a=Ci(n,s.head,"div");null!=s.goalColumn&&(a.left=s.goalColumn),r.push(a.left);var l=nl(n,a,e,t);return"page"==t&&s==i.sel.primary()&&hr(n,ki(n,l,"div").top-a.top),l}),Y),r.length)for(var s=0;s<i.sel.ranges.length;s++)i.sel.ranges[s].goalColumn=r[s]})),findWordAt:function(e){var t=nt(this.doc,e.line).text,n=e.ch,i=e.ch;if(t){var r=this.getHelper(e,"wordChars");"before"!=e.sticky&&i!=t.length||!n?++i:--n;for(var o=t.charAt(n),s=ae(o,r)?function(e){return ae(e,r)}:/\s/.test(o)?function(e){return/\s/.test(e)}:function(e){return!/\s/.test(e)&&!ae(e)};n>0&&s(t.charAt(n-1));)--n;for(;i<t.length&&s(t.charAt(i));)++i}return new uo(ut(e.line,n),ut(e.line,i))},toggleOverwrite:function(e){null!=e&&e==this.state.overwrite||((this.state.overwrite=!this.state.overwrite)?P(this.display.cursorDiv,"CodeMirror-overwrite"):T(this.display.cursorDiv,"CodeMirror-overwrite"),xe(this,"overwriteToggle",this,this.state.overwrite))},hasFocus:function(){return this.display.input.getField()==D(F(this))},isReadOnly:function(){return!(!this.options.readOnly&&!this.doc.cantEdit)},scrollTo:Fr((function(e,t){fr(this,e,t)})),getScrollInfo:function(){var e=this.display.scroller;return{left:e.scrollLeft,top:e.scrollTop,height:e.scrollHeight-Qn(this)-this.display.barHeight,width:e.scrollWidth-Qn(this)-this.display.barWidth,clientHeight:ti(this),clientWidth:ei(this)}},scrollIntoView:Fr((function(e,t){null==e?(e={from:this.doc.sel.primary().head,to:null},null==t&&(t=this.options.cursorScrollMargin)):"number"==typeof e?e={from:ut(e,0),to:null}:null==e.from&&(e={from:e,to:null}),e.to||(e.to=e.from),e.margin=t||0,null!=e.from.line?pr(this,e):mr(this,e.from,e.to,e.margin)})),setSize:Fr((function(e,t){var n=this,i=function(e){return"number"==typeof e||/^\d+$/.test(String(e))?e+"px":e};null!=e&&(this.display.wrapper.style.width=i(e)),null!=t&&(this.display.wrapper.style.height=i(t)),this.options.lineWrapping&&mi(this);var r=this.display.viewFrom;this.doc.iter(r,this.display.viewTo,(function(e){if(e.widgets)for(var t=0;t<e.widgets.length;t++)if(e.widgets[t].noHScroll){qi(n,r,"widget");break}++r})),this.curOp.forceUpdate=!0,xe(this,"refresh",this)})),operation:function(e){return Wr(this,e)},startOperation:function(){return Ar(this)},endOperation:function(){return Mr(this)},refresh:Fr((function(){var e=this.display.cachedTextHeight;ji(this),this.curOp.forceUpdate=!0,vi(this),fr(this,this.doc.scrollLeft,this.doc.scrollTop),Yr(this.display),(null==e||Math.abs(e-Pi(this.display))>.5||this.options.lineWrapping)&&Bi(this),xe(this,"refresh",this)})),swapDoc:Fr((function(e){var t=this.doc;return t.cm=null,this.state.selectingText&&this.state.selectingText(),Co(this,e),vi(this),this.display.input.reset(),fr(this,e.scrollLeft,e.scrollTop),this.curOp.forceScroll=!0,Pn(this,"swapDoc",this,t),t})),phrase:function(e){var t=this.options.phrases;return t&&Object.prototype.hasOwnProperty.call(t,e)?t[e]:e},getInputField:function(){return this.display.input.getField()},getWrapperElement:function(){return this.display.wrapper},getScrollerElement:function(){return this.display.scroller},getGutterElement:function(){return this.display.gutters}},Le(e),e.registerHelper=function(t,i,r){n.hasOwnProperty(t)||(n[t]=e[t]={_global:[]}),n[t][i]=r},e.registerGlobalHelper=function(t,i,r,o){e.registerHelper(t,i,o),n[t]._global.push({pred:r,val:o})}}function tl(e,t,n,i,r){var o=t,s=n,a=nt(e,t.line),l=r&&"rtl"==e.direction?-n:n;function c(){var n=t.line+l;return!(n<e.first||n>=e.first+e.size)&&(t=new ut(n,t.ch,t.sticky),a=nt(e,n))}function u(o){var s;if("codepoint"==i){var u=a.text.charCodeAt(t.ch+(n>0?0:-1));if(isNaN(u))s=null;else{var h=n>0?u>=55296&&u<56320:u>=56320&&u<57343;s=new ut(t.line,Math.max(0,Math.min(a.text.length,t.ch+n*(h?2:1))),-n)}}else s=r?ta(e.cm,a,t,n):Qs(a,t,n);if(null==s){if(o||!c())return!1;t=ea(r,e.cm,a,t.line,l)}else t=s;return!0}if("char"==i||"codepoint"==i)u();else if("column"==i)u(!0);else if("word"==i||"group"==i)for(var h=null,d="group"==i,f=e.cm&&e.cm.getHelper(t,"wordChars"),p=!0;!(n<0)||u(!p);p=!1){var g=a.text.charAt(t.ch)||"\n",m=ae(g,f)?"w":d&&"\n"==g?"n":!d||/\s/.test(g)?null:"p";if(!d||p||m||(m="s"),h&&h!=m){n<0&&(n=1,u(),t.sticky="after");break}if(m&&(h=m),n>0&&!u(!p))break}var v=Qo(e,t,o,s,!0);return dt(o,v)&&(v.hitSide=!0),v}function nl(e,t,n,i){var r,o,s=e.doc,a=t.left;if("page"==i){var l=Math.min(e.display.wrapper.clientHeight,R(e).innerHeight||s(e).documentElement.clientHeight),c=Math.max(l-.5*Pi(e.display),3);r=(n>0?t.bottom:t.top)+n*c}else"line"==i&&(r=n>0?t.bottom+3:t.top-3);for(;(o=Ti(e,a,r)).outside;){if(n<0?r<=0:r>=s.height){o.hitSide=!0;break}r+=5*n}return o}var il=function(e){this.cm=e,this.lastAnchorNode=this.lastAnchorOffset=this.lastFocusNode=this.lastFocusOffset=null,this.polling=new U,this.composing=null,this.gracePeriod=!1,this.readDOMTimeout=null};function rl(e,t){var n=si(e,t.line);if(!n||n.hidden)return null;var i=nt(e.doc,t.line),r=ii(n,i,t.line),o=ve(i,e.doc.direction),s="left";o&&(s=ge(o,t.ch)%2?"right":"left");var a=hi(r.map,t.ch,s);return a.offset="right"==a.collapse?a.end:a.start,a}function ol(e){for(var t=e;t;t=t.parentNode)if(/CodeMirror-gutter-wrapper/.test(t.className))return!0;return!1}function sl(e,t){return t&&(e.bad=!0),e}function al(e,t,n,i,r){var o="",s=!1,a=e.doc.lineSeparator(),l=!1;function c(e){return function(t){return t.id==e}}function u(){s&&(o+=a,l&&(o+=a),s=l=!1)}function h(e){e&&(u(),o+=e)}function d(t){if(1==t.nodeType){var n=t.getAttribute("cm-text");if(n)return void h(n);var o,f=t.getAttribute("cm-marker");if(f){var p=e.findMarks(ut(i,0),ut(r+1,0),c(+f));return void(p.length&&(o=p[0].find(0))&&h(it(e.doc,o.from,o.to).join(a)))}if("false"==t.getAttribute("contenteditable"))return;var g=/^(pre|div|p|li|table|br)$/i.test(t.nodeName);if(!/^br$/i.test(t.nodeName)&&0==t.textContent.length)return;g&&u();for(var m=0;m<t.childNodes.length;m++)d(t.childNodes[m]);/^(pre|p)$/i.test(t.nodeName)&&(l=!0),g&&(s=!0)}else 3==t.nodeType&&h(t.nodeValue.replace(/\u200b/g,"").replace(/\u00a0/g," "))}for(;d(t),t!=n;)t=t.nextSibling,l=!1;return o}function ll(e,t,n){var i;if(t==e.display.lineDiv){if(!(i=e.display.lineDiv.childNodes[n]))return sl(e.clipPos(ut(e.display.viewTo-1)),!0);t=null,n=0}else for(i=t;;i=i.parentNode){if(!i||i==e.display.lineDiv)return null;if(i.parentNode&&i.parentNode==e.display.lineDiv)break}for(var r=0;r<e.display.view.length;r++){var o=e.display.view[r];if(o.node==i)return cl(o,t,n)}}function cl(e,t,n){var i=e.text.firstChild,r=!1;if(!t||!N(i,t))return sl(ut(st(e.line),0),!0);if(t==i&&(r=!0,t=i.childNodes[n],n=0,!t)){var o=e.rest?ee(e.rest):e.line;return sl(ut(st(o),o.text.length),r)}var s=3==t.nodeType?t:null,a=t;for(s||1!=t.childNodes.length||3!=t.firstChild.nodeType||(s=t.firstChild,n&&(n=s.nodeValue.length));a.parentNode!=i;)a=a.parentNode;var l=e.measure,c=l.maps;function u(t,n,i){for(var r=-1;r<(c?c.length:0);r++)for(var o=r<0?l.map:c[r],s=0;s<o.length;s+=3){var a=o[s+2];if(a==t||a==n){var u=st(r<0?e.line:e.rest[r]),h=o[s]+i;return(i<0||a!=t)&&(h=o[s+(i?1:0)]),ut(u,h)}}}var h=u(s,a,n);if(h)return sl(h,r);for(var d=a.nextSibling,f=s?s.nodeValue.length-n:0;d;d=d.nextSibling){if(h=u(d,d.firstChild,0))return sl(ut(h.line,h.ch-f),r);f+=d.textContent.length}for(var p=a.previousSibling,g=n;p;p=p.previousSibling){if(h=u(p,p.firstChild,-1))return sl(ut(h.line,h.ch+g),r);g+=p.textContent.length}}il.prototype.init=function(e){var t=this,n=this,i=n.cm,r=n.div=e.lineDiv;function o(e){for(var t=e.target;t;t=t.parentNode){if(t==r)return!0;if(/\bCodeMirror-(?:line)?widget\b/.test(t.className))break}return!1}function s(e){if(o(e)&&!ke(i,e)){if(i.somethingSelected())Ka({lineWise:!1,text:i.getSelections()}),"cut"==e.type&&i.replaceSelection("",null,"cut");else{if(!i.options.lineWiseCopyCut)return;var t=Ja(i);Ka({lineWise:!0,text:t.text}),"cut"==e.type&&i.operation((function(){i.setSelections(t.ranges,0,G),i.replaceSelection("",null,"cut")}))}if(e.clipboardData){e.clipboardData.clearData();var s=Va.text.join("\n");if(e.clipboardData.setData("Text",s),e.clipboardData.getData("Text")==s)return void e.preventDefault()}var a=Qa(),l=a.firstChild;Za(l),i.display.lineSpace.insertBefore(a,i.display.lineSpace.firstChild),l.value=Va.text.join("\n");var c=D(B(r));W(l),setTimeout((function(){i.display.lineSpace.removeChild(a),c.focus(),c==r&&n.showPrimarySelection()}),50)}}r.contentEditable=!0,Za(r,i.options.spellcheck,i.options.autocorrect,i.options.autocapitalize),be(r,"paste",(function(e){!o(e)||ke(i,e)||Xa(e,i)||a<=11&&setTimeout(Ir(i,(function(){return t.updateFromDOM()})),20)})),be(r,"compositionstart",(function(e){t.composing={data:e.data,done:!1}})),be(r,"compositionupdate",(function(e){t.composing||(t.composing={data:e.data,done:!1})})),be(r,"compositionend",(function(e){t.composing&&(e.data!=t.composing.data&&t.readFromDOMSoon(),t.composing.done=!0)})),be(r,"touchstart",(function(){return n.forceCompositionEnd()})),be(r,"input",(function(){t.composing||t.readFromDOMSoon()})),be(r,"copy",s),be(r,"cut",s)},il.prototype.screenReaderLabelChanged=function(e){e?this.div.setAttribute("aria-label",e):this.div.removeAttribute("aria-label")},il.prototype.prepareSelection=function(){var e=Xi(this.cm,!1);return e.focus=D(B(this.div))==this.div,e},il.prototype.showSelection=function(e,t){e&&this.cm.display.view.length&&((e.focus||t)&&this.showPrimarySelection(),this.showMultipleSelections(e))},il.prototype.getSelection=function(){return this.cm.display.wrapper.ownerDocument.getSelection()},il.prototype.showPrimarySelection=function(){var e=this.getSelection(),t=this.cm,i=t.doc.sel.primary(),r=i.from(),o=i.to();if(t.display.viewTo==t.display.viewFrom||r.line>=t.display.viewTo||o.line<t.display.viewFrom)e.removeAllRanges();else{var s=ll(t,e.anchorNode,e.anchorOffset),a=ll(t,e.focusNode,e.focusOffset);if(!s||s.bad||!a||a.bad||0!=ht(gt(s,a),r)||0!=ht(pt(s,a),o)){var l=t.display.view,c=r.line>=t.display.viewFrom&&rl(t,r)||{node:l[0].measure.map[2],offset:0},u=o.line<t.display.viewTo&&rl(t,o);if(!u){var h=l[l.length-1].measure,d=h.maps?h.maps[h.maps.length-1]:h.map;u={node:d[d.length-1],offset:d[d.length-2]-d[d.length-3]}}if(c&&u){var f,p=e.rangeCount&&e.getRangeAt(0);try{f=L(c.node,c.offset,u.offset,u.node)}catch(e){}f&&(!n&&t.state.focused?(e.collapse(c.node,c.offset),f.collapsed||(e.removeAllRanges(),e.addRange(f))):(e.removeAllRanges(),e.addRange(f)),p&&null==e.anchorNode?e.addRange(p):n&&this.startGracePeriod()),this.rememberSelection()}else e.removeAllRanges()}}},il.prototype.startGracePeriod=function(){var e=this;clearTimeout(this.gracePeriod),this.gracePeriod=setTimeout((function(){e.gracePeriod=!1,e.selectionChanged()&&e.cm.operation((function(){return e.cm.curOp.selectionChanged=!0}))}),20)},il.prototype.showMultipleSelections=function(e){M(this.cm.display.cursorDiv,e.cursors),M(this.cm.display.selectionDiv,e.selection)},il.prototype.rememberSelection=function(){var e=this.getSelection();this.lastAnchorNode=e.anchorNode,this.lastAnchorOffset=e.anchorOffset,this.lastFocusNode=e.focusNode,this.lastFocusOffset=e.focusOffset},il.prototype.selectionInEditor=function(){var e=this.getSelection();if(!e.rangeCount)return!1;var t=e.getRangeAt(0).commonAncestorContainer;return N(this.div,t)},il.prototype.focus=function(){"nocursor"!=this.cm.options.readOnly&&(this.selectionInEditor()&&D(B(this.div))==this.div||this.showSelection(this.prepareSelection(),!0),this.div.focus())},il.prototype.blur=function(){this.div.blur()},il.prototype.getField=function(){return this.div},il.prototype.supportsTouch=function(){return!0},il.prototype.receivedFocus=function(){var e=this,t=this;function n(){t.cm.state.focused&&(t.pollSelection(),t.polling.set(t.cm.options.pollInterval,n))}this.selectionInEditor()?setTimeout((function(){return e.pollSelection()}),20):Wr(this.cm,(function(){return t.cm.curOp.selectionChanged=!0})),this.polling.set(this.cm.options.pollInterval,n)},il.prototype.selectionChanged=function(){var e=this.getSelection();return e.anchorNode!=this.lastAnchorNode||e.anchorOffset!=this.lastAnchorOffset||e.focusNode!=this.lastFocusNode||e.focusOffset!=this.lastFocusOffset},il.prototype.pollSelection=function(){if(null==this.readDOMTimeout&&!this.gracePeriod&&this.selectionChanged()){var e=this.getSelection(),t=this.cm;if(v&&u&&this.cm.display.gutterSpecs.length&&ol(e.anchorNode))return this.cm.triggerOnKeyDown({type:"keydown",keyCode:8,preventDefault:Math.abs}),this.blur(),void this.focus();if(!this.composing){this.rememberSelection();var n=ll(t,e.anchorNode,e.anchorOffset),i=ll(t,e.focusNode,e.focusOffset);n&&i&&Wr(t,(function(){Ko(t.doc,fo(n,i),G),(n.bad||i.bad)&&(t.curOp.selectionChanged=!0)}))}}},il.prototype.pollContent=function(){null!=this.readDOMTimeout&&(clearTimeout(this.readDOMTimeout),this.readDOMTimeout=null);var e,t,n,i=this.cm,r=i.display,o=i.doc.sel.primary(),s=o.from(),a=o.to();if(0==s.ch&&s.line>i.firstLine()&&(s=ut(s.line-1,nt(i.doc,s.line-1).length)),a.ch==nt(i.doc,a.line).text.length&&a.line<i.lastLine()&&(a=ut(a.line+1,0)),s.line<r.viewFrom||a.line>r.viewTo-1)return!1;s.line==r.viewFrom||0==(e=zi(i,s.line))?(t=st(r.view[0].line),n=r.view[0].node):(t=st(r.view[e].line),n=r.view[e-1].node.nextSibling);var l,c,u=zi(i,a.line);if(u==r.view.length-1?(l=r.viewTo-1,c=r.lineDiv.lastChild):(l=st(r.view[u+1].line)-1,c=r.view[u+1].node.previousSibling),!n)return!1;for(var h=i.doc.splitLines(al(i,n,c,t,l)),d=it(i.doc,ut(t,0),ut(l,nt(i.doc,l).text.length));h.length>1&&d.length>1;)if(ee(h)==ee(d))h.pop(),d.pop(),l--;else{if(h[0]!=d[0])break;h.shift(),d.shift(),t++}for(var f=0,p=0,g=h[0],m=d[0],v=Math.min(g.length,m.length);f<v&&g.charCodeAt(f)==m.charCodeAt(f);)++f;for(var y=ee(h),b=ee(d),_=Math.min(y.length-(1==h.length?f:0),b.length-(1==d.length?f:0));p<_&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)++p;if(1==h.length&&1==d.length&&t==s.line)for(;f&&f>s.ch&&y.charCodeAt(y.length-p-1)==b.charCodeAt(b.length-p-1);)f--,p++;h[h.length-1]=y.slice(0,y.length-p).replace(/^\u200b+/,""),h[0]=h[0].slice(f).replace(/\u200b+$/,"");var w=ut(t,f),x=ut(l,d.length?ee(d).length-p:0);return h.length>1||h[0]||ht(w,x)?(cs(i.doc,h,w,x,"+input"),!0):void 0},il.prototype.ensurePolled=function(){this.forceCompositionEnd()},il.prototype.reset=function(){this.forceCompositionEnd()},il.prototype.forceCompositionEnd=function(){this.composing&&(clearTimeout(this.readDOMTimeout),this.composing=null,this.updateFromDOM(),this.div.blur(),this.div.focus())},il.prototype.readFromDOMSoon=function(){var e=this;null==this.readDOMTimeout&&(this.readDOMTimeout=setTimeout((function(){if(e.readDOMTimeout=null,e.composing){if(!e.composing.done)return;e.composing=null}e.updateFromDOM()}),80))},il.prototype.updateFromDOM=function(){var e=this;!this.cm.isReadOnly()&&this.pollContent()||Wr(this.cm,(function(){return ji(e.cm)}))},il.prototype.setUneditable=function(e){e.contentEditable="false"},il.prototype.onKeyPress=function(e){0==e.charCode||this.composing||(e.preventDefault(),this.cm.isReadOnly()||Ir(this.cm,Ga)(this.cm,String.fromCharCode(null==e.charCode?e.keyCode:e.charCode),0))},il.prototype.readOnlyChanged=function(e){this.div.contentEditable=String("nocursor"!=e)},il.prototype.onContextMenu=function(){},il.prototype.resetPosition=function(){},il.prototype.needsContentAttribute=!0;var ul=function(e){this.cm=e,this.prevInput="",this.pollingFast=!1,this.polling=new U,this.hasSelection=!1,this.composing=null,this.resetting=!1};function hl(e,t){if((t=t?j(t):{}).value=e.value,!t.tabindex&&e.tabIndex&&(t.tabindex=e.tabIndex),!t.placeholder&&e.placeholder&&(t.placeholder=e.placeholder),null==t.autofocus){var n=D(B(e));t.autofocus=n==e||null!=e.getAttribute("autofocus")&&n==document.body}function i(){e.value=a.getValue()}var r;if(e.form&&(be(e.form,"submit",i),!t.leaveSubmitMethodAlone)){var o=e.form;r=o.submit;try{var s=o.submit=function(){i(),o.submit=r,o.submit(),o.submit=s}}catch(e){}}t.finishInit=function(n){n.save=i,n.getTextArea=function(){return e},n.toTextArea=function(){n.toTextArea=isNaN,i(),e.parentNode.removeChild(n.getWrapperElement()),e.style.display="",e.form&&(we(e.form,"submit",i),t.leaveSubmitMethodAlone||"function"!=typeof e.form.submit||(e.form.submit=r))}},e.style.display="none";var a=ja((function(t){return e.parentNode.insertBefore(t,e.nextSibling)}),t);return a}function dl(e){e.off=we,e.on=be,e.wheelEventPixels=ao,e.Doc=As,e.splitLines=Be,e.countColumn=q,e.findColumn=J,e.isWordChar=se,e.Pass=K,e.signal=xe,e.Line=pn,e.changeEnd=po,e.scrollbarModel=Sr,e.Pos=ut,e.cmpPos=ht,e.modes=Ue,e.mimeModes=$e,e.resolveMode=Ge,e.getMode=Xe,e.modeExtensions=Ye,e.extendMode=Je,e.copyState=Ze,e.startState=et,e.innerMode=Qe,e.commands=na,e.keyMap=qs,e.keyName=Xs,e.isModifierKey=Ks,e.lookupKey=Vs,e.normalizeKeyMap=$s,e.StringStream=tt,e.SharedTextMarker=xs,e.TextMarker=_s,e.LineWidget=ms,e.e_preventDefault=Te,e.e_stopPropagation=Ae,e.e_stop=Oe,e.addClass=P,e.contains=N,e.rmClass=T,e.keyNames=Bs}ul.prototype.init=function(e){var t=this,n=this,i=this.cm;this.createField(e);var r=this.textarea;function o(e){if(!ke(i,e)){if(i.somethingSelected())Ka({lineWise:!1,text:i.getSelections()});else{if(!i.options.lineWiseCopyCut)return;var t=Ja(i);Ka({lineWise:!0,text:t.text}),"cut"==e.type?i.setSelections(t.ranges,null,G):(n.prevInput="",r.value=t.text.join("\n"),W(r))}"cut"==e.type&&(i.state.cutIncoming=+new Date)}}e.wrapper.insertBefore(this.wrapper,e.wrapper.firstChild),m&&(r.style.width="0px"),be(r,"input",(function(){s&&a>=9&&t.hasSelection&&(t.hasSelection=null),n.poll()})),be(r,"paste",(function(e){ke(i,e)||Xa(e,i)||(i.state.pasteIncoming=+new Date,n.fastPoll())})),be(r,"cut",o),be(r,"copy",o),be(e.scroller,"paste",(function(t){if(!Xn(e,t)&&!ke(i,t)){if(!r.dispatchEvent)return i.state.pasteIncoming=+new Date,void n.focus();var o=new Event("paste");o.clipboardData=t.clipboardData,r.dispatchEvent(o)}})),be(e.lineSpace,"selectstart",(function(t){Xn(e,t)||Te(t)})),be(r,"compositionstart",(function(){var e=i.getCursor("from");n.composing&&n.composing.range.clear(),n.composing={start:e,range:i.markText(e,i.getCursor("to"),{className:"CodeMirror-composing"})}})),be(r,"compositionend",(function(){n.composing&&(n.poll(),n.composing.range.clear(),n.composing=null)}))},ul.prototype.createField=function(e){this.wrapper=Qa(),this.textarea=this.wrapper.firstChild;var t=this.cm.options;Za(this.textarea,t.spellcheck,t.autocorrect,t.autocapitalize)},ul.prototype.screenReaderLabelChanged=function(e){e?this.textarea.setAttribute("aria-label",e):this.textarea.removeAttribute("aria-label")},ul.prototype.prepareSelection=function(){var e=this.cm,t=e.display,n=e.doc,i=Xi(e);if(e.options.moveInputWithCursor){var r=Ci(e,n.sel.primary().head,"div"),o=t.wrapper.getBoundingClientRect(),s=t.lineDiv.getBoundingClientRect();i.teTop=Math.max(0,Math.min(t.wrapper.clientHeight-10,r.top+s.top-o.top)),i.teLeft=Math.max(0,Math.min(t.wrapper.clientWidth-10,r.left+s.left-o.left))}return i},ul.prototype.showSelection=function(e){var t=this.cm.display;M(t.cursorDiv,e.cursors),M(t.selectionDiv,e.selection),null!=e.teTop&&(this.wrapper.style.top=e.teTop+"px",this.wrapper.style.left=e.teLeft+"px")},ul.prototype.reset=function(e){if(!(this.contextMenuPending||this.composing&&e)){var t=this.cm;if(this.resetting=!0,t.somethingSelected()){this.prevInput="";var n=t.getSelection();this.textarea.value=n,t.state.focused&&W(this.textarea),s&&a>=9&&(this.hasSelection=n)}else e||(this.prevInput=this.textarea.value="",s&&a>=9&&(this.hasSelection=null));this.resetting=!1}},ul.prototype.getField=function(){return this.textarea},ul.prototype.supportsTouch=function(){return!1},ul.prototype.focus=function(){if("nocursor"!=this.cm.options.readOnly&&(!y||D(B(this.textarea))!=this.textarea))try{this.textarea.focus()}catch(e){}},ul.prototype.blur=function(){this.textarea.blur()},ul.prototype.resetPosition=function(){this.wrapper.style.top=this.wrapper.style.left=0},ul.prototype.receivedFocus=function(){this.slowPoll()},ul.prototype.slowPoll=function(){var e=this;this.pollingFast||this.polling.set(this.cm.options.pollInterval,(function(){e.poll(),e.cm.state.focused&&e.slowPoll()}))},ul.prototype.fastPoll=function(){var e=!1,t=this;function n(){t.poll()||e?(t.pollingFast=!1,t.slowPoll()):(e=!0,t.polling.set(60,n))}t.pollingFast=!0,t.polling.set(20,n)},ul.prototype.poll=function(){var e=this,t=this.cm,n=this.textarea,i=this.prevInput;if(this.contextMenuPending||this.resetting||!t.state.focused||Re(n)&&!i&&!this.composing||t.isReadOnly()||t.options.disableInput||t.state.keySeq)return!1;var r=n.value;if(r==i&&!t.somethingSelected())return!1;if(s&&a>=9&&this.hasSelection===r||b&&/[\uf700-\uf7ff]/.test(r))return t.display.input.reset(),!1;if(t.doc.sel==t.display.selForContextMenu){var o=r.charCodeAt(0);if(8203!=o||i||(i="​"),8666==o)return this.reset(),this.cm.execCommand("undo")}for(var l=0,c=Math.min(i.length,r.length);l<c&&i.charCodeAt(l)==r.charCodeAt(l);)++l;return Wr(t,(function(){Ga(t,r.slice(l),i.length-l,null,e.composing?"*compose":null),r.length>1e3||r.indexOf("\n")>-1?n.value=e.prevInput="":e.prevInput=r,e.composing&&(e.composing.range.clear(),e.composing.range=t.markText(e.composing.start,t.getCursor("to"),{className:"CodeMirror-composing"}))})),!0},ul.prototype.ensurePolled=function(){this.pollingFast&&this.poll()&&(this.pollingFast=!1)},ul.prototype.onKeyPress=function(){s&&a>=9&&(this.hasSelection=null),this.fastPoll()},ul.prototype.onContextMenu=function(e){var t=this,n=t.cm,i=n.display,r=t.textarea;t.contextMenuPending&&t.contextMenuPending();var o=Ri(n,e),c=i.scroller.scrollTop;if(o&&!d){n.options.resetSelectionOnContextMenu&&-1==n.doc.sel.contains(o)&&Ir(n,Ko)(n.doc,fo(o),G);var u,h=r.style.cssText,f=t.wrapper.style.cssText,p=t.wrapper.offsetParent.getBoundingClientRect();if(t.wrapper.style.cssText="position: static",r.style.cssText="position: absolute; width: 30px; height: 30px;\n      top: "+(e.clientY-p.top-5)+"px; left: "+(e.clientX-p.left-5)+"px;\n      z-index: 1000; background: "+(s?"rgba(255, 255, 255, .05)":"transparent")+";\n      outline: none; border-width: 0; outline: none; overflow: hidden; opacity: .05; filter: alpha(opacity=5);",l&&(u=r.ownerDocument.defaultView.scrollY),i.input.focus(),l&&r.ownerDocument.defaultView.scrollTo(null,u),i.input.reset(),n.somethingSelected()||(r.value=t.prevInput=" "),t.contextMenuPending=v,i.selForContextMenu=n.doc.sel,clearTimeout(i.detectingSelectAll),s&&a>=9&&m(),C){Oe(e);var g=function(){we(window,"mouseup",g),setTimeout(v,20)};be(window,"mouseup",g)}else setTimeout(v,50)}function m(){if(null!=r.selectionStart){var e=n.somethingSelected(),o="​"+(e?r.value:"");r.value="⇚",r.value=o,t.prevInput=e?"":"​",r.selectionStart=1,r.selectionEnd=o.length,i.selForContextMenu=n.doc.sel}}function v(){if(t.contextMenuPending==v&&(t.contextMenuPending=!1,t.wrapper.style.cssText=f,r.style.cssText=h,s&&a<9&&i.scrollbars.setScrollTop(i.scroller.scrollTop=c),null!=r.selectionStart)){(!s||s&&a<9)&&m();var e=0,o=function(){i.selForContextMenu==n.doc.sel&&0==r.selectionStart&&r.selectionEnd>0&&"​"==t.prevInput?Ir(n,ts)(n):e++<10?i.detectingSelectAll=setTimeout(o,500):(i.selForContextMenu=null,i.input.reset())};i.detectingSelectAll=setTimeout(o,200)}}},ul.prototype.readOnlyChanged=function(e){e||this.reset(),this.textarea.disabled="nocursor"==e,this.textarea.readOnly=!!e},ul.prototype.setUneditable=function(){},ul.prototype.needsContentAttribute=!1,Ba(ja),el(ja);var fl="iter insert remove copy getEditor constructor".split(" ");for(var pl in As.prototype)As.prototype.hasOwnProperty(pl)&&$(fl,pl)<0&&(ja.prototype[pl]=function(e){return function(){return e.apply(this.doc,arguments)}}(As.prototype[pl]));return Le(As),ja.inputStyles={textarea:ul,contenteditable:il},ja.defineMode=function(e){ja.defaults.mode||"null"==e||(ja.defaults.mode=e),Ve.apply(this,arguments)},ja.defineMIME=Ke,ja.defineMode("null",(function(){return{token:function(e){return e.skipToEnd()}}})),ja.defineMIME("text/plain","null"),ja.defineExtension=function(e,t){ja.prototype[e]=t},ja.defineDocExtension=function(e,t){As.prototype[e]=t},ja.fromTextArea=hl,dl(ja),ja.version="5.65.19",ja}()},260:e=>{"use strict";function t(e){return e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}t.proto=function(){return RegExp.escape=t,t},e.exports=t},452:(e,t,n)=>{"use strict";n.r(t)},656:(e,t,n)=>{!function(e){"use strict";function t(e){for(var t={},n=0;n<e.length;++n)t[e[n].toLowerCase()]=!0;return t}e.defineMode("css",(function(t,n){var i=n.inline;n.propertyKeywords||(n=e.resolveMode("text/css"));var r,o,s=t.indentUnit,a=n.tokenHooks,l=n.documentTypes||{},c=n.mediaTypes||{},u=n.mediaFeatures||{},h=n.mediaValueKeywords||{},d=n.propertyKeywords||{},f=n.nonStandardPropertyKeywords||{},p=n.fontProperties||{},g=n.counterDescriptors||{},m=n.colorKeywords||{},v=n.valueKeywords||{},y=n.allowNested,b=n.lineComment,_=!0===n.supportsAtComponent,w=!1!==t.highlightNonStandardPropertyKeywords;function x(e,t){return r=t,e}function k(e,t){var n=e.next();if(a[n]){var i=a[n](e,t);if(!1!==i)return i}return"@"==n?(e.eatWhile(/[\w\\\-]/),x("def",e.current())):"="==n||("~"==n||"|"==n)&&e.eat("=")?x(null,"compare"):'"'==n||"'"==n?(t.tokenize=C(n),t.tokenize(e,t)):"#"==n?(e.eatWhile(/[\w\\\-]/),x("atom","hash")):"!"==n?(e.match(/^\s*\w*/),x("keyword","important")):/\d/.test(n)||"."==n&&e.eat(/\d/)?(e.eatWhile(/[\w.%]/),x("number","unit")):"-"!==n?/[,+>*\/]/.test(n)?x(null,"select-op"):"."==n&&e.match(/^-?[_a-z][_a-z0-9-]*/i)?x("qualifier","qualifier"):/[:;{}\[\]\(\)]/.test(n)?x(null,n):e.match(/^[\w-.]+(?=\()/)?(/^(url(-prefix)?|domain|regexp)$/i.test(e.current())&&(t.tokenize=S),x("variable callee","variable")):/[\w\\\-]/.test(n)?(e.eatWhile(/[\w\\\-]/),x("property","word")):x(null,null):/[\d.]/.test(e.peek())?(e.eatWhile(/[\w.%]/),x("number","unit")):e.match(/^-[\w\\\-]*/)?(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?x("variable-2","variable-definition"):x("variable-2","variable")):e.match(/^\w+-/)?x("meta","meta"):void 0}function C(e){return function(t,n){for(var i,r=!1;null!=(i=t.next());){if(i==e&&!r){")"==e&&t.backUp(1);break}r=!r&&"\\"==i}return(i==e||!r&&")"!=e)&&(n.tokenize=null),x("string","string")}}function S(e,t){return e.next(),e.match(/^\s*[\"\')]/,!1)?t.tokenize=null:t.tokenize=C(")"),x(null,"(")}function L(e,t,n){this.type=e,this.indent=t,this.prev=n}function T(e,t,n,i){return e.context=new L(n,t.indentation()+(!1===i?0:s),e.context),n}function A(e){return e.context.prev&&(e.context=e.context.prev),e.context.type}function M(e,t,n){return N[n.context.type](e,t,n)}function O(e,t,n,i){for(var r=i||1;r>0;r--)n.context=n.context.prev;return M(e,t,n)}function E(e){var t=e.current().toLowerCase();o=v.hasOwnProperty(t)?"atom":m.hasOwnProperty(t)?"keyword":"variable"}var N={top:function(e,t,n){if("{"==e)return T(n,t,"block");if("}"==e&&n.context.prev)return A(n);if(_&&/@component/i.test(e))return T(n,t,"atComponentBlock");if(/^@(-moz-)?document$/i.test(e))return T(n,t,"documentTypes");if(/^@(media|supports|(-moz-)?document|import)$/i.test(e))return T(n,t,"atBlock");if(/^@(font-face|counter-style)/i.test(e))return n.stateArg=e,"restricted_atBlock_before";if(/^@(-(moz|ms|o|webkit)-)?keyframes$/i.test(e))return"keyframes";if(e&&"@"==e.charAt(0))return T(n,t,"at");if("hash"==e)o="builtin";else if("word"==e)o="tag";else{if("variable-definition"==e)return"maybeprop";if("interpolation"==e)return T(n,t,"interpolation");if(":"==e)return"pseudo";if(y&&"("==e)return T(n,t,"parens")}return n.context.type},block:function(e,t,n){if("word"==e){var i=t.current().toLowerCase();return d.hasOwnProperty(i)?(o="property","maybeprop"):f.hasOwnProperty(i)?(o=w?"string-2":"property","maybeprop"):y?(o=t.match(/^\s*:(?:\s|$)/,!1)?"property":"tag","block"):(o+=" error","maybeprop")}return"meta"==e?"block":y||"hash"!=e&&"qualifier"!=e?N.top(e,t,n):(o="error","block")},maybeprop:function(e,t,n){return":"==e?T(n,t,"prop"):M(e,t,n)},prop:function(e,t,n){if(";"==e)return A(n);if("{"==e&&y)return T(n,t,"propBlock");if("}"==e||"{"==e)return O(e,t,n);if("("==e)return T(n,t,"parens");if("hash"!=e||/^#([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(t.current())){if("word"==e)E(t);else if("interpolation"==e)return T(n,t,"interpolation")}else o+=" error";return"prop"},propBlock:function(e,t,n){return"}"==e?A(n):"word"==e?(o="property","maybeprop"):n.context.type},parens:function(e,t,n){return"{"==e||"}"==e?O(e,t,n):")"==e?A(n):"("==e?T(n,t,"parens"):"interpolation"==e?T(n,t,"interpolation"):("word"==e&&E(t),"parens")},pseudo:function(e,t,n){return"meta"==e?"pseudo":"word"==e?(o="variable-3",n.context.type):M(e,t,n)},documentTypes:function(e,t,n){return"word"==e&&l.hasOwnProperty(t.current())?(o="tag",n.context.type):N.atBlock(e,t,n)},atBlock:function(e,t,n){if("("==e)return T(n,t,"atBlock_parens");if("}"==e||";"==e)return O(e,t,n);if("{"==e)return A(n)&&T(n,t,y?"block":"top");if("interpolation"==e)return T(n,t,"interpolation");if("word"==e){var i=t.current().toLowerCase();o="only"==i||"not"==i||"and"==i||"or"==i?"keyword":c.hasOwnProperty(i)?"attribute":u.hasOwnProperty(i)?"property":h.hasOwnProperty(i)?"keyword":d.hasOwnProperty(i)?"property":f.hasOwnProperty(i)?w?"string-2":"property":v.hasOwnProperty(i)?"atom":m.hasOwnProperty(i)?"keyword":"error"}return n.context.type},atComponentBlock:function(e,t,n){return"}"==e?O(e,t,n):"{"==e?A(n)&&T(n,t,y?"block":"top",!1):("word"==e&&(o="error"),n.context.type)},atBlock_parens:function(e,t,n){return")"==e?A(n):"{"==e||"}"==e?O(e,t,n,2):N.atBlock(e,t,n)},restricted_atBlock_before:function(e,t,n){return"{"==e?T(n,t,"restricted_atBlock"):"word"==e&&"@counter-style"==n.stateArg?(o="variable","restricted_atBlock_before"):M(e,t,n)},restricted_atBlock:function(e,t,n){return"}"==e?(n.stateArg=null,A(n)):"word"==e?(o="@font-face"==n.stateArg&&!p.hasOwnProperty(t.current().toLowerCase())||"@counter-style"==n.stateArg&&!g.hasOwnProperty(t.current().toLowerCase())?"error":"property","maybeprop"):"restricted_atBlock"},keyframes:function(e,t,n){return"word"==e?(o="variable","keyframes"):"{"==e?T(n,t,"top"):M(e,t,n)},at:function(e,t,n){return";"==e?A(n):"{"==e||"}"==e?O(e,t,n):("word"==e?o="tag":"hash"==e&&(o="builtin"),"at")},interpolation:function(e,t,n){return"}"==e?A(n):"{"==e||";"==e?O(e,t,n):("word"==e?o="variable":"variable"!=e&&"("!=e&&")"!=e&&(o="error"),"interpolation")}};return{startState:function(e){return{tokenize:null,state:i?"block":"top",stateArg:null,context:new L(i?"block":"top",e||0,null)}},token:function(e,t){if(!t.tokenize&&e.eatSpace())return null;var n=(t.tokenize||k)(e,t);return n&&"object"==typeof n&&(r=n[1],n=n[0]),o=n,"comment"!=r&&(t.state=N[t.state](r,e,t)),o},indent:function(e,t){var n=e.context,i=t&&t.charAt(0),r=n.indent;return"prop"!=n.type||"}"!=i&&")"!=i||(n=n.prev),n.prev&&("}"!=i||"block"!=n.type&&"top"!=n.type&&"interpolation"!=n.type&&"restricted_atBlock"!=n.type?(")"!=i||"parens"!=n.type&&"atBlock_parens"!=n.type)&&("{"!=i||"at"!=n.type&&"atBlock"!=n.type)||(r=Math.max(0,n.indent-s)):r=(n=n.prev).indent),r},electricChars:"}",blockCommentStart:"/*",blockCommentEnd:"*/",blockCommentContinue:" * ",lineComment:b,fold:"brace"}}));var n=["domain","regexp","url","url-prefix"],i=t(n),r=["all","aural","braille","handheld","print","projection","screen","tty","tv","embossed"],o=t(r),s=["width","min-width","max-width","height","min-height","max-height","device-width","min-device-width","max-device-width","device-height","min-device-height","max-device-height","aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio","color","min-color","max-color","color-index","min-color-index","max-color-index","monochrome","min-monochrome","max-monochrome","resolution","min-resolution","max-resolution","scan","grid","orientation","device-pixel-ratio","min-device-pixel-ratio","max-device-pixel-ratio","pointer","any-pointer","hover","any-hover","prefers-color-scheme","dynamic-range","video-dynamic-range"],a=t(s),l=["landscape","portrait","none","coarse","fine","on-demand","hover","interlace","progressive","dark","light","standard","high"],c=t(l),u=["align-content","align-items","align-self","alignment-adjust","alignment-baseline","all","anchor-point","animation","animation-delay","animation-direction","animation-duration","animation-fill-mode","animation-iteration-count","animation-name","animation-play-state","animation-timing-function","appearance","azimuth","backdrop-filter","backface-visibility","background","background-attachment","background-blend-mode","background-clip","background-color","background-image","background-origin","background-position","background-position-x","background-position-y","background-repeat","background-size","baseline-shift","binding","bleed","block-size","bookmark-label","bookmark-level","bookmark-state","bookmark-target","border","border-bottom","border-bottom-color","border-bottom-left-radius","border-bottom-right-radius","border-bottom-style","border-bottom-width","border-collapse","border-color","border-image","border-image-outset","border-image-repeat","border-image-slice","border-image-source","border-image-width","border-left","border-left-color","border-left-style","border-left-width","border-radius","border-right","border-right-color","border-right-style","border-right-width","border-spacing","border-style","border-top","border-top-color","border-top-left-radius","border-top-right-radius","border-top-style","border-top-width","border-width","bottom","box-decoration-break","box-shadow","box-sizing","break-after","break-before","break-inside","caption-side","caret-color","clear","clip","color","color-profile","column-count","column-fill","column-gap","column-rule","column-rule-color","column-rule-style","column-rule-width","column-span","column-width","columns","contain","content","counter-increment","counter-reset","crop","cue","cue-after","cue-before","cursor","direction","display","dominant-baseline","drop-initial-after-adjust","drop-initial-after-align","drop-initial-before-adjust","drop-initial-before-align","drop-initial-size","drop-initial-value","elevation","empty-cells","fit","fit-content","fit-position","flex","flex-basis","flex-direction","flex-flow","flex-grow","flex-shrink","flex-wrap","float","float-offset","flow-from","flow-into","font","font-family","font-feature-settings","font-kerning","font-language-override","font-optical-sizing","font-size","font-size-adjust","font-stretch","font-style","font-synthesis","font-variant","font-variant-alternates","font-variant-caps","font-variant-east-asian","font-variant-ligatures","font-variant-numeric","font-variant-position","font-variation-settings","font-weight","gap","grid","grid-area","grid-auto-columns","grid-auto-flow","grid-auto-rows","grid-column","grid-column-end","grid-column-gap","grid-column-start","grid-gap","grid-row","grid-row-end","grid-row-gap","grid-row-start","grid-template","grid-template-areas","grid-template-columns","grid-template-rows","hanging-punctuation","height","hyphens","icon","image-orientation","image-rendering","image-resolution","inline-box-align","inset","inset-block","inset-block-end","inset-block-start","inset-inline","inset-inline-end","inset-inline-start","isolation","justify-content","justify-items","justify-self","left","letter-spacing","line-break","line-height","line-height-step","line-stacking","line-stacking-ruby","line-stacking-shift","line-stacking-strategy","list-style","list-style-image","list-style-position","list-style-type","margin","margin-bottom","margin-left","margin-right","margin-top","marks","marquee-direction","marquee-loop","marquee-play-count","marquee-speed","marquee-style","mask-clip","mask-composite","mask-image","mask-mode","mask-origin","mask-position","mask-repeat","mask-size","mask-type","max-block-size","max-height","max-inline-size","max-width","min-block-size","min-height","min-inline-size","min-width","mix-blend-mode","move-to","nav-down","nav-index","nav-left","nav-right","nav-up","object-fit","object-position","offset","offset-anchor","offset-distance","offset-path","offset-position","offset-rotate","opacity","order","orphans","outline","outline-color","outline-offset","outline-style","outline-width","overflow","overflow-style","overflow-wrap","overflow-x","overflow-y","padding","padding-bottom","padding-left","padding-right","padding-top","page","page-break-after","page-break-before","page-break-inside","page-policy","pause","pause-after","pause-before","perspective","perspective-origin","pitch","pitch-range","place-content","place-items","place-self","play-during","position","presentation-level","punctuation-trim","quotes","region-break-after","region-break-before","region-break-inside","region-fragment","rendering-intent","resize","rest","rest-after","rest-before","richness","right","rotate","rotation","rotation-point","row-gap","ruby-align","ruby-overhang","ruby-position","ruby-span","scale","scroll-behavior","scroll-margin","scroll-margin-block","scroll-margin-block-end","scroll-margin-block-start","scroll-margin-bottom","scroll-margin-inline","scroll-margin-inline-end","scroll-margin-inline-start","scroll-margin-left","scroll-margin-right","scroll-margin-top","scroll-padding","scroll-padding-block","scroll-padding-block-end","scroll-padding-block-start","scroll-padding-bottom","scroll-padding-inline","scroll-padding-inline-end","scroll-padding-inline-start","scroll-padding-left","scroll-padding-right","scroll-padding-top","scroll-snap-align","scroll-snap-type","shape-image-threshold","shape-inside","shape-margin","shape-outside","size","speak","speak-as","speak-header","speak-numeral","speak-punctuation","speech-rate","stress","string-set","tab-size","table-layout","target","target-name","target-new","target-position","text-align","text-align-last","text-combine-upright","text-decoration","text-decoration-color","text-decoration-line","text-decoration-skip","text-decoration-skip-ink","text-decoration-style","text-emphasis","text-emphasis-color","text-emphasis-position","text-emphasis-style","text-height","text-indent","text-justify","text-orientation","text-outline","text-overflow","text-rendering","text-shadow","text-size-adjust","text-space-collapse","text-transform","text-underline-position","text-wrap","top","touch-action","transform","transform-origin","transform-style","transition","transition-delay","transition-duration","transition-property","transition-timing-function","translate","unicode-bidi","user-select","vertical-align","visibility","voice-balance","voice-duration","voice-family","voice-pitch","voice-range","voice-rate","voice-stress","voice-volume","volume","white-space","widows","width","will-change","word-break","word-spacing","word-wrap","writing-mode","z-index","clip-path","clip-rule","mask","enable-background","filter","flood-color","flood-opacity","lighting-color","stop-color","stop-opacity","pointer-events","color-interpolation","color-interpolation-filters","color-rendering","fill","fill-opacity","fill-rule","image-rendering","marker","marker-end","marker-mid","marker-start","paint-order","shape-rendering","stroke","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke-width","text-rendering","baseline-shift","dominant-baseline","glyph-orientation-horizontal","glyph-orientation-vertical","text-anchor","writing-mode"],h=t(u),d=["accent-color","aspect-ratio","border-block","border-block-color","border-block-end","border-block-end-color","border-block-end-style","border-block-end-width","border-block-start","border-block-start-color","border-block-start-style","border-block-start-width","border-block-style","border-block-width","border-inline","border-inline-color","border-inline-end","border-inline-end-color","border-inline-end-style","border-inline-end-width","border-inline-start","border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-style","border-inline-width","content-visibility","margin-block","margin-block-end","margin-block-start","margin-inline","margin-inline-end","margin-inline-start","overflow-anchor","overscroll-behavior","padding-block","padding-block-end","padding-block-start","padding-inline","padding-inline-end","padding-inline-start","scroll-snap-stop","scrollbar-3d-light-color","scrollbar-arrow-color","scrollbar-base-color","scrollbar-dark-shadow-color","scrollbar-face-color","scrollbar-highlight-color","scrollbar-shadow-color","scrollbar-track-color","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","shape-inside","zoom"],f=t(d),p=t(["font-display","font-family","src","unicode-range","font-variant","font-feature-settings","font-stretch","font-weight","font-style"]),g=t(["additive-symbols","fallback","negative","pad","prefix","range","speak-as","suffix","symbols","system"]),m=["aliceblue","antiquewhite","aqua","aquamarine","azure","beige","bisque","black","blanchedalmond","blue","blueviolet","brown","burlywood","cadetblue","chartreuse","chocolate","coral","cornflowerblue","cornsilk","crimson","cyan","darkblue","darkcyan","darkgoldenrod","darkgray","darkgreen","darkgrey","darkkhaki","darkmagenta","darkolivegreen","darkorange","darkorchid","darkred","darksalmon","darkseagreen","darkslateblue","darkslategray","darkslategrey","darkturquoise","darkviolet","deeppink","deepskyblue","dimgray","dimgrey","dodgerblue","firebrick","floralwhite","forestgreen","fuchsia","gainsboro","ghostwhite","gold","goldenrod","gray","grey","green","greenyellow","honeydew","hotpink","indianred","indigo","ivory","khaki","lavender","lavenderblush","lawngreen","lemonchiffon","lightblue","lightcoral","lightcyan","lightgoldenrodyellow","lightgray","lightgreen","lightgrey","lightpink","lightsalmon","lightseagreen","lightskyblue","lightslategray","lightslategrey","lightsteelblue","lightyellow","lime","limegreen","linen","magenta","maroon","mediumaquamarine","mediumblue","mediumorchid","mediumpurple","mediumseagreen","mediumslateblue","mediumspringgreen","mediumturquoise","mediumvioletred","midnightblue","mintcream","mistyrose","moccasin","navajowhite","navy","oldlace","olive","olivedrab","orange","orangered","orchid","palegoldenrod","palegreen","paleturquoise","palevioletred","papayawhip","peachpuff","peru","pink","plum","powderblue","purple","rebeccapurple","red","rosybrown","royalblue","saddlebrown","salmon","sandybrown","seagreen","seashell","sienna","silver","skyblue","slateblue","slategray","slategrey","snow","springgreen","steelblue","tan","teal","thistle","tomato","turquoise","violet","wheat","white","whitesmoke","yellow","yellowgreen"],v=t(m),y=["above","absolute","activeborder","additive","activecaption","afar","after-white-space","ahead","alias","all","all-scroll","alphabetic","alternate","always","amharic","amharic-abegede","antialiased","appworkspace","arabic-indic","armenian","asterisks","attr","auto","auto-flow","avoid","avoid-column","avoid-page","avoid-region","axis-pan","background","backwards","baseline","below","bidi-override","binary","bengali","blink","block","block-axis","blur","bold","bolder","border","border-box","both","bottom","break","break-all","break-word","brightness","bullets","button","buttonface","buttonhighlight","buttonshadow","buttontext","calc","cambodian","capitalize","caps-lock-indicator","caption","captiontext","caret","cell","center","checkbox","circle","cjk-decimal","cjk-earthly-branch","cjk-heavenly-stem","cjk-ideographic","clear","clip","close-quote","col-resize","collapse","color","color-burn","color-dodge","column","column-reverse","compact","condensed","conic-gradient","contain","content","contents","content-box","context-menu","continuous","contrast","copy","counter","counters","cover","crop","cross","crosshair","cubic-bezier","currentcolor","cursive","cyclic","darken","dashed","decimal","decimal-leading-zero","default","default-button","dense","destination-atop","destination-in","destination-out","destination-over","devanagari","difference","disc","discard","disclosure-closed","disclosure-open","document","dot-dash","dot-dot-dash","dotted","double","down","drop-shadow","e-resize","ease","ease-in","ease-in-out","ease-out","element","ellipse","ellipsis","embed","end","ethiopic","ethiopic-abegede","ethiopic-abegede-am-et","ethiopic-abegede-gez","ethiopic-abegede-ti-er","ethiopic-abegede-ti-et","ethiopic-halehame-aa-er","ethiopic-halehame-aa-et","ethiopic-halehame-am-et","ethiopic-halehame-gez","ethiopic-halehame-om-et","ethiopic-halehame-sid-et","ethiopic-halehame-so-et","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","ethiopic-halehame-tig","ethiopic-numeric","ew-resize","exclusion","expanded","extends","extra-condensed","extra-expanded","fantasy","fast","fill","fill-box","fixed","flat","flex","flex-end","flex-start","footnotes","forwards","from","geometricPrecision","georgian","grayscale","graytext","grid","groove","gujarati","gurmukhi","hand","hangul","hangul-consonant","hard-light","hebrew","help","hidden","hide","higher","highlight","highlighttext","hiragana","hiragana-iroha","horizontal","hsl","hsla","hue","hue-rotate","icon","ignore","inactiveborder","inactivecaption","inactivecaptiontext","infinite","infobackground","infotext","inherit","initial","inline","inline-axis","inline-block","inline-flex","inline-grid","inline-table","inset","inside","intrinsic","invert","italic","japanese-formal","japanese-informal","justify","kannada","katakana","katakana-iroha","keep-all","khmer","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","landscape","lao","large","larger","left","level","lighter","lighten","line-through","linear","linear-gradient","lines","list-item","listbox","listitem","local","logical","loud","lower","lower-alpha","lower-armenian","lower-greek","lower-hexadecimal","lower-latin","lower-norwegian","lower-roman","lowercase","ltr","luminosity","malayalam","manipulation","match","matrix","matrix3d","media-play-button","media-slider","media-sliderthumb","media-volume-slider","media-volume-sliderthumb","medium","menu","menulist","menulist-button","menutext","message-box","middle","min-intrinsic","mix","mongolian","monospace","move","multiple","multiple_mask_images","multiply","myanmar","n-resize","narrower","ne-resize","nesw-resize","no-close-quote","no-drop","no-open-quote","no-repeat","none","normal","not-allowed","nowrap","ns-resize","numbers","numeric","nw-resize","nwse-resize","oblique","octal","opacity","open-quote","optimizeLegibility","optimizeSpeed","oriya","oromo","outset","outside","outside-shape","overlay","overline","padding","padding-box","painted","page","paused","persian","perspective","pinch-zoom","plus-darker","plus-lighter","pointer","polygon","portrait","pre","pre-line","pre-wrap","preserve-3d","progress","push-button","radial-gradient","radio","read-only","read-write","read-write-plaintext-only","rectangle","region","relative","repeat","repeating-linear-gradient","repeating-radial-gradient","repeating-conic-gradient","repeat-x","repeat-y","reset","reverse","rgb","rgba","ridge","right","rotate","rotate3d","rotateX","rotateY","rotateZ","round","row","row-resize","row-reverse","rtl","run-in","running","s-resize","sans-serif","saturate","saturation","scale","scale3d","scaleX","scaleY","scaleZ","screen","scroll","scrollbar","scroll-position","se-resize","searchfield","searchfield-cancel-button","searchfield-decoration","searchfield-results-button","searchfield-results-decoration","self-start","self-end","semi-condensed","semi-expanded","separate","sepia","serif","show","sidama","simp-chinese-formal","simp-chinese-informal","single","skew","skewX","skewY","skip-white-space","slide","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","slow","small","small-caps","small-caption","smaller","soft-light","solid","somali","source-atop","source-in","source-out","source-over","space","space-around","space-between","space-evenly","spell-out","square","square-button","start","static","status-bar","stretch","stroke","stroke-box","sub","subpixel-antialiased","svg_masks","super","sw-resize","symbolic","symbols","system-ui","table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row","table-row-group","tamil","telugu","text","text-bottom","text-top","textarea","textfield","thai","thick","thin","threeddarkshadow","threedface","threedhighlight","threedlightshadow","threedshadow","tibetan","tigre","tigrinya-er","tigrinya-er-abegede","tigrinya-et","tigrinya-et-abegede","to","top","trad-chinese-formal","trad-chinese-informal","transform","translate","translate3d","translateX","translateY","translateZ","transparent","ultra-condensed","ultra-expanded","underline","unidirectional-pan","unset","up","upper-alpha","upper-armenian","upper-greek","upper-hexadecimal","upper-latin","upper-norwegian","upper-roman","uppercase","urdu","url","var","vertical","vertical-text","view-box","visible","visibleFill","visiblePainted","visibleStroke","visual","w-resize","wait","wave","wider","window","windowframe","windowtext","words","wrap","wrap-reverse","x-large","x-small","xor","xx-large","xx-small"],b=t(y),_=n.concat(r).concat(s).concat(l).concat(u).concat(d).concat(m).concat(y);function w(e,t){for(var n,i=!1;null!=(n=e.next());){if(i&&"/"==n){t.tokenize=null;break}i="*"==n}return["comment","comment"]}e.registerHelper("hintWords","css",_),e.defineMIME("text/css",{documentTypes:i,mediaTypes:o,mediaFeatures:a,mediaValueKeywords:c,propertyKeywords:h,nonStandardPropertyKeywords:f,fontProperties:p,counterDescriptors:g,colorKeywords:v,valueKeywords:b,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=w,w(e,t))}},name:"css"}),e.defineMIME("text/x-scss",{mediaTypes:o,mediaFeatures:a,mediaValueKeywords:c,propertyKeywords:h,nonStandardPropertyKeywords:f,colorKeywords:v,valueKeywords:b,fontProperties:p,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=w,w(e,t)):["operator","operator"]},":":function(e){return!!e.match(/^\s*\{/,!1)&&[null,null]},$:function(e){return e.match(/^[\w-]+/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"]},"#":function(e){return!!e.eat("{")&&[null,"interpolation"]}},name:"css",helperType:"scss"}),e.defineMIME("text/x-less",{mediaTypes:o,mediaFeatures:a,mediaValueKeywords:c,propertyKeywords:h,nonStandardPropertyKeywords:f,colorKeywords:v,valueKeywords:b,fontProperties:p,allowNested:!0,lineComment:"//",tokenHooks:{"/":function(e,t){return e.eat("/")?(e.skipToEnd(),["comment","comment"]):e.eat("*")?(t.tokenize=w,w(e,t)):["operator","operator"]},"@":function(e){return e.eat("{")?[null,"interpolation"]:!e.match(/^(charset|document|font-face|import|(-(moz|ms|o|webkit)-)?keyframes|media|namespace|page|supports)\b/i,!1)&&(e.eatWhile(/[\w\\\-]/),e.match(/^\s*:/,!1)?["variable-2","variable-definition"]:["variable-2","variable"])},"&":function(){return["atom","atom"]}},name:"css",helperType:"less"}),e.defineMIME("text/x-gss",{documentTypes:i,mediaTypes:o,mediaFeatures:a,propertyKeywords:h,nonStandardPropertyKeywords:f,fontProperties:p,counterDescriptors:g,colorKeywords:v,valueKeywords:b,supportsAtComponent:!0,tokenHooks:{"/":function(e,t){return!!e.eat("*")&&(t.tokenize=w,w(e,t))}},name:"css",helperType:"gss"})}(n(237))},735:e=>{e.exports=function(e){"use strict";var t=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];function n(e,t){var n=e[0],i=e[1],r=e[2],o=e[3];i=((i+=((r=((r+=((o=((o+=((n=((n+=(i&r|~i&o)+t[0]-680876936|0)<<7|n>>>25)+i|0)&i|~n&r)+t[1]-389564586|0)<<12|o>>>20)+n|0)&n|~o&i)+t[2]+606105819|0)<<17|r>>>15)+o|0)&o|~r&n)+t[3]-1044525330|0)<<22|i>>>10)+r|0,i=((i+=((r=((r+=((o=((o+=((n=((n+=(i&r|~i&o)+t[4]-176418897|0)<<7|n>>>25)+i|0)&i|~n&r)+t[5]+1200080426|0)<<12|o>>>20)+n|0)&n|~o&i)+t[6]-1473231341|0)<<17|r>>>15)+o|0)&o|~r&n)+t[7]-45705983|0)<<22|i>>>10)+r|0,i=((i+=((r=((r+=((o=((o+=((n=((n+=(i&r|~i&o)+t[8]+1770035416|0)<<7|n>>>25)+i|0)&i|~n&r)+t[9]-1958414417|0)<<12|o>>>20)+n|0)&n|~o&i)+t[10]-42063|0)<<17|r>>>15)+o|0)&o|~r&n)+t[11]-1990404162|0)<<22|i>>>10)+r|0,i=((i+=((r=((r+=((o=((o+=((n=((n+=(i&r|~i&o)+t[12]+1804603682|0)<<7|n>>>25)+i|0)&i|~n&r)+t[13]-40341101|0)<<12|o>>>20)+n|0)&n|~o&i)+t[14]-1502002290|0)<<17|r>>>15)+o|0)&o|~r&n)+t[15]+1236535329|0)<<22|i>>>10)+r|0,i=((i+=((r=((r+=((o=((o+=((n=((n+=(i&o|r&~o)+t[1]-165796510|0)<<5|n>>>27)+i|0)&r|i&~r)+t[6]-1069501632|0)<<9|o>>>23)+n|0)&i|n&~i)+t[11]+643717713|0)<<14|r>>>18)+o|0)&n|o&~n)+t[0]-373897302|0)<<20|i>>>12)+r|0,i=((i+=((r=((r+=((o=((o+=((n=((n+=(i&o|r&~o)+t[5]-701558691|0)<<5|n>>>27)+i|0)&r|i&~r)+t[10]+38016083|0)<<9|o>>>23)+n|0)&i|n&~i)+t[15]-660478335|0)<<14|r>>>18)+o|0)&n|o&~n)+t[4]-405537848|0)<<20|i>>>12)+r|0,i=((i+=((r=((r+=((o=((o+=((n=((n+=(i&o|r&~o)+t[9]+568446438|0)<<5|n>>>27)+i|0)&r|i&~r)+t[14]-1019803690|0)<<9|o>>>23)+n|0)&i|n&~i)+t[3]-187363961|0)<<14|r>>>18)+o|0)&n|o&~n)+t[8]+1163531501|0)<<20|i>>>12)+r|0,i=((i+=((r=((r+=((o=((o+=((n=((n+=(i&o|r&~o)+t[13]-1444681467|0)<<5|n>>>27)+i|0)&r|i&~r)+t[2]-51403784|0)<<9|o>>>23)+n|0)&i|n&~i)+t[7]+1735328473|0)<<14|r>>>18)+o|0)&n|o&~n)+t[12]-1926607734|0)<<20|i>>>12)+r|0,i=((i+=((r=((r+=((o=((o+=((n=((n+=(i^r^o)+t[5]-378558|0)<<4|n>>>28)+i|0)^i^r)+t[8]-2022574463|0)<<11|o>>>21)+n|0)^n^i)+t[11]+1839030562|0)<<16|r>>>16)+o|0)^o^n)+t[14]-35309556|0)<<23|i>>>9)+r|0,i=((i+=((r=((r+=((o=((o+=((n=((n+=(i^r^o)+t[1]-1530992060|0)<<4|n>>>28)+i|0)^i^r)+t[4]+1272893353|0)<<11|o>>>21)+n|0)^n^i)+t[7]-155497632|0)<<16|r>>>16)+o|0)^o^n)+t[10]-1094730640|0)<<23|i>>>9)+r|0,i=((i+=((r=((r+=((o=((o+=((n=((n+=(i^r^o)+t[13]+681279174|0)<<4|n>>>28)+i|0)^i^r)+t[0]-358537222|0)<<11|o>>>21)+n|0)^n^i)+t[3]-722521979|0)<<16|r>>>16)+o|0)^o^n)+t[6]+76029189|0)<<23|i>>>9)+r|0,i=((i+=((r=((r+=((o=((o+=((n=((n+=(i^r^o)+t[9]-640364487|0)<<4|n>>>28)+i|0)^i^r)+t[12]-421815835|0)<<11|o>>>21)+n|0)^n^i)+t[15]+530742520|0)<<16|r>>>16)+o|0)^o^n)+t[2]-995338651|0)<<23|i>>>9)+r|0,i=((i+=((o=((o+=(i^((n=((n+=(r^(i|~o))+t[0]-198630844|0)<<6|n>>>26)+i|0)|~r))+t[7]+1126891415|0)<<10|o>>>22)+n|0)^((r=((r+=(n^(o|~i))+t[14]-1416354905|0)<<15|r>>>17)+o|0)|~n))+t[5]-57434055|0)<<21|i>>>11)+r|0,i=((i+=((o=((o+=(i^((n=((n+=(r^(i|~o))+t[12]+1700485571|0)<<6|n>>>26)+i|0)|~r))+t[3]-1894986606|0)<<10|o>>>22)+n|0)^((r=((r+=(n^(o|~i))+t[10]-1051523|0)<<15|r>>>17)+o|0)|~n))+t[1]-2054922799|0)<<21|i>>>11)+r|0,i=((i+=((o=((o+=(i^((n=((n+=(r^(i|~o))+t[8]+1873313359|0)<<6|n>>>26)+i|0)|~r))+t[15]-30611744|0)<<10|o>>>22)+n|0)^((r=((r+=(n^(o|~i))+t[6]-1560198380|0)<<15|r>>>17)+o|0)|~n))+t[13]+1309151649|0)<<21|i>>>11)+r|0,i=((i+=((o=((o+=(i^((n=((n+=(r^(i|~o))+t[4]-145523070|0)<<6|n>>>26)+i|0)|~r))+t[11]-1120210379|0)<<10|o>>>22)+n|0)^((r=((r+=(n^(o|~i))+t[2]+718787259|0)<<15|r>>>17)+o|0)|~n))+t[9]-343485551|0)<<21|i>>>11)+r|0,e[0]=n+e[0]|0,e[1]=i+e[1]|0,e[2]=r+e[2]|0,e[3]=o+e[3]|0}function i(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e.charCodeAt(t)+(e.charCodeAt(t+1)<<8)+(e.charCodeAt(t+2)<<16)+(e.charCodeAt(t+3)<<24);return n}function r(e){var t,n=[];for(t=0;t<64;t+=4)n[t>>2]=e[t]+(e[t+1]<<8)+(e[t+2]<<16)+(e[t+3]<<24);return n}function o(e){var t,r,o,s,a,l,c=e.length,u=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=c;t+=64)n(u,i(e.substring(t-64,t)));for(r=(e=e.substring(t-64)).length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<r;t+=1)o[t>>2]|=e.charCodeAt(t)<<(t%4<<3);if(o[t>>2]|=128<<(t%4<<3),t>55)for(n(u,o),t=0;t<16;t+=1)o[t]=0;return s=(s=8*c).toString(16).match(/(.*?)(.{0,8})$/),a=parseInt(s[2],16),l=parseInt(s[1],16)||0,o[14]=a,o[15]=l,n(u,o),u}function s(e){var t,i,o,s,a,l,c=e.length,u=[1732584193,-271733879,-1732584194,271733878];for(t=64;t<=c;t+=64)n(u,r(e.subarray(t-64,t)));for(i=(e=t-64<c?e.subarray(t-64):new Uint8Array(0)).length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],t=0;t<i;t+=1)o[t>>2]|=e[t]<<(t%4<<3);if(o[t>>2]|=128<<(t%4<<3),t>55)for(n(u,o),t=0;t<16;t+=1)o[t]=0;return s=(s=8*c).toString(16).match(/(.*?)(.{0,8})$/),a=parseInt(s[2],16),l=parseInt(s[1],16)||0,o[14]=a,o[15]=l,n(u,o),u}function a(e){var n,i="";for(n=0;n<4;n+=1)i+=t[e>>8*n+4&15]+t[e>>8*n&15];return i}function l(e){var t;for(t=0;t<e.length;t+=1)e[t]=a(e[t]);return e.join("")}function c(e){return/[\u0080-\uFFFF]/.test(e)&&(e=unescape(encodeURIComponent(e))),e}function u(e,t){var n,i=e.length,r=new ArrayBuffer(i),o=new Uint8Array(r);for(n=0;n<i;n+=1)o[n]=e.charCodeAt(n);return t?o:r}function h(e){return String.fromCharCode.apply(null,new Uint8Array(e))}function d(e,t,n){var i=new Uint8Array(e.byteLength+t.byteLength);return i.set(new Uint8Array(e)),i.set(new Uint8Array(t),e.byteLength),n?i:i.buffer}function f(e){var t,n=[],i=e.length;for(t=0;t<i-1;t+=2)n.push(parseInt(e.substr(t,2),16));return String.fromCharCode.apply(String,n)}function p(){this.reset()}return l(o("hello")),"undefined"==typeof ArrayBuffer||ArrayBuffer.prototype.slice||function(){function t(e,t){return(e=0|e||0)<0?Math.max(e+t,0):Math.min(e,t)}ArrayBuffer.prototype.slice=function(n,i){var r,o,s,a,l=this.byteLength,c=t(n,l),u=l;return i!==e&&(u=t(i,l)),c>u?new ArrayBuffer(0):(r=u-c,o=new ArrayBuffer(r),s=new Uint8Array(o),a=new Uint8Array(this,c,r),s.set(a),o)}}(),p.prototype.append=function(e){return this.appendBinary(c(e)),this},p.prototype.appendBinary=function(e){this._buff+=e,this._length+=e.length;var t,r=this._buff.length;for(t=64;t<=r;t+=64)n(this._hash,i(this._buff.substring(t-64,t)));return this._buff=this._buff.substring(t-64),this},p.prototype.end=function(e){var t,n,i=this._buff,r=i.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<r;t+=1)o[t>>2]|=i.charCodeAt(t)<<(t%4<<3);return this._finish(o,r),n=l(this._hash),e&&(n=f(n)),this.reset(),n},p.prototype.reset=function(){return this._buff="",this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},p.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash.slice()}},p.prototype.setState=function(e){return this._buff=e.buff,this._length=e.length,this._hash=e.hash,this},p.prototype.destroy=function(){delete this._hash,delete this._buff,delete this._length},p.prototype._finish=function(e,t){var i,r,o,s=t;if(e[s>>2]|=128<<(s%4<<3),s>55)for(n(this._hash,e),s=0;s<16;s+=1)e[s]=0;i=(i=8*this._length).toString(16).match(/(.*?)(.{0,8})$/),r=parseInt(i[2],16),o=parseInt(i[1],16)||0,e[14]=r,e[15]=o,n(this._hash,e)},p.hash=function(e,t){return p.hashBinary(c(e),t)},p.hashBinary=function(e,t){var n=l(o(e));return t?f(n):n},p.ArrayBuffer=function(){this.reset()},p.ArrayBuffer.prototype.append=function(e){var t,i=d(this._buff.buffer,e,!0),o=i.length;for(this._length+=e.byteLength,t=64;t<=o;t+=64)n(this._hash,r(i.subarray(t-64,t)));return this._buff=t-64<o?new Uint8Array(i.buffer.slice(t-64)):new Uint8Array(0),this},p.ArrayBuffer.prototype.end=function(e){var t,n,i=this._buff,r=i.length,o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(t=0;t<r;t+=1)o[t>>2]|=i[t]<<(t%4<<3);return this._finish(o,r),n=l(this._hash),e&&(n=f(n)),this.reset(),n},p.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},p.ArrayBuffer.prototype.getState=function(){var e=p.prototype.getState.call(this);return e.buff=h(e.buff),e},p.ArrayBuffer.prototype.setState=function(e){return e.buff=u(e.buff,!0),p.prototype.setState.call(this,e)},p.ArrayBuffer.prototype.destroy=p.prototype.destroy,p.ArrayBuffer.prototype._finish=p.prototype._finish,p.ArrayBuffer.hash=function(e,t){var n=l(s(new Uint8Array(e)));return t?f(n):n},p}()},751:(e,t,n)=>{!function(e){"use strict";var t="CodeMirror-hint",n="CodeMirror-hint-active";function i(e,t){if(this.cm=e,this.options=t,this.widget=null,this.debounce=0,this.tick=0,this.startPos=this.cm.getCursor("start"),this.startLen=this.cm.getLine(this.startPos.line).length-this.cm.getSelection().length,this.options.updateOnCursorActivity){var n=this;e.on("cursorActivity",this.activityFunc=function(){n.cursorActivity()})}}e.showHint=function(e,t,n){if(!t)return e.showHint(n);n&&n.async&&(t.async=!0);var i={hint:t};if(n)for(var r in n)i[r]=n[r];return e.showHint(i)},e.defineExtension("showHint",(function(t){t=s(this,this.getCursor("start"),t);var n=this.listSelections();if(!(n.length>1)){if(this.somethingSelected()){if(!t.hint.supportsSelection)return;for(var r=0;r<n.length;r++)if(n[r].head.line!=n[r].anchor.line)return}this.state.completionActive&&this.state.completionActive.close();var o=this.state.completionActive=new i(this,t);o.options.hint&&(e.signal(this,"startCompletion",this),o.update(!0))}})),e.defineExtension("closeHint",(function(){this.state.completionActive&&this.state.completionActive.close()}));var r=window.requestAnimationFrame||function(e){return setTimeout(e,1e3/60)},o=window.cancelAnimationFrame||clearTimeout;function s(e,t,n){var i=e.options.hintOptions,r={};for(var o in p)r[o]=p[o];if(i)for(var o in i)void 0!==i[o]&&(r[o]=i[o]);if(n)for(var o in n)void 0!==n[o]&&(r[o]=n[o]);return r.hint.resolve&&(r.hint=r.hint.resolve(e,t)),r}function a(e){return"string"==typeof e?e:e.text}function l(e,t){var n={Up:function(){t.moveFocus(-1)},Down:function(){t.moveFocus(1)},PageUp:function(){t.moveFocus(1-t.menuSize(),!0)},PageDown:function(){t.moveFocus(t.menuSize()-1,!0)},Home:function(){t.setFocus(0)},End:function(){t.setFocus(t.length-1)},Enter:t.pick,Tab:t.pick,Esc:t.close};/Mac/.test(navigator.platform)&&(n["Ctrl-P"]=function(){t.moveFocus(-1)},n["Ctrl-N"]=function(){t.moveFocus(1)});var i=e.options.customKeys,r=i?{}:n;function o(e,i){var o;o="string"!=typeof i?function(e){return i(e,t)}:n.hasOwnProperty(i)?n[i]:i,r[e]=o}if(i)for(var s in i)i.hasOwnProperty(s)&&o(s,i[s]);var a=e.options.extraKeys;if(a)for(var s in a)a.hasOwnProperty(s)&&o(s,a[s]);return r}function c(e,t){for(;t&&t!=e;){if("LI"===t.nodeName.toUpperCase()&&t.parentNode==e)return t;t=t.parentNode}}function u(i,r){this.id="cm-complete-"+Math.floor(Math.random(1e6)),this.completion=i,this.data=r,this.picked=!1;var o=this,s=i.cm,u=s.getInputField().ownerDocument,h=u.defaultView||u.parentWindow,d=this.hints=u.createElement("ul");d.setAttribute("role","listbox"),d.setAttribute("aria-expanded","true"),d.id=this.id;var f=i.cm.options.theme;d.className="CodeMirror-hints "+f,this.selectedHint=r.selectedHint||0;for(var p=r.list,g=0;g<p.length;++g){var m=d.appendChild(u.createElement("li")),v=p[g],y=t+(g!=this.selectedHint?"":" "+n);null!=v.className&&(y=v.className+" "+y),m.className=y,g==this.selectedHint&&m.setAttribute("aria-selected","true"),m.id=this.id+"-"+g,m.setAttribute("role","option"),v.render?v.render(m,r,v):m.appendChild(u.createTextNode(v.displayText||a(v))),m.hintId=g}var b=i.options.container||u.body,_=s.cursorCoords(i.options.alignWithWord?r.from:null),w=_.left,x=_.bottom,k=!0,C=0,S=0;if(b!==u.body){var L=-1!==["absolute","relative","fixed"].indexOf(h.getComputedStyle(b).position)?b:b.offsetParent,T=L.getBoundingClientRect(),A=u.body.getBoundingClientRect();C=T.left-A.left-L.scrollLeft,S=T.top-A.top-L.scrollTop}d.style.left=w-C+"px",d.style.top=x-S+"px";var M=h.innerWidth||Math.max(u.body.offsetWidth,u.documentElement.offsetWidth),O=h.innerHeight||Math.max(u.body.offsetHeight,u.documentElement.offsetHeight);b.appendChild(d),s.getInputField().setAttribute("aria-autocomplete","list"),s.getInputField().setAttribute("aria-owns",this.id),s.getInputField().setAttribute("aria-activedescendant",this.id+"-"+this.selectedHint);var E,N=i.options.moveOnOverlap?d.getBoundingClientRect():new DOMRect,D=!!i.options.paddingForScrollbar&&d.scrollHeight>d.clientHeight+1;if(setTimeout((function(){E=s.getScrollInfo()})),N.bottom-O>0){var P=N.bottom-N.top,H=N.top-(_.bottom-_.top)-2;O-N.top<H?(P>H&&(d.style.height=(P=H)+"px"),d.style.top=(x=_.top-P)+S+"px",k=!1):d.style.height=O-N.top-2+"px"}var W,I=N.right-M;if(D&&(I+=s.display.nativeBarWidth),I>0&&(N.right-N.left>M&&(d.style.width=M-5+"px",I-=N.right-N.left-M),d.style.left=(w=Math.max(_.left-I-C,0))+"px"),D)for(var F=d.firstChild;F;F=F.nextSibling)F.style.paddingRight=s.display.nativeBarWidth+"px";s.addKeyMap(this.keyMap=l(i,{moveFocus:function(e,t){o.changeActive(o.selectedHint+e,t)},setFocus:function(e){o.changeActive(e)},menuSize:function(){return o.screenAmount()},length:p.length,close:function(){i.close()},pick:function(){o.pick()},data:r})),i.options.closeOnUnfocus&&(s.on("blur",this.onBlur=function(){W=setTimeout((function(){i.close()}),100)}),s.on("focus",this.onFocus=function(){clearTimeout(W)})),s.on("scroll",this.onScroll=function(){var e=s.getScrollInfo(),t=s.getWrapperElement().getBoundingClientRect();E||(E=s.getScrollInfo());var n=x+E.top-e.top,r=n-(h.pageYOffset||(u.documentElement||u.body).scrollTop);if(k||(r+=d.offsetHeight),r<=t.top||r>=t.bottom)return i.close();d.style.top=n+"px",d.style.left=w+E.left-e.left+"px"}),e.on(d,"dblclick",(function(e){var t=c(d,e.target||e.srcElement);t&&null!=t.hintId&&(o.changeActive(t.hintId),o.pick())})),e.on(d,"click",(function(e){var t=c(d,e.target||e.srcElement);t&&null!=t.hintId&&(o.changeActive(t.hintId),i.options.completeOnSingleClick&&o.pick())})),e.on(d,"mousedown",(function(){setTimeout((function(){s.focus()}),20)}));var B=this.getSelectedHintRange();return 0===B.from&&0===B.to||this.scrollToActive(),e.signal(r,"select",p[this.selectedHint],d.childNodes[this.selectedHint]),!0}function h(e,t){if(!e.somethingSelected())return t;for(var n=[],i=0;i<t.length;i++)t[i].supportsSelection&&n.push(t[i]);return n}function d(e,t,n,i){if(e.async)e(t,i,n);else{var r=e(t,n);r&&r.then?r.then(i):i(r)}}function f(t,n){var i,r=t.getHelpers(n,"hint");if(r.length){var o=function(e,t,n){var i=h(e,r);function o(r){if(r==i.length)return t(null);d(i[r],e,n,(function(e){e&&e.list.length>0?t(e):o(r+1)}))}o(0)};return o.async=!0,o.supportsSelection=!0,o}return(i=t.getHelper(t.getCursor(),"hintWords"))?function(t){return e.hint.fromList(t,{words:i})}:e.hint.anyword?function(t,n){return e.hint.anyword(t,n)}:function(){}}i.prototype={close:function(){this.active()&&(this.cm.state.completionActive=null,this.tick=null,this.options.updateOnCursorActivity&&this.cm.off("cursorActivity",this.activityFunc),this.widget&&this.data&&e.signal(this.data,"close"),this.widget&&this.widget.close(),e.signal(this.cm,"endCompletion",this.cm))},active:function(){return this.cm.state.completionActive==this},pick:function(t,n){var i=t.list[n],r=this;this.cm.operation((function(){i.hint?i.hint(r.cm,t,i):r.cm.replaceRange(a(i),i.from||t.from,i.to||t.to,"complete"),e.signal(t,"pick",i),r.cm.scrollIntoView()})),this.options.closeOnPick&&this.close()},cursorActivity:function(){this.debounce&&(o(this.debounce),this.debounce=0);var e=this.startPos;this.data&&(e=this.data.from);var t=this.cm.getCursor(),n=this.cm.getLine(t.line);if(t.line!=this.startPos.line||n.length-t.ch!=this.startLen-this.startPos.ch||t.ch<e.ch||this.cm.somethingSelected()||!t.ch||this.options.closeCharacters.test(n.charAt(t.ch-1)))this.close();else{var i=this;this.debounce=r((function(){i.update()})),this.widget&&this.widget.disable()}},update:function(e){if(null!=this.tick){var t=this,n=++this.tick;d(this.options.hint,this.cm,this.options,(function(i){t.tick==n&&t.finishUpdate(i,e)}))}},finishUpdate:function(t,n){this.data&&e.signal(this.data,"update");var i=this.widget&&this.widget.picked||n&&this.options.completeSingle;this.widget&&this.widget.close(),this.data=t,t&&t.list.length&&(i&&1==t.list.length?this.pick(t,0):(this.widget=new u(this,t),e.signal(t,"shown")))}},u.prototype={close:function(){if(this.completion.widget==this){this.completion.widget=null,this.hints.parentNode&&this.hints.parentNode.removeChild(this.hints),this.completion.cm.removeKeyMap(this.keyMap);var e=this.completion.cm.getInputField();e.removeAttribute("aria-activedescendant"),e.removeAttribute("aria-owns");var t=this.completion.cm;this.completion.options.closeOnUnfocus&&(t.off("blur",this.onBlur),t.off("focus",this.onFocus)),t.off("scroll",this.onScroll)}},disable:function(){this.completion.cm.removeKeyMap(this.keyMap);var e=this;this.keyMap={Enter:function(){e.picked=!0}},this.completion.cm.addKeyMap(this.keyMap)},pick:function(){this.completion.pick(this.data,this.selectedHint)},changeActive:function(t,i){if(t>=this.data.list.length?t=i?this.data.list.length-1:0:t<0&&(t=i?0:this.data.list.length-1),this.selectedHint!=t){var r=this.hints.childNodes[this.selectedHint];r&&(r.className=r.className.replace(" "+n,""),r.removeAttribute("aria-selected")),(r=this.hints.childNodes[this.selectedHint=t]).className+=" "+n,r.setAttribute("aria-selected","true"),this.completion.cm.getInputField().setAttribute("aria-activedescendant",r.id),this.scrollToActive(),e.signal(this.data,"select",this.data.list[this.selectedHint],r)}},scrollToActive:function(){var e=this.getSelectedHintRange(),t=this.hints.childNodes[e.from],n=this.hints.childNodes[e.to],i=this.hints.firstChild;t.offsetTop<this.hints.scrollTop?this.hints.scrollTop=t.offsetTop-i.offsetTop:n.offsetTop+n.offsetHeight>this.hints.scrollTop+this.hints.clientHeight&&(this.hints.scrollTop=n.offsetTop+n.offsetHeight-this.hints.clientHeight+i.offsetTop)},screenAmount:function(){return Math.floor(this.hints.clientHeight/this.hints.firstChild.offsetHeight)||1},getSelectedHintRange:function(){var e=this.completion.options.scrollMargin||0;return{from:Math.max(0,this.selectedHint-e),to:Math.min(this.data.list.length-1,this.selectedHint+e)}}},e.registerHelper("hint","auto",{resolve:f}),e.registerHelper("hint","fromList",(function(t,n){var i,r=t.getCursor(),o=t.getTokenAt(r),s=e.Pos(r.line,o.start),a=r;o.start<r.ch&&/\w/.test(o.string.charAt(r.ch-o.start-1))?i=o.string.substr(0,r.ch-o.start):(i="",s=r);for(var l=[],c=0;c<n.words.length;c++){var u=n.words[c];u.slice(0,i.length)==i&&l.push(u)}if(l.length)return{list:l,from:s,to:a}})),e.commands.autocomplete=e.showHint;var p={hint:e.hint.auto,completeSingle:!0,alignWithWord:!0,closeCharacters:/[\s()\[\]{};:>,]/,closeOnPick:!0,closeOnUnfocus:!0,updateOnCursorActivity:!0,completeOnSingleClick:!0,container:null,customKeys:null,extraKeys:null,paddingForScrollbar:!0,moveOnOverlap:!0};e.defineOption("hintOptions",null)}(n(237))},761:(e,t,n)=>{"use strict";n.r(t)},804:()=>{var e;(e=jQuery).fn.chosenClassPrefix=function(){return e(this).is('[class^="chzn-"]')?"chzn":"chosen"},e.fn.chosenOrder=function(){var t=this.filter("."+this.chosenClassPrefix()+"-sortable[multiple]").first(),n=t.siblings("."+this.chosenClassPrefix()+"-container");return e(n.find("."+this.chosenClassPrefix()+'-choices li[class!="search-field"]').map((function(){return this?t.find("option:contains("+e(this).text().trim()+")")[0]:void 0})))},e.fn.chosenSortable=function(){this.filter("."+this.chosenClassPrefix()+"-sortable[multiple]").each((function(){var t=e(this),n=t.siblings("."+t.chosenClassPrefix()+"-container");e.ui?(n.find("."+t.chosenClassPrefix()+"-choices").bind("mousedown",(function(t){e(t.target).is("span")&&t.stopPropagation()})),n.find("."+t.chosenClassPrefix()+"-choices").sortable({placeholder:"search-choice-placeholder",items:"li:not(.search-field)",tolerance:"pointer",start:function(e,t){t.placeholder.width(t.item.innerWidth()),t.placeholder.height(t.item.innerHeight())},update:function(e,n){t.trigger("chosen_sortabled")}}),t.closest("form")&&t.closest("form").bind("submit",(function(){var e=t.chosenOrder();t.children().remove(),t.append(e)}))):console.error("jquery-chosen-sortable requires JQuery UI to have been initialised.")}))}},907:(e,t,n)=>{"use strict";n.r(t)},927:(e,t,n)=>{!function(e){"use strict";var t={active:1,after:1,before:1,checked:1,default:1,disabled:1,empty:1,enabled:1,"first-child":1,"first-letter":1,"first-line":1,"first-of-type":1,focus:1,hover:1,"in-range":1,indeterminate:1,invalid:1,lang:1,"last-child":1,"last-of-type":1,link:1,not:1,"nth-child":1,"nth-last-child":1,"nth-last-of-type":1,"nth-of-type":1,"only-of-type":1,"only-child":1,optional:1,"out-of-range":1,placeholder:1,"read-only":1,"read-write":1,required:1,root:1,selection:1,target:1,valid:1,visited:1};e.registerHelper("hint","css",(function(n){var i=n.getCursor(),r=n.getTokenAt(i),o=e.innerMode(n.getMode(),r.state);if("css"==o.mode.name){if("keyword"==r.type&&0=="!important".indexOf(r.string))return{list:["!important"],from:e.Pos(i.line,r.start),to:e.Pos(i.line,r.end)};var s=r.start,a=i.ch,l=r.string.slice(0,a-s);/[^\w$_-]/.test(l)&&(l="",s=a=i.ch);var c=e.resolveMode("text/css"),u=[],h=o.state.state;return"pseudo"==h||"variable-3"==r.type?d(t):"block"==h||"maybeprop"==h?d(c.propertyKeywords):"prop"==h||"parens"==h||"at"==h||"params"==h?(d(c.valueKeywords),d(c.colorKeywords)):"media"!=h&&"media_parens"!=h||(d(c.mediaTypes),d(c.mediaFeatures)),u.length?{list:u,from:e.Pos(i.line,s),to:e.Pos(i.line,a)}:void 0}function d(e){for(var t in e)l&&0!=t.lastIndexOf(l,0)||u.push(t)}}))}(n(237),n(656))}},t={};function n(i){var r=t[i];if(void 0!==r)return r.exports;var o=t[i]={exports:{}};return e[i].call(o.exports,o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var i in t)n.o(t,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";const e=jQuery;var t=n.n(e),i=n(237),r=n.n(i);n(751),n(927);function o(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function s(e){return e instanceof o(e).Element||e instanceof Element}function a(e){return e instanceof o(e).HTMLElement||e instanceof HTMLElement}function l(e){return"undefined"!=typeof ShadowRoot&&(e instanceof o(e).ShadowRoot||e instanceof ShadowRoot)}var c=Math.max,u=Math.min,h=Math.round;function d(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map((function(e){return e.brand+"/"+e.version})).join(" "):navigator.userAgent}function f(){return!/^((?!chrome|android).)*safari/i.test(d())}function p(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var i=e.getBoundingClientRect(),r=1,l=1;t&&a(e)&&(r=e.offsetWidth>0&&h(i.width)/e.offsetWidth||1,l=e.offsetHeight>0&&h(i.height)/e.offsetHeight||1);var c=(s(e)?o(e):window).visualViewport,u=!f()&&n,d=(i.left+(u&&c?c.offsetLeft:0))/r,p=(i.top+(u&&c?c.offsetTop:0))/l,g=i.width/r,m=i.height/l;return{width:g,height:m,top:p,right:d+g,bottom:p+m,left:d,x:d,y:p}}function g(e){var t=o(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function m(e){return e?(e.nodeName||"").toLowerCase():null}function v(e){return((s(e)?e.ownerDocument:e.document)||window.document).documentElement}function y(e){return p(v(e)).left+g(e).scrollLeft}function b(e){return o(e).getComputedStyle(e)}function _(e){var t=b(e),n=t.overflow,i=t.overflowX,r=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+r+i)}function w(e,t,n){void 0===n&&(n=!1);var i,r,s=a(t),l=a(t)&&function(e){var t=e.getBoundingClientRect(),n=h(t.width)/e.offsetWidth||1,i=h(t.height)/e.offsetHeight||1;return 1!==n||1!==i}(t),c=v(t),u=p(e,l,n),d={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(s||!s&&!n)&&(("body"!==m(t)||_(c))&&(d=(i=t)!==o(i)&&a(i)?{scrollLeft:(r=i).scrollLeft,scrollTop:r.scrollTop}:g(i)),a(t)?((f=p(t,!0)).x+=t.clientLeft,f.y+=t.clientTop):c&&(f.x=y(c))),{x:u.left+d.scrollLeft-f.x,y:u.top+d.scrollTop-f.y,width:u.width,height:u.height}}function x(e){var t=p(e),n=e.offsetWidth,i=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-i)<=1&&(i=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:i}}function k(e){return"html"===m(e)?e:e.assignedSlot||e.parentNode||(l(e)?e.host:null)||v(e)}function C(e){return["html","body","#document"].indexOf(m(e))>=0?e.ownerDocument.body:a(e)&&_(e)?e:C(k(e))}function S(e,t){var n;void 0===t&&(t=[]);var i=C(e),r=i===(null==(n=e.ownerDocument)?void 0:n.body),s=o(i),a=r?[s].concat(s.visualViewport||[],_(i)?i:[]):i,l=t.concat(a);return r?l:l.concat(S(k(a)))}function L(e){return["table","td","th"].indexOf(m(e))>=0}function T(e){return a(e)&&"fixed"!==b(e).position?e.offsetParent:null}function A(e){for(var t=o(e),n=T(e);n&&L(n)&&"static"===b(n).position;)n=T(n);return n&&("html"===m(n)||"body"===m(n)&&"static"===b(n).position)?t:n||function(e){var t=/firefox/i.test(d());if(/Trident/i.test(d())&&a(e)&&"fixed"===b(e).position)return null;var n=k(e);for(l(n)&&(n=n.host);a(n)&&["html","body"].indexOf(m(n))<0;){var i=b(n);if("none"!==i.transform||"none"!==i.perspective||"paint"===i.contain||-1!==["transform","perspective"].indexOf(i.willChange)||t&&"filter"===i.willChange||t&&i.filter&&"none"!==i.filter)return n;n=n.parentNode}return null}(e)||t}var M="top",O="bottom",E="right",N="left",D="auto",P=[M,O,E,N],H="start",W="end",I="viewport",F="popper",B=P.reduce((function(e,t){return e.concat([t+"-"+H,t+"-"+W])}),[]),R=[].concat(P,[D]).reduce((function(e,t){return e.concat([t,t+"-"+H,t+"-"+W])}),[]),z=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function j(e){var t=new Map,n=new Set,i=[];function r(e){n.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach((function(e){if(!n.has(e)){var i=t.get(e);i&&r(i)}})),i.push(e)}return e.forEach((function(e){t.set(e.name,e)})),e.forEach((function(e){n.has(e.name)||r(e)})),i}var q={placement:"bottom",modifiers:[],strategy:"absolute"};function U(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some((function(e){return!(e&&"function"==typeof e.getBoundingClientRect)}))}function $(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,i=void 0===n?[]:n,r=t.defaultOptions,o=void 0===r?q:r;return function(e,t,n){void 0===n&&(n=o);var r,a,l={placement:"bottom",orderedModifiers:[],options:Object.assign({},q,o),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},c=[],u=!1,h={state:l,setOptions:function(n){var r="function"==typeof n?n(l.options):n;d(),l.options=Object.assign({},o,l.options,r),l.scrollParents={reference:s(e)?S(e):e.contextElement?S(e.contextElement):[],popper:S(t)};var a,u,f=function(e){var t=j(e);return z.reduce((function(e,n){return e.concat(t.filter((function(e){return e.phase===n})))}),[])}((a=[].concat(i,l.options.modifiers),u=a.reduce((function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e}),{}),Object.keys(u).map((function(e){return u[e]}))));return l.orderedModifiers=f.filter((function(e){return e.enabled})),l.orderedModifiers.forEach((function(e){var t=e.name,n=e.options,i=void 0===n?{}:n,r=e.effect;if("function"==typeof r){var o=r({state:l,name:t,instance:h,options:i}),s=function(){};c.push(o||s)}})),h.update()},forceUpdate:function(){if(!u){var e=l.elements,t=e.reference,n=e.popper;if(U(t,n)){l.rects={reference:w(t,A(n),"fixed"===l.options.strategy),popper:x(n)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach((function(e){return l.modifiersData[e.name]=Object.assign({},e.data)}));for(var i=0;i<l.orderedModifiers.length;i++)if(!0!==l.reset){var r=l.orderedModifiers[i],o=r.fn,s=r.options,a=void 0===s?{}:s,c=r.name;"function"==typeof o&&(l=o({state:l,options:a,name:c,instance:h})||l)}else l.reset=!1,i=-1}}},update:(r=function(){return new Promise((function(e){h.forceUpdate(),e(l)}))},function(){return a||(a=new Promise((function(e){Promise.resolve().then((function(){a=void 0,e(r())}))}))),a}),destroy:function(){d(),u=!0}};if(!U(e,t))return h;function d(){c.forEach((function(e){return e()})),c=[]}return h.setOptions(n).then((function(e){!u&&n.onFirstUpdate&&n.onFirstUpdate(e)})),h}}var V={passive:!0};function K(e){return e.split("-")[0]}function G(e){return e.split("-")[1]}function X(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Y(e){var t,n=e.reference,i=e.element,r=e.placement,o=r?K(r):null,s=r?G(r):null,a=n.x+n.width/2-i.width/2,l=n.y+n.height/2-i.height/2;switch(o){case M:t={x:a,y:n.y-i.height};break;case O:t={x:a,y:n.y+n.height};break;case E:t={x:n.x+n.width,y:l};break;case N:t={x:n.x-i.width,y:l};break;default:t={x:n.x,y:n.y}}var c=o?X(o):null;if(null!=c){var u="y"===c?"height":"width";switch(s){case H:t[c]=t[c]-(n[u]/2-i[u]/2);break;case W:t[c]=t[c]+(n[u]/2-i[u]/2)}}return t}var J={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Z(e){var t,n=e.popper,i=e.popperRect,r=e.placement,s=e.variation,a=e.offsets,l=e.position,c=e.gpuAcceleration,u=e.adaptive,d=e.roundOffsets,f=e.isFixed,p=a.x,g=void 0===p?0:p,m=a.y,y=void 0===m?0:m,_="function"==typeof d?d({x:g,y}):{x:g,y};g=_.x,y=_.y;var w=a.hasOwnProperty("x"),x=a.hasOwnProperty("y"),k=N,C=M,S=window;if(u){var L=A(n),T="clientHeight",D="clientWidth";if(L===o(n)&&"static"!==b(L=v(n)).position&&"absolute"===l&&(T="scrollHeight",D="scrollWidth"),r===M||(r===N||r===E)&&s===W)C=O,y-=(f&&L===S&&S.visualViewport?S.visualViewport.height:L[T])-i.height,y*=c?1:-1;if(r===N||(r===M||r===O)&&s===W)k=E,g-=(f&&L===S&&S.visualViewport?S.visualViewport.width:L[D])-i.width,g*=c?1:-1}var P,H=Object.assign({position:l},u&&J),I=!0===d?function(e,t){var n=e.x,i=e.y,r=t.devicePixelRatio||1;return{x:h(n*r)/r||0,y:h(i*r)/r||0}}({x:g,y},o(n)):{x:g,y};return g=I.x,y=I.y,c?Object.assign({},H,((P={})[C]=x?"0":"",P[k]=w?"0":"",P.transform=(S.devicePixelRatio||1)<=1?"translate("+g+"px, "+y+"px)":"translate3d("+g+"px, "+y+"px, 0)",P)):Object.assign({},H,((t={})[C]=x?y+"px":"",t[k]=w?g+"px":"",t.transform="",t))}const Q={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach((function(e){var n=t.styles[e]||{},i=t.attributes[e]||{},r=t.elements[e];a(r)&&m(r)&&(Object.assign(r.style,n),Object.keys(i).forEach((function(e){var t=i[e];!1===t?r.removeAttribute(e):r.setAttribute(e,!0===t?"":t)})))}))},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach((function(e){var i=t.elements[e],r=t.attributes[e]||{},o=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce((function(e,t){return e[t]="",e}),{});a(i)&&m(i)&&(Object.assign(i.style,o),Object.keys(r).forEach((function(e){i.removeAttribute(e)})))}))}},requires:["computeStyles"]};const ee={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,i=e.name,r=n.offset,o=void 0===r?[0,0]:r,s=R.reduce((function(e,n){return e[n]=function(e,t,n){var i=K(e),r=[N,M].indexOf(i)>=0?-1:1,o="function"==typeof n?n(Object.assign({},t,{placement:e})):n,s=o[0],a=o[1];return s=s||0,a=(a||0)*r,[N,E].indexOf(i)>=0?{x:a,y:s}:{x:s,y:a}}(n,t.rects,o),e}),{}),a=s[t.placement],l=a.x,c=a.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=c),t.modifiersData[i]=s}};var te={left:"right",right:"left",bottom:"top",top:"bottom"};function ne(e){return e.replace(/left|right|bottom|top/g,(function(e){return te[e]}))}var ie={start:"end",end:"start"};function re(e){return e.replace(/start|end/g,(function(e){return ie[e]}))}function oe(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&l(n)){var i=t;do{if(i&&e.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function se(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ae(e,t,n){return t===I?se(function(e,t){var n=o(e),i=v(e),r=n.visualViewport,s=i.clientWidth,a=i.clientHeight,l=0,c=0;if(r){s=r.width,a=r.height;var u=f();(u||!u&&"fixed"===t)&&(l=r.offsetLeft,c=r.offsetTop)}return{width:s,height:a,x:l+y(e),y:c}}(e,n)):s(t)?function(e,t){var n=p(e,!1,"fixed"===t);return n.top=n.top+e.clientTop,n.left=n.left+e.clientLeft,n.bottom=n.top+e.clientHeight,n.right=n.left+e.clientWidth,n.width=e.clientWidth,n.height=e.clientHeight,n.x=n.left,n.y=n.top,n}(t,n):se(function(e){var t,n=v(e),i=g(e),r=null==(t=e.ownerDocument)?void 0:t.body,o=c(n.scrollWidth,n.clientWidth,r?r.scrollWidth:0,r?r.clientWidth:0),s=c(n.scrollHeight,n.clientHeight,r?r.scrollHeight:0,r?r.clientHeight:0),a=-i.scrollLeft+y(e),l=-i.scrollTop;return"rtl"===b(r||n).direction&&(a+=c(n.clientWidth,r?r.clientWidth:0)-o),{width:o,height:s,x:a,y:l}}(v(e)))}function le(e,t,n,i){var r="clippingParents"===t?function(e){var t=S(k(e)),n=["absolute","fixed"].indexOf(b(e).position)>=0&&a(e)?A(e):e;return s(n)?t.filter((function(e){return s(e)&&oe(e,n)&&"body"!==m(e)})):[]}(e):[].concat(t),o=[].concat(r,[n]),l=o[0],h=o.reduce((function(t,n){var r=ae(e,n,i);return t.top=c(r.top,t.top),t.right=u(r.right,t.right),t.bottom=u(r.bottom,t.bottom),t.left=c(r.left,t.left),t}),ae(e,l,i));return h.width=h.right-h.left,h.height=h.bottom-h.top,h.x=h.left,h.y=h.top,h}function ce(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function ue(e,t){return t.reduce((function(t,n){return t[n]=e,t}),{})}function he(e,t){void 0===t&&(t={});var n=t,i=n.placement,r=void 0===i?e.placement:i,o=n.strategy,a=void 0===o?e.strategy:o,l=n.boundary,c=void 0===l?"clippingParents":l,u=n.rootBoundary,h=void 0===u?I:u,d=n.elementContext,f=void 0===d?F:d,g=n.altBoundary,m=void 0!==g&&g,y=n.padding,b=void 0===y?0:y,_=ce("number"!=typeof b?b:ue(b,P)),w=f===F?"reference":F,x=e.rects.popper,k=e.elements[m?w:f],C=le(s(k)?k:k.contextElement||v(e.elements.popper),c,h,a),S=p(e.elements.reference),L=Y({reference:S,element:x,strategy:"absolute",placement:r}),T=se(Object.assign({},x,L)),A=f===F?T:S,N={top:C.top-A.top+_.top,bottom:A.bottom-C.bottom+_.bottom,left:C.left-A.left+_.left,right:A.right-C.right+_.right},D=e.modifiersData.offset;if(f===F&&D){var H=D[r];Object.keys(N).forEach((function(e){var t=[E,O].indexOf(e)>=0?1:-1,n=[M,O].indexOf(e)>=0?"y":"x";N[e]+=H[n]*t}))}return N}function de(e,t,n){return c(e,u(t,n))}const fe={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,i=e.name,r=n.mainAxis,o=void 0===r||r,s=n.altAxis,a=void 0!==s&&s,l=n.boundary,h=n.rootBoundary,d=n.altBoundary,f=n.padding,p=n.tether,g=void 0===p||p,m=n.tetherOffset,v=void 0===m?0:m,y=he(t,{boundary:l,rootBoundary:h,padding:f,altBoundary:d}),b=K(t.placement),_=G(t.placement),w=!_,k=X(b),C="x"===k?"y":"x",S=t.modifiersData.popperOffsets,L=t.rects.reference,T=t.rects.popper,D="function"==typeof v?v(Object.assign({},t.rects,{placement:t.placement})):v,P="number"==typeof D?{mainAxis:D,altAxis:D}:Object.assign({mainAxis:0,altAxis:0},D),W=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,I={x:0,y:0};if(S){if(o){var F,B="y"===k?M:N,R="y"===k?O:E,z="y"===k?"height":"width",j=S[k],q=j+y[B],U=j-y[R],$=g?-T[z]/2:0,V=_===H?L[z]:T[z],Y=_===H?-T[z]:-L[z],J=t.elements.arrow,Z=g&&J?x(J):{width:0,height:0},Q=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},ee=Q[B],te=Q[R],ne=de(0,L[z],Z[z]),ie=w?L[z]/2-$-ne-ee-P.mainAxis:V-ne-ee-P.mainAxis,re=w?-L[z]/2+$+ne+te+P.mainAxis:Y+ne+te+P.mainAxis,oe=t.elements.arrow&&A(t.elements.arrow),se=oe?"y"===k?oe.clientTop||0:oe.clientLeft||0:0,ae=null!=(F=null==W?void 0:W[k])?F:0,le=j+re-ae,ce=de(g?u(q,j+ie-ae-se):q,j,g?c(U,le):U);S[k]=ce,I[k]=ce-j}if(a){var ue,fe="x"===k?M:N,pe="x"===k?O:E,ge=S[C],me="y"===C?"height":"width",ve=ge+y[fe],ye=ge-y[pe],be=-1!==[M,N].indexOf(b),_e=null!=(ue=null==W?void 0:W[C])?ue:0,we=be?ve:ge-L[me]-T[me]-_e+P.altAxis,xe=be?ge+L[me]+T[me]-_e-P.altAxis:ye,ke=g&&be?function(e,t,n){var i=de(e,t,n);return i>n?n:i}(we,ge,xe):de(g?we:ve,ge,g?xe:ye);S[C]=ke,I[C]=ke-ge}t.modifiersData[i]=I}},requiresIfExists:["offset"]};const pe={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,i=e.name,r=e.options,o=n.elements.arrow,s=n.modifiersData.popperOffsets,a=K(n.placement),l=X(a),c=[N,E].indexOf(a)>=0?"height":"width";if(o&&s){var u=function(e,t){return ce("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:ue(e,P))}(r.padding,n),h=x(o),d="y"===l?M:N,f="y"===l?O:E,p=n.rects.reference[c]+n.rects.reference[l]-s[l]-n.rects.popper[c],g=s[l]-n.rects.reference[l],m=A(o),v=m?"y"===l?m.clientHeight||0:m.clientWidth||0:0,y=p/2-g/2,b=u[d],_=v-h[c]-u[f],w=v/2-h[c]/2+y,k=de(b,w,_),C=l;n.modifiersData[i]=((t={})[C]=k,t.centerOffset=k-w,t)}},effect:function(e){var t=e.state,n=e.options.element,i=void 0===n?"[data-popper-arrow]":n;null!=i&&("string"!=typeof i||(i=t.elements.popper.querySelector(i)))&&oe(t.elements.popper,i)&&(t.elements.arrow=i)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function ge(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function me(e){return[M,E,O,N].some((function(t){return e[t]>=0}))}var ve=$({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,i=e.options,r=i.scroll,s=void 0===r||r,a=i.resize,l=void 0===a||a,c=o(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return s&&u.forEach((function(e){e.addEventListener("scroll",n.update,V)})),l&&c.addEventListener("resize",n.update,V),function(){s&&u.forEach((function(e){e.removeEventListener("scroll",n.update,V)})),l&&c.removeEventListener("resize",n.update,V)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Y({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,i=n.gpuAcceleration,r=void 0===i||i,o=n.adaptive,s=void 0===o||o,a=n.roundOffsets,l=void 0===a||a,c={placement:K(t.placement),variation:G(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,Z(Object.assign({},c,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:s,roundOffsets:l})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,Z(Object.assign({},c,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},Q,ee,{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,i=e.name;if(!t.modifiersData[i]._skip){for(var r=n.mainAxis,o=void 0===r||r,s=n.altAxis,a=void 0===s||s,l=n.fallbackPlacements,c=n.padding,u=n.boundary,h=n.rootBoundary,d=n.altBoundary,f=n.flipVariations,p=void 0===f||f,g=n.allowedAutoPlacements,m=t.options.placement,v=K(m),y=l||(v===m||!p?[ne(m)]:function(e){if(K(e)===D)return[];var t=ne(e);return[re(e),t,re(t)]}(m)),b=[m].concat(y).reduce((function(e,n){return e.concat(K(n)===D?function(e,t){void 0===t&&(t={});var n=t,i=n.placement,r=n.boundary,o=n.rootBoundary,s=n.padding,a=n.flipVariations,l=n.allowedAutoPlacements,c=void 0===l?R:l,u=G(i),h=u?a?B:B.filter((function(e){return G(e)===u})):P,d=h.filter((function(e){return c.indexOf(e)>=0}));0===d.length&&(d=h);var f=d.reduce((function(t,n){return t[n]=he(e,{placement:n,boundary:r,rootBoundary:o,padding:s})[K(n)],t}),{});return Object.keys(f).sort((function(e,t){return f[e]-f[t]}))}(t,{placement:n,boundary:u,rootBoundary:h,padding:c,flipVariations:p,allowedAutoPlacements:g}):n)}),[]),_=t.rects.reference,w=t.rects.popper,x=new Map,k=!0,C=b[0],S=0;S<b.length;S++){var L=b[S],T=K(L),A=G(L)===H,W=[M,O].indexOf(T)>=0,I=W?"width":"height",F=he(t,{placement:L,boundary:u,rootBoundary:h,altBoundary:d,padding:c}),z=W?A?E:N:A?O:M;_[I]>w[I]&&(z=ne(z));var j=ne(z),q=[];if(o&&q.push(F[T]<=0),a&&q.push(F[z]<=0,F[j]<=0),q.every((function(e){return e}))){C=L,k=!1;break}x.set(L,q)}if(k)for(var U=function(e){var t=b.find((function(t){var n=x.get(t);if(n)return n.slice(0,e).every((function(e){return e}))}));if(t)return C=t,"break"},$=p?3:1;$>0;$--){if("break"===U($))break}t.placement!==C&&(t.modifiersData[i]._skip=!0,t.placement=C,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},fe,pe,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,i=t.rects.reference,r=t.rects.popper,o=t.modifiersData.preventOverflow,s=he(t,{elementContext:"reference"}),a=he(t,{altBoundary:!0}),l=ge(s,i),c=ge(a,r,o),u=me(l),h=me(c);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:u,hasPopperEscaped:h},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":u,"data-popper-escaped":h})}}]}),ye="tippy-content",be="tippy-backdrop",_e="tippy-arrow",we="tippy-svg-arrow",xe={passive:!0,capture:!0},ke=function(){return document.body};function Ce(e,t,n){if(Array.isArray(e)){var i=e[t];return null==i?Array.isArray(n)?n[t]:n:i}return e}function Se(e,t){var n={}.toString.call(e);return 0===n.indexOf("[object")&&n.indexOf(t+"]")>-1}function Le(e,t){return"function"==typeof e?e.apply(void 0,t):e}function Te(e,t){return 0===t?e:function(i){clearTimeout(n),n=setTimeout((function(){e(i)}),t)};var n}function Ae(e){return[].concat(e)}function Me(e,t){-1===e.indexOf(t)&&e.push(t)}function Oe(e){return e.split("-")[0]}function Ee(e){return[].slice.call(e)}function Ne(e){return Object.keys(e).reduce((function(t,n){return void 0!==e[n]&&(t[n]=e[n]),t}),{})}function De(){return document.createElement("div")}function Pe(e){return["Element","Fragment"].some((function(t){return Se(e,t)}))}function He(e){return Se(e,"MouseEvent")}function We(e){return!(!e||!e._tippy||e._tippy.reference!==e)}function Ie(e){return Pe(e)?[e]:function(e){return Se(e,"NodeList")}(e)?Ee(e):Array.isArray(e)?e:Ee(document.querySelectorAll(e))}function Fe(e,t){e.forEach((function(e){e&&(e.style.transitionDuration=t+"ms")}))}function Be(e,t){e.forEach((function(e){e&&e.setAttribute("data-state",t)}))}function Re(e){var t,n=Ae(e)[0];return null!=n&&null!=(t=n.ownerDocument)&&t.body?n.ownerDocument:document}function ze(e,t,n){var i=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(t){e[i](t,n)}))}function je(e,t){for(var n=t;n;){var i;if(e.contains(n))return!0;n=null==n.getRootNode||null==(i=n.getRootNode())?void 0:i.host}return!1}var qe={isTouch:!1},Ue=0;function $e(){qe.isTouch||(qe.isTouch=!0,window.performance&&document.addEventListener("mousemove",Ve))}function Ve(){var e=performance.now();e-Ue<20&&(qe.isTouch=!1,document.removeEventListener("mousemove",Ve)),Ue=e}function Ke(){var e=document.activeElement;if(We(e)){var t=e._tippy;e.blur&&!t.state.isVisible&&e.blur()}}var Ge=!!("undefined"!=typeof window&&"undefined"!=typeof document)&&!!window.msCrypto;var Xe={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},Ye=Object.assign({appendTo:ke,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},Xe,{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),Je=Object.keys(Ye);function Ze(e){var t=(e.plugins||[]).reduce((function(t,n){var i,r=n.name,o=n.defaultValue;r&&(t[r]=void 0!==e[r]?e[r]:null!=(i=Ye[r])?i:o);return t}),{});return Object.assign({},e,t)}function Qe(e,t){var n=Object.assign({},t,{content:Le(t.content,[e])},t.ignoreAttributes?{}:function(e,t){return(t?Object.keys(Ze(Object.assign({},Ye,{plugins:t}))):Je).reduce((function(t,n){var i=(e.getAttribute("data-tippy-"+n)||"").trim();if(!i)return t;if("content"===n)t[n]=i;else try{t[n]=JSON.parse(i)}catch(e){t[n]=i}return t}),{})}(e,t.plugins));return n.aria=Object.assign({},Ye.aria,n.aria),n.aria={expanded:"auto"===n.aria.expanded?t.interactive:n.aria.expanded,content:"auto"===n.aria.content?t.interactive?null:"describedby":n.aria.content},n}function et(e,t){e.innerHTML=t}function tt(e){var t=De();return!0===e?t.className=_e:(t.className=we,Pe(e)?t.appendChild(e):et(t,e)),t}function nt(e,t){Pe(t.content)?(et(e,""),e.appendChild(t.content)):"function"!=typeof t.content&&(t.allowHTML?et(e,t.content):e.textContent=t.content)}function it(e){var t=e.firstElementChild,n=Ee(t.children);return{box:t,content:n.find((function(e){return e.classList.contains(ye)})),arrow:n.find((function(e){return e.classList.contains(_e)||e.classList.contains(we)})),backdrop:n.find((function(e){return e.classList.contains(be)}))}}function rt(e){var t=De(),n=De();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var i=De();function r(n,i){var r=it(t),o=r.box,s=r.content,a=r.arrow;i.theme?o.setAttribute("data-theme",i.theme):o.removeAttribute("data-theme"),"string"==typeof i.animation?o.setAttribute("data-animation",i.animation):o.removeAttribute("data-animation"),i.inertia?o.setAttribute("data-inertia",""):o.removeAttribute("data-inertia"),o.style.maxWidth="number"==typeof i.maxWidth?i.maxWidth+"px":i.maxWidth,i.role?o.setAttribute("role",i.role):o.removeAttribute("role"),n.content===i.content&&n.allowHTML===i.allowHTML||nt(s,e.props),i.arrow?a?n.arrow!==i.arrow&&(o.removeChild(a),o.appendChild(tt(i.arrow))):o.appendChild(tt(i.arrow)):a&&o.removeChild(a)}return i.className=ye,i.setAttribute("data-state","hidden"),nt(i,e.props),t.appendChild(n),n.appendChild(i),r(e.props,e.props),{popper:t,onUpdate:r}}rt.$$tippy=!0;var ot=1,st=[],at=[];function lt(e,t){var n,i,r,o,s,a,l,c,u=Qe(e,Object.assign({},Ye,Ze(Ne(t)))),h=!1,d=!1,f=!1,p=!1,g=[],m=Te(K,u.interactiveDebounce),v=ot++,y=(c=u.plugins).filter((function(e,t){return c.indexOf(e)===t})),b={id:v,reference:e,popper:De(),popperInstance:null,props:u,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:y,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(i),cancelAnimationFrame(r)},setProps:function(t){0;if(b.state.isDestroyed)return;D("onBeforeUpdate",[b,t]),$();var n=b.props,i=Qe(e,Object.assign({},n,Ne(t),{ignoreAttributes:!0}));b.props=i,U(),n.interactiveDebounce!==i.interactiveDebounce&&(W(),m=Te(K,i.interactiveDebounce));n.triggerTarget&&!i.triggerTarget?Ae(n.triggerTarget).forEach((function(e){e.removeAttribute("aria-expanded")})):i.triggerTarget&&e.removeAttribute("aria-expanded");H(),N(),x&&x(n,i);b.popperInstance&&(J(),Q().forEach((function(e){requestAnimationFrame(e._tippy.popperInstance.forceUpdate)})));D("onAfterUpdate",[b,t])},setContent:function(e){b.setProps({content:e})},show:function(){0;var e=b.state.isVisible,t=b.state.isDestroyed,n=!b.state.isEnabled,i=qe.isTouch&&!b.props.touch,r=Ce(b.props.duration,0,Ye.duration);if(e||t||n||i)return;if(A().hasAttribute("disabled"))return;if(D("onShow",[b],!1),!1===b.props.onShow(b))return;b.state.isVisible=!0,T()&&(w.style.visibility="visible");N(),R(),b.state.isMounted||(w.style.transition="none");if(T()){var o=O();Fe([o.box,o.content],0)}a=function(){var e;if(b.state.isVisible&&!p){if(p=!0,w.offsetHeight,w.style.transition=b.props.moveTransition,T()&&b.props.animation){var t=O(),n=t.box,i=t.content;Fe([n,i],r),Be([n,i],"visible")}P(),H(),Me(at,b),null==(e=b.popperInstance)||e.forceUpdate(),D("onMount",[b]),b.props.animation&&T()&&function(e,t){j(e,t)}(r,(function(){b.state.isShown=!0,D("onShown",[b])}))}},function(){var e,t=b.props.appendTo,n=A();e=b.props.interactive&&t===ke||"parent"===t?n.parentNode:Le(t,[n]);e.contains(w)||e.appendChild(w);b.state.isMounted=!0,J(),!1}()},hide:function(){0;var e=!b.state.isVisible,t=b.state.isDestroyed,n=!b.state.isEnabled,i=Ce(b.props.duration,1,Ye.duration);if(e||t||n)return;if(D("onHide",[b],!1),!1===b.props.onHide(b))return;b.state.isVisible=!1,b.state.isShown=!1,p=!1,h=!1,T()&&(w.style.visibility="hidden");if(W(),z(),N(!0),T()){var r=O(),o=r.box,s=r.content;b.props.animation&&(Fe([o,s],i),Be([o,s],"hidden"))}P(),H(),b.props.animation?T()&&function(e,t){j(e,(function(){!b.state.isVisible&&w.parentNode&&w.parentNode.contains(w)&&t()}))}(i,b.unmount):b.unmount()},hideWithInteractivity:function(e){0;M().addEventListener("mousemove",m),Me(st,m),m(e)},enable:function(){b.state.isEnabled=!0},disable:function(){b.hide(),b.state.isEnabled=!1},unmount:function(){0;b.state.isVisible&&b.hide();if(!b.state.isMounted)return;Z(),Q().forEach((function(e){e._tippy.unmount()})),w.parentNode&&w.parentNode.removeChild(w);at=at.filter((function(e){return e!==b})),b.state.isMounted=!1,D("onHidden",[b])},destroy:function(){0;if(b.state.isDestroyed)return;b.clearDelayTimeouts(),b.unmount(),$(),delete e._tippy,b.state.isDestroyed=!0,D("onDestroy",[b])}};if(!u.render)return b;var _=u.render(b),w=_.popper,x=_.onUpdate;w.setAttribute("data-tippy-root",""),w.id="tippy-"+b.id,b.popper=w,e._tippy=b,w._tippy=b;var k=y.map((function(e){return e.fn(b)})),C=e.hasAttribute("aria-expanded");return U(),H(),N(),D("onCreate",[b]),u.showOnCreate&&ee(),w.addEventListener("mouseenter",(function(){b.props.interactive&&b.state.isVisible&&b.clearDelayTimeouts()})),w.addEventListener("mouseleave",(function(){b.props.interactive&&b.props.trigger.indexOf("mouseenter")>=0&&M().addEventListener("mousemove",m)})),b;function S(){var e=b.props.touch;return Array.isArray(e)?e:[e,0]}function L(){return"hold"===S()[0]}function T(){var e;return!(null==(e=b.props.render)||!e.$$tippy)}function A(){return l||e}function M(){var e=A().parentNode;return e?Re(e):document}function O(){return it(w)}function E(e){return b.state.isMounted&&!b.state.isVisible||qe.isTouch||o&&"focus"===o.type?0:Ce(b.props.delay,e?0:1,Ye.delay)}function N(e){void 0===e&&(e=!1),w.style.pointerEvents=b.props.interactive&&!e?"":"none",w.style.zIndex=""+b.props.zIndex}function D(e,t,n){var i;(void 0===n&&(n=!0),k.forEach((function(n){n[e]&&n[e].apply(n,t)})),n)&&(i=b.props)[e].apply(i,t)}function P(){var t=b.props.aria;if(t.content){var n="aria-"+t.content,i=w.id;Ae(b.props.triggerTarget||e).forEach((function(e){var t=e.getAttribute(n);if(b.state.isVisible)e.setAttribute(n,t?t+" "+i:i);else{var r=t&&t.replace(i,"").trim();r?e.setAttribute(n,r):e.removeAttribute(n)}}))}}function H(){!C&&b.props.aria.expanded&&Ae(b.props.triggerTarget||e).forEach((function(e){b.props.interactive?e.setAttribute("aria-expanded",b.state.isVisible&&e===A()?"true":"false"):e.removeAttribute("aria-expanded")}))}function W(){M().removeEventListener("mousemove",m),st=st.filter((function(e){return e!==m}))}function I(t){if(!qe.isTouch||!f&&"mousedown"!==t.type){var n=t.composedPath&&t.composedPath()[0]||t.target;if(!b.props.interactive||!je(w,n)){if(Ae(b.props.triggerTarget||e).some((function(e){return je(e,n)}))){if(qe.isTouch)return;if(b.state.isVisible&&b.props.trigger.indexOf("click")>=0)return}else D("onClickOutside",[b,t]);!0===b.props.hideOnClick&&(b.clearDelayTimeouts(),b.hide(),d=!0,setTimeout((function(){d=!1})),b.state.isMounted||z())}}}function F(){f=!0}function B(){f=!1}function R(){var e=M();e.addEventListener("mousedown",I,!0),e.addEventListener("touchend",I,xe),e.addEventListener("touchstart",B,xe),e.addEventListener("touchmove",F,xe)}function z(){var e=M();e.removeEventListener("mousedown",I,!0),e.removeEventListener("touchend",I,xe),e.removeEventListener("touchstart",B,xe),e.removeEventListener("touchmove",F,xe)}function j(e,t){var n=O().box;function i(e){e.target===n&&(ze(n,"remove",i),t())}if(0===e)return t();ze(n,"remove",s),ze(n,"add",i),s=i}function q(t,n,i){void 0===i&&(i=!1),Ae(b.props.triggerTarget||e).forEach((function(e){e.addEventListener(t,n,i),g.push({node:e,eventType:t,handler:n,options:i})}))}function U(){var e;L()&&(q("touchstart",V,{passive:!0}),q("touchend",G,{passive:!0})),(e=b.props.trigger,e.split(/\s+/).filter(Boolean)).forEach((function(e){if("manual"!==e)switch(q(e,V),e){case"mouseenter":q("mouseleave",G);break;case"focus":q(Ge?"focusout":"blur",X);break;case"focusin":q("focusout",X)}}))}function $(){g.forEach((function(e){var t=e.node,n=e.eventType,i=e.handler,r=e.options;t.removeEventListener(n,i,r)})),g=[]}function V(e){var t,n=!1;if(b.state.isEnabled&&!Y(e)&&!d){var i="focus"===(null==(t=o)?void 0:t.type);o=e,l=e.currentTarget,H(),!b.state.isVisible&&He(e)&&st.forEach((function(t){return t(e)})),"click"===e.type&&(b.props.trigger.indexOf("mouseenter")<0||h)&&!1!==b.props.hideOnClick&&b.state.isVisible?n=!0:ee(e),"click"===e.type&&(h=!n),n&&!i&&te(e)}}function K(e){var t=e.target,n=A().contains(t)||w.contains(t);if("mousemove"!==e.type||!n){var i=Q().concat(w).map((function(e){var t,n=null==(t=e._tippy.popperInstance)?void 0:t.state;return n?{popperRect:e.getBoundingClientRect(),popperState:n,props:u}:null})).filter(Boolean);(function(e,t){var n=t.clientX,i=t.clientY;return e.every((function(e){var t=e.popperRect,r=e.popperState,o=e.props.interactiveBorder,s=Oe(r.placement),a=r.modifiersData.offset;if(!a)return!0;var l="bottom"===s?a.top.y:0,c="top"===s?a.bottom.y:0,u="right"===s?a.left.x:0,h="left"===s?a.right.x:0,d=t.top-i+l>o,f=i-t.bottom-c>o,p=t.left-n+u>o,g=n-t.right-h>o;return d||f||p||g}))})(i,e)&&(W(),te(e))}}function G(e){Y(e)||b.props.trigger.indexOf("click")>=0&&h||(b.props.interactive?b.hideWithInteractivity(e):te(e))}function X(e){b.props.trigger.indexOf("focusin")<0&&e.target!==A()||b.props.interactive&&e.relatedTarget&&w.contains(e.relatedTarget)||te(e)}function Y(e){return!!qe.isTouch&&L()!==e.type.indexOf("touch")>=0}function J(){Z();var t=b.props,n=t.popperOptions,i=t.placement,r=t.offset,o=t.getReferenceClientRect,s=t.moveTransition,l=T()?it(w).arrow:null,c=o?{getBoundingClientRect:o,contextElement:o.contextElement||A()}:e,u={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(e){var t=e.state;if(T()){var n=O().box;["placement","reference-hidden","escaped"].forEach((function(e){"placement"===e?n.setAttribute("data-placement",t.placement):t.attributes.popper["data-popper-"+e]?n.setAttribute("data-"+e,""):n.removeAttribute("data-"+e)})),t.attributes.popper={}}}},h=[{name:"offset",options:{offset:r}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!s}},u];T()&&l&&h.push({name:"arrow",options:{element:l,padding:3}}),h.push.apply(h,(null==n?void 0:n.modifiers)||[]),b.popperInstance=ve(c,w,Object.assign({},n,{placement:i,onFirstUpdate:a,modifiers:h}))}function Z(){b.popperInstance&&(b.popperInstance.destroy(),b.popperInstance=null)}function Q(){return Ee(w.querySelectorAll("[data-tippy-root]"))}function ee(e){b.clearDelayTimeouts(),e&&D("onTrigger",[b,e]),R();var t=E(!0),i=S(),r=i[0],o=i[1];qe.isTouch&&"hold"===r&&o&&(t=o),t?n=setTimeout((function(){b.show()}),t):b.show()}function te(e){if(b.clearDelayTimeouts(),D("onUntrigger",[b,e]),b.state.isVisible){if(!(b.props.trigger.indexOf("mouseenter")>=0&&b.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(e.type)>=0&&h)){var t=E(!1);t?i=setTimeout((function(){b.state.isVisible&&b.hide()}),t):r=requestAnimationFrame((function(){b.hide()}))}}else z()}}function ct(e,t){void 0===t&&(t={});var n=Ye.plugins.concat(t.plugins||[]);document.addEventListener("touchstart",$e,xe),window.addEventListener("blur",Ke);var i=Object.assign({},t,{plugins:n}),r=Ie(e).reduce((function(e,t){var n=t&&lt(t,i);return n&&e.push(n),e}),[]);return Pe(e)?r[0]:r}ct.defaultProps=Ye,ct.setDefaultProps=function(e){Object.keys(e).forEach((function(t){Ye[t]=e[t]}))},ct.currentInput=qe;Object.assign({},Q,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}});ct.setDefaultProps({render:rt});const ut=ct;n(124),n(804);const ht=function(){var e,n={token:null,isLoggedIn:!1};function i(){document.body.style.overflow=""}var r=window.linguise_configs.vars.configs,o=document.querySelector('[data-template="linguise-register-frame"]').content.querySelector(".linguise-register-area").cloneNode(!0);o.setAttribute("data-modal","linguise-register-frame");var s=document.querySelector("#linguise-site-url"),a=new URL(function(){var e="https://dashboard.linguise.com",t=new URL(e);return"".concat(t.protocol,"//").concat(t.host)}()),l=new URL(s.getAttribute("data-url"));function c(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=o.querySelector(".content-wrapper");e?t.classList.add("with-login"):t.classList.remove("with-login")}function u(e){arguments.length>1&&void 0!==arguments[1]&&arguments[1]?e.classList.add("is-visible"):e.classList.remove("is-visible")}function h(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"login";c(!1),n.isLoggedIn=!1;var t=o.querySelector(".frame-content");t.querySelector("iframe")&&(t.innerHTML="");var i=document.createElement("iframe"),r=new URLSearchParams;r.set("plugin",l.href),r.set("start",e);var s=new URL(a);s.pathname="/",n.token&&r.set("token",n.token),s.search=r.toString(),i.src=s.href,i.style.width="100%",i.style.height="100%",t.appendChild(i),o.style.display="block",document.body.style.overflow="hidden"}function d(){document.querySelectorAll("[data-linguise-popup]").forEach((function(e){u(e)}))}function f(e){!function(e){r.default_language=e.language,r.current_language=e.language,r.enabled_languages=e.allowed_languages,r.dynamic_translations.enabled=e.dynamicTl,r.dynamic_translations.public_key=e.public_key,r.token=e.token}(e),document.querySelector('input[name="linguise_options[token]"]').value=e.token;var n=document.querySelector('select[name="linguise_options[default_language]"]');n.querySelectorAll("option").forEach((function(t){t.value===e.language?t.selected=!0:t.selected=!1})),n.dispatchEvent(new Event("change"));var i,o=document.querySelector("#ms-translate-into");o.querySelectorAll("option").forEach((function(t){e.allowed_languages.includes(t.value)?t.selected=!0:t.selected=!1})),o.dispatchEvent(new Event("change")),t()(o).trigger("chosen:updated"),document.querySelector('input[name="linguise_options[dynamic_translations]"]').checked=e.dynamicContent,i=e,t().ajax({url:window.linguise_admin_iframe.ajax_url,type:"POST",data:{nonce:window.linguise_admin_iframe.nonce,config:i},success:function(e){e.error&&console.error("Error updating config:",e)},error:function(e){console.error("AJAX error:",e)}})}l.search="",l.hash="",document.querySelector('[data-modal="linguise-register-frame"]')||document.body.appendChild(o),document.querySelector('[data-linguise-register-action="register"]').addEventListener("click",(function(e){e.preventDefault(),h("register")})),document.querySelector('[data-linguise-register-action="login"]').addEventListener("click",(function(e){e.preventDefault(),h("login")})),document.querySelector('[data-linguise-action="close-modal"]').addEventListener("click",(function(e){if(e.preventDefault(),n.isLoggedIn){var t=document.querySelector('[data-linguise-popup="linguise-modal-abort"]');t&&u(t,!0)}else{var r=o.querySelector(".frame-content");r.querySelector("iframe")&&(r.innerHTML=""),o.style.display="none",i()}})),document.querySelector('[data-linguise-action="translate-save"]').addEventListener("click",(function(e){var t=o.querySelector("iframe");t?t.contentWindow?t.contentWindow.postMessage(JSON.stringify({t:"pluginSubmit",d:!0}),a.href):console.log("No content window for Linguise dashboard!"):console.log("No iframe found for Linguise dashboard!")})),document.querySelectorAll('[data-linguise-action="close-modal-force"]').forEach((function(e){e.addEventListener("click",(function(e){e.preventDefault();var t=e.currentTarget.getAttribute("data-linguise-action-target"),n=o.querySelector(".frame-content");n.querySelector("iframe")&&(n.innerHTML=""),o.style.display="none",i(),d(),"saved"===t&&(document.querySelector("#login-register-btn-area").style.display="none",document.querySelector('[data-id="linguise-register-warn"]').style.display="none",document.querySelector(".save-settings-input").removeAttribute("disabled"),document.querySelectorAll(".linguise-options").forEach((function(e){e.classList.remove("is-disabled")})))}))})),null===(e=document.querySelector('[data-linguise-action="submit-try-again"]'))||void 0===e||e.addEventListener("click",(function(e){e.preventDefault(),d();var t=o.querySelector("iframe");t?t.contentWindow?t.contentWindow.postMessage(JSON.stringify({t:"pluginSubmit",d:!0}),a.href):console.log("No content window for Linguise dashboard!"):console.log("No iframe found for Linguise dashboard!")})),document.querySelectorAll('[data-linguise-action="popup-cancel-modal"]').forEach((function(e){e.addEventListener("click",(function(e){e.preventDefault(),d()}))})),window.addEventListener("message",(function(e){var t=new URL(e.origin);if(t.pathname="/",t.search="",t.href===a.href&&e.data){var i;try{i=JSON.parse(e.data)}catch(e){return}if(i){var s=o.querySelector("iframe");if(s)if(s.contentWindow)switch(i.t){case"pluginRequestInit":if(c(!0),n.isLoggedIn=!0,i.d!==l.href)break;var h={url:l.href,original:r.default_language,languages:r.enabled_languages,platform:r.platform||"other_php",dynamicContent:Boolean(r.dynamic_translations.enabled)};"auto"===h.platform&&(h.platform="other_php"),r.original_default&&"en"!==r.original_default&&"en"!==h.original&&(h.original=r.original_default),console.log("Plugin request init!",h),s.contentWindow.postMessage(JSON.stringify({t:"pluginInit",d:h}),a.href);break;case"pluginSetupComplete":console.log("Plugin setup complete!"),f(i.d),n.token=null,n.isLoggedIn=!1,o.style.display="none";break;case"pluginLoginToken":i.d&&(n.token=i.d);break;case"pluginSaving":d();var p=document.querySelector('[data-linguise-popup="linguise-modal-saving"]');p&&u(p,!0);break;case"pluginValidationFail":default:break;case"pluginSaved":d();var g=document.querySelector('[data-linguise-popup="linguise-modal-saved"]');g&&u(g,!0);break;case"pluginSavingFail":d();var m=document.querySelector('[data-linguise-popup="linguise-modal-error"]');m&&u(m,!0);break;case"pluginTranslateButton":"boolean"==typeof i.d&&function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],t=document.querySelector('[data-linguise-action="translate-save"]');t&&(e?t.removeAttribute("disabled"):t.setAttribute("disabled","disabled"))}(i.d)}else console.log("No content window for Linguise dashboard!");else console.log("No iframe found for Linguise dashboard!")}}}))};const dt=function(e){if(e){if("FORM"===e.tagName){var t=function(e,t){e&&(t?e.classList.remove("is-hidden"):e.classList.add("is-hidden"))},n=function(e,t){e&&(t?e.classList.add("is-invalid"):e.classList.remove("is-invalid"))};return e.addEventListener("submit",(function(n){if(n.preventDefault(),a(n.currentTarget))e.querySelectorAll(".linguise-form-invalid").forEach((function(e){t(e,!1)})),e.submit();else{var i=e.querySelector(".linguise-form-invalid:not(.is-hidden)");i&&function(e){if(e){var t=e.closest(".linguise-config-form .tab-content");if(t){var n=t.dataset.id;window.location.hash="#".concat(n),setTimeout((function(){e.scrollIntoView({behavior:"smooth",block:"center"})}))}}}(i)}})),e.addEventListener("input",(function(e){var i=e.target;if(("INPUT"===i.tagName||"TEXTAREA"===i.tagName||"SELECT"===i.tagName)&&!i.hasAttribute("novalidate")&&s(i)){var a=r(i);i.validity.valid?(o(a),t(a,!1),n(i,!1)):(o(a,i.validationMessage||"This field is invalid."),t(a,!0),n(i,!0))}})),e.setAttribute("novalidate","novalidate"),e.querySelectorAll("[data-validate-warn]").forEach((function(e){i(e)})),a(e),e}console.error("The formValidator function must be called with a form element, found ".concat(e.tagName," instead."))}function i(e){return e?(e.classList.add("linguise-form-invalid","is-hidden"),e.setAttribute("role","alert"),e.setAttribute("aria-live","assertive"),e):e}function r(t){var n=t.dataset.validateTarget;if(n)return i(e.querySelector('[data-validate-warn="'.concat(n,'"]')));if(t.nextElementSibling&&t.nextElementSibling.classList.contains("linguise-form-invalid"))return t.nextElementSibling;var r=document.createElement("label");return i(r),t.id&&(r.setAttribute("for",t.id),r.setAttribute("id","".concat(t.id,"-warning"))),t.insertAdjacentElement("afterend",r),r}function o(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(e)if(t){var n=e.dataset.prefix;n&&(t="".concat(n," ").concat(t)),e.textContent=t}else e.textContent=""}function s(e){return["required","pattern","minlength","maxlength","min","max","step"].some((function(t){return e.hasAttribute(t)}))}function a(e){var i=e.querySelectorAll("input, textarea, select"),a=!0;return i.forEach((function(e){if("hidden"!==e.type&&"submit"!==e.type&&!e.hasAttribute("novalidate")&&s(e)){e.dataset.validateTarget;var i=r(e);e.validity.valid?(o(i),t(i,!1),n(e,!1)):(a=!1,o(i,e.validationMessage||"This field is invalid."),t(i,!0),n(e,!0))}})),a}};let ft=!1;try{crypto.randomUUID(),ft=!0}catch(e){console.warn("Linguise: crypto.randomUUID is not available, using unsafe UUID generator")}const pt={IMG:["alt","title","src"],A:["href"],INPUT:["placeholder"],TEXTAREA:["placeholder"]};const gt="v4.1",mt="_linguise_ttl",vt="linguiseDynamicCache";function yt(e){return Math.floor(((new Date).getTime()+e)/1e3)}function bt(e,t){return t?`${t}::${e}`:e}const _t=new class{constructor(){this.caches={},this.global_ttl=void 0,this.MAX_TTL_TIME=2592e5,this.saveToStorageDebounced=function(e,t){let n=null;return(...i)=>{clearTimeout(n),n=setTimeout((()=>{e(...i)}),t)}}(this.saveToStorage.bind(this),300),document.addEventListener("visibilitychange",(()=>{"hidden"===document.visibilityState&&this.saveToStorage()}))}importFromStorage(){const e=localStorage.getItem(vt);if(!e)return;const t=JSON.parse(e);t.version===gt&&(this.caches=t.caches,this.global_ttl=Number(t[mt]),this.updateTTL())}updateTTL(){const e=Math.floor((new Date).getTime()/1e3);if(isNaN(this.global_ttl))return this.global_ttl=yt(this.MAX_TTL_TIME),void this.saveToStorageDebounced();e>this.global_ttl&&(this.caches={}),this.global_ttl=yt(this.MAX_TTL_TIME),this.saveToStorageDebounced()}setMaxTTL(e){this.MAX_TTL_TIME=e,this.updateTTL()}add(e,t,n,i){if(!n)throw new Error("Language is required");this.updateTTL(),this.caches[n]=this.caches[n]||{};const r=bt(e,i);this.caches[n][r]?(this.caches[n][r].value=t,this.caches[n][r].ttl=yt(this.MAX_TTL_TIME)):this.caches[n][r]={value:t,ttl:yt(this.MAX_TTL_TIME)},this.saveToStorageDebounced()}get(e,t,n){if(!t)throw new Error("Language is required");const i=(new Date).getTime();if(1e3*this.global_ttl<i)return void this.flush();this.caches[t]=this.caches[t]||{};const r=bt(e,n),o=this.caches[t][r];if(o){if(!(1e3*o.ttl<i))return this.caches[t][r].ttl=Math.floor((i+this.MAX_TTL_TIME)/1e3),this.updateTTL(),o.value;try{delete this.caches[t][r]}catch{}this.updateTTL()}}saveToStorage(){localStorage.setItem(vt,JSON.stringify({version:gt,caches:this.caches,[mt]:this.global_ttl}))}flush(){this.caches={},this.updateTTL()}};window.globalLinguiseCache=_t;var wt="undefined"!=typeof Buffer&&Buffer.from?function(e){return Buffer.from(e,"utf8")}:e=>(new TextEncoder).encode(e);function xt(e){return e instanceof Uint8Array?e:"string"==typeof e?wt(e):ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength/Uint8Array.BYTES_PER_ELEMENT):new Uint8Array(e)}function kt(e){return"string"==typeof e?0===e.length:0===e.byteLength}var Ct={name:"SHA-256"},St={name:"HMAC",hash:Ct},Lt=new Uint8Array([227,176,196,66,152,252,28,20,154,251,244,200,153,111,185,36,39,174,65,228,100,155,147,76,164,149,153,27,120,82,184,85]);const Tt={};function At(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:Tt}var Mt=function(){function e(e){this.toHash=new Uint8Array(0),this.secret=e,this.reset()}return e.prototype.update=function(e){if(!kt(e)){var t=xt(e),n=new Uint8Array(this.toHash.byteLength+t.byteLength);n.set(this.toHash,0),n.set(t,this.toHash.byteLength),this.toHash=n}},e.prototype.digest=function(){var e=this;return this.key?this.key.then((function(t){return At().crypto.subtle.sign(St,t,e.toHash).then((function(e){return new Uint8Array(e)}))})):kt(this.toHash)?Promise.resolve(Lt):Promise.resolve().then((function(){return At().crypto.subtle.digest(Ct,e.toHash)})).then((function(e){return Promise.resolve(new Uint8Array(e))}))},e.prototype.reset=function(){var e=this;this.toHash=new Uint8Array(0),this.secret&&void 0!==this.secret&&(this.key=new Promise((function(t,n){At().crypto.subtle.importKey("raw",xt(e.secret),St,!1,["sign"]).then(t,n)})),this.key.catch((function(){})))},e}();function Ot(e,t,n,i){return new(n||(n=Promise))((function(r,o){function s(e){try{l(i.next(e))}catch(e){o(e)}}function a(e){try{l(i.throw(e))}catch(e){o(e)}}function l(e){var t;e.done?r(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}l((i=i.apply(e,t||[])).next())}))}function Et(e,t){var n,i,r,o={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]},s=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return s.next=a(0),s.throw=a(1),s.return=a(2),"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(a){return function(l){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;s&&(s=0,a[0]&&(o=0)),o;)try{if(n=1,i&&(r=2&a[0]?i.return:a[0]?i.throw||((r=i.return)&&r.call(i),0):i.next)&&!(r=r.call(i,a[1])).done)return r;switch(i=0,r&&(a=[2&a[0],r.value]),a[0]){case 0:case 1:r=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,i=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(r=o.trys,(r=r.length>0&&r[r.length-1])||6!==a[0]&&2!==a[0])){o=0;continue}if(3===a[0]&&(!r||a[1]>r[0]&&a[1]<r[3])){o.label=a[1];break}if(6===a[0]&&o.label<r[1]){o.label=r[1],r=a;break}if(r&&o.label<r[2]){o.label=r[2],o.ops.push(a);break}r[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],i=0}finally{n=r=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,l])}}}Object.create;Object.create;"function"==typeof SuppressedError&&SuppressedError;var Nt=64,Dt=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),Pt=[1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225],Ht=Math.pow(2,53)-1,Wt=function(){function e(){this.state=Int32Array.from(Pt),this.temp=new Int32Array(64),this.buffer=new Uint8Array(64),this.bufferLength=0,this.bytesHashed=0,this.finished=!1}return e.prototype.update=function(e){if(this.finished)throw new Error("Attempted to update an already finished hash.");var t=0,n=e.byteLength;if(this.bytesHashed+=n,8*this.bytesHashed>Ht)throw new Error("Cannot hash more than 2^53 - 1 bits");for(;n>0;)this.buffer[this.bufferLength++]=e[t++],n--,this.bufferLength===Nt&&(this.hashBuffer(),this.bufferLength=0)},e.prototype.digest=function(){if(!this.finished){var e=8*this.bytesHashed,t=new DataView(this.buffer.buffer,this.buffer.byteOffset,this.buffer.byteLength),n=this.bufferLength;if(t.setUint8(this.bufferLength++,128),n%Nt>=56){for(var i=this.bufferLength;i<Nt;i++)t.setUint8(i,0);this.hashBuffer(),this.bufferLength=0}for(i=this.bufferLength;i<56;i++)t.setUint8(i,0);t.setUint32(56,Math.floor(e/4294967296),!0),t.setUint32(60,e),this.hashBuffer(),this.finished=!0}var r=new Uint8Array(32);for(i=0;i<8;i++)r[4*i]=this.state[i]>>>24&255,r[4*i+1]=this.state[i]>>>16&255,r[4*i+2]=this.state[i]>>>8&255,r[4*i+3]=this.state[i]>>>0&255;return r},e.prototype.hashBuffer=function(){for(var e=this.buffer,t=this.state,n=t[0],i=t[1],r=t[2],o=t[3],s=t[4],a=t[5],l=t[6],c=t[7],u=0;u<Nt;u++){if(u<16)this.temp[u]=(255&e[4*u])<<24|(255&e[4*u+1])<<16|(255&e[4*u+2])<<8|255&e[4*u+3];else{var h=this.temp[u-2],d=(h>>>17|h<<15)^(h>>>19|h<<13)^h>>>10,f=((h=this.temp[u-15])>>>7|h<<25)^(h>>>18|h<<14)^h>>>3;this.temp[u]=(d+this.temp[u-7]|0)+(f+this.temp[u-16]|0)}var p=(((s>>>6|s<<26)^(s>>>11|s<<21)^(s>>>25|s<<7))+(s&a^~s&l)|0)+(c+(Dt[u]+this.temp[u]|0)|0)|0,g=((n>>>2|n<<30)^(n>>>13|n<<19)^(n>>>22|n<<10))+(n&i^n&r^i&r)|0;c=l,l=a,a=s,s=o+p|0,o=r,r=i,i=n,n=p+g|0}t[0]+=n,t[1]+=i,t[2]+=r,t[3]+=o,t[4]+=s,t[5]+=a,t[6]+=l,t[7]+=c},e}(),It=function(){function e(e){this.secret=e,this.hash=new Wt,this.reset()}return e.prototype.update=function(e){if(!kt(e)&&!this.error)try{this.hash.update(xt(e))}catch(e){this.error=e}},e.prototype.digestSync=function(){if(this.error)throw this.error;return this.outer?(this.outer.finished||this.outer.update(this.hash.digest()),this.outer.digest()):this.hash.digest()},e.prototype.digest=function(){return Ot(this,void 0,void 0,(function(){return Et(this,(function(e){return[2,this.digestSync()]}))}))},e.prototype.reset=function(){if(this.hash=new Wt,this.secret){this.outer=new Wt;var e=function(e){var t=xt(e);if(t.byteLength>Nt){var n=new Wt;n.update(t),t=n.digest()}var i=new Uint8Array(Nt);return i.set(t),i}(this.secret),t=new Uint8Array(Nt);t.set(e);for(var n=0;n<Nt;n++)e[n]^=54,t[n]^=92;this.hash.update(e),this.outer.update(t);for(n=0;n<e.byteLength;n++)e[n]=0}},e}();var Ft=["decrypt","digest","encrypt","exportKey","generateKey","importKey","sign","verify"];function Bt(e){return e&&Ft.every((function(t){return"function"==typeof e[t]}))}var Rt=function(){function e(e){!function(e){return!(!function(e){return"object"==typeof e&&"object"==typeof e.crypto&&"function"==typeof e.crypto.getRandomValues}(e)||"object"!=typeof e.crypto.subtle)&&Bt(e.crypto.subtle)}(At())?this.hash=new It(e):this.hash=new Mt(e)}return e.prototype.update=function(e,t){this.hash.update(xt(e))},e.prototype.digest=function(){return this.hash.digest()},e.prototype.reset=function(){this.hash.reset()},e}(),zt=n(735),jt=n.n(zt);const qt=new class{getLanguageUrl(e,t){return this.isDemoMode(e)?"#":e.is_joomla?this.fixTrailingSlashes(e,this.getJoomlaLanguageUrl(t,e)):e.is_wp?this.fixTrailingSlashes(e,this.getWpLanguageUrl(t,e)):this.fixTrailingSlashes(e,this.getOtherCMSUrl(e,t))}getOtherCMSUrl(e,t){return"subdomains"===e.domain_structure?this.getUrlForSubDomains(e,t):this.getUrlForSubFolders(e,t)}getWpLanguageUrl(e,t){const n=document.querySelector('link[rel="alternate"][hreflang="'+e+'"]');return null!==n&&void 0!==n.href?n.href:t.scheme+"//"+t.host+(t.base?t.base:"")+(e===t.default_language?"":"/"+e)+t.original_path+t.trailing_slashes+(t.search?t.search:"")}getJoomlaLanguageUrl(e,t){const n=document.querySelector('link[rel="alternate"][hreflang="'+e+'"]');if(null!==n&&void 0!==n.href)return n.href;return-1!==(t.linguise_configs?.joomla_languages||[]).indexOf(e)&&e!==t.default_language?`${t.scheme}//${t.host+(t.base?t.base:"")+"/"+e+t.trailing_slashes}`:`${t.scheme}//${t.host+(t.base?t.base:"")+(e===t.default_language?"":"/"+e)+t.original_path+t.trailing_slashes+(t.search?t.search:"")}`}fixTrailingSlashes(e,t){const n=new URL(t);return"/"===n.pathname||(n.pathname=n.pathname.replace(/\/+$/,""),n.pathname=n.pathname+e.trailing_slashes),n.toString()}getUrlForSubFolders(e,t){return e.url_translations&&void 0!==e.url_translations[t]?e.domain+(t===e.default_language?"":"/"+t)+e.url_translations[t]:e.url_translations&&void 0!==e.url_translations[e.default_language]?e.domain+(t===e.default_language?"":"/"+t)+e.url_translations[e.default_language]:e.domain+(t===e.default_language?"":"/"+t)+e.path+e.search}getUrlForSubDomains(e,t){const n=e.domain,i=n.substring(0,n.indexOf("//")+2),r=e.available_languages.map((e=>e.code));let o=n.replace(new RegExp(`(https?://(?:${r.join("|")})\\.|https?://)`,""),"");return t!==e.default_language&&o.startsWith("www.")&&(o=o.substring(4)),e.url_translations&&void 0!==e.url_translations[t]?i+(t===e.default_language?"":t+".")+o+e.url_translations[t]:e.url_translations&&void 0!==e.url_translations[e.default_language]?i+(t===e.default_language?"":t+".")+o+e.url_translations[e.default_language]:i+(t===e.default_language?"":t+".")+o+e.path+e.search}sort(e){e.is_joomla||e.is_wp||e.available_languages.sort(((t,n)=>t.code===e.default_language?-1:n.code===e.default_language?1:t.original_name<n.original_name))}getFlagClass(e,t){return"en"===t&&"en-gb"===e?.flag_en_type?"en_gb":"de"===t&&"de-at"===e?.flag_de_type?"de_at":"es"===t&&"es-mx"===e?.flag_es_type?"es_mx":"es"===t&&"es-pu"===e?.flag_es_type?"es_pu":"pt"===t&&"pt-br"===e?.flag_pt_type?"pt_br":"zh-tw"===t&&"zh-cn"===e?.flag_tw_type?"zh-cn":t}renderFlag(e,t){if(e){const{enabled_flag:n,flag_width:i,flag_shape:r,flag_border_radius:o}=e;return n?`<span \n                        class="linguise_flags linguise_flag_${this.getFlagClass(e,t)} linguise_language_icon" \n                        style="width: ${i+"px"}; min-width: ${i+"px"};\n                               height: ${("rectangular"===r?i/1.7:i)+"px"};\n                               min-height: ${("rectangular"===r?i/1.7:i)+"px"};\n                               border-radius: ${"rectangular"===r?o+"px":"50%"};">         \n                    </span>`:""}return`<span class="linguise_flags linguise_flag_${this.getFlagClass(e,t)} linguise_language_icon" style="width: 24px; height: 24px; border-radius: 50%"></span>`}renderLanguageName(e,t,n,i,r=!1){return e?e.enabled_lang_short_name?`<span class="linguise_lang_name mr-10 ${r?"popup_linguise_lang_name":""}">${t.toUpperCase()}</span>`:e.enabled_lang_name?`<span class="linguise_lang_name mr-10 ${r?"popup_linguise_lang_name":""}">${"en"===e.lang_name_display?n:i}</span>`:"":`<span class="linguise_lang_name popup_linguise_lang_name">${i}</span>`}colorAndHoverLanguageName(e,t,n){const i=e?.flag_shadow_color||"#eeeeee",r=e?.flag_hover_shadow_color||"#bfbfbf",o=e?.flag_shadow_color_alpha??1,s=e?.flag_hover_shadow_color_alpha??1,a=this.mixinColorAndAlpha(i,this.clampAlpha(o))||i,l=this.mixinColorAndAlpha(r,this.clampAlpha(s))||r,c=""+((e?.flag_shadow_h??2)+"px "+(e?.flag_shadow_v??2)+"px "+(e?.flag_shadow_blur??12)+"px "+(e?.flag_shadow_spread??0)+"px "+a),u=""+((e?.flag_hover_shadow_h??3)+"px "+(e?.flag_hover_shadow_v??3)+"px "+(e?.flag_hover_shadow_blur??6)+"px "+(e?.flag_hover_shadow_spread??0)+"px "+l),h=(e,t,n,i,r,o)=>{e.style.color=n||"#000000",t&&(t.style.boxShadow=r),e.parentNode.addEventListener("mouseover",(function(){e.style.color=i||"#000000",t&&(t.style.boxShadow=o)})),e.parentNode.addEventListener("mouseout",(function(){e.style.color=n||"#000000",t&&(t.style.boxShadow=r)}))};Array.from(t).forEach((function(t){const n=t.previousElementSibling;h(t,n,e?.language_name_color,e?.language_name_hover_color,c,u)})),Array.from(n).forEach((function(t){const n=t.previousElementSibling;h(t,n,e?.popup_language_name_color,e?.popup_language_name_hover_color,c,u)}))}languagePosition(e,t,n){const i=e.querySelector(".linguise_switcher"),r=e.querySelector(".linguise_switcher_sub");let o="";switch(n){case"top_left":case"top_left_no_scroll":o="position: fixed; top: 10px; left: 10px; z-index: 99999; background: #fff; border-radius: 0;";break;case"top_right":case"top_right_no_scroll":o="position: fixed; top: 10px; right: 10px;z-index: 99999; background: #fff; border-radius: 0;";break;case"bottom_left":case"bottom_left_no_scroll":o="position: fixed; bottom: 10px; left: 10px;z-index: 99999; background: #fff; border-radius: 0;","dropdown"===t&&r&&(r.style.cssText="top: unset; bottom: 100%");break;case"bottom_right":case"bottom_right_no_scroll":o="position: fixed; bottom: 10px; right: 10px;z-index: 99999; background: #fff; border-radius: 0;","dropdown"===t&&r&&(r.style.cssText="top: unset; bottom: 100%")}i&&(i.style.cssText=o,["top_left_no_scroll","top_right_no_scroll","bottom_left_no_scroll","bottom_right_no_scroll"].includes(n)&&(i.style.position="absolute"))}isAlreadyInjected(e){const t=e.innerHTML?.trim()||"",n=t.length>0&&"[linguise]"!==t;return"function"==typeof e.getAttribute&&"1"===e.getAttribute("data-flag-ready")&&n}replaceAndPaintElement(e,t,n){if(this.isAlreadyInjected(e))return;e.innerHTML="";const i=t.cloneNode(!0);i.dataset.flagReady="1",e.appendChild(i),e.setAttribute("data-flag-ready","1");const r=e.getElementsByClassName("linguise_lang_name"),o=e.getElementsByClassName("popup_linguise_lang_name");this.colorAndHoverLanguageName(n,r,o)}replaceLinguiseShortcodeToSwitcher(e,t,n){const i=this.isDemoMode(n),r=document.getElementById("dashboard-live-preview"),o=document.getElementsByClassName("linguise_switcher_root"),s=this.findElementWithShortcode("[linguise]");let a=!1;if(r){const e=t.getElementsByClassName("linguise_lang_name"),i=t.getElementsByClassName("popup_linguise_lang_name");return this.colorAndHoverLanguageName(n.language_settings,e,i),void r.appendChild(t)}s.length>0&&Array.from(s).forEach((e=>{this.replaceAndPaintElement(e,t,n.language_settings),a=!0})),o.length>0&&Array.from(o).forEach((r=>{const o=r.classList.contains("linguise_menu_root")||r.classList.contains("linguise_menu");this.replaceAndPaintElement(r,t,n.language_settings),o||i||this.languagePosition(r,n.language_settings?.display,e),a=!0}));const l=n.is_joomla||n.is_wp;if(!a||null!==e&&"no"!==e){if(l&&!n.cms_automatic_switcher)return;const r=document.createElement("div");r.className="linguise_switcher_wrapper",this.replaceAndPaintElement(r,t,n.language_settings),i||this.languagePosition(r,n.language_settings?.display,e),document.body.appendChild(r)}}findElementWithShortcode(e){const t=document.body.getElementsByTagName("*"),n=[];for(let i=0;i<t.length;i++)t[i].textContent?.trim()===e&&n.push(t[i]);return n}isDemoMode(e){return!!e.demo_mode||Boolean(document.getElementById("dashboard-live-preview"))}async tryGetLinguiseEditorCookie(){try{const e=document.cookie.split(";");for(let t=0;t<e.length;t++){const n=e[t].split("=");if("linguiseEditorToken"===n[0].trim())return n[1]}}catch{if(window.cookieStore){const e=await window.cookieStore.get("linguiseEditorToken");return e?e.value:null}}return null}getOrderedLanguageList(e,t,n){if(!n)return e;const i=[...n];for(let t=0;t<e.length;t++){const r=e[t];n.includes(r.code)||i.push(r.code)}const r=[];for(let n=0;n<i.length;n++){const o=i[n];if(o===t)continue;const s=e.find((e=>e.code===o));s&&r.push(s)}const o=e.find((e=>e.code===t));return o&&r.unshift(o),r}parseHexColor(e){if(3===(e=e.replace("#","")).length)e=e.split("").map((e=>e+e)).join("");else if(6!==e.length)return null;if(e=e.toUpperCase(),!/^[0-9A-F]{6}$/i.test(e))return null;return{r:parseInt(e.slice(0,2),16),g:parseInt(e.slice(2,4),16),b:parseInt(e.slice(4,6),16)}}mixinColorAndAlpha(e,t){if(1===t)return e;if(e.trim().startsWith("rgba("))return e;if(e.trim().startsWith("rgb(")){const n=e.match(/rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/);if(!n)return null;return`rgba(${parseInt(n[1],10)}, ${parseInt(n[2],10)}, ${parseInt(n[3],10)}, ${t})`}const n=this.parseHexColor(e);if(!n)return null;const{r:i,g:r,b:o}=n;return`rgba(${i}, ${r}, ${o}, ${t})`}clampAlpha(e){return e<0?0:e>1?1:e}},Ut=qt;const $t=new class{constructor(){if(this._cached_config=null,this._stored_pkey=null,this.linguiseFetch=window.fetch.bind(window),document.currentScript){const e=new URL(document.currentScript.src);this._stored_pkey=e.searchParams.get("d")}this._config_request=!1,"undefined"!=typeof originalFetch&&"function"==typeof originalFetch&&(this.linguiseFetch=originalFetch.bind(window))}setPublicKey(e){!e&&this._stored_pkey||(this._stored_pkey=e)}_waitUntilConfigUnlock(){return new Promise((e=>{const t=setInterval((()=>{this._config_request||(clearInterval(t),e())}),100)}))}getStoredMetadata(){const e=document.querySelector("script#linguise-extra-metadata");if(e){const t=JSON.parse(e.textContent);return t.url.startsWith("http")||(t.url=atob(t.url),t.domain=atob(t.domain)),t}}async getConfig(e){if(this._config_request&&await this._waitUntilConfigUnlock(),this._config_request=!0,null!==this._cached_config)return this._cached_config;const t=this.getStoredMetadata();if(t)return this._cached_config=t,this._stored_pkey=t.public_key,this._config_request=!1,t;if(!this._stored_pkey)throw new Error("Linguise: No available key can be used");try{const t=await this.callAPI("/config/"+this._stored_pkey+(e?"/"+e:""),{method:"GET",headers:{"Content-Type":"application/json"}});return this._cached_config=t,this._config_request=!1,t}catch(e){throw this._config_request=!1,e}}async getUrlTranslations(e,t){if(!this._stored_pkey)throw new Error("Linguise: No available key can be used");return await this.callAPI("/translations/url/"+this._stored_pkey+"/"+e+"/"+jt().hash(decodeURI(t)),{method:"GET",headers:{"Content-Type":"application/json"}})}async callAPI(e,t={}){const n=new URL("http"+("1"==={}.API_USE_HTTPS?"s":"")+"://"+{}.API_DOMAIN+({}.API_DOMAIN_PORT?":"+{}.API_DOMAIN_PORT:"")+{}.API_BASE_PATH+e),i={};-1!==["POST","PUT"].indexOf(t.method)&&(i["Content-Type"]="application/json"),t={...t,referrerPolicy:"origin",headers:new Headers(i)};const r=await this.linguiseFetch(n,t),o=await r.json();if(!1===o.success)throw o;return o.data}getRequestedPath(e,t,n){const i=new URL(t);let r=new URL(e).pathname;if("/"===i.pathname&&r.startsWith(`/${n}`)){const e=r.replace(new RegExp(`^/${n}`),"");r=""===e.trim()||"/"===e.trim()?"/":e}else if("/"!==i.pathname){r=r.replace(i.pathname,"");const e=r.replace(new RegExp(`^/${n}`),"");r=""===e.trim()||"/"===e.trim()?"/":e}return r}async translate(e,t,n,i=null,r=!1){if(null===i&&(i=new URL("http"+("1"==={}.API_TRANSLATE_HTTPS?"s":"")+"://"+{}.API_TRANSLATE_HOST+({}.API_TRANSLATE_PORT?":"+{}.API_TRANSLATE_PORT:""))),!this._stored_pkey)throw new Error("Linguise: No available key can be used");const o=new FormData,s=new URL(window.location.href);if(o.append("version","2.3.0"),o.append("url",e),o.append("original_url",`${s.protocol}//${s.host}`),o.append("language",t),o.append("requested_path",this.getRequestedPath(window.location.href,e,t)),o.append("content",n),o.append("public_key",this._stored_pkey),o.append("response_code","200"),o.append("user_agent",navigator.userAgent),r){const e=await Ut.tryGetLinguiseEditorCookie();e&&o.append("editor_token",e)}const a=(new Date).getTime().toString(),l={Referer:window.location.href,"X-Linguise-Nonce":a,"X-Linguise-Key":this._stored_pkey,"X-Linguise-Language":t,"X-Linguise-Hash":await(async(e,t,n)=>{const i=new Rt;i.update(`${e}_${t}-${n}`);const r=await i.digest();return Array.from(r).map((e=>e.toString(16).padStart(2,"0"))).join("")})(this._stored_pkey,a,t)},c=await this.linguiseFetch(i,{method:"POST",body:o,headers:l}),u=await c.json();if(void 0!==u.response_code&&200!==u.response_code)throw new Error(u.message);return u}},Vt=$t;n(260);const Kt=new Set;for(const e in pt)pt[e].forEach((e=>{Kt.add(e)}));const Gt=class{constructor(){this.initalized=!1,this.opened=!1,Promise.resolve().then(n.bind(n,761))}load(e){const t=e.available_languages.find((t=>t.code===e.current_language)),n=e.language_settings,i=document.createElement("div");return i.id="linguise_floating_wrapper",i.innerHTML=`<div class="linguise_switcher ${"rectangular"===n?.flag_shape?"linguise_flag_rectangular":"linguise_flag_rounded"}">\n                            <a href="#" class="linguise_switcher_popup" data-linguise-switcher="floating">\n                                ${Ut.renderFlag(n,t.code)}\n                                ${Ut.renderLanguageName(n,t.code,t.name,t.original_name)}\n                            </a>\n                          </div>`,Ut.replaceLinguiseShortcodeToSwitcher(n?.position,i,e),this.postLoad(e),i}postLoad(e){document.querySelectorAll('[data-linguise-switcher="floating"]').forEach((t=>{t.__linguise_click_handled||(t.addEventListener("click",(t=>{t.preventDefault(),this.click(e.language_settings,e)})),t.__linguise_click_handled=!0)}))}click(e,t){if(!document.getElementById("dashboard-live-preview")){if(!0===this.initalized)return void this.switch();this.initalized=!0}Ut.sort(t);const n=e&&Object.keys(e).length>0?e:t.language_settings,i=Ut.getOrderedLanguageList(t.available_languages,t.default_language,n?.language_flag_order),r=document.createElement("div");r.innerHTML=`\n            <a class="close" href="#"><span></span></a>\n            <ul>\n                ${i.map((e=>`\n                            <li class="${t.default_language===e.code?"current":""}">\n                                <a href="${Ut.getLanguageUrl(t,e.code)}">\n                                   ${Ut.renderFlag(n,e.code)}\n                                   ${Ut.renderLanguageName(n,e.code,e.name,e.original_name,!0)}\n                                </a>\n                            </li>`)).join("")}\n            </ul>\n`,r.id="linguise_popup",r.className="linguise_switcher "+("rectangular"===n?.flag_shape?"linguise_flag_rectangular":"linguise_flag_rounded");const o=r.querySelector("ul");if(o){if(n?.pre_text){const e=document.createElement("p");e.classList.add("linguise_pre_text"),e.innerHTML=n.pre_text,o.parentNode.insertBefore(e,o)}if(n?.post_text){const e=document.createElement("p");e.classList.add("linguise_post_text"),e.innerHTML=n.post_text,o.parentNode.appendChild(e)}}const s=document.createElement("div");s.id="linguise_popup_container",s.appendChild(r),document.body.appendChild(s),setTimeout((function(){s.classList.add("show_linguise_popup_container")}),100);const a=document.createElement("div");a.id="linguise_background",document.body.appendChild(a);const l=document.getElementsByClassName("popup_linguise_lang_name");Ut.colorAndHoverLanguageName(n,[],l),document.querySelectorAll("#linguise_background, #linguise_popup .close").forEach((e=>e.addEventListener("click",(e=>{e.preventDefault(),this.switch()})))),this.opened=!0}switch(){document.getElementById("dashboard-live-preview")?(document.getElementById("linguise_background").remove(),document.getElementById("linguise_popup_container").classList.remove("show_linguise_popup_container"),setTimeout((()=>{document.getElementById("linguise_popup_container").remove()}),100),this.opened=!1):(document.getElementById("linguise_background").style.display=this.opened?"none":"",document.querySelector("#linguise_popup_container").classList.toggle("show_linguise_popup_container"),this.opened=!this.opened)}};const Xt=class{constructor(e){Promise.resolve().then(n.bind(n,56))}load(e){const t=e.available_languages.find((t=>t.code===e.current_language));Ut.sort(e);const n=e.language_settings,i=Ut.getOrderedLanguageList(e.available_languages,e.default_language,n?.language_flag_order),r=document.createElement("div");return r.innerHTML=`\n            <ul class="linguise_switcher linguise_switcher_dropdown ${"rectangular"===n?.flag_shape?"linguise_flag_rectangular":"linguise_flag_rounded"}">\n                <li class="linguise_current">\n                    <div class="linguise_current_lang">\n                        ${Ut.renderFlag(n,e.current_language)}\n                        ${Ut.renderLanguageName(n,e.current_language,t.name,t.original_name)}\n                        <span class="lccaret top">\n                            <svg height="48" viewBox="0 96 960 960" width="48">\n                                <path d="M480 699q-6 0-11-2t-10-7L261 492q-8-8-7.5-21.5T262 449q10-10 21.5-8.5T304 450l176 176 176-176q8-8 21.5-9t21.5 9q10 8 8.5 21t-9.5 22L501 690q-5 5-10 7t-11 2Z"/>\n                            </svg>\n                        </span>\n                    </div>\n                    <ul class="linguise_switcher_sub linguise_switcher_un_mm ${e.available_languages.length>9?"many_languages":""}">\n                        ${i.map((t=>{if(t.code!==e.current_language)return`<a href="${Ut.getLanguageUrl(e,t.code)}">\n                                            <li class="linguise_lang_item" data-lang="${t.code}" id="linguise_lang_item">\n                                                ${Ut.renderFlag(n,t.code)}\n                                                ${Ut.renderLanguageName(n,t.code,t.name,t.original_name,!0)}\n                                            </li>\n                                        </a>`})).join("")}\n                    </ul>\n                </li>\n            </ul>`,r.id="dropdown",Ut.replaceLinguiseShortcodeToSwitcher(n?.position,r,e),this.postLoad(e),r}postLoad(e){const t="linguise_switcher_sub linguise_switcher_un_mm "+(e.available_languages.length>9?"many_languages":""),n=document.querySelectorAll(".linguise_switcher_un_mm");Array.from(n).forEach((e=>{if(e.__observer)return;const n=new MutationObserver((i=>{i.forEach((i=>{if("attributes"===i.type&&"class"===i.attributeName){[e.classList.contains("linguise_switcher_un_mm"),e.classList.contains("linguise_switcher_sub"),e.classList.contains("many_languages")].filter(Boolean).length.length!==e.classList.length&&(e.className=t,n.disconnect(),e.__observer=null)}}))}));e.__observer=n,n.observe(e,{attributes:!0,attributeFilter:["class"]})}))}};const Yt=class{constructor(){Promise.resolve().then(n.bind(n,452))}load(e){Ut.sort(e);const t=e.language_settings,n=Ut.getOrderedLanguageList(e.available_languages,e.default_language,t?.language_flag_order),i=document.createElement("div");return i.innerHTML=`\n            <div class="linguise_switcher side_by_side_lang_list ${"rectangular"===t?.flag_shape?"linguise_flag_rectangular":"linguise_flag_rounded"}">\n                ${n.map((n=>`<a href="${Ut.getLanguageUrl(e,n.code)}">\n                                ${Ut.renderFlag(t,n.code)}\n                                ${Ut.renderLanguageName(t,n.code,n.name,n.original_name)}\n                            </a>`)).join("")}\n            </div>`,i.id="side_by_side",Ut.replaceLinguiseShortcodeToSwitcher(t?.position,i,e),i}postLoad(){}};const Jt=class{constructor(e){this.domain=null,this.base_url=null,this.domain_id=null,this.current_language=null,this.default_language=null,this.translate_urls=null,this.available_languages=[],this.scheme=null,this.host=null,this.original_path="",this.alternate_link_injected=!1,this.cms_automatic_switcher=!1,this.path=null,this.trailing_slashes="",this.search=null,this.url_translations={},this.language_settings={},this.domain_structure=null,this.linguise_configs={},this.demo_mode=!1,this._current_script=e}async initialize(){if(this.setConfigs(),0===Object.keys(this.linguise_configs).length&&!await this.getDomainConfig())return;const e=this.getCurrentLanguage(),t=this.getPath();if("/"!==t&&!t.endsWith(".php")&&this.translate_urls&&(this.url_translations=await Vt.getUrlTranslations(e,this.getPath())),this.addAlternates(),this.injectCustomCSS(),this.addFlag(),"shopify"===this.platform&&!this.pathShopifyCookies()){const e=setInterval((()=>{this.pathShopifyCookies()&&clearInterval(e)}),1e3)}}pathShopifyCookies(){const e=document.cookie.split(";");for(let t=0;t<e.length;t++){const n=e[t].trim();if(n.startsWith("cart=")){const e="cart="+n.substring(5)+";domain=."+this.domain.split("//",2)[1]+";path=/";return document.cookie=e,!0}}return!1}getCurrentLanguage(){if(null!==this.current_language)return this.current_language;let e=this.default_language;for(const t in this.available_languages)if(this.available_languages[t].code===document.documentElement.lang){e=document.documentElement.lang;break}if(this.current_language=e,"subdomains"===this.domain_structure){const e=this.getCurrentLanguageFromDomain();e&&e!==this.current_language&&(this.current_language=e)}return this.current_language}getCurrentLanguageFromDomain(){let e=null;const t=new URL(this.domain).hostname.split(".")[0];for(const n in this.available_languages)if(this.available_languages[n].code===t){e=t;break}return e}async getDomainConfig(){const e=await Vt.getConfig(this.domain_id);return!!e&&(this.base_url=e.url,this.domain=e.domain,this.available_languages=e.languages,this.default_language=e.language,this.translate_urls=!!e.translate_urls,this.language_settings=e.language_settings,this.domain_structure=e.structure,!0)}addFlag(e={}){switch(Object.keys(e).length>0&&(this.language_settings=e),this.language_settings?.display){case"dropdown":{const e=new Xt;this._switcher=e,this._switcher_element=e.load(this),e.postLoad(this);break}case"popup":{const e=new Gt;this._switcher=e,this._switcher_element=e.load(this),e.postLoad(this);break}case"side_by_side":{const e=new Yt;this._switcher=e,this._switcher_element=e.load(this);break}default:{const e=new Gt;this._switcher=e,this._switcher_element=e.load(this);break}}this._switcher.postLoad(this),this.monitorDOM(),Promise.resolve().then(n.bind(n,907))}addAlternates(){this.alternate_link_injected||(this.available_languages.forEach((e=>{this.injectAlternate(e.code)})),this.injectAlternate("x-default"))}injectAlternate(e){const t=document.createElement("link");t.rel="alternate",t.hreflang=e,"x-default"===e||e===this.default_language?t.href=Ut.fixTrailingSlashes(this,this.base_url):t.href=Ut.getLanguageUrl(this,e),document.head.appendChild(t)}injectCustomCSS(){if(!this.is_joomla&&!this.is_wp&&this.language_settings?.custom_css){const e=document.createElement("style");e.setAttribute("data-linguise-css","custom-css"),e.innerHTML=this.language_settings.custom_css,document.head.appendChild(e)}}getPath(){if(null!==this.path)return this.path;if(this.demo_mode)return this.path="/",this.trailing_slashes="",this.search="",this.path;const e=new URL(this.domain);this.path=document.location.pathname.replace(/\/+$/,""),this.trailing_slashes=document.location.pathname.endsWith("/")?"/":"",this.search=document.location.search,"/"!==e.pathname&&(this.path=this.path.substring(e.pathname.length)),this.scheme=e.protocol,this.host=e.host;for(let e=0;e<this.available_languages.length;e++){const t=new RegExp("^/(?:"+this.available_languages[e].code+")($|/.*)").exec(this.path);if(t){this.path=t[1];break}}return""===this.path&&(this.path="/"),this.path}setConfigs(){if(this.is_joomla=!("undefined"==typeof linguise_configs||!linguise_configs.vars.configs.joomla_languages),this.is_wp=!("undefined"==typeof linguise_configs||linguise_configs.vars.configs.joomla_languages),this.is_joomla)return this.setJoomlaLinguiseConfig();if(this.is_wp)return this.setWPLinguiseConfig();const e=this._current_script||document.currentScript;if(e){const t=new URL(e.src),n=t.searchParams.get("d"),i=t.searchParams.get("i");if(!n&&!Vt._stored_pkey)return;Vt.setPublicKey(n),this.domain_id=i||null}}getCurrentLocation(){const e=new URL(window.location.href),t=e.pathname.endsWith("/")?"/":"";return`${e.protocol}//${e.host}${t}`}setJoomlaLinguiseConfig(){const{flag_display_type:e,language_default:t,display_position:n,languages:i,base_url:r,enable_language_name:o,enable_flag:s,enable_language_short_name:a,language_name_display:l,alternate_link:c,original_path:u,automatic_switcher:h}=linguise_configs.vars.configs;this.domain=r||this.getCurrentLocation(),this.base_url=r||this.getCurrentLocation(),this.linguise_configs=linguise_configs.vars.configs,this.available_languages=Object.keys(i).map((e=>({code:e,name:i[e],original_name:i[e]}))),this.default_language=t,this.alternate_link_injected=Boolean(c),this.cms_automatic_switcher=Boolean(h),this.original_path=u,this.language_settings={display:e,position:n,enabled_lang_name:parseInt(o),enabled_lang_short_name:parseInt(a),enabled_flag:parseInt(s),lang_name_display:l,...this.linguise_configs}}setWPLinguiseConfig(){const{flag_display_type:e,default_language:t,display_position:n,languages:i,base_url:r,demo_mode:o,enable_language_short_name:s,enable_language_name:a,enable_flag:l,language_name_display:c,alternate_link:u,original_path:h,add_flag_automatically:d}=linguise_configs.vars.configs,f=this.getCurrentLocation();o?(this.base_url=f,this.domain=f):(this.base_url=r||f,this.domain=r||f),this.demo_mode=Boolean(o),this.linguise_configs=linguise_configs.vars.configs,this.available_languages=Object.keys(i).map((e=>({code:e,name:i[e],original_name:i[e]}))),this.default_language=t,this.original_path=h,this.alternate_link_injected=Boolean(u),this.cms_automatic_switcher=Boolean(d),this.language_settings={display:e,position:n,enabled_lang_short_name:s,enabled_lang_name:a,enabled_flag:l,lang_name_display:c,...this.linguise_configs}}_uniqueElementNode(e){const t=new Set,n=[];for(const i of e)t.has(i)||(t.add(i),n.push(i));return n}_findLinguiseSwitcherRoot(e){const t=[];if(e.nodeType===Node.ELEMENT_NODE){let n=!1;if(e.classList&&e.classList.contains("linguise_switcher_root")?(t.push(e),n=!0):"[linguise]"===e.textContent?.trim()&&(e.__linguise_in_place=!0,t.push(e),n=!0),!n)for(let n=0;n<e.children?.length;n++)t.push(...this._findLinguiseSwitcherRoot(e.children[n]))}return t}monitorDOM(){new MutationObserver((e=>{e.forEach((e=>{let t=!1;if("childList"===e.type){e.addedNodes.forEach((e=>{if(!e.isConnected)return;const n=this._uniqueElementNode(this._findLinguiseSwitcherRoot(e));if(n.length>0&&this._switcher&&this._switcher_element){const i=this.language_settings;n.forEach((n=>{const r=n.classList?.contains("linguise_menu_root")||e.classList?.contains("linguise_menu")||n?.__linguise_in_place;Ut.replaceAndPaintElement(n,this._switcher_element,i),r||this.demo_mode||Ut.languagePosition(n,i?.display,i?.position),t=!0}))}}));const n=e.target;if(n.nodeType===Node.ELEMENT_NODE&&this._switcher_element&&this._switcher){const e=this.language_settings;if(n.classList&&n.classList.contains("linguise_switcher_root")){const i=n.classList.contains("linguise_menu_root")||n.classList.contains("linguise_menu");Ut.replaceAndPaintElement(n,this._switcher_element,e),i||this.demo_mode||Ut.languagePosition(n,e?.display,e?.position),t=!0}else"[linguise]"===n.textContent?.trim()&&(Ut.replaceAndPaintElement(n,this._switcher_element,e),t=!0)}}t&&this._switcher&&this._switcher.postLoad(this)}))})).observe(document.body,{childList:!0,subtree:!0})}};var Zt=document.currentScript,Qt=new URL(Zt.src),en=new URL("../../assets/images/transparent-checker.png",Qt);function tn(e){if(e)setTimeout((function(){e.fadeOut(2e3),setTimeout((function(){e.remove()}),2100)}),3e3);else{var n=t()(".linguise-notification-popup");n.length&&setTimeout((function(){n.fadeOut(2e3),setTimeout((function(){n.remove()}),2100)}),3e3)}}jQuery(document).ready((function(e){var t,n={isSaving:!1,isNoAPIKey:!0,storedToken:null,activeTab:null,tabs:[],tabsHideSave:[],coloramaSupported:[]};if(window.linguise_configs){var i=window.linguise_configs.vars.configs;i.current_language=i.default_language;var o,s=document.querySelector('[name="linguise_options[custom_css]"]');s&&(o=r().fromTextArea(s,{theme:"linguise-day",lineNumbers:!0,lineWrapping:!0,autoRefresh:!0,styleActiveLine:!0,fixedGutter:!0,coverGutterNextToScrollbar:!1,gutters:["CodeMirror-lint-markers"],extraKeys:{"Ctrl-Space":"autocomplete"},mode:"css"}));var a=document.createElement("style");a.setAttribute("data-linguise-custom-css","1"),document.head.appendChild(a),i.custom_css&&(a.innerHTML=i.custom_css),o&&o.on("blur",(function(e,t){var n=e.getValue();s&&(s.value=n),i.custom_css=n,a.innerHTML=n}));var l=e('.nav-tabs[data-toggle="tab"]'),c=e(".save-settings-btn");l.each((function(e,t){var i=t.dataset,r=i.target,o=i.saveHide;if(n.tabs.push(r),t.classList.contains("active")&&!n.activeTab){n.activeTab=r;var s=document.querySelector("#".concat(r));s&&(s.classList.add("active"),s.setAttribute("data-linguise-fieldset",r))}else if(t.classList.contains("active")&&n.activeTab&&n.activeTab!==r){t.classList.remove("active");var a=document.querySelector("#".concat(r));a&&(a.classList.remove("active"),a.setAttribute("data-linguise-fieldset",r))}o&&"1"===o&&n.tabsHideSave.push(r)})),window.addEventListener("hashchange",(function(e){var t=window.location.hash.replace("#",""),i=n.tabs.find((function(e){return e===t}));i&&i!==n.activeTab&&(b(i),"advanced"===i&&setTimeout((function(){var e;null===(e=o)||void 0===e||e.refresh()}),1))})),l.on("click",(function(e){e.stopPropagation(),e.preventDefault();var t=e.currentTarget.dataset.target;window.location.hash=t}));var u=window.location.hash.replace("#","");u&&n.tabs.includes(u)&&(b(u),"advanced"===u&&setTimeout((function(){var e;null===(e=o)||void 0===e||e.refresh()}),1)),Boolean(null===(t=i.token)||void 0===t?void 0:t.trim())||u&&(window.location.hash=n.tabs[0]),e(".chat-with-us").on("click",(function(e){window.Tawk_API.toggle()})),e(".chosen-select").chosen().chosenSortable();var h=/\(([\w-]{2,5})\)$/;e("#ms-translate-into").on("chosen_sortabled",(function(t){var n={},r=[];n[i.current_language]=_(i.current_language),e("#ms_translate_into_chosen .search-choice").each((function(t,i){var o=e(i).find("span").text().trim().match(h);n[o[1]]=_(o[1]),r.push(o[1])})),i.enabled_languages=r,i.languages=n,e('[name="enabled_languages_sortable"]').val(r.join()).trigger("change"),y(i)})),e('select[name="linguise_options[default_language]"]').on("change",(function(e){w(),y(i)})),e("#ms-translate-into").on("change",(function(e){w(),y(i)}));var d=["language_name_display"];e("[data-linguise-radio]").on("change",(function(e){var t=e.currentTarget.dataset.linguiseRadio;t&&(i[t]=e.currentTarget.value,d.includes(t)&&w(),y(i))})),e("[data-linguise-radio-int]").on("change",(function(e){var t=e.currentTarget.dataset.linguiseRadioInt,n=e.currentTarget.dataset.linguiseRadioIntCorrect;t&&n&&(i[t]=e.currentTarget.value===n?1:0,y(i))})),e("[data-int-checkbox]").on("change",(function(e){var t=e.currentTarget.dataset.intCheckbox,n=e.currentTarget.checked;t&&(i[t]=n?1:0,y(i))})),e("[data-linguise-int]").on("change",(function(e){var t=e.currentTarget.dataset.linguiseInt;if(t){var n=parseInt(e.currentTarget.value,10);Number.isNaN(n)||(i[t]=n,y(i))}})),e("[data-colorama]").each((function(t,r){var o=e(r),s=r.parentElement,a=s.querySelector("[data-alpharama]"),l=s.querySelector("[data-alpharama-target]");a&&l&&k(l,r.value,a.value),o.iris({hide:!0,change:function(t,n){var r=e(this).data("colorama");if(e('[data-colorama-target="'.concat(r,'"]')).css("background-color",n.color.toString()),i[r]=n.color.toString(),a&&l){var o=k(l,n.color.toString(),a.value);o&&(i[r]=o)}this.dispatchEvent(new Event("input",{bubbles:!0})),y(i)}}),n.coloramaSupported.push(r);var c=s.querySelector(".iris-picker");r.addEventListener("focus",(function(e){c.style.display="block"}))})),e("[data-alpharama]").each((function(e,t){var n=t.dataset.alpharama,r=document.querySelector('[data-alpharama-target="'.concat(n,'"]')),o=t.parentElement.querySelector("[data-colorama]");t.addEventListener("change",(function(e){var t,s=null!==(t=parseFloat(e.currentTarget.value))&&void 0!==t?t:1,a=o.value,l=k(r,a,s);i[n]=s,l&&(i[n.replace("_alpha","")]=l),y(i)}))})),document.addEventListener("click",(function(e){null===e.target.closest(".linguise-color-group")&&n.coloramaSupported.forEach((function(e){var t=e.parentElement.querySelector(".iris-picker");"none"!==t.style.display&&(t.style.display="none")}))})),e("[data-colorama-target]").on("click",(function(e){e.stopPropagation(),e.preventDefault();var t=e.currentTarget.parentElement.querySelector(".iris-picker"),n=t.style.display;t.style.display="none"===n?"block":"none"}));document.querySelectorAll("[data-clipboard-text]").forEach((function(t){t.addEventListener("click",(function(t){var n;t.preventDefault();var i=null===(n=t.currentTarget)||void 0===n?void 0:n.getAttribute("data-clipboard-text");i&&function(t){if(window.navigator.clipboard)window.navigator.clipboard.writeText(t).then((function(){var t=e('<div class="linguise-notification-popup"><span class="material-icons">done</span>Copied to clipboard</div>');e("body").append(t),tn(t)})).catch((function(t){console.log(t);var n=e('<div class="linguise-notification-popup"><span class="material-icons fail">close</span>Failed copying to clipboard</div>');e("body").append(n),tn(n)}));else{var n=e('<div class="linguise-notification-popup"><span class="material-icons fail">close</span>Failed copying to clipboard</div>');e("body").append(n),tn(n)}}(i)}))})),e('[name="linguise_options[cache_enabled]"]').on("change",(function(t){t.currentTarget.checked?e('[data-id="cache-wrapper"]').show():e('[data-id="cache-wrapper"]').hide()})),e('[name="linguise_options[cache_enabled]"]').trigger("change"),e('[data-linguise-action="clear-cache"]').on("click",(function(t){t.preventDefault();var n=t.currentTarget.getAttribute("data-action-link");e.ajax({url:n,method:"POST",success:function(t){if("string"==typeof t){"0"!==t&&""!==t||(t="Cache empty!");var n=e('<div class="linguise-notification-popup"><span class="material-icons">done</span> '+t+"</div>");e("body").append(n),tn(n)}else{var i=e('<div class="linguise-notification-popup"><span class="material-icons fail">close</span> Failed to clear cache!</div>');e("body").append(i),tn(i)}},error:function(t,n,i){console.error("Error:",i);var r=t.responseJSON,o='<div class="linguise-notification-popup"><span class="material-icons fail">close</span> ';r&&r.message?o+=r.message:o+="Failed to clear cache!";var s=e(o+="</div>");e("body").append(s),tn(s)}})})),e('[data-linguise-action="clear-debug"]').on("click",(function(t){t.preventDefault();var n=t.currentTarget.getAttribute("href");e.ajax({url:n,method:"POST",success:function(t){if(t.error){console.error("Clear debug:",t.message);var n=e('<div class="linguise-notification-popup"><span class="material-icons fail">close</span> Failed to clear debug file!</div>');e("body").append(n),tn(n)}else{var i=e('<div class="linguise-notification-popup"><span class="material-icons">done</span> '+t.message+"</div>");e("body").append(i),tn(i)}},error:function(t,n,i){console.error("Error:",i);var r=t.responseJSON,o='<div class="linguise-notification-popup"><span class="material-icons fail">close</span> ';r&&r.message?o+=r.message:o+="Failed to clear debug file!";var s=e(o+="</div>");e("body").append(s),tn(s)}})})),window.forceEnableLinguise=function(){document.querySelectorAll(".linguise-options").forEach((function(e){e.classList.remove("is-disabled")}))},i.enabled_languages.forEach((function(e){var t,n=(null===(t=i.all_languages[e])||void 0===t?void 0:t.name)||e;i.languages[e]=n}));var f=x(i.flag_shadow_color||"#000000",i.flag_shadow_color_alpha||1);f&&(i.flag_shadow_color=f);var p=x(i.flag_hover_shadow_color||"#000000",i.flag_hover_shadow_color_alpha||1);p&&(i.flag_hover_shadow_color=p),dt(document.querySelector("form.linguise-config-form")),y(i),document.querySelectorAll('template[data-linguise="modal-iframe"]').forEach((function(e){var t=e.content.querySelector(".linguise-modal-warn-area").cloneNode(!0),n=e.getAttribute("data-template");t.setAttribute("data-linguise-popup",n),document.querySelector('[data-linguise-popup="'.concat(n,'"]'))||document.body.appendChild(t)}));var g="preview-visible",m=document.querySelector(".preview-toggle"),v=document.querySelector(".preview-area");m.addEventListener("click",(function(e){e.preventDefault(),m.classList.toggle(g),v.classList.toggle(g)})),document.querySelectorAll('[data-linguise-action="logout"]').forEach((function(t){t.addEventListener("click",(function(n){n.preventDefault();var i=new URL(t.getAttribute("data-href"));i.searchParams.set("linguise_action","logout"),e.ajax({url:i,method:"POST",success:function(t){if(t.error){var n=e('<div class="linguise-notification-popup"><span class="material-icons fail">close</span> Failed to logout!</div>');e("body").append(n),tn(n)}else window.location.reload()},error:function(t){var n=e('<div class="linguise-notification-popup"><span class="material-icons fail">close</span> Failed to logout!</div>');e("body").append(n),tn(n)}})}))})),ht()}else console.warn("No linguise configs found, exiting early.");function y(e){document.getElementById("dashboard-live-preview").innerHTML="",document.querySelector("script#config-script").textContent="var linguise_configs = {vars: {configs: ".concat(JSON.stringify(e),"}}");var t=new Jt;t.setConfigs(),t.getCurrentLanguage(),t.getPath(),t.demo_mode=!0,t.is_joomla=!1,t.is_wp=!1,"function"==typeof t.injectCustomCSS&&t.injectCustomCSS(),t.addFlag(t.language_settings)}function b(t){l.each((function(i,r){var o=e(r).data("target"),s=document.querySelector('[data-id="'.concat(o,'"]'));t===o?(r.classList.add("active"),null==s||s.classList.add("active"),n.activeTab=t):(r.classList.remove("active"),null==s||s.classList.remove("active")),n.tabsHideSave.includes(t)?c.hide():c.show()}))}function _(e){var t,n,r=null===(t=i.all_languages[e])||void 0===t?void 0:t.name,o=null===(n=i.all_languages[e])||void 0===n?void 0:n.original_name;return("en"===i.language_name_display?r:o)||e}function w(){var t=document.querySelector('select[name="linguise_options[default_language]"]');i.default_language=t.value,i.current_language=t.value;var n={};n[i.current_language]=_(i.current_language);var r=[],o=document.querySelector("#ms-translate-into"),s=o.querySelectorAll("option");s.forEach((function(e){e.selected&&(e.value===i.current_language?e.setAttribute("disabled","disabled"):e.removeAttribute("disabled"),r.push(e.value))})),i.enabled_languages=r.filter((function(e){return e!==i.current_language})),s.forEach((function(t){t.value===i.current_language&&t.selected&&(t.selected=!1,e(o).trigger("chosen:updated"))})),r.forEach((function(e){n[e]=_(e)})),i.languages=n}function x(e,t){var n=function(e){if(3===(e=e.replace("#","")).length)e=e.split("").map((function(e){return e+e})).join("");else if(6!==e.length)return null;return e=e.toUpperCase(),/^[0-9A-F]{6}$/i.test(e)?{r:parseInt(e.slice(0,2),16),g:parseInt(e.slice(2,4),16),b:parseInt(e.slice(4,6),16)}:null}(e);if(!n)return null;var i=n.r,r=n.g,o=n.b;return"rgba(".concat(i,", ").concat(r,", ").concat(o,", ").concat(t,")")}function k(e,t,n){var i=x(t,n);return null!==i&&(e.style.backgroundImage="linear-gradient(".concat(i,"), url(").concat(en.href,")")),i}})),jQuery(document).ready((function(e){tn(),window.history.replaceState(null,null,window.location.href),document.querySelectorAll("[data-tippy]").forEach((function(e){var t=e.dataset.tippyDirection||"top";ut(e,{theme:"reviews",animation:"scale",animateFill:!1,maxWidth:300,duration:0,arrow:!0,placement:t,onShow:function(e){e.setContent(e.reference.dataset.tippy),e.popper.hidden=!e.reference.dataset.tippy;var t=e.popper.querySelector(".tippy-box");t&&t.classList.add("show")}})}))}))})()})();