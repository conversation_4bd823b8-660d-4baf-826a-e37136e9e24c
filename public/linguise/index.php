<?php

// Redirect to zz-zz if accessed directly

// Check if ui-config.php exists
$current_dir = realpath(dirname(__FILE__));
$ui_config_path = $current_dir . DIRECTORY_SEPARATOR . 'ui-config.php';

function get_zz_zz_path() {
    $has_ending_slash = substr($_SERVER['REQUEST_URI'], -1) === '/';
    $script_location = rtrim(dirname($_SERVER['SCRIPT_NAME']), '/');
    $correct_url = rtrim(str_replace('linguise', 'zz-zz', $script_location), '/');
    $correct_url .= $has_ending_slash ? '/' : '';
    return $correct_url;
}

function do_redirect_and_exit() {
    $correct_url = get_zz_zz_path();
    header('Location: ' . $correct_url);
    http_response_code(302);
    exit;
}

if (!file_exists($ui_config_path)) {
    do_redirect_and_exit();
} else {
    define('LINGUISE_SCRIPT_TRANSLATION', true);
    require_once $ui_config_path;

    if (defined('LINGUISE_OOBE_DONE') && !LINGUISE_OOBE_DONE) {
        // If OOBE is not done, redirect to zz-zz
        do_redirect_and_exit();
    }
}

?>

<h1>Linguise</h1>
<p>
    You're currently accessing the Linguise folder directly.<br />
    You might meant to access <code><?php echo get_zz_zz_path(); ?></code> instead for Linguise management.
</p>
