<IfModule mod_rewrite.c>
    RewriteEngine On

    # Let Linguise handle its own endpoint
    RewriteRule ^linguise/linguise.php$ - [L]

    # ✅ Skip /api and /cms from being processed by Ling<PERSON> or <PERSON><PERSON> rewrite
    RewriteCond %{REQUEST_URI} ^/(api|cms)(/|$)
    RewriteRule ^ - [L]

    # Linguise language-prefixed URL rewrite
    RewriteRule ^(af|sq|am|ar|hy|az|eu|be|bn|bs|bg|ca|ceb|ny|zh-cn|zh-tw|co|hr|cs|da|nl|en|eo|et|tl|fi|fr|fy|gl|ka|de|el|gu|ht|ha|haw|iw|hi|hmn|hu|is|ig|id|ga|it|ja|jw|kn|kk|km|ko|ku|ky|lo|la|lv|lt|lb|mk|mg|ms|ml|mt|mi|mr|mn|my|ne|no|ps|fa|pl|pt|pa|ro|ru|sm|gd|sr|st|sn|sd|si|sk|sl|so|es|su|sw|sv|tg|ta|te|th|tr|uk|ur|uz|vi|cy|xh|yi|yo|zu|zz-zz)(?:$|/)(.*)$ linguise/linguise.php?linguise_language=$1&original_url=$2 [L,QSA]

    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # Laravel fallback (route everything else to index.php)
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>
