<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit662cefddda37d75487a93c211e51de4e
{
    public static $prefixLengthsPsr4 = array (
        'L' => 
        array (
            'Linguise\\Script\\Core\\' => 21,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'Linguise\\Script\\Core\\' => 
        array (
            0 => __DIR__ . '/../..' . '/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
        'Linguise\\Script\\Core\\AfterUpdate' => __DIR__ . '/../..' . '/src/AfterUpdate.php',
        'Linguise\\Script\\Core\\Boundary' => __DIR__ . '/../..' . '/src/Boundary.php',
        'Linguise\\Script\\Core\\Cache' => __DIR__ . '/../..' . '/src/Cache.php',
        'Linguise\\Script\\Core\\Certificates' => __DIR__ . '/../..' . '/src/Certificates.php',
        'Linguise\\Script\\Core\\Configuration' => __DIR__ . '/../..' . '/src/Configuration.php',
        'Linguise\\Script\\Core\\CurlMulti' => __DIR__ . '/../..' . '/src/CurlMulti.php',
        'Linguise\\Script\\Core\\CurlRequest' => __DIR__ . '/../..' . '/src/CurlRequest.php',
        'Linguise\\Script\\Core\\Database' => __DIR__ . '/../..' . '/src/Database.php',
        'Linguise\\Script\\Core\\Databases\\Mysql' => __DIR__ . '/../..' . '/src/Databases/Mysql.php',
        'Linguise\\Script\\Core\\Databases\\Sqlite' => __DIR__ . '/../..' . '/src/Databases/Sqlite.php',
        'Linguise\\Script\\Core\\Debug' => __DIR__ . '/../..' . '/src/Debug.php',
        'Linguise\\Script\\Core\\Defer' => __DIR__ . '/../..' . '/src/Defer.php',
        'Linguise\\Script\\Core\\Helper' => __DIR__ . '/../..' . '/src/Helper.php',
        'Linguise\\Script\\Core\\Hook' => __DIR__ . '/../..' . '/src/Hook.php',
        'Linguise\\Script\\Core\\Processor' => __DIR__ . '/../..' . '/src/Processor.php',
        'Linguise\\Script\\Core\\Request' => __DIR__ . '/../..' . '/src/Request.php',
        'Linguise\\Script\\Core\\Response' => __DIR__ . '/../..' . '/src/Response.php',
        'Linguise\\Script\\Core\\SetCookie' => __DIR__ . '/../..' . '/src/SetCookie.php',
        'Linguise\\Script\\Core\\Translation' => __DIR__ . '/../..' . '/src/Translation.php',
        'Linguise\\Script\\Core\\Updater' => __DIR__ . '/../..' . '/src/Updater.php',
        'Linguise\\Script\\Core\\Url' => __DIR__ . '/../..' . '/src/Url.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit662cefddda37d75487a93c211e51de4e::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit662cefddda37d75487a93c211e51de4e::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit662cefddda37d75487a93c211e51de4e::$classMap;

        }, null, ClassLoader::class);
    }
}
