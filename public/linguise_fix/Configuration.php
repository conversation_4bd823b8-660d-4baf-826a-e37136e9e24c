<?php
namespace Linguise\Script;

use Linguise\Script\Core\Response;

if (!defined('LINGUISE_SCRIPT_TRANSLATION')) die();

class Configuration {
    /** Mandatory configuration **/
    public static $token = 'nyAqq8s4mOHgSfuPNQjxSvH8yj8x2EX6'; //Replace the token by the one found in your Linguise dashboard

    /** Basic configuration **/
    /*
     * Update the CMS value according to your CMS
     * Available CMS are: laravel, prestashop, magento
     */
    public static $cms = 'laravel';

    public $cache_enabled = true;
    public $cache_max_size = 200; // In megabyte

    /** Advanced configuration **/
    public static $server_ip = null;
    public static $server_port = 443;
    public static $debug = false;
    public static $data_dir = null;
    public static $base_dir = null;
    public static $dl_certificates = false;

    /** Advanced database configuration **/
    /*
     *  In case you don't want to use Sqlite, you can use MySQL
     *  To do so, you need to fill the following variables
     *  Linguise will create the tables for you
     */
    public static $db_host = '*************';
    public static $db_user = 'bih_web';
    public static $db_password = 'Vb{[E;+q;FQ5J{v0';
    public static $db_name = 'bih_web';
    public static $db_prefix = '';
    // If your database use SSL connection, set this into MYSQLI_CLIENT_SSL
    // https://www.php.net/manual/en/mysqli.constants.php
    public static $db_flags = 0;

    /** Development configuration */
    public static $port = 443;
    public static $host = 'translate.linguise.com';
    public static $update_url = 'https://www.linguise.com/files/php-script-update.json';

    public static function onAfterMakeRequest()
    {
        if (\Linguise\Script\Core\Configuration::getInstance()->get('cms') === 'prestashop' && !empty($_POST['action']) && !empty($_POST['id_product'])) {
            // This is a cart request, let's just return the response
            Response::getInstance()->end();
        }
    }
}
