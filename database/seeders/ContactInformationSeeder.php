<?php

namespace Database\Seeders;

use App\Models\BihConfigs;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ContactInformationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $data = [
            [
                'group' => 'contactInformation',
                'key' => 'Call Center',
                'value' => '150442'
            ],
            [
                'group' => 'contactInformation',
                'key' => 'Chat with GP (Appointment Booking)',
                'value' => '6281211661127'
            ],
            [
                'group' => 'contactInformation',
                'key' => 'Customer Service',
                'value' => '6281211661127'
            ],
            [
                'group' => 'contactInformation',
                'key' => 'Email',
                'value' => '<EMAIL>'
            ],
            [
                'group' => 'contactInformation',
                'key' => 'Emergency Number',
                'value' => '150911'
            ]
        ];

        foreach ($data as $record) {
            BihConfigs::create($record);
        }
    }
}
