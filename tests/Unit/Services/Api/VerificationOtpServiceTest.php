<?php

namespace Tests\Unit\Services\Api;

use App\Enums\Table\Otp\Type;
use App\Services\Api\VerificationOtpService;
use Tests\TestCase;

class VerificationOtpServiceTest extends TestCase
{
    /**
     * A basic unit test example.
     */
    public function test_example(): void
    {

        $data = [
            'phone'         => '89680988231',
            'country_code'  => '62',
            'email'         => '',
            'type'          => Type::LOGIN,
            'otp'           => 349042,
            "login_device_type" => 1,
            'recaptcha_token'         => '03AFcWeA4CcuNTyeX80TSGrUkDhHES8MiWfsctcUEdFS0NlS6bDVORm260lvTaZKlPy3_wQ6ivPhjmwuRiwiUrRXDAqNGwqVUYJGX8t6KcHqV3uO4-Ihn7MKt_IODFYbQsEt2JObMe8VBkZYK-Lf1ZnDtY1fSDOpWUj-YXJrVw00fvIzojqcok0aq2Oxs_3VW4zPYa0Cw0ls9Lg0Xwab6qDircgd9u73GfAqiKegpfoZkk0E7Voe5ylbsrkbNlMpl6owyGZhLAd3qmqr2pxVTyX66CbDcLyJwWSCymsaYcbn5hVij8jPtcko6BlzaB33sgFTWIE37vzJ3AoWA-4Y14vdfYR_0QgsNQNKYuIYMeXrIhmvLfHvs7tnr7L7BWvke9_4lPnU9jJQGJw65XnoAq2GnjEK7se7oCIGY5U0TVWgxNaU8XLfxA_ozvJ_PdKCZ4YeWYe6uo_0mJIqKMYqjy1mVg6Tu9cGW_SrLAeruhmnptCizhVigSr6xKg7qymfefTFbr49CNbRraH_4WnrCx3rBC3RPvYFHVJ1jyXmxKf_SqX73NEcmn-mGQflJXgzUAr85lQ_3DoJ-r5tnSEK4HsmnIkxXR1ey-dONgQbWOcRo8gZDwqDuD10nw-AEK_0l0CgUSS5EDfWlr0NpsW-2wx7b7xb-SKh8RhfesrInFj7qTeEIJ5rP6Nb23fxUN20pk1kTVT19oFEcTTukc5OGu9V_VVgt2p7zdnuewYUqDc3odvpWBmbzPDeZOvz8AF40DOQk0jgVO8ax1vhlWgFGXbAoyPvVdZb8FitZELXkpz4_vAmne20wyrgAQP1RMloM0y7Rc0INldx_ptID79mmQE_8u5vSTC5v9E-63UciDTEdv-6J0pTJzvRKMc2rZ-Bpay7DPA46RNCvL3YqVcTa2yT73-g36hXXDBQvH8YSOrRxFyUr6lo8ppgT4qrTryaRYSPmVNBnIiXtgNWNBApYIB3oqEBY-CO4d83L5MpQN8ZEB7n2hIqbmR05H8tt92FrJLwu9dWX5cUFNWhfr8y62HxPPn-PRz3X3sfKn-D0I9st43VBA1n3eC3pajg3sK5niZ1Zhi8v0qfoHUItgY9uipYHs9AHXfjHs4SgdmQQoi0M7rt4HuB6r-kMeMZg1XCJxrf3WvxQMKCDn6ZH4gu_SNJJc-TzYVDCBogG0yBZ6nwHGc4uOhAd5yXAN6UJbMpO1aZ_eUmu9cnPv1HN9kuYitamp5WEXuccO3m2_4Zclk2NNqTCWNUNHD-1GuSpSH6TVZ-BKJUWq354X1j1hBu--8exGmfoJDLbXk1oeibJE1dIwpwxi_AOXOe2pEaJyVE_Ctkuhlo8gCk7zYubdxUBKEqGSfvRbldp36sHZOES0jcUUKcXsy1XeqZ7bbjk7_u4jWBrEoXKjT5pDd9GUeGITUfYWOwFqKNX6n3XPDMbfbSUSULP51V_RRZuUJfXuyLf5OqtGYZqdqEcTp2YOYrw8_S8rSGwAx1NYf_YoNMOZ8l5vH8fwZ4stg2vD5x17rJB5H7NB65MggpPUYNFh9hO-N2Eii5t-jjeHXXeKr-slIrGZjRVdmnJltmLr8L4Aznczlrxfee_pOrwBEjQkRv6r_GdKofVPr_uVOsuf1ti8WNQvlu2j9mu0r4XIuJcmQtNTHlpINCirnaCVRW8pMX9noRAl6YjufPCAxgTQXWPawDSd2B0hHMkNyt4AqYjh3nhkkANsXffFHOV5ATWVeLj2VSzLkh2wLx6cj4Byd3eIpZl4B6yeUkGWDOqD1lOY2y80DX7tVrj8Zzisf4A0Hko1eTgRjI1kqq21OzjRTK2wAUdG0O3YOklIyWSgA4bsVHmGCx9w3O39j0p9Hu3bTjeQon53ti4f48QOFl3Jf3ZOjrZWoZrNvA8Pmlft3lEk0-1x3SuvfixfBHuvbzoh33s_IniX7mv46Uc6kus5nkicm_S-5_k4r7p8Xz9o5muSDCUessU8PLeAJvS7GGbWuXU70gP9DXJFP5-XXGJbR6dQLzFB98rLJvBybMGrSRZBg7SmtZ_P1ZBiCzskJw-y1pJde3MLRo-0gU08-Y4Em7MKV4CY3qLIxAXiUtnhTM8tN-LMOiHDKM1mZ2kXfoaezTsRjp5jdFcvOTQpVe0NjPoRuCtr42WaFNLpkTPdhF8tOcNdbVuGJUypy5p6owdwg6TolbagddQXWPue7y0I2zH-H0m2xlTIxHgMzR9Juge_nJ0qLIqBRcTN6966xuNmYWmtcGXSFayZHDYKnbw9SveilU8xCcxOYtRfwt63n4TTCtLxoem93ew74XGvSc2HAxQ4W7OKzqYi8qFqTqSZHcGFC1jcfd-s9iKlHv0oWOqSsN5nUKsL6H6QGezayOI3OX_pyKkU-OsEkx6p5ukpkshG_fM9RjOBZ-4gdawn9I2SWTyEXlGMFQ3sjUjrMRxqvLX_KazpDfHR3tyXX_rB0Jzn_viYbSdJl_zuhJ5ob9Q5j1OBTPx9Qz_H-JJ6xO9nhK-VEM3P1ekMfCzaNMyjP5JDdgHvncK8AyFJK-L_bpS3hp8wu9IZYfEyP76zgRZI77TSM2p4kDZ4fiM58UPTV-iijxuKLA0N2em3-VBZXHxySKzWdVAoRSmR_aUI6J0TEqtrTxVyGBFuv4Ild06ykUHJ1ZXhMQQa8tPzSbFd93RRS_1pM1twN4uO_gNYnoa4AaSMed11rYvumk0GYWW5FmoxBzeTa09m55CQlhf285S4rgKecxgrf931atdOwIUSia6GvXvoYLrxzqoJSVYvd2Py2Bxw18vW0MyhZzmm11Y72gfKyEAVq5D3GqChtDgr2HG8xX6zQ_3-eTzTJQT1z8q5yCIp2Yr_etz00PrYRPH13_F8sBE_sHyDegnSAFoVmVcoKZhIl2GPtDFGVti-cIyELf2Dt4BJztsXdNGd4jc8kduBeHz6ve-1NfHss2hKO3riCuGAfOhQw7BUcr-ph2u5okvALL_4uWje29hDdeGNI_XMYu5TdqZ_hkxU03GBKSxEDmEaScq8ptrgeZo9_iRzxgicuVW9anJlyc_N00k-5Gotm-megdoj0D0wXvcSC8tio3YTsVjQFD-vN8n2l1SJFRESdzovP47z2wDS5cafRrX0au5jBgsKwZgb4IoaimAgPLGN3SJ5OoOZfQ_k4wlut72F-JQ3AmnH9ZLBEJ8w_r0h41NyvwnKHDUUb58T5PlVzPIxzrIonVOMuR2VhnBUKfM-4jEBTHo1dHA3AbOG9oWv_8UW0pHspP8sqGDFqYeecAFxXPKhiyR4_txt7cQoljyc4uYPWx_C-2DQdN_PGsMiVwKG8am7vR_r_GcCS8lzXq1Af1zDRF4ZwLdOvKMFt9sy8n5YnYOmcEiU3xFOTdVGxI0ao6Mwj1kaK3pZ7GtupZFWXQ6qfpKim3Xe2RtsLM3f-TM93ggkx4IL-Bb6IKTU5YEEhZO5IuKO1aLJq_TlgdJPxdLul3nGrNp9Ve3Q7_a7FD358HC-AftgvTj7bCTgkfwgw3rNzapU3Ng1julnnTeBaocgfbivKevLfG26XLinrsgY6hYm6iuxN1e6Gm0jMeQP3lxU1zj2pWutKlXGyT4o9fUt_gtdrS3-1J9IEvbG2lFbgnu0vSZ13Dfw3Uu8UT5JmXIRLBfrZ7e_98Ywrm6s2u6Fs_TkKwzyqBVcueCcZPYfFMzpqHRL1oCCEwZK1JkEE7nlLwPrlAaMUAS1pj_cpWfQlleSqaqYUIbh0mh2fWoMeXsAO5wZEZUi-ocdmpr1r3wIzg5TRH4_zC7zyVNDUpLY58FR_VtdimK4kcR5TXk950Ttt-0nd6iine8jTADmsK0_CwKvfU8zf8-hIvBihN3w-ya9wmqcTqsLjEN6xsiq_nDuaVpFAwKq3tucBa6Og20U8yTQ8JibxBl59mpKRcNScf4K0eveTnotcArfHHC-C2LiXTXdOMVGlAtRF7VRAd7IMnTC10wjR3LJhGANLHE6se903muQ4XhoAjiH7ae_OrDeMa4-yWPZl1nL8PFoyVkfR8A7Gsg1kXh_9aPkGbzhfPNs1CIHPd3bwEW3HXKnyvxGvFzjv2E44EIBgzG8R_-4w9SpxPSiLBg1ukoqlxYdmBQXjgQ5bIU1d9hzEu1zTqn7eHJaD6mrSN6cJK4P9WDY691lW46Nok6r6FrlnTVqVHU1WsPt9knNdxa3_83CL6SFoVOb8Hns2afgBzavK8a8Pgyg54Zhdf9ohqr7iO2ipu0P8C7rz6_-UdQnLZZ1BLqp88yz1j0_FV6lU4vFs7iXZooIMRpDk_YixOMST2DOEeGx8WAjq0dW_u5ewXMWfjyfeXFrYOBRPjuQfIIjreIkl_nq0gAuhlj_Jpx-EYGNO4NZhcpJ6jR9Xuh7a4RbOmDRjVbZKaTa3rRuPtT0PxrppsgpQyesKaw1wWelglyTFtyeuLFk0cbgizKggEwyfewfiIF8-ke4x3o60tefUjExfBUxJJ1X-lU74m1jxIuM3j3FsxeV3bOxr9Qy5eDbJrfuUSWpaCgaB5auJvjh0sJQueW8iMIt4gNptAW37uwkUjA0dpIQ93qiN12wxFS1QKO6Edspaz4tlzDVz_tdymqXoTbyeUupsspUHdDvJ1IOXeUynZFN39MLdU96uDoz0ozpYsTbTxBsKKoFjrjo1Xh8IMwKaAg8tDSGVba6Ku4h8J55d19uJz7UnTes8lhhTO0s5Nt04muVbh5NKZcbGLeZwGxJGREeb12cojWjozJ7lRHAalVBhpFQlIl5kHkGYCI5YhN6mtP7wugCZczyup7mHujXmA5njWUDzzrKXt27-HgIEXixCPwC6oJcnqu2-TTOqA6Ca99G0VFdRM8AXsBsrQME7bFgpEfX7PL6Q9MDrYgVthtzIy5wb04bK2cvxF0iCEcf5cH2t70bv-GVow6zUMzwvqHFWz6y_IJ7r6YjInpTyYRO5CP4ocY2qZIaCfDNLgGWvR86W10d34jqoKlz3QyKbLpOJAH4wtcYU6hLTS2aApgy8pPB3t-FAgiCxb62yhUkBDzwgaN2l1l5sOruPYbSVwqama4mjYuOKO3KsBiG2grGllQDset2j8YTaEse1FgbJW-3JQdEcGuTJu-MpHm-RiNJlZuGMb8F1aIp6swwXOxUcMAb0PL5pFXE7W1RIYvpmP_GBIESkSz5RF_o0ucV9PUEgSBJOQx5cVBvAPqEWeIjvaOKh8b1CMOtdN0PVEfGNptIfkXyl9Nxrd2O05idgTRq_ruheNLm5IcA0989gy1BvOSzHfUqB_b2GiVSBLvSCC2u-caoXs-eh72bC_rsQ8-500NsQKwtXizyqyYPicK83uySERMOtMveW4R-nYEkTwbwPHh7jlCqe8pwv1b-o7YBJp9anqgHvbfbLe5tdfwZzNv0WKZeimSfZ8k-oIcXWBeliVk1fU65mbeKqs5pYtlbT9yHMdI7QDbXjh508fCggo3Tz4fdM7wXq9zPUCh_w_gi8xSW7wAOjIWBEfAOqiGspZE_UMXYczdj-n13KCftBTHWtA_myz2_a2cwfbEirQKbX7JCOdKPcDIhcoUu_FF99iUvTq8RjAoywt_Jl_mVLGLaTn9gjWzTPVzB9iavnpzsPegMrqV8tymAY3cOB98bGdSguxYuUIv3fvHxaJy7dR5-BJKfPsziuQxc37g'
        ];

        $service = (new VerificationOtpService($data))->call();

        dd($service);
        echo self::class . ' ' . __FUNCTION__ . ' RESULT ' . json_encode($service);

        $this->assertTrue(true);
    }
}
